# java
load("@buildifier_prebuilt//:rules.bzl", "buildifier")
load("@gazelle//:def.bzl", "gazelle")
load("@rules_python//python:pip.bzl", "compile_pip_requirements")

buildifier(
    name = "buildifier",
    exclude_patterns = [
        "./.git/*",
    ],
    lint_mode = "warn",
    mode = "fix",
)

# gazelle:build_file_name BUILD.bazel
# gazelle:java_test_file_suffixes Test.java,ITCase.java
# gazelle:java_test_mode suite
# gazelle:resolve java dagger //third_party:dagger
# gazelle:resolve java org.openjdk.jmh //third_party:jmh

# This rule adds a convenient way to update the requirements file.
compile_pip_requirements(
    name = "requirements",
    src = "requirements.in",
    requirements_txt = "requirements_lock.txt",
)

# gazelle:prefix pointer
gazelle(
    name = "gazelle-update-repos",
    args = [
        "-from_file=go.mod",
        "-to_macro=deps.bzl%go_dependencies",
        "-prune",
    ],
    command = "update-repos",
)

# gazelle:prefix pointer
gazelle(name = "gazelle")
