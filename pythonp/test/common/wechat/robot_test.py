import unittest
from unittest.mock import patch, MagicMock
import base64
import hashlib
from pythonp.common.wechat.robot import WeChatWorkBot


class TestWeChatWorkBot(unittest.TestCase):
    """Test cases for WeChatWorkBot class"""

    def setUp(self):
        """Set up test fixtures"""
        self.robot_key = "test_key"
        self.bot = WeChatWorkBot(self.robot_key)

    def test_init(self):
        """Test initialization of WeChatWorkBot"""
        # Test with default URL
        bot = WeChatWorkBot("test_key")
        self.assertEqual(bot.robot_key, "test_key")
        self.assertEqual(
            bot.robot_url,
            "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={}")

        # Test with custom URL
        custom_url = "https://custom.url/send?key={}"
        bot = WeChatWorkBot("test_key", custom_url)
        self.assertEqual(bot.robot_url, custom_url)

    def test_is_url(self):
        """Test URL validation"""
        # Valid URLs
        self.assertTrue(WeChatWorkBot.is_url("https://example.com"))
        self.assertTrue(WeChatWorkBot.is_url("http://example.com"))
        self.assertTrue(WeChatWorkBot.is_url("ftp://example.com"))

        # Invalid URLs
        self.assertFalse(WeChatWorkBot.is_url("not_a_url"))
        self.assertFalse(WeChatWorkBot.is_url("example.com"))
        self.assertFalse(WeChatWorkBot.is_url(""))

    @patch('requests.get')
    def test_get_image_base64_from_url(self, mock_get):
        """Test getting base64 from image URL"""
        # Mock the response
        mock_response = MagicMock()
        mock_response.content = b"fake_image_data"
        mock_get.return_value = mock_response

        # Test with URL
        result = WeChatWorkBot.get_image_base64(
            "https://example.com/image.jpg")
        self.assertEqual(result,
                         base64.b64encode(b"fake_image_data").decode('utf-8'))
        mock_get.assert_called_once_with("https://example.com/image.jpg")

    @patch('builtins.open', create=True)
    def test_get_image_base64_from_file(self, mock_open):
        """Test getting base64 from local file"""
        # Mock file read
        mock_file = MagicMock()
        mock_file.read.return_value = b"fake_image_data"
        mock_open.return_value.__enter__.return_value = mock_file

        # Test with file path
        result = WeChatWorkBot.get_image_base64("/path/to/image.jpg")
        self.assertEqual(result,
                         base64.b64encode(b"fake_image_data").decode('utf-8'))
        mock_open.assert_called_once_with("/path/to/image.jpg", 'rb')

    @patch('builtins.open', create=True)
    def test_get_image_md5(self, mock_open):
        """Test getting MD5 hash of image"""
        # Mock file read
        mock_file = MagicMock()
        mock_file.read.return_value = b"fake_image_data"
        mock_open.return_value.__enter__.return_value = mock_file

        # Calculate expected MD5
        expected_md5 = hashlib.md5(b"fake_image_data").hexdigest().upper()

        # Test MD5 calculation
        result = WeChatWorkBot.get_image_md5("/path/to/image.jpg")
        self.assertEqual(result, expected_md5)
        mock_open.assert_called_once_with("/path/to/image.jpg", mode="rb")

    @patch('requests.post')
    def test_send_message_success(self, mock_post):
        """Test successful message sending"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"errcode": 0, "errmsg": "ok"}
        mock_post.return_value = mock_response

        # Test message sending
        msg = {"msgtype": "text", "text": {"content": "test message"}}
        result = self.bot.send_message(msg)

        self.assertIsNotNone(result)
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        self.assertEqual(kwargs["json"], msg)

    @patch('requests.post')
    def test_send_message_failure(self, mock_post):
        """Test failed message sending"""
        # Mock failed response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"errcode": 1, "errmsg": "error"}
        mock_post.return_value = mock_response

        # Test message sending
        msg = {"msgtype": "text", "text": {"content": "test message"}}
        result = self.bot.send_message(msg)

        self.assertIsNone(result)

    @patch('requests.post')
    def test_send_text(self, mock_post):
        """Test sending text message"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"errcode": 0, "errmsg": "ok"}
        mock_post.return_value = mock_response

        # Test basic text message
        result = self.bot.send_text("Hello, World!")
        self.assertIsNotNone(result)

        # Test text message with mentions
        result = self.bot.send_text("Hello, @someone",
                                    mentioned_list=["someone"],
                                    mentioned_mobile_list=["13800138000"])
        self.assertIsNotNone(result)

    @patch('requests.post')
    def test_send_markdown(self, mock_post):
        """Test sending markdown message"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"errcode": 0, "errmsg": "ok"}
        mock_post.return_value = mock_response

        # Test markdown message
        markdown_content = "# Title\n**Bold** text"
        result = self.bot.send_markdown(markdown_content)
        self.assertIsNotNone(result)

    @patch('requests.post')
    @patch.object(WeChatWorkBot, 'get_image_base64')
    @patch.object(WeChatWorkBot, 'get_image_md5')
    def test_send_image(self, mock_md5, mock_base64, mock_post):
        """Test sending image message"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"errcode": 0, "errmsg": "ok"}
        mock_post.return_value = mock_response

        # Mock image processing
        mock_base64.return_value = "base64_data"
        mock_md5.return_value = "md5_hash"

        # Test image message
        result = self.bot.send_image("/path/to/image.jpg")
        self.assertIsNotNone(result)

        # Verify image processing was called
        mock_base64.assert_called_once_with("/path/to/image.jpg")
        mock_md5.assert_called_once_with("/path/to/image.jpg")


if __name__ == '__main__':
    unittest.main()
