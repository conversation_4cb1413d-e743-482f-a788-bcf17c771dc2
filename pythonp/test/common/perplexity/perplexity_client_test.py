import unittest
from unittest.mock import patch, MagicMock
import json
from openai import OpenAIError
from pythonp.common.ai.perplexity.perplexity_client import PerplexityClient, PerplexityResponse
from pythonp.common.ai.perplexity.perplexity_model import SonarModels


class TestPerplexityClient(unittest.TestCase):
    """PerplexityClient 类的测试用例"""

    def setUp(self):
        """设置测试环境"""
        self.api_key = "test_api_key"
        self.client = PerplexityClient(self.api_key)
        self.base_request = {
            "query": "你好，世界！",
            "model": SonarModels.SONAR,
            "system_prompt": "你是一个有帮助的助手"
        }

    @patch('requests.post')
    def test_chat_completion_basic(self, mock_post):
        """测试基本的聊天完成功能"""
        # 模拟响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "choices": [{
                "message": {
                    "content": "你好！很高兴见到你！"
                }
            }],
            "model": "sonar",
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 5,
                "total_tokens": 15
            }
        }
        mock_post.return_value = mock_response

        # 调用方法
        response = self.client.chat_completion(**self.base_request)

        # 断言
        self.assertIsInstance(response, dict)  # 修改为检查字典类型
        self.assertEqual(response["choices"][0]["message"]["content"],
                         "你好！很高兴见到你！")
        self.assertEqual(response["model"], "sonar")
        self.assertEqual(response["usage"]["total_tokens"], 15)

        # 验证请求参数
        mock_post.assert_called_once()
        args, kwargs = mock_post.call_args
        self.assertEqual(kwargs["headers"]["Authorization"],
                         f"Bearer {self.api_key}")
        self.assertEqual(kwargs["json"]["model"], SonarModels.SONAR.value)
        self.assertEqual(kwargs["json"]["messages"][0]["role"], "system")
        self.assertEqual(kwargs["json"]["messages"][0]["content"],
                         self.base_request["system_prompt"])
        self.assertEqual(kwargs["json"]["messages"][1]["role"], "user")
        self.assertEqual(kwargs["json"]["messages"][1]["content"],
                         self.base_request["query"])

    @patch('requests.post')
    def test_chat_completion_with_options(self, mock_post):
        """测试带选项的聊天完成功能"""
        # 模拟响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "choices": [{
                "message": {
                    "content": "这是一个测试响应"
                }
            }],
            "model": "sonar",
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 5,
                "total_tokens": 15
            }
        }
        mock_post.return_value = mock_response

        # 创建带选项的请求
        options = {
            "temperature": 0.7,
            "top_p": 0.9,
            "stream": False,  # 修改为 False，因为流式响应可能影响内容获取
            "web_search_options": {
                "search_context_size": "high"
            }
        }
        request_with_options = {
            **self.base_request,
            "options": json.dumps(options)  # 将选项转换为 JSON 字符串
        }

        # 调用方法
        response = self.client.chat_completion(**request_with_options)

        # 断言
        self.assertIsInstance(response, dict)  # 修改为检查字典类型
        self.assertEqual(response["choices"][0]["message"]["content"],
                         "这是一个测试响应")
        self.assertEqual(response["model"], "sonar")
        self.assertEqual(response["usage"]["total_tokens"], 15)

        # 验证请求参数
        args, kwargs = mock_post.call_args
        self.assertEqual(kwargs["json"]["temperature"], 0.7)
        self.assertEqual(kwargs["json"]["top_p"], 0.9)
        self.assertEqual(kwargs["json"]["stream"], False)
        self.assertEqual(
            kwargs["json"]["web_search_options"]["search_context_size"],
            "high")

    @patch('requests.post')
    def test_chat_completion_error_handling(self, mock_post):
        """测试错误处理"""
        # 模拟错误响应
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        mock_post.return_value = mock_response

        # 测试 API 错误
        with self.assertRaises(Exception) as context:
            self.client.chat_completion(**self.base_request)
        self.assertIn("API request failed with status code 400",
                      str(context.exception))

        # 模拟网络错误
        mock_post.side_effect = Exception("Network Error")
        with self.assertRaises(Exception) as context:
            self.client.chat_completion(**self.base_request)
        self.assertIn("Network Error", str(context.exception))

    @patch('requests.post')
    def test_chat_completion_streaming(self, mock_post):
        """测试流式响应功能"""
        # 模拟流式响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"choices": [{"delta": {"content": "Hello"}}]}',
            b'data: {"choices": [{"delta": {"content": ", "}}]}',
            b'data: {"choices": [{"delta": {"content": "World"}}]}',
            b'data: {"choices": [{"delta": {"content": "!"}}]}',
            b'data: [DONE]'
        ]
        mock_post.return_value = mock_response

        # 创建流式请求
        streaming_request = {
            **self.base_request,
            "options": json.dumps({"stream": True})  # 将选项转换为 JSON 字符串
        }

        # 调用方法
        response = self.client.chat_completion(**streaming_request)

        # 验证请求参数
        args, kwargs = mock_post.call_args
        self.assertEqual(kwargs["json"]["stream"], True)

    def test_invalid_api_key(self):
        """测试无效的 API 密钥"""
        with self.assertRaises(OpenAIError):  # 使用 OpenAIError
            PerplexityClient(None)  # 使用 None 而不是空字符串

    @patch('requests.post')
    def test_invalid_model(self, mock_post):
        """测试无效的模型"""
        # 模拟 401 错误响应
        mock_response = MagicMock()
        mock_response.status_code = 401
        mock_response.text = "Unauthorized"
        mock_post.return_value = mock_response

        invalid_request = {
            **self.base_request,
            "model": SonarModels.SONAR  # 使用有效的模型枚举
        }
        with self.assertRaises(Exception) as context:
            self.client.chat_completion(**invalid_request)
        self.assertIn("API request failed with status code 401",
                      str(context.exception))


if __name__ == '__main__':
    unittest.main()
