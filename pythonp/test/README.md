# Python 测试目录

本目录包含 Python 项目的各种测试代码和测试数据。

## 📁 目录结构

### common/
公共库的测试代码，包含对 `pythonp/common/` 下各个模块的单元测试和集成测试。

## 🧪 测试类型

### 单元测试
- 针对单个函数或类的测试
- 验证基本功能的正确性
- 快速执行，便于开发过程中频繁运行

### 集成测试
- 测试多个模块之间的交互
- 验证系统组件的协同工作
- 包含数据库、API 等外部依赖的测试

### 性能测试
- 测试关键功能的性能表现
- 包含响应时间、吞吐量等指标
- 用于性能优化和回归检测

## 🚀 运行测试

### 使用 Bazel 运行测试
```bash
# 运行所有测试
bazel test //pythonp/test/...

# 运行特定测试
bazel test //pythonp/test/common:test_target

# 运行测试并显示详细输出
bazel test //pythonp/test/... --test_output=all
```

### 使用 pytest 运行测试
```bash
# 在项目根目录运行
pytest pythonp/test/

# 运行特定测试文件
pytest pythonp/test/common/test_example.py

# 运行测试并生成覆盖率报告
pytest pythonp/test/ --cov=pythonp/common
```

## 📝 测试编写规范

### 文件命名
- 测试文件以 `test_` 开头
- 测试类以 `Test` 开头
- 测试方法以 `test_` 开头

### 测试结构
```python
import unittest
from pythonp.common.module import function_to_test

class TestModuleName(unittest.TestCase):
    def setUp(self):
        """测试前的准备工作"""
        pass
    
    def test_function_name(self):
        """测试具体功能"""
        # Arrange
        input_data = "test_input"
        expected_result = "expected_output"
        
        # Act
        actual_result = function_to_test(input_data)
        
        # Assert
        self.assertEqual(actual_result, expected_result)
    
    def tearDown(self):
        """测试后的清理工作"""
        pass
```

### 测试数据
- 测试数据应放在 `test_data/` 子目录中
- 使用 JSON、CSV 等格式存储测试数据
- 避免在测试代码中硬编码大量数据

## 🔧 测试工具

### 推荐的测试库
- **pytest**: 主要的测试框架
- **unittest.mock**: 模拟对象和依赖
- **coverage**: 代码覆盖率分析
- **factory_boy**: 测试数据生成
- **responses**: HTTP 请求模拟

### 测试配置
- 使用 `pytest.ini` 或 `pyproject.toml` 配置测试参数
- 设置测试环境变量
- 配置测试数据库连接

## 📊 持续集成

测试应该集成到 CI/CD 流程中：

1. **代码提交时**: 运行快速的单元测试
2. **合并请求时**: 运行完整的测试套件
3. **发布前**: 运行性能测试和端到端测试

## 🎯 最佳实践

1. **测试隔离**: 每个测试应该独立运行
2. **快速反馈**: 单元测试应该快速执行
3. **清晰命名**: 测试名称应该清楚描述测试内容
4. **适当覆盖**: 重点测试核心业务逻辑
5. **定期维护**: 及时更新和修复失效的测试

更多详细信息请查看各个测试子目录的具体文档。
