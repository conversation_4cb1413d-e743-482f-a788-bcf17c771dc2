# AI 模块

这个目录包含了与各种 AI API 交互的基础类和实现。

## 目录结构

- `base_ai.py`: AI API 交互的基础类和抽象类
- `tongyi/`: 通义千问 API 的实现
- `perplexity/`: Perplexity API 的实现

## 基础类

- `BaseAIModels`: AI 模型枚举基类
- `BaseAIRequestWrapper`: AI API 请求包装器基类
- `BaseAIClient`: AI API 客户端基类

## 使用方法

每个 AI 实现都应该：
1. 继承 `BaseAIModels` 定义自己的模型枚举
2. 继承 `BaseAIRequestWrapper` 实现自己的请求包装器
3. 继承 `BaseAIClient` 实现自己的客户端

## 示例

```python
from pythonp.common.ai.base_ai import BaseAIClient, BaseAIRequestWrapper, BaseAIModels

class MyModels(BaseAIModels):
    MODEL_1 = "model1"
    MODEL_2 = "model2"

class MyClient(BaseAIClient):
    def chat_completion(self, query: str, system_prompt: str, model: Optional[MyModels] = None, options: Optional[str] = None) -> Dict[str, Any]:
        # 实现聊天完成方法
        pass
``` 