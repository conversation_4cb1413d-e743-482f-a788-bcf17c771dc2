"""
通义千问 API 客户端模块

这个模块提供了与通义千问 API 交互的客户端类。
包括认证、请求发送和响应处理等功能。
"""

import json
import logging
from typing import Dict, Optional, Any
import requests
from pythonp.common.ai.base_ai import BaseAIClient, BaseAIModels, BaseAIRequestWrapper
from pythonp.common.ai.tongyi.tongyi_model import TongyiModels


class TongyiClient(BaseAIClient):
    """
    通义千问 API 客户端
    
    提供了与通义千问 API 交互的方法，包括认证、请求发送和响应处理。
    """

    def __init__(self, api_key: str):
        """
        初始化通义千问客户端
        
        参数:
            api_key: 通义千问 API 密钥
        """
        self.api_key = api_key
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def chat_completion(
        self,
        query: str,
        system_prompt:
        str = "You are a helpful assistance that can answer questions.",
        model: Optional[BaseAIModels] = None,
        options: Optional[str] = None,
        enable_search: Optional[bool] = False,
    ) -> Dict[str, Any]:
        """
        发送聊天完成请求
        
        参数:
            query: 用户查询文本
            system_prompt: 系统提示文本
            model: 使用的模型
            options: JSON 字符串，包含模型选项、搜索选项和流式响应设置
            enable_search: 是否启用互联网搜索
        
        返回:
            API 响应数据
        """
        # 将通用的 BaseAIModels 转换为 TongyiModels
        tongyi_model = None
        if model is not None:
            if isinstance(model, TongyiModels):
                tongyi_model = model
            else:
                if tongyi_model is None:
                    tongyi_model = TongyiModels.QWEN_TURBO

        # 创建请求包装器
        request = BaseAIRequestWrapper(
            query=query,
            system_prompt=system_prompt,
            model=tongyi_model,
            options=options,
        )

        # 发送请求
        url = f"{self.base_url}/chat/completions"
        request_data = request.to_request_data()

        # 检查是否启用流式响应
        stream = False
        if options:
            try:
                options_dict = json.loads(options)
                stream = options_dict.get("stream", False)
            except json.JSONDecodeError:
                logging.warning(
                    "Invalid options JSON, ignoring stream setting")

        # 新增：处理 enable_search
        extra_body = None
        if enable_search:
            extra_body = {"enable_search": True}

        # 如果 extra_body 存在，合并到 request_data
        if extra_body:
            request_data.update(extra_body)

        response = requests.post(url,
                                 headers=self.headers,
                                 json=request_data,
                                 stream=stream)
        response.raise_for_status()

        if stream:
            # 对于流式响应，直接返回响应对象
            return response
        else:
            # 对于非流式响应，返回解析后的 JSON
            return response.json()
