import os
from pythonp.common.ai.tongyi.tongyi_client import TongyiClient
from pythonp.common.ai.tongyi.tongyi_model import TongyiModels


def main():
    api_key = os.environ.get("TONGYI_API_KEY", "")
    if not api_key:
        print("请设置环境变量 TONGYI_API_KEY")
        return

    client = TongyiClient(api_key)
    query = "明天杭州的天气怎么样"
    system_prompt = "你是一个有帮助的助手。"
    model = TongyiModels.QWEN_TURBO
    options = '{"temperature": 0.2}'

    print("\n===== 带 enable_search 的请求 =====")
    response1 = client.chat_completion(
        query=query,
        system_prompt=system_prompt,
        model=model,
        options=options,
        enable_search=True,
    )
    print("Response with enable_search:\n", response1)

    print("\n===== 不带 enable_search 的请求 =====")
    response2 = client.chat_completion(
        query=query,
        system_prompt=system_prompt,
        model=model,
        options=options,
        enable_search=False,
    )
    print("Response without enable_search:\n", response2)


if __name__ == "__main__":
    main()
