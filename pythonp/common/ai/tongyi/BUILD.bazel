load("@py_deps//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary", "py_library", "py_test")

py_library(
    name = "tongyi",
    srcs = glob(["**/*.py"]),
    visibility = ["//visibility:public"],
    deps = [
        "//pythonp/common/ai:base_ai",
        requirement("requests"),
    ],
)

py_test(
    name = "test_tongyi_client",
    srcs = ["test_tongyi_client.py"],
    deps = [
        ":tongyi",
        "//pythonp/common/ai:base_ai",
        requirement("requests"),
        requirement("pytest"),
    ],
)

py_binary(
    name = "run_tongyi_client_demo",
    srcs = ["run_tongyi_client_demo.py"],
    deps = [
        ":tongyi",
    ],
)
