# 通义千问模块

这个目录包含了与通义千问 API 交互的实现。

## 文件结构

- `tongyi_model.py`: 通义千问模型枚举定义
- `tongyi_client.py`: 通义千问 API 客户端实现

## 模型

通义千问支持以下模型：
- `QwenTurbo`: 通义千问 Turbo 模型
- `QwenPlus`: 通义千问 Plus 模型
- `QwenMax`: 通义千问 Max 模型

## 使用方法

### 命令行工具

```bash
bazel run //pythonp/apps/tongyi:tongyi_cli -- \
    --model 1 --api-key="your-api-key" \
    --query "What is the capital of France?" \
    --system-prompt "You are a helpful assistant that can answer questions." \
    --options '{"temperature": 0.2, "top_p": 0.9, "stream": true}'
```

### Python API

```python
from pythonp.common.ai.tongyi.tongyi_client import TongyiClient
from pythonp.common.ai.tongyi.tongyi_model import QwenModels

client = TongyiClient(api_key="your-api-key")
response = client.chat_completion(
    query="What is the capital of France?",
    system_prompt="You are a helpful assistant that can answer questions.",
    model=QwenModels.QwenTurbo,
    options='{"temperature": 0.2, "top_p": 0.9, "stream": true}'
)
```

## 配置选项

- `temperature`: 温度参数，控制输出的随机性
- `top_p`: 核采样参数，控制输出的多样性
- `stream`: 是否使用流式响应
- 其他通义千问 API 支持的参数 
