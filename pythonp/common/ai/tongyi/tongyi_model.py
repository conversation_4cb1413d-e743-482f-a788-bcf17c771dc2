"""
通义千问 API 模块

这个模块提供了与通义千问 API 交互的基础类和枚举。
包括通义千问模型枚举。
"""

from typing import Dict, List, Optional, Any, Type
from enum import Enum
from pythonp.common.ai.base_ai import BaseAIModels


class TongyiModels(BaseAIModels):
    """
    通义千问模型枚举
    
    定义了通义千问 API 支持的不同模型。
    模型按大小和功能排序。
    """
    QWEN_TURBO = "qwen-turbo"  # 快速响应模型
    QWEN_PLUS = "qwen-plus"  # 增强模型
    QWEN_MAX = "qwen-max"  # 最大模型，适合复杂任务
    QWEN_PLUS_LATEST = "qwen-plus-latest"  # 最新增强模型
