import os
import sys
import pytest
from pythonp.common.ai.tongyi.tongyi_client import TongyiClient
from pythonp.common.ai.tongyi.tongyi_model import TongyiModels


def get_api_key():
    api_key = os.environ.get("TONGYI_API_KEY", "")
    if not api_key:
        pytest.skip(
            "No TONGYI_API_KEY set in environment, skipping online test.")
    return api_key


def test_chat_completion_with_enable_search_online():
    api_key = get_api_key()
    client = TongyiClient(api_key)
    query = "请用一句话总结今天的中国互联网大新闻。"
    system_prompt = "你是一个有帮助的助手。"
    model = TongyiModels.QWEN_TURBO
    options = '{"temperature": 0.2}'

    response = client.chat_completion(
        query=query,
        system_prompt=system_prompt,
        model=model,
        options=options,
        enable_search=True,
    )
    # 检查返回结构
    assert isinstance(response, dict)
    assert "choices" in response
    assert len(response["choices"]) > 0
    content = response["choices"][0]["message"]["content"]
    assert isinstance(content, str)
    print("Response with enable_search:", content)


def test_chat_completion_without_enable_search_online():
    api_key = get_api_key()
    client = TongyiClient(api_key)
    query = "请用一句话总结今天的中国互联网大新闻。"
    system_prompt = "你是一个有帮助的助手。"
    model = TongyiModels.QWEN_TURBO
    options = '{"temperature": 0.2}'

    response = client.chat_completion(
        query=query,
        system_prompt=system_prompt,
        model=model,
        options=options,
        enable_search=False,
    )
    # 检查返回结构
    assert isinstance(response, dict)
    assert "choices" in response
    assert len(response["choices"]) > 0
    content = response["choices"][0]["message"]["content"]
    assert isinstance(content, str)
    print("Response without enable_search:", content)


def test_smoke():
    print("SMOKE TEST EXECUTED!")
    assert True


if __name__ == "__main__":
    sys.exit(pytest.main(sys.argv[1:]))
