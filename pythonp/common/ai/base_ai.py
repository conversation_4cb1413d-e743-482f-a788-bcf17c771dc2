"""
AI API 基础模块

这个模块提供了与 AI API 交互的基础类和抽象类。
包括模型枚举和请求包装器基类。
"""

from typing import Dict, List, Optional, Any, TypeVar, Generic, Type, Union, Tuple
from enum import Enum
from abc import ABC, abstractmethod
import json
import logging
from openai.types.chat import ChatCompletion
from rich.console import Console

T = TypeVar('T', bound='BaseAIModels')


class BaseAIModels(Enum):
    """
    AI 模型枚举基类
    
    所有 AI 实现都应该继承这个基类并定义自己的模型枚举。
    """
    pass


class BaseAIRequestWrapper(Generic[T]):
    """
    AI API 请求包装器基类
    
    封装了发送到 AI API 的请求参数的基类。
    所有 AI 实现都应该继承这个基类。
    
    属性:
        query: 用户查询文本
        system_prompt: 系统提示文本
        model: 使用的模型
        options: 模型选项，包含搜索选项和流式响应设置
    """

    def __init__(
        self,
        query: str,
        system_prompt:
        str = "You are a helpful assistance that can answer questions.",
        model: Optional[T] = None,
        options: Optional[str] = None,
    ):
        """
        初始化 AI 请求包装器基类
        
        参数:
            query: 用户查询文本
            system_prompt: 系统提示文本
            model: 使用的模型
            options: JSON 字符串，包含模型选项、搜索选项和流式响应设置
        """
        self.query = query
        self.system_prompt = system_prompt
        self.model = model
        self.options = options

    def get_messages(self) -> List[Dict[str, str]]:
        """
        获取消息列表
        
        返回:
            包含系统提示和用户查询的消息列表
        """
        return [
            {
                "role": "system",
                "content": self.system_prompt
            },
            {
                "role": "user",
                "content": self.query
            },
        ]

    def get_options_dict(self) -> Dict[str, Any]:
        """
        获取解析后的选项字典
        
        返回:
            解析后的选项字典，如果选项为 None 则返回空字典
        """
        if self.options is None:
            return {}

        try:
            return json.loads(self.options)
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse options JSON: {e}")
            return {}

    def to_request_data(self) -> Dict[str, Any]:
        """
        转换为请求数据
        
        返回:
            包含所有请求参数的字典
        """
        request_data = {
            "messages": self.get_messages(),
        }

        if self.model is not None:
            request_data["model"] = self.model.value

        options_dict = self.get_options_dict()
        if options_dict:
            request_data.update(options_dict)

        return request_data


class BaseAIClient(ABC):
    """
    AI API 客户端基类
    
    提供了与 AI API 交互的基础方法。
    所有 AI 客户端都应该继承这个基类。
    """

    @abstractmethod
    def chat_completion(
        self,
        query: str,
        system_prompt:
        str = "You are a helpful assistance that can answer questions.",
        model: Optional[BaseAIModels] = None,
        options: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        发送聊天完成请求
        
        参数:
            query: 用户查询文本
            system_prompt: 系统提示文本
            model: 使用的模型
            options: JSON 字符串，包含模型选项、搜索选项和流式响应设置
        
        返回:
            API 响应数据
        """
        pass

    def _handle_streaming_response(self, response,
                                   model_name: str) -> Dict[str, Any]:
        """
        处理流式响应
        
        参数:
            response: 从 API 返回的响应
            model_name: 使用的模型名称
            
        返回:
            包含完整响应内容的字典
        """
        console = Console()
        full_content = ""

        # 处理流式响应
        for line in response.iter_lines():
            if line:
                # 解析 SSE 格式的数据
                line_text = line.decode('utf-8')
                if line_text.startswith('data: '):
                    data = line_text[6:]  # 去掉 'data: ' 前缀
                    if data == '[DONE]':
                        break

                    try:
                        # 解析 JSON 数据
                        json_data = json.loads(data)

                        # 提取增量内容
                        if 'choices' in json_data and len(
                                json_data['choices']) > 0:
                            delta = json_data['choices'][0].get('delta', {})
                            if 'content' in delta:
                                content_chunk = delta['content']
                                full_content += content_chunk
                                # 实时打印内容
                                console.print(content_chunk, end='')
                    except json.JSONDecodeError:
                        # 忽略无法解析的 JSON 数据
                        pass

        # 返回完整的响应
        return {
            "choices": [{
                "message": {
                    "content": full_content
                }
            }],
            "model": model_name,
            "usage": {}
        }

    def format_response(self, response: Dict[str,
                                             Any]) -> Tuple[str, List[Any]]:
        """
        格式化响应以便显示
        
        参数:
            response: 从 API 返回的响应
            
        返回:
            包含原始内容和可渲染对象列表的元组
        """
        content = ""
        if "choices" in response and len(response["choices"]) > 0:
            message = response["choices"][0].get("message", {})
            content = message.get("content", "")

        # 创建可渲染对象列表
        renderables = []

        # 添加响应内容
        if content:
            renderables.append(content)

        # 添加模型信息
        if "model" in response:
            renderables.append(f"\n[bold]模型:[/bold] {response['model']}")

        # 添加使用情况
        if "usage" in response:
            renderables.append(f"[bold]使用情况:[/bold] {response['usage']}")

        return content, renderables

    def display_response(self,
                         console,
                         response: Union[ChatCompletion, Dict[str, Any]],
                         is_stream: bool = False):
        """
        以格式化的方式显示响应
        
        参数:
            console: rich Console 实例
            response: 从 API 返回的响应，可以是 ChatCompletion 对象或字典
            is_stream: 是否为流式响应
        """
        # 格式化响应
        _, renderables = self.format_response(response)

        # 显示可渲染对象
        if not is_stream:
            console.print("\n")
            for renderable in renderables:
                console.print(renderable)
            console.print("\n")
        else:
            # 对于流式响应，只显示模型信息
            if "model" in response:
                console.print("\n[bold]模型:[/bold]", response["model"])
            console.print("[bold]使用情况:[/bold] 流式响应不提供使用情况信息")
