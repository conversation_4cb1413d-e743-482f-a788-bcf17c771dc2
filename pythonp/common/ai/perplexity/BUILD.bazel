load("@py_deps//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_library")

py_library(
    name = "perplexity_client",
    srcs = ["perplexity_client.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":perplexity_model",
        requirement("openai"),
        requirement("pydantic"),
        requirement("rich"),
        requirement("requests"),
    ],
)

py_library(
    name = "perplexity_model",
    srcs = ["perplexity_model.py"],
    visibility = ["//visibility:public"],
    deps = [
        "//pythonp/common/ai:base_ai",
    ],
)
