"""
Perplexity API 客户端模块

这个模块提供了与 Perplexity API 交互的客户端类和响应模型。
包括发送请求、处理响应和格式化输出的功能。
"""

from typing import Dict, List, Optional, Tuple, Any, Union
import json
from pydantic import BaseModel, Field
from openai import OpenAI
from openai.types.chat import ChatCompletion
from rich.console import Console
from pythonp.common.ai.perplexity.perplexity_model import SonarModels
from pythonp.common.ai.base_ai import BaseAIClient, BaseAIRequestWrapper
import logging
import requests


class PerplexityResponse(BaseModel):
    """
    Perplexity API 响应模型
    
    封装了从 Perplexity API 返回的响应数据。
    
    属性:
        content: 模型生成的文本内容
        model: 使用的模型名称
        usage: 使用统计信息，如 token 数量等
    """
    content: str
    model: str
    usage: Dict[str, Any] = Field(default_factory=dict)


class PerplexityClient(BaseAIClient):
    """
    Perplexity API 客户端
    
    提供了与 Perplexity API 交互的方法，包括发送请求和处理响应。
    
    属性:
        client: OpenAI 客户端实例
        console: 用于格式化输出的控制台
    """

    def __init__(self, api_key: str):
        """
        初始化 Perplexity 客户端
        
        参数:
            api_key: Perplexity API 密钥
        """
        self.api_key = api_key
        self.client = OpenAI(api_key=api_key,
                             base_url="https://api.perplexity.ai")
        self.console = Console()

    def chat_completion(
        self,
        query: str,
        system_prompt:
        str = "You are a helpful assistance that can answer questions.",
        model: Optional[SonarModels] = None,
        options: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        发送聊天完成请求到 Perplexity API
        
        参数:
            query: 用户查询文本
            system_prompt: 系统提示文本
            model: 使用的模型
            options: JSON 字符串，包含模型选项、搜索选项和流式响应设置
        
        返回:
            API 响应数据
        """
        # 创建请求包装器
        request = BaseAIRequestWrapper(
            query=query,
            system_prompt=system_prompt,
            model=model,
            options=options,
        )

        # 准备请求参数
        request_data = request.to_request_data()

        headers = {
            "Authorization": f"Bearer {self.client.api_key}",
            "Content-Type": "application/json",
        }

        response = requests.post(f"{self.client.base_url}/chat/completions",
                                 headers=headers,
                                 json=request_data,
                                 stream=request.get_options_dict().get(
                                     "stream", False))

        if response.status_code != 200:
            raise Exception(
                f"API request failed with status code {response.status_code}: {response.text}"
            )

        # 处理流式响应
        if request.get_options_dict().get("stream", False):
            # 流式响应需要特殊处理
            return self._handle_streaming_response(
                response, model.value if model else "sonar")

        # 处理非流式响应
        response_data = response.json()

        # 从响应中提取内容和使用情况
        content = response_data["choices"][0]["message"]["content"]
        model_name = response_data["model"]
        usage = response_data.get("usage", {})

        # 移除可能导致验证错误的字段
        # if 'completion_tokens_details' in usage:
        #     del usage['completion_tokens_details']
        # if 'prompt_tokens_details' in usage:
        #     del usage['prompt_tokens_details']
        # if 'search_context_size' in usage:
        #     del usage['search_context_size']

        # 创建并返回响应对象
        return {
            "choices": [{
                "message": {
                    "content": content
                }
            }],
            "model": model_name,
            "usage": usage
        }

    def _handle_streaming_response(self, response,
                                   model_name: str) -> PerplexityResponse:
        """
        处理流式响应
        
        参数:
            response: 从 API 返回的响应
            model_name: 使用的模型名称
            
        返回:
            PerplexityResponse 实例，包含模型生成的响应
        """
        from rich.console import Console
        console = Console()

        # 用于存储完整的响应内容
        full_content = ""

        # 处理流式响应
        for line in response.iter_lines():
            if line:
                # 解析 SSE 格式的数据
                line_text = line.decode('utf-8')
                if line_text.startswith('data: '):
                    data = line_text[6:]  # 去掉 'data: ' 前缀
                    if data == '[DONE]':
                        break

                    try:
                        # 解析 JSON 数据
                        json_data = json.loads(data)

                        # 提取增量内容
                        if 'choices' in json_data and len(
                                json_data['choices']) > 0:
                            delta = json_data['choices'][0].get('delta', {})
                            if 'content' in delta:
                                content_chunk = delta['content']
                                full_content += content_chunk
                                # 实时打印内容
                                console.print(content_chunk, end='')
                    except json.JSONDecodeError:
                        # 忽略无法解析的 JSON 数据
                        pass

        # 返回完整的响应
        return PerplexityResponse(
            content=full_content,
            model=model_name,
            usage={},
        )
