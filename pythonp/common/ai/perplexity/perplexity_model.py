"""
Perplexity API 模型定义模块

这个模块定义了 Perplexity API 使用的模型枚举。
包括 Sonar 系列模型的枚举定义。
"""

from typing import Dict, List, Optional, Any
from pythonp.common.ai.base_ai import BaseAIModels


class SonarModels(BaseAIModels):
    """
    Sonar 模型枚举
    
    定义了 Perplexity API 支持的不同 Sonar 模型。
    模型按大小和功能排序，从最小的 SONAR 到最大的 SONAR_DEEP_RESEARCH。
    """
    SONAR_DEEP_RESEARCH = "sonar-deep-research"  # 最大的模型，适合深度研究
    SONAR_REASONING_PRO = "sonar-reasoning-pro"  # 专业推理模型
    SONAR_REASONING = "sonar-reasoning"  # 基础推理模型
    SONAR_PRO = "sonar-pro"  # 专业模型
    SONAR = "sonar"  # 基础模型，最小的
