# Perplexity API 集成

本模块提供了 Perplexity API 的 Python 集成，支持与各种 Sonar 模型进行文本生成和对话交互。

## 功能特点

- 支持所有 Sonar 模型系列
- 流式响应支持
- 网络搜索集成
- 可自定义系统提示词
- 可配置模型参数（温度、top_p 等）
- 丰富的控制台输出格式
- 完善的错误处理机制

## 可用模型

从轻量到重量级排序：

1. `SONAR` - 基础模型（最小）
2. `SONAR_PRO` - 专业版模型
3. `SONAR_REASONING` - 基础推理模型
4. `SONAR_REASONING_PRO` - 专业推理模型
5. `SONAR_DEEP_RESEARCH` - 深度研究模型（最大）

## 快速开始

### Python 代码示例

```python
from pythonp.common.ai.perplexity.perplexity_model import SonarModels
from pythonp.common.ai.perplexity.perplexity_client import PerplexityClient

# 初始化客户端
client = PerplexityClient(api_key="your-api-key")

# 创建请求
request = {
    "query": "你的问题",
    "system_prompt": "你是一个有帮助的助手",
    "model": SonarModels.SONAR,
    "options": {
        "temperature": 0.2,
        "top_p": 0.9,
        "stream": True,
        "web_search_options": {
            "search_context_size": "high"
        }
    }
}

# 获取响应
response = client.chat_completion(**request)
print(response.content)
```

### 命令行使用

```bash
bazel run //pythonp/apps/perplexity:perplexity_cli -- \
    --model 2 \
    --api-key="your-api-key" \
    --query "你的问题" \
    --system-prompt "你是一个有帮助的助手" \
    --search-context-size high \
    --stream \
    --options '{"temperature": 0.2, "top_p": 0.9}'
```

## 命令行参数说明

- `--query`: 查询文本（必需）
- `--model`: 模型选择（1-5，默认为1）
  - 1: SONAR（最小）
  - 2: SONAR_PRO
  - 3: SONAR_REASONING
  - 4: SONAR_REASONING_PRO
  - 5: SONAR_DEEP_RESEARCH（最大）
- `--system-prompt`: 系统提示词（默认为"You are a helpful assistance that can answer questions."）
- `--api-key`: Perplexity API 密钥（默认使用 PERPLEXITY_API_KEY 环境变量）
- `--search-context-size`: 搜索上下文大小（可选：low/medium/high）
- `--stream`: 启用流式响应
- `--options`: 模型选项的 JSON 字符串，支持以下参数：
  - temperature: 温度参数
  - top_p: 采样概率
  - return_images: 是否返回图片
  - return_related_questions: 是否返回相关问题
  - top_k: 采样数量
  - presence_penalty: 存在惩罚
  - frequency_penalty: 频率惩罚

## 配置说明

API 密钥可以通过以下两种方式提供：
1. 环境变量：`PERPLEXITY_API_KEY`
2. 命令行参数：`--api-key`

## 项目结构

- `perplexity_model.py`: 模型定义
  - `SonarModels`: 可用模型枚举

- `perplexity_client.py`: API 客户端实现
  - `PerplexityClient`: 主客户端类
  - 响应处理与显示功能

- `apps/perplexity/perplexity_cli.py`: 命令行工具
  - 参数解析
  - 请求处理
  - 响应展示

## 依赖项

- openai
- pydantic
- rich
- requests
- argparse
- logging

## 构建说明

项目使用 Bazel 进行构建，相关配置在 `BUILD.bazel` 文件中。 
