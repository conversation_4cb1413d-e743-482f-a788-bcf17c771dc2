load("@py_deps//:requirements.bzl", "requirement")
load("@rules_python//python:defs.bzl", "py_binary", "py_library", "py_test")

py_library(
    name = "tavily_client",
    srcs = ["tavily_client.py"],
    visibility = ["//visibility:public"],
    deps = [
        requirement("tavily-python"),
    ],
)

py_binary(
    name = "run_tavily_client_demo",
    srcs = ["run_tavily_client_demo.py"],
    visibility = ["//visibility:public"],
    deps = [
        ":tavily_client",
        "//pythonp/common/ai:base_ai",
        requirement("tavily-python"),
    ],
)

py_test(
    name = "test_tavily_client",
    srcs = ["test_tavily_client.py"],
    deps = [
        ":tavily_client",
        requirement("pytest"),
        requirement("tavily-python"),
    ],
)
