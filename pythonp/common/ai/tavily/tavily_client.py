"""
Tavily API 客户端模块

本模块提供 Tavily API 的 Python 客户端实现，基于官方 tavily-python 包。
"""

from typing import Dict, Optional, Any
from tavily import TavilyClient as OfficialTavilyClient
from pythonp.common.ai.base_ai import BaseAIModels


class TavilyClient():
    """
    Tavily API 客户端（官方 tavily-python 封装）
    """

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.client = OfficialTavilyClient(api_key)

    def search(
        self,
        query: str,
        system_prompt: Optional[str] = None,
        model: Optional[BaseAIModels] = None,
        options: Optional[str] = None,
        stream: bool = False,
    ) -> Dict[str, Any]:
        """
        调用 tavily-python 的 search 方法，并适配为统一的 chat_completion 接口。
        
        Args:
            query: 搜索查询文本
            system_prompt: 系统提示文本（可选，Tavily 不使用）
            model: 模型选择（可选，Tavily 不使用）
            options: JSON 字符串，包含模型选项（可选，Tavily 不使用）
            stream: 是否使用流式响应（可选，Tavily 不支持）
            
        Returns:
            适配后的响应格式，包含 choices、model、usage 和 raw 字段
        """
        # 构建搜索查询
        search_query = query
        if system_prompt:
            search_query = f"{system_prompt}\n\n{query}"

        # 调用 Tavily search API
        response = self.client.search(query=search_query)

        # 适配返回格式，保持与其他客户端一致
        return {
            "choices": [{
                "message": {
                    "content": response.get("answer", str(response))
                }
            }],
            "model":
            "tavily-official",
            "usage": {},
            "raw":
            response,
        }

    def extract(self, urls: list[str]) -> Any:
        """
        调用 tavily-python 的 extract 方法。
        """
        return self.client.extract(urls=urls)

    def crawl(self, url: str) -> Any:
        """
        调用 tavily-python 的 crawl 方法。
        """
        return self.client.crawl(url=url)
