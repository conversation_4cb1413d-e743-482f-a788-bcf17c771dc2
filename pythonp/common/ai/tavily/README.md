# Tavily API 集成

本模块提供了 Tavily API 的 Python 集成，基于官方 tavily-python 包。

## 功能特点

- 支持 Tavily 官方 search、extract、crawl 能力
- 简单易用的 API 封装
- 兼容 chat_completion 风格接口

## 快速开始

### Python 代码示例

#### 搜索（chat_completion）
```python
from pythonp.common.ai.tavily.tavily_client import TavilyClient

# 初始化客户端
client = TavilyClient(api_key="your-api-key")

# 直接搜索
response = client.chat_completion(query="你的问题")
print(response["choices"][0]["message"]["content"])
```

#### 网页内容抽取（extract）
```python
from pythonp.common.ai.tavily.tavily_client import TavilyClient

client = TavilyClient(api_key="your-api-key")
response = client.extract(urls=["https://example.com"])
print(response)
```

#### 网页爬取（crawl）
```python
from pythonp.common.ai.tavily.tavily_client import TavilyClient

client = TavilyClient(api_key="your-api-key")
response = client.crawl(url="https://example.com")
print(response)
```

## 配置说明

API 密钥可以通过以下方式提供：
- 直接传递给 `TavilyClient(api_key=...)`
- 或设置环境变量 `TAVILY_API_KEY`

## 依赖项

- tavily-python

## 构建说明

项目使用 Bazel 进行构建，相关配置在 `BUILD.bazel` 文件中。 