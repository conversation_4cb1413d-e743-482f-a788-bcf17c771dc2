import os
import sys
import pytest
from pythonp.common.ai.tavily.tavily_client import TavilyClient


def get_api_key():
    api_key = os.environ.get("TAVILY_API_KEY", "sk-test-placeholder")
    if not api_key:
        pytest.skip(
            "No TAVILY_API_KEY set in environment, skipping online test.")
    return api_key


def test_chat_completion_online():
    api_key = get_api_key()
    client = TavilyClient(api_key)
    query = "What is the capital of France?"

    response = client.search(query=query, )
    assert isinstance(response, dict)
    assert "choices" in response
    assert len(response["choices"]) > 0
    content = response["choices"][0]["message"]["content"]
    assert isinstance(content, str)
    print("Tavily response:", content)


def test_smoke():
    print("SMOKE TEST EXECUTED!")
    assert True


if __name__ == "__main__":
    sys.exit(pytest.main(sys.argv[1:]))
