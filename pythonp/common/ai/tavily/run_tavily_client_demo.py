import os
from pythonp.common.ai.tavily.tavily_client import TavilyClient


def main():
    api_key = os.environ.get("TAVILY_API_KEY",
                             "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")
    if not api_key:
        print("请设置环境变量 TAVILY_API_KEY")
        return

    client = TavilyClient(api_key)
    query = "What is the capital of France?"

    print("\n===== Tavily 请求演示 =====")
    response = client.search(query=query, )
    print("Response:\n", response)


if __name__ == "__main__":
    main()
