"""
企业微信群聊机器人组件包
支持多种消息类型：文本、Markdown、图片、图文
文档: https://developer.work.weixin.qq.com/document/path/91770
"""
import base64
import hashlib
import logging
import time
from typing import Dict, List, Optional, Union, Any
from pythonp.common.logging.logger import logging
import requests
import re


class WeChatWorkBot:
    """企业微信群聊机器人组件"""

    def __init__(self, robot_key: str, robot_url: Optional[str] = None):
        """
        初始化企业微信群聊机器人
        
        Args:
            robot_key: 企业微信群消息机器人key
            robot_url: 自定义机器人URL，默认使用官方URL
        """
        self.robot_key = robot_key
        if not robot_url:
            self.robot_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={}"
        else:
            self.robot_url = robot_url

    @staticmethod
    def is_url(string: str) -> bool:
        url_pattern = re.compile(r'^(https?|ftp)://[^\s/$.?#].[^\s]*$',
                                 re.IGNORECASE)
        return re.match(url_pattern, string) is not None

    @staticmethod
    def get_image_base64(image_source: str) -> str:
        """
        获取图片的base64编码，支持URL和文件路径
        
        Args:
            image_source: 图片文件路径或URL
            
        Returns:
            base64编码的图片数据
        """
        try:
            if WeChatWorkBot.is_url(image_source):
                response = requests.get(image_source)
                response.raise_for_status()
                image_data = response.content
            else:
                with open(image_source, 'rb') as f:
                    image_data = f.read()

            base64_data = base64.b64encode(image_data)
            return str(base64_data, 'utf-8')
        except Exception as e:
            logging.error(f"获取图片base64编码失败: {str(e)}")
            raise

    @staticmethod
    def get_image_md5(path: str) -> str:
        """
        获取图片的MD5值
        
        Args:
            path: 图片文件路径
            
        Returns:
            图片的MD5值（大写）
        """
        try:
            md5_l = hashlib.md5()
            with open(path, mode="rb") as f:
                by = f.read()
            md5_l.update(by)
            md5_code = md5_l.hexdigest()
            return md5_code.upper()
        except Exception as e:
            logging.error(f"获取图片MD5值失败: {str(e)}")
            raise

    def send_message(self,
                     msg: Dict[str, Any],
                     max_retries: int = 3,
                     timeout: int = 10) -> Optional[requests.Response]:
        """
        发送消息到企业微信群
        
        Args:
            msg: 消息内容，必须包含msgtype字段
            max_retries: 请求重试次数
            timeout: 请求失败等待时间（秒）
            
        Returns:
            响应对象，失败时返回None
        """
        if not isinstance(msg, dict) or 'msgtype' not in msg:
            logging.error("消息格式错误：必须包含msgtype字段")
            return None

        real_url = self.robot_url.format(self.robot_key)
        print(f"real_url: {real_url}")
        retries = 0

        while retries < max_retries:
            try:
                resp = requests.post(real_url, json=msg, timeout=timeout)
                if resp.status_code == 200 and resp.json().get('errcode') == 0:
                    logging.info("发送消息通知成功")
                    return resp
                else:
                    logging.error(f"发送消息失败：{resp.json().get('errmsg')}")
            except Exception as e:
                logging.error(f"发送消息异常：{str(e)}")

            retries += 1
            if retries < max_retries:
                logging.info(f"正在进行第{retries}次重试...")
                time.sleep(timeout)

        return None

    def send_text(
        self,
        content: str,
        mentioned_list: Optional[List[str]] = None,
        mentioned_mobile_list: Optional[List[str]] = None
    ) -> Optional[requests.Response]:
        """
        发送文本消息
        
        Args:
            content: 文本内容
            mentioned_list: 需要@的用户ID列表，可以使用"@all"@所有人
            mentioned_mobile_list: 需要@的手机号列表，可以使用"@all"@所有人
            
        Returns:
            响应对象，失败时返回None
            
        Example:
            {
                "msgtype": "text",
                "text": {
                    "content": "广州今日天气：29度，大部分多云，降雨概率：60%",
                    "mentioned_list":["wangqing","@all"],
                    "mentioned_mobile_list":["13800001111","@all"]
                }
            }
        """
        if not content:
            logging.error("文本内容不能为空")
            return None

        msg = {"msgtype": "text", "text": {"content": str(content)}}

        # 添加@功能
        if mentioned_list:
            msg["text"]["mentioned_list"] = mentioned_list

        if mentioned_mobile_list:
            msg["text"]["mentioned_mobile_list"] = mentioned_mobile_list

        return self.send_message(msg)

    def send_markdown(self, content: str) -> Optional[requests.Response]:
        """
        发送Markdown消息
        
        Args:
            content: Markdown格式的文本内容，支持部分HTML标签
            
        Returns:
            响应对象，失败时返回None
            
        Example:
            {
                "msgtype": "markdown",
                "markdown": {
                    "content": "实时新增用户反馈<font color=\"warning\">132例</font>，请相关同事注意。\n
                     >类型:<font color=\"comment\">用户反馈</font>
                     >普通用户反馈:<font color=\"comment\">117例</font>
                     >VIP用户反馈:<font color=\"comment\">15例</font>"
                }
            }
        """
        if not content:
            logging.error("Markdown内容不能为空")
            return None

        msg = {"msgtype": "markdown", "markdown": {"content": str(content)}}
        return self.send_message(msg)

    def send_image(self, img_path: str) -> Optional[requests.Response]:
        """
        发送图片消息
        
        Args:
            img_path: 图片文件路径
            
        Returns:
            响应对象，失败时返回None
            
        Example:
            {
                "msgtype": "image",
                "image": {
                    "base64": "DATA",
                    "md5": "MD5"
                }
            }
        """
        try:
            img_msg = {
                "msgtype": "image",
                "image": {
                    "base64": self.get_image_base64(img_path),
                    "md5": self.get_image_md5(img_path)
                }
            }
            return self.send_message(img_msg)
        except Exception as e:
            logging.error(f"发送图片消息失败: {str(e)}")
            return None

    def send_news(
            self, articles: List[Dict[str,
                                      str]]) -> Optional[requests.Response]:
        """
        发送图文消息
        
        Args:
            articles: 图文消息列表，每个元素包含以下字段:
                     - title: 标题
                     - description: 描述
                     - url: 点击后跳转的链接
                     - picurl: 图片链接
            
        Returns:
            响应对象，失败时返回None
            
        Example:
            {
                "msgtype": "news",
                "news": {
                   "articles" : [
                       {
                           "title" : "中秋节礼品领取",
                           "description" : "今年中秋节公司有豪礼相送",
                           "url" : "www.qq.com",
                           "picurl" : "http://res.mail.qq.com/node/ww/wwopenmng/images/independent/doc/test_pic_msg1.png"
                       }
                    ]
                }
            }
        """
        if not articles:
            logging.error("图文消息列表不能为空")
            return None

        # 验证每个文章的必要字段
        for article in articles:
            required_fields = ["title", "description", "url", "picurl"]
            missing_fields = [
                field for field in required_fields if field not in article
            ]
            if missing_fields:
                logging.error(f"图文消息缺少必要字段: {', '.join(missing_fields)}")
                return None

        msg = {"msgtype": "news", "news": {"articles": articles}}
        return self.send_message(msg)

    def send_article(self, title: str, description: str, url: str,
                     picurl: str) -> Optional[requests.Response]:
        """
        发送单条图文消息
        
        Args:
            title: 标题
            description: 描述
            url: 点击后跳转的链接
            picurl: 图片链接
            
        Returns:
            响应对象，失败时返回None
        """
        article = {
            "title": title,
            "description": description,
            "url": url,
            "picurl": picurl
        }
        return self.send_news([article])
