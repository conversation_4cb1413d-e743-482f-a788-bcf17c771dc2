import json
import re
from pythonp.common.logging.logger import logging
from typing import Union


def parse_json_response(content: str) -> dict | list:
    """
    Parse JSON response from LLM, handling cases where the response is wrapped in code blocks.
    
    Args:
        content: The response string from LLM
        
    Returns:
        Union[dict, list]: Parsed JSON data (either a dictionary or list)
        
    Raises:
        ValueError: If the content cannot be parsed as JSON
    """
    try:
        # Try direct JSON parsing first
        return json.loads(content)
    except json.JSONDecodeError:
        # If direct parsing fails, try to extract JSON from code blocks
        json_pattern = r"```(?:json)?\s*([\s\S]*?)\s*```"
        matches = re.findall(json_pattern, content)

        if matches:
            try:
                return json.loads(matches[0])
            except json.JSONDecodeError as e:
                logging.error(f"Failed to parse JSON from code block: {e}")
                raise ValueError(f"Invalid JSON in code block: {matches[0]}")

        # If no code blocks found or parsing failed, try to find any JSON-like structure
        try:
            # Find the first { or [ and last } or ] in the content
            start_obj = content.find('{')
            start_arr = content.find('[')
            end_obj = content.rfind('}')
            end_arr = content.rfind(']')

            # Determine which type of JSON structure we found
            if start_obj >= 0 and end_obj > start_obj:
                start = start_obj
                end = end_obj + 1
            elif start_arr >= 0 and end_arr > start_arr:
                start = start_arr
                end = end_arr + 1
            else:
                raise ValueError(
                    "No valid JSON object or array found in content")

            return json.loads(content[start:end])
        except json.JSONDecodeError as e:
            logging.error(f"Failed to parse JSON from content: {e}")
            raise ValueError(
                f"Could not find valid JSON in content: {content}")

        raise ValueError(f"Could not parse content as JSON: {content}")
