import os
import logging
from datetime import datetime, timezone
from logging.handlers import TimedRotatingFileHandler
from typing import Optional


class CustomTimedRotatingFileHandler(TimedRotatingFileHandler):
    """Custom handler that organizes log files by date."""

    def __init__(self, log_dir, *args, **kwargs):
        self.log_dir = log_dir
        super().__init__(*args, **kwargs)

    def doRollover(self):
        current_date = datetime.now(timezone.utc).strftime("%Y-%m-%d")
        log_path = os.path.join(self.log_dir, current_date)
        if not os.path.exists(log_path):
            os.makedirs(log_path)

        self.baseFilename = os.path.join(log_path, 'bot_log.log')
        super().doRollover()


class LoggerManager:
    """Centralized logger management."""

    _instance = None
    _initialized = False
    _default_log_dir = "logs"
    _default_file_name = "pointer.log"

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LoggerManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not LoggerManager._initialized:
            self.formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(pathname)s - %(levelname)s - %(funcName)s - line: %(lineno)d - %(message)s'
            )
            self.root_logger = logging.getLogger()
            self.root_logger.setLevel(logging.DEBUG)
            LoggerManager._initialized = True

    def setup(self,
              log_dir: Optional[str] = None,
              file_name: Optional[str] = None) -> None:
        """
        Setup global logging configuration.
        
        Args:
            log_dir: Directory for log files
            file_name: Base name for log files
        """
        log_dir = log_dir or self._default_log_dir
        file_name = file_name or self._default_file_name

        # Create log directory if it doesn't exist
        current_date = datetime.now(timezone.utc).strftime("%Y-%m-%d")
        log_path = os.path.join(log_dir, current_date)
        os.makedirs(log_path, exist_ok=True)

        # Setup file handler
        log_file = os.path.join(log_path, file_name)
        file_handler = CustomTimedRotatingFileHandler(log_dir,
                                                      log_file,
                                                      when="midnight",
                                                      interval=1)
        file_handler.setFormatter(self.formatter)
        file_handler.setLevel(logging.DEBUG)

        # Setup console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(self.formatter)
        console_handler.setLevel(logging.DEBUG)

        # Remove existing handlers if any
        self.root_logger.handlers.clear()

        # Add handlers
        self.root_logger.addHandler(file_handler)
        self.root_logger.addHandler(console_handler)


def setup_logging(log_dir: Optional[str] = None,
                  file_name: Optional[str] = None) -> None:
    """
    Initialize global logging configuration.
    
    Args:
        log_dir: Directory for log files
        file_name: Base name for log files
    """
    LoggerManager().setup(log_dir, file_name)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Name for the logger, typically __name__
        
    Returns:
        Configured logger instance
    """
    if not LoggerManager._initialized:
        setup_logging()
    return logging.getLogger(name)
