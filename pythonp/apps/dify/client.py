'''
Description: 
Author: Devin
Date: 2025-04-29 16:54:49
'''
import uuid
import json
import logging
from typing import Dict
from os import getenv

from pythonp.common.dify_client import Client, models

dify_api_key_message = getenv('DIFY_API_KEY_MESSAGE')
dify_api_base = getenv('DIFY_BASE_HOST')

client = Client(api_key=dify_api_key_message, api_base=dify_api_base)

if not dify_api_base or not dify_api_key_message:
    raise ValueError(
        "Dify API key and base host are not set in the environment")


def chat_blocking(data):
    try:
        user = str(uuid.uuid4())
        logging.info("Generated user ID: %s", user)

        logging.info("Received data: %s", data)
        # Create a blocking chat request
        blocking_chat_req = models.ChatRequest(
            query=data.get("query"),
            inputs=data.get("inputs", {"data": ""}),
            user=str(data.get("user", user)),
            response_mode=models.ResponseMode.BLOCKING,
        )

        logging.info("Sending blocking chat request: %s", blocking_chat_req)

        # Send the chat message
        chat_response = client.chat_messages(blocking_chat_req, timeout=60.)
        chat_response_dict = json.loads(
            json.dumps(chat_response,
                       default=lambda o: o.__dict__))  # Convert to dictionary

        # logging.info("Received chat response: %s", chat_response_dict)

        # Extract the answer from the chat response
        answer = chat_response_dict.get('answer', 'No answer found')
        logging.info("Answer: %s", answer)
        # 处理推荐结果
        return answer

    except Exception as e:
        logging.error("Error during chat_blocking: %s", e)
        return None


def chat_streaming(data):
    try:
        user = str(uuid.uuid4())
        logging.info("Generated user ID: %s", user)

        streaming_chat_req = models.ChatRequest(
            query=data.get("query"),
            inputs=data.get("inputs", {}),
            user=user,
            response_mode=models.ResponseMode.STREAMING,
        )

        logging.info("Sending streaming chat request: %s", streaming_chat_req)

        for chunk in client.chat_messages(streaming_chat_req, timeout=60.):
            chunk_dict = json.loads(
                json.dumps(
                    chunk,
                    default=lambda o: o.__dict__))  # Convert to dictionary
            answer = chunk_dict.get('answer', None)
            if answer is not None and answer != '':
                yield answer

    except Exception as e:
        logging.error("Error during chat_streaming: %s", e)
        yield ""


if __name__ == "__main__":
    # 流式调用示例
    # print("Streaming response:")
    # for response in chat_streaming({"query": "你好啊", "inputs": {}}):
    #     print(response, end="", flush=True)
    # print()  # 添加一个换行

    # 阻塞式调用示例
    print("\nBlocking response:")
    blocking_response = chat_blocking({"query": "你好啊", "inputs": {}})
    print(blocking_response)
