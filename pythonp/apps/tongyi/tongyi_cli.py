import argparse
import os
import sys
import json
from pythonp.common.ai.tongyi.tongyi_model import TongyiModels
from pythonp.common.ai.tongyi.tongyi_client import TongyiClient
from pythonp.common.logging.logger import logging
from rich.console import Console
from typing import Dict, Any, Tuple, List
"""
通义千问 CLI - 命令行工具
Docs: https://help.aliyun.com/zh/model-studio/use-qwen-by-calling-api

这个工具允许用户通过命令行与通义千问 API 交互，使用通义千问模型生成文本。
支持以下功能：
- 选择不同的通义千问模型
- 自定义系统提示
- 提供 API 密钥
- 发送查询并显示响应
- 配置模型选项（温度、top_p、max_tokens、search_engine、stream 等）

使用示例：
    bazel run //pythonp/apps/tongyi:tongyi_cli -- \
    --model 1 --api-key="api-key"  \
    --query "CE0068 卡地亚love戒指 宽版 47号 玫瑰金材质   尺寸：47  附件无 市场参考价15000 好价"  \
    --system-prompt "你是一个专业的奢侈品电商助手，擅长从商品描述中准确提取出奢侈品牌名称。用户可能提供任意语言的商品标题或描述，你的任务是仅返回该商品的奢侈品牌名，且输出为中文品牌名称。不需要任何解释或格式修饰。如果描述中没有提到奢侈品牌，或者品牌不属于奢侈品类别，请返回 “未知品牌”。" \
    --options '{"temperature": 0.2, "top_p": 0.9, "max_tokens": 1000, "search_engine": "bing", "stream": true}'
"""


def main():
    """
    主函数，处理命令行参数并执行通义千问 API 请求
    
    处理流程：
    1. 解析命令行参数
    2. 验证 API 密钥
    3. 选择模型
    4. 处理查询输入
    5. 创建请求
    6. 发送请求并显示响应
    """
    parser = argparse.ArgumentParser(description="通义千问 CLI - 与通义千问模型交互")
    parser.add_argument("--query", help="查询文本")
    parser.add_argument(
        "--model",
        type=str,
        default="1",
        help=
        "模型选择 (1-3 或模型名称): 1=QWEN_TURBO (默认/最快), 2=QWEN_PLUS, 3=QWEN_MAX (最大), 或直接传入模型名称"
    )
    parser.add_argument("--system-prompt",
                        type=str,
                        default="你是一个有帮助的助手，可以回答问题。",
                        help="系统提示")
    parser.add_argument("--api-key",
                        type=str,
                        default=os.environ.get("TONGYI_API_KEY"),
                        help="通义千问 API 密钥 (默认为 TONGYI_API_KEY 环境变量)")
    parser.add_argument(
        "--options",
        type=str,
        help=
        "模型选项的 JSON 字符串 (temperature, top_p, max_tokens, search_engine, search_depth, stream 等)"
    )

    args = parser.parse_args()

    # 检查 API 密钥
    if not args.api_key:
        logging.error("未提供 API 密钥。请设置 TONGYI_API_KEY 环境变量或使用 --api-key。")
        sys.exit(1)

    # 处理模型选择
    try:
        # 尝试将输入转换为数字
        model_num = int(args.model)
        if model_num not in range(1, 4):
            raise ValueError("模型编号必须在 1-3 之间")
        # 将模型编号映射到 TongyiModels 枚举
        model_map = {
            1: TongyiModels.QWEN_TURBO,  # 最快
            2: TongyiModels.QWEN_PLUS,
            3: TongyiModels.QWEN_MAX  # 最大
        }
        selected_model = model_map[model_num]
    except ValueError:
        # 如果不是数字，则直接使用输入的模型名称
        selected_model = args.model

    logging.info(
        f"使用模型: {selected_model if isinstance(selected_model, str) else selected_model.value}"
    )

    # 处理查询输入
    if not args.query:
        logging.error("未提供查询。请使用 --query 参数提供查询。")
        sys.exit(1)

    query_text = args.query
    logging.info("使用命令行参数中的查询")

    # 处理选项
    options_dict = {}

    # 添加用户提供的其他选项
    if args.options:
        try:
            user_options = json.loads(args.options)
            options_dict.update(user_options)

            # 记录搜索和流式选项
            if "search_engine" in user_options:
                logging.info(f"使用搜索引擎: {user_options['search_engine']}")
            if "stream" in user_options:
                logging.info("启用流式响应")

        except json.JSONDecodeError:
            logging.error("无效的 JSON 格式选项")
            sys.exit(1)

    # 创建通义千问客户端
    client = TongyiClient(args.api_key)

    # 合并所有选项到一个 JSON 字符串
    options_json = json.dumps(options_dict) if options_dict else None

    # 创建请求
    logging.info("使用系统提示创建请求: %s", args.system_prompt)

    # 发送请求并显示响应
    logging.info("向通义千问 API 发送请求")
    response = client.chat_completion(query=query_text,
                                      system_prompt=args.system_prompt,
                                      model=selected_model,
                                      options=options_json)
    logging.info("从通义千问 API 收到响应")

    # 使用 rich console 显示响应
    console = Console()

    if not options_dict.get("stream", False):
        console.print("\n[bold green]响应:[/bold green]")
        client.display_response(console, response)
    else:
        # 处理流式响应
        console.print("\n[bold green]响应:[/bold green]")
        model_name = selected_model if isinstance(
            selected_model, str) else selected_model.value
        response = client._handle_streaming_response(response, model_name)
        client.display_response(console, response, is_stream=True)

    logging.info("请求成功完成")


if __name__ == "__main__":
    main()
