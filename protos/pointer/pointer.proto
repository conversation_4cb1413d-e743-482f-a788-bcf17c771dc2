syntax = "proto3";

package example;

option go_package = "pointer/protos/example";
option java_package = "com.example";
option java_multiple_files = true;

// =============== Example Messages ===============

// User represents a simple user entity
message User {
    int32 id = 1;           // User ID
    string name = 2;        // User's full name
    string email = 3;       // User's email address
    int32 age = 4;          // User's age
    bool is_active = 5;     // Whether the user is active
    repeated string tags = 6; // User tags
}

// Address represents a user's address
message Address {
    string street = 1;      // Street address
    string city = 2;        // City
    string state = 3;       // State or province
    string zip_code = 4;    // ZIP or postal code
    string country = 5;     // Country
}

// UserProfile combines user and address information
message UserProfile {
    User user = 1;          // User information
    Address address = 2;    // User's address
    string created_at = 3;  // Profile creation timestamp
    string updated_at = 4;  // Last update timestamp
}

// =============== Service Definition ===============

// UserService provides user management operations
service UserService {
    // CreateUser creates a new user
    rpc CreateUser (CreateUserRequest) returns (CreateUserResponse);

    // GetUser retrieves a user by ID
    rpc GetUser (GetUserRequest) returns (GetUserResponse);

    // ListUsers lists users with pagination
    rpc ListUsers (ListUsersRequest) returns (ListUsersResponse);

    // UpdateUser updates an existing user
    rpc UpdateUser (UpdateUserRequest) returns (UpdateUserResponse);

    // DeleteUser deletes a user by ID
    rpc DeleteUser (DeleteUserRequest) returns (DeleteUserResponse);
}

// UpdateUserRequest for updating an existing user
message UpdateUserRequest {
    int32 id = 1;           // User ID
    string name = 2;        // Updated name
    string email = 3;       // Updated email
    int32 age = 4;          // Updated age
    bool is_active = 5;     // Updated active status
    repeated string tags = 6; // Updated tags
}

// UpdateUserResponse for user update response
message UpdateUserResponse {
    User user = 1;          // Updated user
    string message = 2;     // Response message
    bool success = 3;       // Operation success status
}

// DeleteUserRequest for deleting a user
message DeleteUserRequest {
    int32 id = 1;           // User ID to delete
}

// DeleteUserResponse for user deletion response
message DeleteUserResponse {
    string message = 1;     // Response message
    bool success = 2;       // Operation success status
}
