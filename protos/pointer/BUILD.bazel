load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_proto_grpc_doc//:defs.bzl", "doc_html_compile", "doc_markdown_compile")
load("@rules_proto_grpc_go//:defs.bzl", "go_grpc_library", "go_proto_library")
load("@rules_proto_grpc_java//:defs.bzl", "java_grpc_library", "java_proto_library")
load("@rules_proto_grpc_python//:defs.bzl", "python_grpc_library", "python_proto_library")

proto_library(
    name = "pointer_proto",
    srcs = ["pointer.proto"],
    visibility = ["//visibility:public"],
)

# Documentation generation
doc_markdown_compile(
    name = "pointer_grpc_doc",
    protos = [":pointer_proto"],
    visibility = ["//visibility:public"],
)

doc_html_compile(
    name = "pointer_grpc_html",
    protos = [":pointer_proto"],
    visibility = ["//visibility:public"],
)

# Go libraries
go_proto_library(
    name = "pointer_go_proto",
    importpath = "pointer/protos/example",
    protos = [":pointer_proto"],
    visibility = ["//visibility:public"],
)

go_grpc_library(
    name = "pointer_go_grpc",
    importpath = "pointer/protos/example",
    protos = [":pointer_proto"],
    visibility = ["//visibility:public"],
)

# Java libraries
java_proto_library(
    name = "pointer_java_proto",
    protos = [":pointer_proto"],
    visibility = ["//visibility:public"],
)

java_grpc_library(
    name = "pointer_java_grpc",
    protos = [":pointer_proto"],
    visibility = ["//visibility:public"],
)

# Python libraries
python_proto_library(
    name = "pointer_py_proto",
    protos = [":pointer_proto"],
    visibility = ["//visibility:public"],
)

python_grpc_library(
    name = "pointer_py_grpc",
    protos = [":pointer_proto"],
    visibility = ["//visibility:public"],
)
