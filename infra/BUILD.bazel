load("@rules_pkg//pkg:mappings.bzl", "pkg_files", "strip_prefix")
load("@rules_pkg//pkg:tar.bzl", "pkg_tar")

package(default_visibility = ["//visibility:public"])

pkg_files(
    name = "ansible_files",
    srcs = [
        "//infra/ansible",
    ],
    # Where it should be in the final package
    prefix = "ansible/",
    # Required, but why?: see #354
    strip_prefix = strip_prefix.from_pkg(),
)

pkg_tar(
    name = "ansible_tar",
    srcs = [
        "README.md",
        ":ansible_files",
    ],
)
