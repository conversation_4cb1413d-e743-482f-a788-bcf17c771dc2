---
- hosts: vpn_server
  gather_facts: true
  become: yes
  become_user: root
  vars:
    openvpn_client_platforms: ["windows", "linux"]
    is_only_revoke_cert: false
    is_only_add_client: false
    add_clients:
      - sdj

    clients:
      - sdj
  #    openvpn_revoke_these_certs:
  #      - shantong

  pre_tasks:
    - name: Minio python dependency install
      become: no
      import_role:
        name: minio
      delegate_to: localhost

  tasks:
    - name: revoke the client config directory
      include_role:
        name: openvpn
        tasks_from: only_revoke_cert.yml
      when: is_only_revoke_cert

    - name: Set common OpenVPN variables
      set_fact:
        openvpn_port: 4500
        openvpn_proto: tcp
        openvpn_sync_certs: true
        openvpn_use_crl: true
        openvpn_server_hostname: network.sdj.cn
        openvpn_server_netmask: *************
        openvpn_server_network: *********
        hangzhou_office_network: ********** *************
        dns_server:
          ip: ************
          domain: corp.sdj.cn

    - name: Set OpenVPN push options
      set_fact:
        route_list:
          - "{{ shenzhen_office_network | replace(' ', '/') | ipaddr('cidr') }}"
          # - "{{ shanghai_office_network | replace(' ', '/') | ipaddr('cidr') }}"
        gateway: "{{ openvpn_server_network | ipaddr('network') | ipmath(1) }}"
        openvpn_push:
          - route {{ hangzhou_office_network }}
          - dhcp-option DNS {{ dns_server.ip }}
          - dhcp-option DOMAIN {{ dns_server.domain }}
        openvpn_addl_client_options:
          - push "route {{ hangzhou_office_network }}"
          - push "dhcp-option DNS {{ dns_server.ip }}"
          - push "dhcp-option DOMAIN {{ dns_server.domain }}"

    - name: Set OpenVPN client-specific configurations
      set_fact:
        openvpn_client_configs:
          shenzhen_office:
            - "ifconfig-push *********** ***********"
            - "iroute *********** *************"

    - name: Create the client config directory
      include_role:
        name: openvpn
        tasks_from: add_client_keys.yml
      when: is_only_add_client

    - name: Include the openvpn role
      include_role:
        name: openvpn
      when: not is_only_revoke_cert and not is_only_add_client

    - name: Upload client ovpn files to s3
      delegate_to: localhost
      become: no
      run_once: true
      block:
        - include_role:
            name: minio
            tasks_from: upload.yaml
          vars:
            m_bucket: "openvpn"
            m_object: "{{ item[1] }}-client/sdj-{{ item[1] }}-{{ item[0] }}.ovpn"
            m_source: "/tmp/ansible/jancsivpn-{{ item[1] }}-{{ item[0] }}.ovpn"
            m_access_key: "sdj"
            m_secret_key: "sdj"
          with_nested:
            - "{{ clients }}"
            - "{{ openvpn_client_platforms }}"
      when: not is_only_revoke_cert and not is_only_add_client

    - name: Upload add_clients ovpn files to s3
      delegate_to: localhost
      become: no
      run_once: true
      block:
        - include_role:
            name: minio
            tasks_from: upload.yaml
          vars:
            m_bucket: "openvpn"
            m_object: "{{ item[1] }}-client/sdj-{{ item[1] }}-{{ item[0] }}.ovpn"
            m_source: "/tmp/ansible/jancsivpn-{{ item[1] }}-{{ item[0] }}.ovpn"
            m_access_key: "sdj"
            m_secret_key: "sdj"
          with_nested:
            - "{{ add_clients }}"
            - "{{ openvpn_client_platforms }}"
      when: is_only_add_client
