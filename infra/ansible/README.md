# IT Automation with Ansible

This directory contains Ansible playbooks and inventory files for automating infrastructure.

## Prerequisites

Install Ansible with pip:

```bash
pip3 install ansible
```

You may also need `sshpass` to enable SSH connections with passwords:

```bash
# Debian/Ubuntu
sudo apt-get install sshpass
```

For more details, see:
<https://stackoverflow.com/questions/42835626/ansible-to-use-the-ssh-connection-type-with-passwords-you-must-install-the-s>.

## Usage

All Ansible commands should be run from this directory.

To test connectivity to all hosts in the `development` inventory:

```bash
ansible -i development all -m ping
```

To run a playbook with the `development` inventory:

```bash
ansible-playbook -i development orthanc.yaml
```

## Passing Extra Variables

Pass extra variables to the playbook using the `--extra-vars` (or `-e`) option:

```bash
# Single variable
ansible-playbook -i development orthanc.yaml --extra-vars "fruit=apple"

# Multiple variables in JSON format
ansible-playbook -i development orthanc.yaml --extra-vars '{"fruit":"apple"}'

# Variables from an external file
ansible-playbook -i development orthanc.yaml --extra-vars @vars.json
ansible-playbook -i development orthanc.yaml --extra-vars @vars.yml
```

Extra variables are useful when integrating playbooks into existing automation or scripts.
