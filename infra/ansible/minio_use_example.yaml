---
- name: Test Download
  hosts: orthanc_server
  pre_tasks:
    - name: Minio python dependency install
      import_role:
        name: minio

  tasks:
    - name: Download from minio bucket
      import_role:
        name: minio
        tasks_from: download.yaml
      vars:
        m_bucket: "test"
        m_object: "img001.jpg"
        m_dest: "/tmp/img001.jpg"
        m_access_key: "sdj"
        m_secret_key: "sdj"

    - name: Upload to minio bucket
      import_role:
        name: minio
        tasks_from: upload.yaml
      vars:
        m_bucket: "test"
        m_object: "img002.jpg"
        m_source: "/tmp/img001.jpg"
        m_access_key: "sdj"
        m_secret_key: "sdj"
