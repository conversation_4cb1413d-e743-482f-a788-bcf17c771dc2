---
- hosts: postgresql_server
  become: true
  become_user: root
  vars:
    postgresql_objects_users:
      - name: pgadmin
        password: arxtect
        role_attr_flags: SUPERUSER

      - name: arxtect-einstein
        password: einstein123

      - name: pointer_center
        password: pointer_center123

    postgresql_objects_privileges:
      - database: arxtect-einstein
        roles: arxtect-einstein
        objs: ALL_IN_SCHEMA
        privs: ALL

      - database: pointer_center
        roles: pointer_center
        objs: ALL_IN_SCHEMA
        privs: ALL

    postgresql_objects_databases:
      - name: pgadmin
        owner: pgadmin

      - name: arxtect-einstein
        owner: arxtect-einstein

      - name: pointer_center
        owner: pointer_center

    postgresql_objects_extensions:
      - name: hstore
        db: pointer_center

      - name: hstore
        db: pointer_center

  pre_tasks:
    - name: Install required packaged
      apt:
        name: "{{ item }}"
        state: present
      with_items:
        - acl
        - python3-pip

  roles:
    - role: postgresql_objects
      become: true
      become_user: postgres
