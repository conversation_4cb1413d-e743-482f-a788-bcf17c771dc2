---
- name: Deploy and run Flink SQL jobs
  hosts: flink_server
  become: yes
  become_user: root

  vars:
    # Job name filter - can be overridden from command line
    flink_job_name: "{{ job_name | default('') }}"

    # All available SQL jobs configuration
    flink_all_sql_jobs:
      # Product sync jobs
      - src: "roles/flink/file/product_sync/init.sql"
        dest: "product_sync_init.sql"
      - src: "roles/flink/file/product_sync/pro_product.sql"
        dest: "product_sync_pro_product.sql"
      - src: "roles/flink/file/product_sync/pro_detail.sql"
        dest: "product_sync_pro_detail.sql"
      # Add other job types here in the future
      # - src: "roles/flink/file/other_sync/init.sql"
      #   dest: "other_sync_init.sql"

    flink_all_jobs_to_submit:
      # Product sync jobs
      - job_name: "product_sync_pro_product"
        sql_file: "product_sync_pro_product.sql"
        category: "product_sync"
        auto_submit: false
      - job_name: "product_sync_pro_detail"
        sql_file: "product_sync_pro_detail.sql"
        category: "product_sync"
        auto_submit: false
      # Add other job types here in the future
      # - job_name: "other_sync_job"
      #   sql_file: "other_sync_job.sql"
      #   category: "other_sync"
      #   auto_submit: false
      #   extra_args: ""

    # Dynamic job filtering based on input
    flink_sql_jobs: "{{ flink_all_sql_jobs }}"
    flink_sql_jobs_to_submit: "{{ flink_all_jobs_to_submit }}"

  tasks:
    - name: Include flink role defaults
      include_vars: roles/flink/defaults/main.yml

    - name: Display available jobs
      debug:
        msg: |
          Available Flink SQL jobs:
          {% for job in flink_all_jobs_to_submit %}
          - {{ job.job_name }} (category: {{ job.category }})
          {% endfor %}

          Usage examples:
          # Run specific job:
          ansible-playbook -i development/ flink_jobs.yaml -e "job_name=product_sync_pro_product"
          ansible-playbook -i development/ flink_jobs.yaml -e "job_name=product_sync_pro_detail"

          # Run all jobs in a category:
          ansible-playbook -i development/ flink_jobs.yaml -e "job_name=product_sync"

          # List all jobs:
          ansible-playbook -i development/ flink_jobs.yaml
      when: flink_job_name == ""

    - name: Filter jobs by category
      set_fact:
        filtered_jobs: "{{ flink_all_jobs_to_submit | selectattr('category', 'equalto', flink_job_name) | list }}"
      when:
        - flink_job_name != ""
        - flink_all_jobs_to_submit | selectattr('category', 'equalto', flink_job_name) | list | length > 0

    - name: Filter jobs by exact name
      set_fact:
        filtered_jobs: "{{ flink_all_jobs_to_submit | selectattr('job_name', 'equalto', flink_job_name) | list }}"
      when:
        - flink_job_name != ""
        - filtered_jobs is not defined

    - name: Validate job name
      fail:
        msg: |
          Job '{{ flink_job_name }}' not found!
          Available jobs:
          {% for job in flink_all_jobs_to_submit %}
          - {{ job.job_name }} (category: {{ job.category }})
          {% endfor %}
      when:
        - flink_job_name != ""
        - filtered_jobs is not defined or filtered_jobs | length == 0

    - name: Create Flink jobs directories
      file:
        path: "{{ flink_jobs_path }}/sql"
        state: directory
        mode: 0755
      when: filtered_jobs is defined and filtered_jobs | length > 0

    - name: Deploy required SQL files
      copy:
        src: "{{ item.src }}"
        dest: "{{ flink_jobs_path }}/sql/{{ item.dest | default(item.src | basename) }}"
        mode: 0644
      with_items: "{{ flink_all_sql_jobs }}"
      when: filtered_jobs is defined and filtered_jobs | length > 0

    - name: Set ownership of SQL files
      file:
        path: "{{ flink_jobs_path }}/sql"
        owner: "{{ flink_user }}"
        group: "{{ flink_group }}"
        recurse: yes
      when: filtered_jobs is defined and filtered_jobs | length > 0

    - name: Execute Flink SQL jobs
      shell: |
        cd {{ flink_install_path }}
        sudo -u {{ flink_user }} ./bin/sql-client.sh embedded \
          -i {{ flink_jobs_path }}/sql/product_sync_init.sql \
          -f {{ flink_jobs_path }}/sql/{{ item.sql_file }}
      with_items: "{{ filtered_jobs | default([]) }}"
      when: filtered_jobs is defined and filtered_jobs | length > 0
      register: job_execution_result

    - name: Display job execution results
      debug:
        msg: |
          Job: {{ item.item.job_name }}
          Status: {{ 'Success' if item.rc == 0 else 'Failed' }}
          Output: {{ item.stdout }}
          {% if item.stderr %}Error: {{ item.stderr }}{% endif %}
      with_items: "{{ job_execution_result.results | default([]) }}"
      when: job_execution_result is defined
