---
- hosts: vpn_client_service
  gather_facts: true
  become: yes
  become_user: root
  vars:
    versions: v1
    openvpn_base_dir: /etc/openvpn
    client_name: test2
    client_password: test2@123

  tasks:
    - name: Install OpenVPN
      apt:
        name: openvpn
        state: present

    - name: Download Client Config file from minio bucket
      import_role:
        name: minio
        tasks_from: download.yaml
      vars:
        m_bucket: "openvpn"
        m_object: "linux-client/sdj-linux-{{ client_name }}.ovpn"
        m_dest: "{{ openvpn_base_dir }}/sdj-linux-{{ client_name }}.conf"
        m_access_key: "sdj"
        m_secret_key: "sdj"
      when: versions == 'v2'

    - name: Create OpenVPN config file from template
      template:
        src: roles/openvpn/templates/{{ item }}.j2
        dest: "{{ openvpn_base_dir }}/{{ item }}"
        owner: root
        group: root
      with_items:
        - test_openvpn_client_official.conf
        - my-credentials.txt
      when: versions == 'v1'

    - name: Create OpenVPN service unit file from template
      template:
        src: roles/openvpn/templates/openvpn{{ versions }}.service.j2
        dest: /lib/systemd/system/openvpn.service
        owner: root
        group: root

    - name: Reload systemd
      command: systemctl daemon-reload
      become: yes

    - name: Enable OpenVPN service
      systemd:
        name: openvpn
        enabled: yes
        state: started

    - name: Restart OpenVPN service
      systemd:
        name: openvpn
        state: restarted
