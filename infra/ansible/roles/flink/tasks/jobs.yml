---
- name: FLINK | Create flink jobs path
  file:
    path: "{{ flink_jobs_path }}"
    state: directory
    owner: "{{ flink_user }}"
    group: "{{ flink_group }}"
    recurse: true
    mode: 01777

- name: FLINK | Create SQL jobs directory
  file:
    path: "{{ flink_jobs_path }}/sql"
    state: directory
    owner: "{{ flink_user }}"
    group: "{{ flink_group }}"
    recurse: true
    mode: 0755

- name: FLINK | Create job submission script
  template:
    src: "{{ flink_job_submit_script_template_path }}"
    dest: "{{ flink_install_path }}/bin/submit-sql-job.sh"
    owner: "{{ flink_user }}"
    group: "{{ flink_group }}"
    mode: 0755
  when: flink_job_submit_script_template_path is defined

- name: FLINK | Copy SQL job files
  copy:
    src: "{{ item.src }}"
    dest: "{{ flink_jobs_path }}/sql/{{ item.dest | default(item.src | basename) }}"
    owner: "{{ flink_user }}"
    group: "{{ flink_group }}"
    mode: 0644
  with_items: "{{ flink_sql_jobs | default([]) }}"
  when: flink_sql_jobs is defined

- name: FLINK | Submit SQL jobs
  shell: |
    cd {{ flink_install_path }}
    sudo -u {{ flink_user }} ./bin/submit-sql-job.sh \
      --job "{{ item.job_name }}" \
      --file "{{ flink_jobs_path }}/sql/{{ item.sql_file }}" \
      {{ item.extra_args | default('') }}
  with_items: "{{ flink_sql_jobs_to_submit | default([]) }}"
  when:
    - flink_sql_jobs_to_submit is defined
    - item.auto_submit | default(false)
  register: flink_job_submission_result
  failed_when: flink_job_submission_result.rc != 0

- name: FLINK | Display job submission results
  debug:
    msg: |
      Job: {{ item.item.job_name }}
      Status: {{ 'Success' if item.rc == 0 else 'Failed' }}
      Output: {{ item.stdout }}
      Error: {{ item.stderr if item.stderr else 'None' }}
  with_items: "{{ flink_job_submission_result.results | default([]) }}"
  when: flink_job_submission_result is defined

- name: FLINK | Submit specific SQL job by name
  shell: |
    cd {{ flink_install_path }}
    sudo -u {{ flink_user }} ./bin/submit-sql-job.sh \
      --job "{{ item.job_name }}" \
      --file "{{ flink_jobs_path }}/sql/{{ item.sql_file }}" \
      {{ item.extra_args | default('') }}
  with_items: "{{ flink_sql_jobs_to_submit | default([]) }}"
  when:
    - flink_sql_jobs_to_submit is defined
    - flink_job_name is defined
    - item.job_name.startswith(flink_job_name)
  register: flink_specific_job_result
  failed_when: flink_specific_job_result.rc != 0

- name: FLINK | Display specific job submission results
  debug:
    msg: |
      Job: {{ item.item.job_name }}
      Status: {{ 'Success' if item.rc == 0 else 'Failed' }}
      Output: {{ item.stdout }}
      Error: {{ item.stderr if item.stderr else 'None' }}
  with_items: "{{ flink_specific_job_result.results | default([]) }}"
  when: flink_specific_job_result is defined
