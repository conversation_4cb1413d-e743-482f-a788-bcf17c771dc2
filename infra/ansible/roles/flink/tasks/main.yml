---
- name: <PERSON>IN<PERSON> | Check required var flink_manager_type
  fail:
    msg: "flink_manager_type is required. Values possible [jobmanager, taskmanager, history]"
  when: flink_manager_type is undefined and flink_manager_type != 'jobmanager' and flink_manager_type != 'taskmanager' and flink_manager_type != 'history'

- name: <PERSON>INK | Requirements
  include: requirements.yml
  tags:
    - flink_requirements

- name: FLINK | Install
  include: install.yml
  tags:
    - flink_install

- name: <PERSON>INK | Configure
  include: config.yml
  tags:
    - flink_configure

- name: FLINK | Plugins
  include: plugins.yml
  tags:
    - flink_plugins

- name: FLINK | Service
  include: service.yml
  tags:
    - flink_service
# - name: <PERSON>INK | Jobs
#   include: jobs.yml
#   tags:
#     - flink_jobs
