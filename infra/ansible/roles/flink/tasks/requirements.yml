---
# Required packages, libs, etc (for binary installation)
- name: FLINK | Requirements | Install some packages (binary installation)
  apt:
    name: "{{ flink_required_libs }}"
    state: present
  when: flink_install_type == "bin"
  tags:
    - requirements

# For package installation, minimal requirements on target hosts
- name: <PERSON>INK | Requirements | Install minimal packages (package installation)
  apt:
    name:
      - sudo
      - tar
    state: present
  when: flink_install_type == "package"
  tags:
    - requirements
