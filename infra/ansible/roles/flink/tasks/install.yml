---
- name: FLIN<PERSON> | Ensure flink group
  group:
    name: "{{ flink_group }}"
    gid: "{{ flink_gid }}"
  when: flink_install_type == "bin"

- name: FLINK | Ensure flink user
  user:
    name: "{{ flink_user }}"
    group: "{{ flink_group }}"
    uid: "{{ flink_uid }}"
    home: /bin/false
    create_home: false
    comment: "Apache Flink"
  when: flink_install_type == "bin"

# Package installation (download to localhost then copy and extract)
- name: FLINK | Check flink version (package installation)
  command: bash -c "ls {{ flink_install_path }}/lib | grep 'flink-.*_{{ flink_scala_version }}-{{ flink_version }}.jar'"
  register: flink_check
  changed_when: false
  ignore_errors: true
  args:
    chdir: "{{ flink_install_path }}/bin"
  when: flink_install_type == "package"

- name: FLINK | Package installation block
  block:
    - name: FLINK | Create local download directory
      file:
        path: "{{ flink_package_download_path }}"
        state: directory
        mode: 0755
      delegate_to: localhost
      run_once: true

    - name: FLIN<PERSON> | Download flink package to localhost
      get_url:
        url: "{{ flink_mirror + flink_bin_url }}"
        dest: "{{ flink_package_download_path }}/{{ flink_package }}"
        mode: 0644
      delegate_to: localhost
      run_once: true
      register: flink_package_download
      ignore_errors: true

    - name: FLINK | Download flink package from archive mirror
      get_url:
        url: "{{ flink_mirror_archive + flink_bin_url }}"
        dest: "{{ flink_package_download_path }}/{{ flink_package }}"
        mode: 0644
      delegate_to: localhost
      run_once: true
      when: flink_package_download is failed

    - name: FLINK | Copy package to remote hosts
      copy:
        src: "{{ flink_package_download_path }}/{{ flink_package }}"
        dest: "{{ flink_package_remote_path }}/{{ flink_package }}"
        mode: 0644

    - name: FLINK | Create install path
      file:
        path: "{{ flink_install_path }}"
        state: directory
        owner: "{{ flink_user }}"
        group: "{{ flink_group }}"
        mode: 0755

    - name: FLINK | Extract flink package
      unarchive:
        extra_opts: ["--strip-components=1"]
        src: "{{ flink_package_remote_path }}/{{ flink_package }}"
        remote_src: true
        dest: "{{ flink_install_path }}"
        owner: "{{ flink_user }}"
        group: "{{ flink_group }}"

    - name: FLINK | Clean up remote package file
      file:
        path: "{{ flink_package_remote_path }}/{{ flink_package }}"
        state: absent

  when: flink_install_type == "package" and (flink_force_reinstall or flink_check is failed or flink_check.stdout|length == 0)

# Binary installation
- name: FLINK | Check flink version (binary installation)
  command: bash -c "ls {{ flink_install_path }}/lib | grep 'flink-.*_{{ flink_scala_version }}-{{ flink_version }}.jar'"
  register: flink_check
  changed_when: false
  ignore_errors: true
  args:
    chdir: "{{ flink_install_path }}/bin"
  when: flink_install_type == "bin"

- name: FLINK | Install (binary)
  block:
    - name: FLINK | Delete installation older
      file:
        path: "{{ flink_install_path }}"
        state: absent

    - name: FLINK | Create install path
      file:
        path: "{{ flink_install_path }}"
        state: directory
        owner: "{{ flink_user }}"
        group: "{{ flink_group }}"
        mode: 0755

    - name: FLINK | Untar flink
      unarchive:
        extra_opts: ["--strip-components=1"]
        src: "{{ flink_mirror + flink_bin_url }}"
        remote_src: true
        dest: "{{ flink_install_path }}"
        owner: "{{ flink_user }}"
        group: "{{ flink_group }}"
      register: flink_download
      ignore_errors: true

    - name: FLINK | Untar flink by archive
      unarchive:
        extra_opts: ["--strip-components=1"]
        src: "{{ flink_mirror_archive + flink_bin_url }}"
        remote_src: true
        dest: "{{ flink_install_path }}"
        owner: "{{ flink_user }}"
        group: "{{ flink_group }}"
      when: "flink_download is failed"
  when: "flink_install_type == 'bin' and (flink_force_reinstall or flink_check is failed or flink_check.stdout|length == 0)"
