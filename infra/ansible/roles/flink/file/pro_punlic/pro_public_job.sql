-- 设置 Job 名称
SET 'pipeline.name' = 'Sync pro_public to luxurydata';

-- 1. 创建源表，从 pointer 数据库的 pro_public 表读取数据
CREATE TABLE pro_public (
    id STRING,
    en_name STRING,
    model_name STRING,
    classify_code STRING,
    cn_name STRING,
    description STRING,
    small_img STRING,
    public_price DECIMAL(10, 2),
    source STRING,
    insert_time TIMESTAMP(3),
    update_time TIMESTAMP(3),
    update_price_time TIMESTAMP(3),
    state INT,
    watch_core_type STRING,
    watchcase STRING,
    watchcase_size STRING,
    material STRING,
    object_size STRING,
    clothes_size STRING
) WITH (
    'connector' = 'mysql-cdc',
    'hostname' = '***********',
    'port' = '3306',
    'username' = 'username',
    'password' = 'password',
    'database-name' = 'pointer',
    'table-name' = 'pro_public',
    'scan.incremental.snapshot.chunk.key-column' = 'id',
    'scan.startup.mode' = 'initial',
    'scan.incremental.snapshot.chunk.size' = '1000',
    'scan.snapshot.fetch.size' = '5000',
    'server-time-zone' = 'UTC',
    'debezium.min.row.count.to.stream.results' = '10000'
);

-- 2. 创建目标表：products
CREATE TABLE products (
    id STRING,
    sku STRING,
    brand STRING,
    model STRING,
    category STRING,
    name STRING,
    description STRING,
    metadata STRING,
    created_at TIMESTAMP(3),
    updated_at TIMESTAMP(3),
    pro_public_id INT,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'jdbc',
    'url' = '*******************************************************',
    'table-name' = 'products',
    'driver' = 'com.mysql.cj.jdbc.Driver',
    'username' = 'username',
    'password' = 'password',
    'sink.parallelism' = '2',
    'sink.buffer-flush.max-rows' = '5000',
    'sink.buffer-flush.interval' = '5s'
);

-- 3. 创建目标表：product_images
CREATE TABLE product_images (
    id STRING,
    product_id STRING,
    image_url STRING,
    is_primary INT,
    created_at TIMESTAMP(3),
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'jdbc',
    'url' = '*******************************************************',
    'table-name' = 'product_images',
    'driver' = 'com.mysql.cj.jdbc.Driver',
    'username' = '',
    'password' = '',
    'sink.parallelism' = '2',
    'sink.buffer-flush.max-rows' = '5000',
    'sink.buffer-flush.interval' = '5s'
);

-- 4. 创建目标表：product_prices
CREATE TABLE product_prices (
    id STRING,
    product_id STRING,
    currency STRING,
    price DECIMAL(10, 2),
    source STRING,
    retrieved_at TIMESTAMP(3),
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'jdbc',
    'url' = '*******************************************************',
    'table-name' = 'product_prices',
    'driver' = 'com.mysql.cj.jdbc.Driver',
    'username' = '',
    'password' = '',
    'sink.parallelism' = '2',
    'sink.buffer-flush.max-rows' = '5000',
    'sink.buffer-flush.interval' = '5s'
);

-- 5. 创建临时视图 enriched_products
CREATE TEMPORARY VIEW enriched_products AS
SELECT 
    UUID() AS id,
    CONCAT(COALESCE(pp.en_name, '未知品牌'), '-', COALESCE(pp.model_name, '未知型号')) AS sku,
    COALESCE(pp.en_name, '未知品牌') AS brand,
    COALESCE(pp.model_name, '未知型号') AS model,
    COALESCE(pp.classify_code, 'QT') AS category,
    COALESCE(pp.cn_name, '') AS name,
    COALESCE(pp.description, '') AS description,
    CASE 
        WHEN pp.classify_code = 'WB' THEN 
            JSON_OBJECT(
                'watch_core_type' VALUE COALESCE(pp.watch_core_type, ''), 
                'watchcase' VALUE COALESCE(pp.watchcase, ''), 
                'watchcase_size' VALUE COALESCE(pp.watchcase_size, '')
            )
        WHEN pp.classify_code = 'FS' THEN 
            JSON_OBJECT(
                'material' VALUE COALESCE(pp.material, ''), 
                'clothes_size' VALUE COALESCE(pp.clothes_size, '')
            )
        ELSE 
            JSON_OBJECT(
                'material' VALUE COALESCE(pp.material, ''), 
                'object_size' VALUE COALESCE(pp.object_size, '')
            )
    END AS metadata,
    pp.insert_time AS created_at,
    pp.update_time AS updated_at,
    CAST(pp.id AS INT) AS pro_public_id,
    pp.small_img,
    pp.public_price,
    pp.source,
    COALESCE(pp.update_price_time, pp.update_time) AS price_retrieved_at
FROM pro_public pp
WHERE pp.state = 1;

-- 6. 使用 STATEMENT SET 一次性写入所有表
BEGIN STATEMENT SET;

-- 插入 products 表
INSERT INTO products
SELECT 
    id, sku, brand, model, category, name, description, 
    metadata, created_at, updated_at, pro_public_id
FROM enriched_products;

-- 插入 product_images 表
INSERT INTO product_images
SELECT 
    UUID() AS id,
    enriched_products.id AS product_id,
    small_img AS image_url,
    1 AS is_primary,
    created_at
FROM enriched_products
WHERE small_img IS NOT NULL;

-- 插入 product_prices 表
INSERT INTO product_prices
SELECT 
    UUID() AS id,
    enriched_products.id AS product_id,
    'CNY' AS currency,
    public_price AS price,
    COALESCE(source, '官网') AS source,
    price_retrieved_at AS retrieved_at
FROM enriched_products
WHERE public_price IS NOT NULL;

END;
