SET 'execution.runtime-mode' = 'streaming';
SET 'execution.checkpointing.interval' = '20s';
SET 'execution.checkpointing.timeout' = '1min';
SET 'execution.checkpointing.tolerable-failed-checkpoints' = '3';
SET 'parallelism.default' = '2';
SET 'table.exec.state.ttl' = '10min';
SET 'table.exec.mini-batch.enabled' = 'true';
SET 'table.exec.mini-batch.size' = '5000';
SET 'table.exec.mini-batch.allow-latency' = '5s';
SET 'table.exec.source.idle-timeout' = '30s';
SET 'execution.buffer-timeout' = '10ms';
SET 'pipeline.name' = 'product_initialization_job';
