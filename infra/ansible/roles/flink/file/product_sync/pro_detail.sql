-- 设置 Job 名称
SET 'pipeline.name' = 'Sync pro_detail to sync_service.pro_detail';

-- 1. 创建源表，从 pointer 数据库的 pro_detail 表读取数据
CREATE TABLE pro_detail_source (
    id INT,
    fk_pro_product_id INT,
    fk_shp_shop_id INT,
    accessory STRING,
    source STRING,
    buy_channel STRING,
    pop_style STRING,
    material STRING,
    shoes_size STRING,
    customer_name STRING,
    customer_phone STRING,
    customer_remark STRING,
    repair_card STRING,
    repair_card_time STRING,
    unique_code STRING,
    card_code_img STRING,
    product_img STRING,
    flaw_img STRING,
    video_url STRING,
    insert_time TIMESTAMP(3),
    update_time TIMESTAMP(3),
    update_admin INT,
    versions INT,
    del STRING,
    remark STRING,
    remark_img_url STRING,
    customer_info STRING,
    auto_number STRING,
    media_json STRING,
    sale_admin INT
) WITH (
    'connector' = 'mysql-cdc',
    'hostname' = 'mysql.corp.pointer.cn',
    'port' = '3306',
    'username' = 'username',
    'password' = 'password',
    'database-name' = 'sync_service',
    'table-name' = 'pro_detail',
    'scan.incremental.snapshot.chunk.key-column' = 'id',
    'scan.startup.mode' = 'initial',
    'scan.incremental.snapshot.chunk.size' = '1000',
    'scan.snapshot.fetch.size' = '5000',
    'server-time-zone' = 'Asia/Shanghai',
    'debezium.min.row.count.to.stream.results' = '10000'
);

-- 2. 创建目标表：pro_detail (直接复制结构)
CREATE TABLE pro_detail_dest (
    id INT NOT NULL,
    fk_pro_product_id INT,
    fk_shp_shop_id INT,
    accessory STRING,
    source STRING,
    buy_channel STRING,
    pop_style STRING,
    material STRING,
    shoes_size STRING,
    customer_name STRING,
    customer_phone STRING,
    customer_remark STRING,
    repair_card STRING,
    repair_card_time STRING,
    unique_code STRING,
    card_code_img STRING,
    product_img STRING,
    flaw_img STRING,
    video_url STRING,
    insert_time TIMESTAMP(3),
    update_time TIMESTAMP(3),
    update_admin INT,
    versions INT,
    del STRING,
    remark STRING,
    remark_img_url STRING,
    customer_info STRING,
    auto_number STRING,
    media_json STRING,
    sale_admin INT,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'jdbc',
    'url' = '********************************************************',
    'table-name' = 'pro_detail',
    'driver' = 'com.mysql.cj.jdbc.Driver',
    'username' = 'username',
    'password' = 'password',
    'sink.parallelism' = '1',
    'sink.buffer-flush.max-rows' = '5000',
    'sink.buffer-flush.interval' = '5s'
);

-- 3. 直接同步 pro_detail 数据
INSERT INTO pro_detail_dest
SELECT
    id,
    fk_pro_product_id,
    fk_shp_shop_id,
    accessory,
    source,
    buy_channel,
    pop_style,
    material,
    shoes_size,
    customer_name,
    customer_phone,
    customer_remark,
    repair_card,
    repair_card_time,
    unique_code,
    card_code_img,
    product_img,
    flaw_img,
    video_url,
    insert_time,
    update_time,
    update_admin,
    versions,
    del,
    remark,
    remark_img_url,
    customer_info,
    auto_number,
    media_json,
    sale_admin
FROM pro_detail_source;


