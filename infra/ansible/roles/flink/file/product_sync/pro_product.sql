-- 设置 Job 名称
SET 'pipeline.name' = 'Sync pro_product to sync_service.pro_product';

-- 1. 创建源表，从 pointer 数据库的 pro_product 表读取数据
CREATE TABLE pro_product_source (
    id INT,
    biz_id STRING,
    doudian_classify_id INT,
    fk_shp_shop_id INT,
    fk_pro_state_code INT,
    fk_pro_attribute_code STRING,
    fk_pro_classify_code STRING,
    fk_pro_classify_sub_id STRING,
    fk_pro_recycle_id INT,
    fk_pro_tag_id STRING,
    fk_pro_classify_sub_name STRING,
    fk_pro_sub_series_name STRING,
    fk_pro_series_model_name STRING,
    fk_pro_public_id INT,
    name STRING,
    description STRING,
    article_number STRING,
    quality STRING,
    target_user STRING,
    tag STRING,
    total_num INT,
    sale_num INT,
    hot STRING,
    share STRING,
    xianyu_sync_state STRING,
    yanhuobao STRING,
    rent_sync_state STRING,
    kuaishou_sync_state STRING,
    doudian_sync_state STRING,
    ninetyfive_sync_state STRING,
    lock_source STRING,
    init_price DECIMAL(15,0),
    trade_price DECIMAL(15,0),
    agency_price DECIMAL(15,0),
    sale_price DECIMAL(15,0),
    public_price INT,
    finish_price DECIMAL(19,0),
    small_img STRING,
    public_product_img STRING,
    biz_date DATE,
    insert_time TIMESTAMP(3),
    update_time TIMESTAMP(3),
    release_time TIMESTAMP(3),
    lock_time TIMESTAMP(3),
    lock_user_id INT,
    finish_time TIMESTAMP(3),
    save_end_time TIMESTAMP(3),
    insert_admin INT,
    update_admin INT,
    versions INT,
    del STRING,
    union_state STRING,
    delete_remark STRING,
    remark STRING,
    recycle_admin INT,
    recycle_img STRING,
    appraisal_admin INT,
    retrieve_remark STRING,
    fk_shp_retrieve_user_id INT,
    retrieve_time TIMESTAMP(3),
    convey_state STRING,
    fk_pro_convey_id INT,
    refresh_time TIMESTAMP(3),
    recycle_time DATE,
    update_init_price_time TIMESTAMP(3),
    total_additional_cost_price DECIMAL(15,0),
    upload_source STRING,
    union_trade_state INT,
    no_reason_return INT,
    pro_quality STRING,
    show_accessory INT,
    show_flaw_img INT,
    query_param STRING,
    releate_type STRING
) WITH (
    'connector' = 'mysql-cdc',
    'hostname' = 'mysql.corp.sdj.cn',
    'port' = '3306',
    'username' = 'username',
    'password' = 'password',
    'database-name' = 'sync_service',
    'table-name' = 'pro_product',
    'scan.incremental.snapshot.chunk.key-column' = 'id',
    'scan.startup.mode' = 'initial',
    'scan.incremental.snapshot.chunk.size' = '1000',
    'scan.snapshot.fetch.size' = '5000',
    'server-time-zone' = 'Asia/Shanghai',
    'debezium.min.row.count.to.stream.results' = '10000'
);

-- 2. 创建目标表：pro_product (直接复制结构)
CREATE TABLE pro_product_dest (
    id INT NOT NULL,
    biz_id STRING,
    doudian_classify_id INT,
    fk_shp_shop_id INT,
    fk_pro_state_code INT,
    fk_pro_attribute_code STRING,
    fk_pro_classify_code STRING,
    fk_pro_classify_sub_id STRING,
    fk_pro_recycle_id INT,
    fk_pro_tag_id STRING,
    fk_pro_classify_sub_name STRING,
    fk_pro_sub_series_name STRING,
    fk_pro_series_model_name STRING,
    fk_pro_public_id INT,
    name STRING,
    description STRING,
    article_number STRING,
    quality STRING,
    target_user STRING,
    tag STRING,
    total_num INT,
    sale_num INT,
    hot STRING,
    share STRING,
    xianyu_sync_state STRING,
    yanhuobao STRING,
    rent_sync_state STRING,
    kuaishou_sync_state STRING,
    doudian_sync_state STRING,
    ninetyfive_sync_state STRING,
    lock_source STRING,
    init_price DECIMAL(15,0),
    trade_price DECIMAL(15,0),
    agency_price DECIMAL(15,0),
    sale_price DECIMAL(15,0),
    public_price INT,
    finish_price DECIMAL(19,0),
    small_img STRING,
    public_product_img STRING,
    biz_date DATE,
    insert_time TIMESTAMP(3),
    update_time TIMESTAMP(3),
    release_time TIMESTAMP(3),
    lock_time TIMESTAMP(3),
    lock_user_id INT,
    finish_time TIMESTAMP(3),
    save_end_time TIMESTAMP(3),
    insert_admin INT,
    update_admin INT,
    versions INT,
    del STRING,
    union_state STRING,
    delete_remark STRING,
    remark STRING,
    recycle_admin INT,
    recycle_img STRING,
    appraisal_admin INT,
    retrieve_remark STRING,
    fk_shp_retrieve_user_id INT,
    retrieve_time TIMESTAMP(3),
    convey_state STRING,
    fk_pro_convey_id INT,
    refresh_time TIMESTAMP(3),
    recycle_time DATE,
    update_init_price_time TIMESTAMP(3),
    total_additional_cost_price DECIMAL(15,0),
    upload_source STRING,
    union_trade_state INT,
    no_reason_return INT,
    pro_quality STRING,
    show_accessory INT,
    show_flaw_img INT,
    query_param STRING,
    releate_type STRING,
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'jdbc',
    'url' = '********************************************************',
    'table-name' = 'pro_product',
    'driver' = 'com.mysql.cj.jdbc.Driver',
    'username' = 'username',
    'password' = 'password',
    'sink.parallelism' = '1',
    'sink.buffer-flush.max-rows' = '5000',
    'sink.buffer-flush.interval' = '5s'
);

-- 3. 直接同步 pro_product 数据
INSERT INTO pro_product_dest
SELECT
    id,
    biz_id,
    doudian_classify_id,
    fk_shp_shop_id,
    fk_pro_state_code,
    fk_pro_attribute_code,
    fk_pro_classify_code,
    fk_pro_classify_sub_id,
    fk_pro_recycle_id,
    fk_pro_tag_id,
    fk_pro_classify_sub_name,
    fk_pro_sub_series_name,
    fk_pro_series_model_name,
    fk_pro_public_id,
    name,
    description,
    article_number,
    quality,
    target_user,
    tag,
    total_num,
    sale_num,
    hot,
    share,
    xianyu_sync_state,
    yanhuobao,
    rent_sync_state,
    kuaishou_sync_state,
    doudian_sync_state,
    ninetyfive_sync_state,
    lock_source,
    init_price,
    trade_price,
    agency_price,
    sale_price,
    public_price,
    finish_price,
    small_img,
    public_product_img,
    biz_date,
    insert_time,
    update_time,
    release_time,
    lock_time,
    lock_user_id,
    finish_time,
    save_end_time,
    insert_admin,
    update_admin,
    versions,
    del,
    union_state,
    delete_remark,
    remark,
    recycle_admin,
    recycle_img,
    appraisal_admin,
    retrieve_remark,
    fk_shp_retrieve_user_id,
    retrieve_time,
    convey_state,
    fk_pro_convey_id,
    refresh_time,
    recycle_time,
    update_init_price_time,
    total_additional_cost_price,
    upload_source,
    union_trade_state,
    no_reason_return,
    pro_quality,
    show_accessory,
    show_flaw_img,
    query_param,
    releate_type
FROM pro_product_source;
