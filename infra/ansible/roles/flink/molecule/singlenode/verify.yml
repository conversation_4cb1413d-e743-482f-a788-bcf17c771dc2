---
# This is an example playbook to execute goss tests.
# Tests need distributed to the appropriate ansible host/groups
# prior to execution by `goss validate`.

- name: Verify
  hosts: all
  become: true
  vars:
    goss_version: v0.3.16
    goss_arch: amd64
    goss_sha256sum: 827e354b48f93bce933f5efcd1f00dc82569c42a179cf2d384b040d8a80bfbfb
    goss_dst: /usr/local/bin/goss
    goss_url: "https://github.com/aelsabbahy/goss/releases/download/{{ goss_version }}/goss-linux-{{ goss_arch }}"
    goss_test_directory: /tmp
    goss_format: documentation

  tasks:

    - name: Download and install Goss
      get_url:
        url: "{{ goss_url }}"
        dest: "{{ goss_dst }}"
        mode: 0755
      register: download_goss
      until: download_goss is succeeded
      retries: 3

    - name: Copy Goss tests to remote
      template:
        src: "{{ item }}"
        dest: "{{ goss_test_directory }}/{{ item | basename }}"
      with_fileglob:
        - "{{ lookup('env', 'MOLECULE_VERIFIER_TEST_DIRECTORY') }}/test_*.yml"

    - name: Register test files
      shell: "ls {{ goss_test_directory }}/test_*.yml"
      register: test_files

    - name: Execute Goss tests
      command: "{{ goss_dst }} -g {{ item }} validate --format {{ goss_format }}"
      register: test_results
      with_items: "{{ test_files.stdout_lines }}"

    - name: Display details about the Goss results
      debug:
        msg: "{{ item.stdout_lines }}"
      with_items: "{{ test_results.results }}"

    - name: Fail when tests fail
      fail:
        msg: "Goss failed to validate"
      when: item.rc != 0
      with_items: "{{ test_results.results }}"
