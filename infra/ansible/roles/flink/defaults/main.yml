---
## General
flink_version: 1.19.1
flink_scala_version: 2.12

# Installation type: bin (binary/tgz) or package (download to localhost then copy)
flink_install_type: bin

# Owner
flink_user: flink
flink_group: flink
flink_uid: 789
flink_gid: 790

# Files & Paths
flink_install_path: "/opt/flink"
flink_conf_path: "/opt/flink/conf"
flink_log_path: "/var/log/flink"
flink_data_path: "/data/flink"
flink_jobs_path: "{{ flink_data_path }}/jobs"
# It is used for create directories, for example io.temp.dir, checkpoint.dir
flink_extra_path: []

# Download paths (for binary installation)
flink_mirror: "https://downloads.apache.org/flink"
flink_mirror_archive: "https://archive.apache.org/dist/flink"
flink_package_name: "flink-{{ flink_version }}-bin-scala_{{ flink_scala_version }}"
flink_package: "{{ flink_package_name }}.tgz"
flink_bin_url: "/flink-{{ flink_version }}/{{ flink_package }}"

# Package installation (download to localhost then copy and extract)
# Uses the same binary package as bin installation but downloads to localhost first
flink_package_download_path: "/tmp/flink_packages" # Local download directory
flink_package_remote_path: "/tmp" # Remote temporary directory

# Templates path
flink_jobmanger_service_template_path: templates/flink-jobmanager.service.j2
flink_taskmanager_service_template_path: templates/flink-taskmanager.service.j2
flink_history_service_template_path: templates/flink-history.service.j2
flink_conf_yaml_template_path: templates/flink-conf.yaml.j2
flink_masters_template_path: templates/masters.j2
flink_workers_template_path: templates/workers.j2

### Configuration
flink_configuration:
  jobmanager.rpc.address: localhost
  jobmanager.rpc.port: 6123
  jobmanager.bind-host: localhost
  jobmanager.memory.process.size: 1600m
  taskmanager.bind-host: localhost
  taskmanager.host: localhost
  taskmanager.memory.process.size: 4096m
  taskmanager.numberOfTaskSlots: 4
  parallelism.default: 2
  jobmanager.execution.failover-strategy: region
  rest.address: 0.0.0.0
  rest.bind-address: 0.0.0.0
  state.backend: filesystem
  state.checkpoints.dir: file:///opt/flink/checkpoints

flink_manager_type: [jobmanager, taskmanager, history]

# Masters, host:port array
flink_masters: ["localhost:8081"]

# Workers, hosts array
flink_workers: ["localhost"]

### Log configuration
# flink_log4j_template_path: templates/log4j.properties.j2
# flink_log4j_console_template_path: templates/log4j-console.properties.j2
# flink_log4j_cli_template_path: templates/log4j-cli.properties.j2

## Service
# start on boot
flink_service_enabled: true
# current state: started, stopped
flink_service_state: started
flink_service_start_limit_burst: 5
flink_service_start_limit_interval_sec: 400
flink_service_restart_sec: 60

## Miscellaneous
flink_force_reinstall: false

## hadoop plugin
flink_plugin_hadoop: false

## python
flink_python_active: false
flink_python_libs:
  - python3

## jemalloc
flink_jemalloc_active: true
flink_jemalloc_libs: libjemalloc-dev
flink_jemalloc_path: "/usr/lib/x86_64-linux-gnu/libjemalloc.so"

## java
flink_java_home: "/usr/lib/jvm/java-11-openjdk-amd64"

## env variables
flink_env_variables:
  - "JAVA_HOME={{ flink_java_home }}"
  - "{{ flink_jemalloc_active|ternary('LD_PRELOAD='+flink_jemalloc_path, '') }}"

# Requirements
flink_required_libs:
  - wget
  - tar
  - sudo

## SQL Jobs Configuration
# Default SQL jobs - can be overridden in playbooks
flink_sql_jobs:
  - src: "product_sync/init.sql"
    dest: "product_sync_init.sql"
  - src: "product_sync/pro_product.sql"
    dest: "product_sync_pro_product.sql"
  - src: "product_sync/pro_detail.sql"
    dest: "product_sync_pro_detail.sql"

# Default jobs to submit - can be overridden in playbooks
flink_sql_jobs_to_submit: []

# Job submission script template
flink_job_submit_script_template_path: templates/submit-sql-job.sh.j2
