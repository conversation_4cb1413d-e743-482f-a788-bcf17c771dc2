# Flink Jobs 执行指南

## 概述

`flink_jobs.yaml` 是一个通用的 Flink SQL 作业提交 playbook，支持通过 `-e` 参数传入作业名称来执行不同的 job。

## 使用方法

### 1. 查看所有可用作业

```bash
ansible-playbook -i development/ flink_jobs.yaml
```

### 2. 执行特定作业

```bash
# 执行 product_sync_pro_product 作业
ansible-playbook -i development/ flink_jobs.yaml -e "job_name=product_sync_pro_product"

# 执行 product_sync_pro_detail 作业
ansible-playbook -i development/ flink_jobs.yaml -e "job_name=product_sync_pro_detail"
```

### 3. 执行某个类别的所有作业

```bash
# 执行所有 product_sync 类别的作业
ansible-playbook -i development/ flink_jobs.yaml -e "job_name=product_sync"
```

## 当前可用作业

### Product Sync 类别
- `product_sync_pro_product` - 同步 pro_product 表
- `product_sync_pro_detail` - 同步 pro_detail 表

## 添加新作业

### 1. 添加 SQL 文件

将新的 SQL 文件放在 `roles/flink/file/` 目录下的相应子目录中。

### 2. 更新 flink_jobs.yaml

在 `flink_all_sql_jobs` 和 `flink_all_jobs_to_submit` 中添加新作业配置：

```yaml
flink_all_sql_jobs:
  # 现有作业...
  - src: "new_sync/init.sql"
    dest: "new_sync_init.sql"
  - src: "new_sync/new_job.sql"
    dest: "new_sync_job.sql"

flink_all_jobs_to_submit:
  # 现有作业...
  - job_name: "new_sync_job"
    sql_file: "new_sync_job.sql"
    category: "new_sync"
    auto_submit: false
    extra_args: "--init-file new_sync_init.sql"
```

## 文件结构

```
infra/ansible/
├── flink_jobs.yaml                    # 主要的作业提交 playbook
├── roles/flink/
│   ├── defaults/main.yml              # 默认配置
│   ├── tasks/jobs.yml                 # 作业相关任务
│   ├── templates/submit-sql-job.sh.j2 # 作业提交脚本模板
│   └── file/
│       └── product_sync/              # Product sync 作业文件
│           ├── init.sql
│           ├── pro_product.sql
│           └── pro_detail.sql
└── development/hosts                  # 开发环境主机配置
```

## 注意事项

1. 确保 Flink 集群正在运行
2. 确保数据库连接配置正确
3. 检查 SQL 文件中的表名和字段名是否正确
4. 作业执行日志会保存在 `/var/log/flink/` 目录下
