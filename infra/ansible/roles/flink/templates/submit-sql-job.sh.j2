#!/bin/bash

# Flink SQL Job Submission Script
# Usage: ./submit-sql-job.sh --job <job_name> --file <sql_file> [--init-file <init_file>] [other options]

set -e

FLINK_HOME="{{ flink_install_path }}"
JOBS_PATH="{{ flink_jobs_path }}/sql"
LOG_PATH="{{ flink_log_path }}"

# Default values
JOB_NAME=""
SQL_FILE=""
INIT_FILE=""
EXTRA_ARGS=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --job)
            JOB_NAME="$2"
            shift 2
            ;;
        --file)
            SQL_FILE="$2"
            shift 2
            ;;
        --init-file)
            INIT_FILE="$2"
            shift 2
            ;;
        *)
            EXTRA_ARGS="$EXTRA_ARGS $1"
            shift
            ;;
    esac
done

# Validate required parameters
if [[ -z "$JOB_NAME" ]]; then
    echo "Error: --job parameter is required"
    exit 1
fi

if [[ -z "$SQL_FILE" ]]; then
    echo "Error: --file parameter is required"
    exit 1
fi

# Check if SQL file exists
if [[ ! -f "$JOBS_PATH/$SQL_FILE" ]]; then
    echo "Error: SQL file $JOBS_PATH/$SQL_FILE not found"
    exit 1
fi

# Check if init file exists (if specified)
if [[ -n "$INIT_FILE" && ! -f "$JOBS_PATH/$INIT_FILE" ]]; then
    echo "Error: Init file $JOBS_PATH/$INIT_FILE not found"
    exit 1
fi

echo "Starting Flink SQL job submission..."
echo "Job Name: $JOB_NAME"
echo "SQL File: $SQL_FILE"
echo "Init File: ${INIT_FILE:-None}"
echo "Extra Args: ${EXTRA_ARGS:-None}"

# Create log directory if it doesn't exist
mkdir -p "$LOG_PATH"

# Prepare SQL command
SQL_COMMAND=""

# Add init file if specified
if [[ -n "$INIT_FILE" ]]; then
    echo "Loading init file: $INIT_FILE"
    SQL_COMMAND="$(cat $JOBS_PATH/$INIT_FILE)"$'\n'
fi

# Add main SQL file
echo "Loading main SQL file: $SQL_FILE"
SQL_COMMAND="$SQL_COMMAND$(cat $JOBS_PATH/$SQL_FILE)"

# Submit the job
echo "Submitting job to Flink..."
LOG_FILE="$LOG_PATH/${JOB_NAME}_$(date +%Y%m%d_%H%M%S).log"

# Use Flink SQL Client to submit the job
cd "$FLINK_HOME"
echo "$SQL_COMMAND" | ./bin/sql-client.sh embedded \
    --jar lib/flink-connector-jdbc-*.jar \
    --jar lib/flink-connector-mysql-cdc-*.jar \
    $EXTRA_ARGS \
    > "$LOG_FILE" 2>&1

if [[ $? -eq 0 ]]; then
    echo "Job $JOB_NAME submitted successfully!"
    echo "Log file: $LOG_FILE"
else
    echo "Job $JOB_NAME submission failed!"
    echo "Check log file: $LOG_FILE"
    exit 1
fi
