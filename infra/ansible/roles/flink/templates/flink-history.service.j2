[Unit]
Description=Apache Flink
After=network.target

[Service]
Type=forking
Restart=on-failure
{% if flink_env_variables | length > 0 %}
{% for var in flink_env_variables | select('!=', '')  %}
Environment="{{ var }}"
{% endfor %}
{% endif %}

ExecStart={{ flink_install_path }}/bin/historyserver.sh start
ExecStop={{ flink_install_path }}/bin/historyserver.sh stop

User={{ flink_user }}

# Specifies the maximum file descriptor number that can be opened by this process
{% if flink_max_open_files is defined %}
LimitNOFILE={{ flink_max_open_files }}
{% endif %}

[Install]
WantedBy=multi-user.target
