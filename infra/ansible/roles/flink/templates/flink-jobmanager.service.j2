[Unit]
Description=Apache Flink
After=network.target
StartLimitIntervalSec={{ flink_service_start_limit_interval_sec }}
StartLimitBurst={{ flink_service_start_limit_burst }}

[Service]
Type=forking
Restart=on-failure
RestartSec={{ flink_service_restart_sec }}
{% if flink_env_variables | length > 0 %}
{% for var in flink_env_variables | select('!=', '')  %}
Environment="{{ var }}"
{% endfor %}
{% endif %}

ExecStart={{ flink_install_path }}/bin/jobmanager.sh start
ExecStop={{ flink_install_path }}/bin/jobmanager.sh stop

User={{ flink_user }}
{% if flink_max_open_files is defined %}
# Specifies the maximum file descriptor number that can be opened by this process
LimitNOFILE={{ flink_max_open_files }}
{% endif %}

[Install]
WantedBy=multi-user.target
