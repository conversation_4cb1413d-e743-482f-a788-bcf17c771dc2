# Flink Ansible role

![Logo](logo.gif)

[![Build Status](https://app.travis-ci.com/idealista/flink_role.svg)](https://app.travis-ci.com/github/idealista/flink_role)
[![Ansible Galaxy](https://img.shields.io/badge/galaxy-idealista.flink_role-B62682.svg)](https://galaxy.ansible.com/idealista/flink_role)

This ansible role installs Flink in a Debian environment. It has been tested for the following Debian versions:

- Bullseye

This role has been generated using the [cookiecutter](https://github.com/cookiecutter/cookiecutter) tool, you can generate a similar role that fits your needs using the this [cookiecutter template](https://github.com/idealista/cookiecutter-ansible-role).

- [Getting Started](#getting-started)
  - [Prerequisities](#prerequisities)
  - [Installing](#installing)
- [Usage](#usage)
- [Testing](#testing)
- [Built With](#built-with)
- [Versioning](#versioning)
- [Authors](#authors)
- [License](#license)
- [Contributing](#contributing)

## Getting Started

These instructions will get you a copy of the role for your Ansible playbook. Once launched, it will install Flink in a Debian system.

### Prerequisities

Ansible 4.4.0 version installed.

Molecule 3.x.x version installed.

For testing purposes, [Molecule](https://molecule.readthedocs.io/) with [Docker](https://www.docker.com/) as driver and [Goss](https://github.com/aelsabbahy/goss) as verifier.

### Installing

Create or add to your roles dependency file (e.g requirements.yml):

```
- src: idealista.flink_role
  version: 1.0.0
  name: flink_role
```

Install the role with ansible-galaxy command:

```
ansible-galaxy install -p roles -r requirements.yml -f
```

Use in a playbook:

```
---
- hosts: someserver
  roles:
    - role: flink_role
```

## Usage

Look to the [defaults](defaults/main.yml) properties file to see the possible configuration properties, it is very likely that you will not need to override any variables.

### Installation Types

This role supports two installation methods:

1. **Binary Installation (default)**: Downloads and installs Flink from Apache archives

   ```yaml
   flink_install_type: bin
   ```

2. **Package Installation**: Downloads binary package to localhost then copies and extracts on target hosts
   ```yaml
   flink_install_type: package
   flink_version: 1.19.1
   flink_scala_version: 2.12
   flink_package_download_path: "/tmp/flink_packages" # Local download directory
   flink_package_remote_path: "/tmp" # Remote temporary directory
   ```

The binary installation downloads directly to target hosts, while package installation downloads to the Ansible control node (localhost) first, then copies to target hosts. This is useful when target hosts have limited internet access or when you want to cache packages locally. Both methods use the same Apache Flink binary packages.

## Testing

### Install dependencies

```sh
$ pipenv sync
```

For more information read the [pipenv docs](ipenv-fork.readthedocs.io/en/latest/).

### Testing

```sh
$ pipenv run molecule test
```

## Built With

![Ansible](https://img.shields.io/badge/ansible-4.4.0-green.svg)
![Molecule](https://img.shields.io/badge/molecule-3.4.0-green.svg)
![Goss](https://img.shields.io/badge/goss-0.3.16-green.svg)

## Versioning

For the versions available, see the [tags on this repository](https://github.com/idealista/flink_role/tags).

Additionaly you can see what change in each version in the [CHANGELOG.md](CHANGELOG.md) file.

## Authors

- **Idealista** - _Work with_ - [idealista](https://github.com/idealista)

See also the list of [contributors](https://github.com/idealista/flink_role/contributors) who participated in this project.

## License

![Apache 2.0 License](https://img.shields.io/hexpm/l/plug.svg)

This project is licensed under the [Apache 2.0](https://www.apache.org/licenses/LICENSE-2.0) license - see the [LICENSE](LICENSE) file for details.

## Contributing

Please read [CONTRIBUTING.md](.github/CONTRIBUTING.md) for details on our code of conduct, and the process for submitting pull requests to us.
