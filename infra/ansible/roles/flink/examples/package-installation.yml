---
# Example playbook for package installation (download to localhost then copy and extract)
- name: Install Flink via package download
  hosts: all
  vars:
    # Use package installation (download to localhost then copy and extract)
    flink_install_type: package
    flink_version: 1.19.1
    flink_scala_version: 2.12
    flink_package_download_path: "/tmp/flink_packages"
    flink_package_remote_path: "/tmp"

    # Configure Flink services to run
    flink_manager_type: [jobmanager, taskmanager]

    # Basic Flink configuration
    flink_configuration:
      jobmanager.rpc.address: "{{ ansible_default_ipv4.address }}"
      jobmanager.rpc.port: 6123
      jobmanager.bind-host: "0.0.0.0"
      jobmanager.memory.process.size: 1600m
      taskmanager.bind-host: "0.0.0.0"
      taskmanager.host: "{{ ansible_default_ipv4.address }}"
      taskmanager.memory.process.size: 4096m
      taskmanager.numberOfTaskSlots: 4
      parallelism.default: 2
      rest.address: "0.0.0.0"
      rest.bind-address: "0.0.0.0"

  roles:
    - role: flink_role
      tags: flink
