---
# Example playbook for binary installation (default)
- name: Install Flink via binary download
  hosts: all
  vars:
    # Use binary installation (this is the default)
    flink_install_type: bin
    flink_version: 1.19.1
    flink_scala_version: 2.12
    
    # Configure Flink services to run
    flink_manager_type: [jobmanager, taskmanager]
    
    # Custom installation path
    flink_install_path: "/opt/flink"
    
    # User and group configuration
    flink_user: flink
    flink_group: flink
    flink_uid: 789
    flink_gid: 790
    
    # Basic Flink configuration
    flink_configuration:
      jobmanager.rpc.address: "{{ ansible_default_ipv4.address }}"
      jobmanager.rpc.port: 6123
      jobmanager.bind-host: "0.0.0.0"
      jobmanager.memory.process.size: 1600m
      taskmanager.bind-host: "0.0.0.0"
      taskmanager.host: "{{ ansible_default_ipv4.address }}"
      taskmanager.memory.process.size: 4096m
      taskmanager.numberOfTaskSlots: 4
      parallelism.default: 2
      rest.address: "0.0.0.0"
      rest.bind-address: "0.0.0.0"
      
  roles:
    - role: flink_role
      tags: flink
