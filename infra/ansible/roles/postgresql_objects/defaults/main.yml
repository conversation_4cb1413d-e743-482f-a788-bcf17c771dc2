---
# Revoking privileges from a deleted user, database, or table would normally
# cause a failure with one of the following messages from <PERSON>gres:
#
#   role "foo" does not exist
#   Could not connect to database: FATAL:  database "foo" does not exist
#   relation "public.foo" does not exist
#
# If not handled, this would prevent this role from being used idempotently.
# Caveat: if you typo a user, db, or table to revoke, this will happily
# indicate that revoking was successful (the named user does not have the
# listed privilege(s) on the provided db, after all) and continue on.
#
# This only works if your roles parameter in postgresql_privileges only
# contains one role, since the entire revoke will fail and roll back if any of
# the listed roles don't exist, and that's a condition that we don't want to
# treat as successful.
#
# The error string is checked for the text 'does not exist', so if your
# Postgres server returns messages in a different locale, this probably won't
# work.
postgresql_objects_ignore_revoke_failure: true

postgresql_objects_users: []
postgresql_objects_groups: []
postgresql_objects_databases: []
postgresql_objects_privileges: []
postgresql_objects_extensions: []
