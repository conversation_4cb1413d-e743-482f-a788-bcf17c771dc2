---

# Fail is used here to make the message stand out (red)
- name: Warn if deprecated user priv param is set
  fail:
    msg: "Support for the deprecated `priv` param to postgresql_objects_users has been removed, please use postgresql_objects_privileges instead"
  loop: "{{ postgresql_objects_users }}"
  loop_control:
    label: "{{ item.name }}"
  when: item.priv is defined
  ignore_errors: true

- name: Revoke extra privileges
  community.postgresql.postgresql_privs:
    database: "{{ item.database }}"
    roles: "{{ item.roles }}"
    type: "{{ item.type | default(omit) }}"
    objs: "{{ item.objs | default(omit) }}"
    privs: "{{ item.privs | default(omit) }}"
    schema: "{{ item.schema | default(omit) }}"
    grant_option: "{{ item.grant_option | default(omit) }}"
    state: "{{ item.state | default(omit) }}"
    login_host: "{{ postgresql_objects_login_host | default(omit) }}"
    login_user: "{{ postgresql_objects_login_user | default(omit) }}"
    login_password: "{{ postgresql_objects_login_password | default(omit) }}"
    port: "{{ postgresql_objects_port | default(omit) }}"
  loop: "{{ postgresql_objects_privileges }}"
  register: revoke
  failed_when: >-
    revoke is failed and (
      not postgresql_objects_ignore_revoke_failure or (
        postgresql_objects_ignore_revoke_failure and (
          ('does not exist' not in revoke.msg) or (',' in item.roles)
        )
      )
    )
  when: (item.state | default('present')) == 'absent'

# Drop databases first so later user drop can succeed
- name: Drop databases
  community.postgresql.postgresql_db:
    name: "{{ item.name }}"
    owner: "{{ item.owner | default(omit) }}"
    template: "{{ item.template | default(omit) }}"
    encoding: "{{ item.encoding | default(omit) }}"
    lc_collate: "{{ item.lc_collate | default(omit) }}"
    lc_ctype: "{{ item.lc_ctype | default(omit) }}"
    state: "{{ item.state }}"
    login_host: "{{ postgresql_objects_login_host | default(omit) }}"
    login_user: "{{ postgresql_objects_login_user | default(omit) }}"
    login_password: "{{ postgresql_objects_login_password | default(omit) }}"
    port: "{{ postgresql_objects_port | default(omit) }}"
  loop: "{{ postgresql_objects_databases }}"
  when: (item.state | default('present')) == 'absent'

- name: Create and drop users
  community.postgresql.postgresql_user:
    name: "{{ item.name }}"
    password: "{{ item.password | default(omit) }}"
    role_attr_flags: "{{ item.role_attr_flags | default(omit) }}"
    encrypted: "{{ item.encrypted | default(omit) }}"
    state: "{{ item.state | default(omit) }}"
    login_host: "{{ postgresql_objects_login_host | default(omit) }}"
    login_user: "{{ postgresql_objects_login_user | default(omit) }}"
    login_password: "{{ postgresql_objects_login_password | default(omit) }}"
    port: "{{ postgresql_objects_port | default(omit) }}"
  loop: "{{ postgresql_objects_users }}"
  loop_control:
    label: "{{ item.name }}"

- name: Create groups
  community.postgresql.postgresql_user:
    name: "{{ item.name }}"
    password: "{{ item.password | default(omit) }}"
    role_attr_flags: "{{ item.role_attr_flags | default(omit) }}"
    encrypted: "{{ item.encrypted | default(omit) }}"
    login_host: "{{ postgresql_objects_login_host | default(omit) }}"
    login_user: "{{ postgresql_objects_login_user | default(omit) }}"
    login_password: "{{ postgresql_objects_login_password | default(omit) }}"
    port: "{{ postgresql_objects_port | default(omit) }}"
  loop: "{{ postgresql_objects_groups }}"
  when: (item.state | default('present')) == 'present'
  loop_control:
    label: "{{ item.name }}"

# this could be done more efficiently if we changed the data structure in a backwards-incompatible way
- name: Add or remove users from groups
  community.postgresql.postgresql_membership:
    group: "{{ item.0.name }}"
    target_role: "{{ item.1.name }}"
    state: "{{ item.1.state | default('present') }}"
    login_host: "{{ postgresql_objects_login_host | default(omit) }}"
    login_user: "{{ postgresql_objects_login_user | default(omit) }}"
    login_password: "{{ postgresql_objects_login_password | default(omit) }}"
    port: "{{ postgresql_objects_port | default(omit) }}"
  loop: "{{ postgresql_objects_groups | subelements('users', skip_missing=True) }}"
  when: (item.0.state | default('present')) == 'present'
  loop_control:
    label: "{{ item.0.name }}: {{ item.1.name }}"

- name: Drop groups
  community.postgresql.postgresql_user:
    name: "{{ item.name }}"
    state: "{{ item.state }}"
    login_host: "{{ postgresql_objects_login_host | default(omit) }}"
    login_user: "{{ postgresql_objects_login_user | default(omit) }}"
    login_password: "{{ postgresql_objects_login_password | default(omit) }}"
    port: "{{ postgresql_objects_port | default(omit) }}"
  loop: "{{ postgresql_objects_groups }}"
  when: (item.state | default('present')) == 'absent'
  loop_control:
    label: "{{ item.name }}"

- name: Create databases
  community.postgresql.postgresql_db:
    name: "{{ item.name }}"
    owner: "{{ item.owner | default(omit) }}"
    template: "{{ item.template | default(omit) }}"
    encoding: "{{ item.encoding | default(omit) }}"
    lc_collate: "{{ item.lc_collate | default(omit) }}"
    lc_ctype: "{{ item.lc_ctype | default(omit) }}"
    state: "{{ item.state | default(omit) }}"
    login_host: "{{ postgresql_objects_login_host | default(omit) }}"
    login_user: "{{ postgresql_objects_login_user | default(omit) }}"
    login_password: "{{ postgresql_objects_login_password | default(omit) }}"
    port: "{{ postgresql_objects_port | default(omit) }}"
  loop: "{{ postgresql_objects_databases }}"
  when: (item.state | default('present')) == 'present'
  loop_control:
    label: "{{ item.name }}"

- name: Grant extra privileges
  community.postgresql.postgresql_privs:
    database: "{{ item.database | default(omit) }}"
    roles: "{{ item.roles }}"
    type: "{{ item.type | default(omit) }}"
    objs: "{{ item.objs | default(omit) }}"
    privs: "{{ item.privs | default(omit) }}"
    schema: "{{ item.schema | default(omit) }}"
    grant_option: "{{ item.grant_option | default(omit) }}"
    state: "{{ item.state | default(omit) }}"
    login_host: "{{ postgresql_objects_login_host | default(omit) }}"
    login_user: "{{ postgresql_objects_login_user | default(omit) }}"
    login_password: "{{ postgresql_objects_login_password | default(omit) }}"
    port: "{{ postgresql_objects_port | default(omit) }}"
  loop: "{{ postgresql_objects_privileges }}"
  when: (item.state | default('present')) == 'present'
  loop_control:
    label: "{{ item.roles }}"

- name: Create or drop extensions
  community.postgresql.postgresql_ext:
    name: "{{ item.name }}"
    db: "{{ item.db }}"
    version: latest
    state: "{{ item.state | default(omit) }}"
  loop: "{{ postgresql_objects_extensions }}"

