---
galaxy_info:
  namespace: galaxyproject
  role_name: postgresql_objects
  author: The Galaxy Project
  company: The Galaxy Project
  description: "Configure PostgreSQL objects: users (roles), databases, and privileges"
  license: MIT
  min_ansible_version: "2.4"
  platforms:
    - name: EL
      versions:
        - all
    - name: GenericUNIX
      versions:
        - all
    - name: Fedora
      versions:
        - all
    - name: opensuse
      versions:
        - all
    - name: Amazon
      versions:
        - all
    - name: GenericBSD
      versions:
        - all
    - name: FreeBSD
      versions:
        - all
    - name: Ubuntu
      versions:
        - all
    - name: SLES
      versions:
        - all
    - name: GenericLinux
      versions:
        - all
    - name: Debian
      versions:
        - all
  galaxy_tags:
    - database
    - postgres
    - postgresql
dependencies: []
