# - name: Ensure required packages are installed on remote
#   apt:
#     name:
#       - curl
#       - tar
#       - openjdk-8-jdk
#     state: present
#     update_cache: yes

# - name: Ensure nexus_install_dir exists
#   file:
#     path: "{{ nexus_install_dir }}"
#     state: directory
#     mode: "0755"
#     owner: "{{ nexus_user }}"
#     group: "{{ nexus_user }}"

# - name: Create Nexus user on remote
#   user:
#     name: "{{ nexus_user }}"
#     home: "/home/<USER>"
#     shell: /bin/bash
#     create_home: yes

# - name: Download Nexus tarball locally (delegate to localhost)
#   get_url:
#     url: "{{ nexus_tarball_url }}"
#     dest: "/tmp/{{ nexus_tarball_url | basename }}"
#     mode: "0644"
#   delegate_to: localhost
#   run_once: true

# - name: Copy Nexus tarball to remote server
#   copy:
#     src: "/tmp/{{ nexus_tarball_url | basename }}"
#     dest: "{{ nexus_tarball_dest }}"
#     mode: "0644"
#   become: true

# - name: Extract Nexus tarball on remote
#   unarchive:
#     src: "{{ nexus_tarball_dest }}"
#     dest: "{{ nexus_install_dir }}"
#     remote_src: yes
#     creates: "{{ nexus_install_dir }}/nexus-3.81.1-01"

- name: Create symlink to Nexus directory
  file:
    src: "{{ nexus_install_dir }}/nexus-3.81.1-01"
    dest: "{{ nexus_symlink }}"
    state: link
    force: true
  become: true

- name: Ensure nexus.rc exists and is configured
  copy:
    dest: "{{ nexus_symlink }}/bin/nexus.rc"
    content: |
      run_as_user="{{ nexus_user }}"
    owner: "{{ nexus_user }}"
    group: "{{ nexus_user }}"
    mode: "0644"

- name: Set ownership for Nexus directories
  file:
    path: "{{ item }}"
    owner: "{{ nexus_user }}"
    group: "{{ nexus_user }}"
    recurse: yes
    follow: yes
  loop:
    - "{{ nexus_data_dir }}"
  become: true

- name: Create systemd service for Nexus
  copy:
    dest: /etc/systemd/system/nexus.service
    content: |
      [Unit]
      Description=nexus service
      After=network.target

      [Service]
      Type=forking
      LimitNOFILE=65536
      ExecStart={{ nexus_symlink }}/bin/nexus start
      ExecStop={{ nexus_symlink }}/bin/nexus stop
      User={{ nexus_user }}
      Restart=on-abort

      [Install]
      WantedBy=multi-user.target
    mode: "0644"

- name: Reload systemd
  systemd:
    daemon_reload: yes

- name: Enable and start Nexus service
  systemd:
    name: nexus
    enabled: yes
    state: started
