---
- name: Include OS-specific variables for Fedora or FreeBSD.
  include_vars: "{{ ansible_distribution }}.yml"
  when: ansible_distribution == 'FreeBSD' or ansible_distribution == 'Fedora'

- name: Include version-specific variables for CentOS/RHEL.
  include_vars: "RedHat-{{ ansible_distribution_version.split('.')[0] }}.yml"
  when: >-
    ansible_distribution in [
      'CentOS',
      'Red Hat Enterprise Linux',
      'RedHat',
      'Rocky',
      'AlmaLinux'
    ]

- name: Include version-specific variables for Ubuntu.
  include_vars: "{{ ansible_distribution }}-{{ ansible_distribution_version.split('.')[0] }}.yml"
  when: ansible_distribution == 'Ubuntu'

- name: Include version-specific variables for Debian.
  include_vars: "{{ ansible_distribution | title }}-{{ ansible_distribution_version.split('.')[0] }}.yml"
  when: ansible_os_family == 'Debian'

- name: Define java_packages.
  set_fact:
    java_packages: "{{ __java_packages | list }}"
  when: java_packages is not defined

# Setup/install tasks.
- include_tasks: setup-RedHat.yml
  when: ansible_os_family == 'RedHat'

- include_tasks: setup-Debian.yml
  when: ansible_os_family == 'Debian'

- include_tasks: setup-FreeBSD.yml
  when: ansible_os_family == 'FreeBSD'

# Environment setup.
- name: Set JAVA_HOME if configured.
  template:
    src: java_home.sh.j2
    dest: /etc/profile.d/java_home.sh
    mode: 0644
  when: java_home is defined and java_home
