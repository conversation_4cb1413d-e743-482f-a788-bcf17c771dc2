---
- name: Include OS-specific variables.
  include_vars: "{{ ansible_os_family }}.yml"

- name: Check if GitLab configuration file already exists.
  stat: path=/etc/gitlab/gitlab.rb
  register: gitlab_config_file

- name: Check if Git<PERSON>ab is already installed.
  stat: path=/usr/bin/gitlab-ctl
  register: gitlab_file

# Install GitLab and its dependencies.
- name: Install GitLab dependencies.
  package:
    name: "{{ gitlab_dependencies }}"
    state: present

- name: Install GitLab dependencies (Debian).
  apt:
    name: gnupg2
    state: present
  when: ansible_os_family == 'Debian'

- name: Download GitLab repository installation script using curl
  ansible.builtin.command: >
    curl -sS https://packages.gitlab.com/install/repositories/gitlab/gitlab-ce/script.deb.sh -o /tmp/gitlab_install_repository.sh
  when: not gitlab_file.stat.exists

- name: Install GitLab repository.
  command: bash /tmp/gitlab_install_repository.sh
  register: output
  when: not gitlab_file.stat.exists

- name: Define the Gitlab package name.
  set_fact:
    gitlab_package_name: "{{ gitlab_edition }}{{ gitlab_package_version_separator }}{{ gitlab_version }}"
  when: gitlab_version | default(false)

- name: Install GitLab
  package:
    name: "{{ gitlab_package_name | default(gitlab_edition) }}"
    state: present
  async: 300
  poll: 5
  when: not gitlab_file.stat.exists

# Start and configure GitLab. Sometimes the first run fails, but after that,
# restarts fix problems, so ignore failures on this run.
- name: Reconfigure GitLab (first run).
  command: >
    gitlab-ctl reconfigure
    creates=/var/opt/gitlab/bootstrapped
  failed_when: false

- name: Create GitLab SSL configuration folder.
  file:
    path: /etc/gitlab/ssl
    state: directory
    owner: root
    group: root
    mode: 0700
  when: gitlab_create_self_signed_cert

- name: Create self-signed certificate.
  command: >
    openssl req -new -nodes -x509 -subj "{{ gitlab_self_signed_cert_subj }}"
    -days 3650 -keyout {{ gitlab_ssl_certificate_key }} -out {{ gitlab_ssl_certificate }} -extensions v3_ca
    creates={{ gitlab_ssl_certificate }}
  when: gitlab_create_self_signed_cert

- name: Copy GitLab configuration file.
  template:
    src: "{{ gitlab_config_template }}"
    dest: /etc/gitlab/gitlab.rb
    owner: root
    group: root
    mode: 0600
  notify: restart gitlab
