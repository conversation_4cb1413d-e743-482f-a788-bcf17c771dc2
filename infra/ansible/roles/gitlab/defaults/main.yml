---
# General config.
service_name: "gitlab"
gitlab_domain: "{{ 'dev.' if deploy_type == 'dev' else '' }}{{ service_name }}.{{ fqdn_suffix }}"
gitlab_external_url: "http://{{ gitlab_domain }}/"
gitlab_git_data_dir: "/var/opt/gitlab/git-data"
gitlab_edition: "gitlab-ce"
gitlab_version: ""
gitlab_backup_path: "/var/opt/gitlab/backups"
gitlab_config_template: "gitlab.rb.j2"

# SSL Configuration.
gitlab_redirect_http_to_https: false
gitlab_ssl_certificate: "/etc/gitlab/ssl/{{ gitlab_domain }}.crt"
gitlab_ssl_certificate_key: "/etc/gitlab/ssl/{{ gitlab_domain }}.key"

# SSL Self-signed Certificate Configuration.
gitlab_create_self_signed_cert: false
gitlab_self_signed_cert_subj: "/C=US/ST=Missouri/L=Saint Louis/O=IT/CN={{ gitlab_domain }}"

# LDAP Configuration.
gitlab_ldap_enabled: false
gitlab_ldap_host: "example.com"
gitlab_ldap_port: "389"
gitlab_ldap_uid: "sAMAccountName"
gitlab_ldap_method: "plain"
gitlab_ldap_bind_dn: "CN=Username,CN=Users,DC=example,DC=com"
gitlab_ldap_password: "password"
gitlab_ldap_base: "DC=example,DC=com"

# SMTP Configuration
gitlab_smtp_enable: true
gitlab_smtp_address: "smtp.exmail.qq.com"
gitlab_smtp_port: 465
gitlab_smtp_user_name: "<EMAIL>"
gitlab_smtp_password: ""
gitlab_smtp_domain: "sdj"
gitlab_smtp_authentication: "login"
gitlab_smtp_enable_starttls_auto: true
gitlab_smtp_tls: true
gitlab_smtp_openssl_verify_mode: "none"
gitlab_smtp_ca_path: "/etc/ssl/certs"
gitlab_smtp_ca_file: "/etc/ssl/certs/ca-certificates.crt"

# 2-way SSL Client Authentication support.
gitlab_nginx_ssl_verify_client: ""
gitlab_nginx_ssl_client_certificate: ""

# Probably best to leave this as the default, unless doing testing.
gitlab_restart_handler_failed_when: "gitlab_restart.rc != 0"

# Dependencies.
gitlab_dependencies:
  - openssh-server
  - postfix
  - curl
  - openssl
  - tzdata

# Optional settings.
gitlab_time_zone: "UTC"
gitlab_backup_keep_time: "604800"
gitlab_download_validate_certs: true
gitlab_default_theme: "2"

# Email configuration.
gitlab_email_enabled: true
gitlab_email_from: "infra-bot@sdj"
gitlab_email_display_name: "SDJ Gitlab{{ ' DEV' if deploy_type == 'dev' else '' }}"
gitlab_email_reply_to: "infra-bot@sdj"

# Registry configuration.
gitlab_registry_enable: false
gitlab_registry_external_url: "https://gitlab.example.com:4567"
gitlab_registry_nginx_ssl_certificate: "/etc/gitlab/ssl/gitlab.crt"
gitlab_registry_nginx_ssl_certificate_key: "/etc/gitlab/ssl/gitlab.key"

# LetsEncrypt configuration.
gitlab_letsencrypt_enable: false
gitlab_letsencrypt_contact_emails: ["<EMAIL>"]
gitlab_letsencrypt_auto_renew_hour: 1
gitlab_letsencrypt_auto_renew_minute: 30
gitlab_letsencrypt_auto_renew_day_of_month: "*/7"
gitlab_letsencrypt_auto_renew: true
