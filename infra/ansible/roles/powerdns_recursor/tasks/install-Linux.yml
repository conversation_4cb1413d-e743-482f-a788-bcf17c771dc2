---

- block:

  - name: Prefix the version of the PowerDNS Recursor package with the correct separator on RedHat
    set_fact:
      _pdns_rec_package_version: "-{{ pdns_rec_package_version }}"
    when: ansible_os_family == 'RedHat'

  - name: Prefix the version of the PowerDNS Recursor package with the correct separator on Debian
    set_fact:
      _pdns_rec_package_version: "={{ pdns_rec_package_version }}"
    when: ansible_os_family == 'Debian'

  when: "pdns_rec_package_version | length > 0"

- name: Install the PowerDNS Recursor
  package:
    name: "{{ pdns_rec_package_name }}{{ _pdns_rec_package_version | default('') }}"
    state: present


- name: Install PowerDNS Recursor debug symbols
  package:
    name: "{{ pdns_rec_debug_symbols_package_name }}{{ _pdns_rec_package_version | default('') }}"
    state: present
  when: pdns_rec_install_debug_symbols_package

- block:
    - name: Obtain pdns version string
      shell: |
        {{ pdns_rec_bin_name }} --version 2>&1 | awk '/PowerDNS Recursor / {print $6}'
      register: _pdns_rec_ver_output
      changed_when: false

    - name: Set pdns version
      set_fact:
        _pdns_rec_version: "{{ _pdns_rec_ver_output['stdout'] | regex_replace('-[.\\d\\w]+$', '') }}"
      when: not ansible_check_mode
