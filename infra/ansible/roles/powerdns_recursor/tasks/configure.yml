---

- block:

  - name: Ensure the PowerDNS Recursor drop-in unit overrides directory exists (systemd)
    file:
      name: "/etc/systemd/system/{{ pdns_rec_service_name }}.service.d"
      state: directory
      owner: root
      group: root

  - name: Override the PowerDNS Recursor unit (systemd)
    template:
      src: "override-service.systemd.conf.j2"
      dest: "/etc/systemd/system/{{ pdns_rec_service_name }}.service.d/override.conf"
      owner: root
      group: root
    when: pdns_rec_service_overrides | length > 0
    register: _pdns_recursor_override_unit

  - name: Reload systemd
    command: systemctl daemon-reload
    when: not pdns_rec_disable_handlers
      and _pdns_recursor_override_unit.changed

  when: ansible_service_mgr == "systemd"

- name: Ensure that the PowerDNS Recursor configuration directory exists
  file:
    name: "{{ pdns_rec_config_dir }}"
    state: directory
    owner: "{{ pdns_rec_file_owner }}"
    group: "{{ pdns_rec_file_group }}"
    mode: 0750

- name: Generate the PowerDNS Recursor configuration
  template:
    src: recursor.conf.j2
    dest: "{{ pdns_rec_config_dir }}/{{ pdns_rec_config_file }}"
    owner: "{{ pdns_rec_file_owner }}"
    group: "{{ pdns_rec_file_group }}"
    mode: 0640
  register: _pdns_recursor_configuration

- name: Ensure that the PowerDNS Recursor 'include-dir' directory exists
  file:
    name: "{{ pdns_rec_config['include-dir'] }}"
    state: directory
    owner: "{{ pdns_rec_file_owner }}"
    group: "{{ pdns_rec_file_group }}"
    mode: 0750
  when: "pdns_rec_config['include-dir'] is defined"

- name: Generate the PowerDNS Recursor Lua config-file
  copy:
    dest: "{{ pdns_rec_config_lua }}"
    content: "{{ pdns_rec_config_lua_file_content }}"
    owner: "{{ pdns_rec_file_owner }}"
    group: "{{ pdns_rec_file_group }}"
    mode: 0640
  register: _pdns_recursor_lua_file_configuraton
  when: "pdns_rec_config_lua_file_content | length > 0"

- name: Generate PowerDNS Recursor Lua dns-script
  copy:
    dest: "{{ pdns_rec_config_dns_script }}"
    content: "{{ pdns_rec_config_dns_script_file_content }}"
    owner: "{{ pdns_rec_file_owner }}"
    group: "{{ pdns_rec_file_group }}"
    mode: 0640
  register: _pdns_recursor_dns_script_configuration
  when: "pdns_rec_config_dns_script_file_content | length > 0"

- name: Restart PowerDNS Recursor
  service:
    name: "{{ pdns_rec_service_name }}"
    state: restarted
    sleep: 1
