---

- name: Include OS-specific variables
  include_vars: "{{ item }}"
  with_first_found:
    - "{{ ansible_os_family }}.yml"
    - Other.yml
  tags:
    - always

- include: "repo-{{ ansible_os_family }}.yml"
  when: "pdns_rec_install_repo | length > 0"
  tags:
    - install
    - repository

- include: "install-{{ansible_system}}.yml"
  tags:
    - install

- include: configure.yml
  tags:
    - config

- name: Set the status of the PowerDNS Recursor service
  service:
    name: "{{ pdns_rec_service_name }}"
    state: "{{ pdns_rec_service_state }}"
    enabled: "{{ pdns_rec_service_enabled }}"
  tags:
    - service
