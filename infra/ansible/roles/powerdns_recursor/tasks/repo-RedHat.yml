---

- block:

  - name: Install epel-release on CentOS
    package:
      name: epel-release
      state: present
    when: ansible_distribution in [ 'CentOS' ]

  - name: Install epel-release on RHEL
    package:
      name: "https://dl.fedoraproject.org/pub/epel/epel-release-latest-{{ ansible_distribution_major_version }}.noarch.rpm"
      state: present
    when: ansible_distribution in [ 'RedHat' ]

  # FIXME: this only works for 8
  - name: Install epel-release on OracleLinux
    package:
      name: oracle-epel-release-el8
      state: present
    when: ansible_distribution in [ 'OracleLinux' ]

  when: pdns_rec_install_epel

- name: Install yum-plugin-priorities
  package:
    name: yum-plugin-priorities
    state: present
  when: ansible_distribution in [ 'CentOS' ]
    and ansible_pkg_mgr in [ 'yum' ]

- name: Add the PowerDNS Recursor YUM Repository
  yum_repository:
    name: "{{ pdns_rec_install_repo['name'] }}"
    file: "{{ pdns_rec_install_repo['name'] }}"
    description: PowerDNS Recursor
    baseurl: "{{ pdns_rec_install_repo['yum_repo_baseurl'] }}"
    gpgkey: "{{ pdns_rec_install_repo['gpg_key'] }}"
    gpgcheck: yes
    priority: "90"
    state: present

- name: Add the PowerDNS Recursor debug symbols YUM Repository
  yum_repository:
    name: "{{ pdns_rec_install_repo['name'] }}-debuginfo"
    file: "{{ pdns_rec_install_repo['name'] }}"
    description: PowerDNS Recursor - debug symbols
    baseurl: "{{ pdns_rec_install_repo['yum_debug_symbols_repo_baseurl'] }}"
    gpgkey: "{{ pdns_rec_install_repo['gpg_key'] }}"
    gpgcheck: yes
    priority: "90"
    state: present
  when: pdns_rec_install_debug_symbols_package
