---

default_pdns_rec_package_name: "pdns-recursor"

pdns_rec_powerdns_repo_master:
  apt_repo_origin: "repo.powerdns.com"
  apt_repo: "deb [arch=amd64] https://repo.powerdns.com/{{ ansible_distribution | lower }} {{ ansible_distribution_release | lower }}-rec-master main"
  gpg_key: "https://repo.powerdns.com/CBC8B383-pub.asc"
  gpg_key_id: "D47975F8DAE32700A563E64FFF389421CBC8B383"
  yum_repo_baseurl: "https://repo.powerdns.com/centos/$basearch/$releasever/rec-master"
  yum_debug_symbols_repo_baseurl: "https://repo.powerdns.com/centos/$basearch/$releasever/rec-master/debug"
  name: "powerdns-rec-master"

pdns_rec_powerdns_repo_43:
  apt_repo_origin: "repo.powerdns.com"
  apt_repo: "deb [arch=amd64] https://repo.powerdns.com/{{ ansible_distribution | lower }} {{ ansible_distribution_release | lower }}-rec-43 main"
  gpg_key: "https://repo.powerdns.com/FD380FBB-pub.asc"
  gpg_key_id: "9FAAA5577E8FCF62093D036C1B0C6205FD380FBB"
  yum_repo_baseurl: "https://repo.powerdns.com/centos/$basearch/$releasever/rec-43"
  yum_debug_symbols_repo_baseurl: "https://repo.powerdns.com/centos/$basearch/$releasever/rec-43/debug"
  name: "powerdns-rec-43"

pdns_rec_powerdns_repo_44:
  apt_repo_origin: "repo.powerdns.com"
  apt_repo: "deb [arch=amd64] https://repo.powerdns.com/{{ ansible_distribution | lower }} {{ ansible_distribution_release | lower }}-rec-44 main"
  gpg_key: "https://repo.powerdns.com/FD380FBB-pub.asc"
  gpg_key_id: "9FAAA5577E8FCF62093D036C1B0C6205FD380FBB"
  yum_repo_baseurl: "https://repo.powerdns.com/centos/$basearch/$releasever/rec-44"
  yum_debug_symbols_repo_baseurl: "https://repo.powerdns.com/centos/$basearch/$releasever/rec-44/debug"
  name: "powerdns-rec-44"

pdns_rec_powerdns_repo_45:
  apt_repo_origin: "repo.powerdns.com"
  apt_repo: "deb [arch=amd64] https://repo.powerdns.com/{{ ansible_distribution | lower }} {{ ansible_distribution_release | lower }}-rec-45 main"
  gpg_key: "https://repo.powerdns.com/FD380FBB-pub.asc"
  gpg_key_id: "9FAAA5577E8FCF62093D036C1B0C6205FD380FBB"
  yum_repo_baseurl: "https://repo.powerdns.com/centos/$basearch/$releasever/rec-45"
  yum_debug_symbols_repo_baseurl: "https://repo.powerdns.com/centos/$basearch/$releasever/rec-45/debug"
  name: "powerdns-rec-45"



default_pdns_rec_service_overrides: >-
  {{  { 'User'  : pdns_rec_user
      , 'Group' : pdns_rec_group
      } 
  }}

# Default value intended to be used during check mode, when proper version
# detection does not run.
_pdns_rec_version: "{{ pdns_rec_package_version if pdns_rec_package_version != '' else 0 }}"
