config-dir={{ pdns_rec_config_dir }}
setuid={{ pdns_rec_user }}
setgid={{ pdns_rec_group }}

{% for config_item, value in pdns_rec_config.items() | sort() %}
{% if config_item not in ["config-dir", "setuid", "setgid"] %}
{% if config_item == 'threads' %}
{{ config_item }}={{ value | string }}
{% elif value is sameas True %}
{{ config_item }}=yes
{% elif value is sameas False %}
{{ config_item }}=no
{% elif value is string %}
{{ config_item }}={{ value | string }}
{% elif value is sequence %}
{{ config_item }}={{ value | join(',') }}
{% else %}
{{ config_item }}={{ value | string }}
{% endif %}
{% endif %}
{% endfor %}

{% if pdns_rec_config_lua_file_content != "" %}
lua-config-file={{ pdns_rec_config_lua }}
{% endif %}

{% if pdns_rec_config_dns_script_file_content != "" %}
lua-dns-script={{ pdns_rec_config_dns_script }}
{% endif %}
