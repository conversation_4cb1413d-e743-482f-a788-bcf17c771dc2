---

galaxy_info:
  author: PowerDNS Engineering Team
  description: Install and configure the PowerDNS Recursor
  company: PowerDNS.COM BV
  license: MIT
  min_ansible_version: 2.7
  platforms:
    - name: EL
      versions:
        - 7
        - 8
    - name: Debian
      versions:
        - stretch
        - buster
    - name: Ubuntu
      versions:
        - bionic
        - focal
    - name: FreeBSD
      versions:
        - 11
        - 12
  galaxy_tags:
    - system
    - dns
    - pdns
    - powerdns
    - recursor
