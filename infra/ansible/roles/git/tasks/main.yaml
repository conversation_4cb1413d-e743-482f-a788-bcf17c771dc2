- name: Copy {{ gitlab_repo }} deploy key
  copy:
    src: "{{ role_path }}/files/{{ gitlab_repo }}_id_rsa"
    dest: "{{ workspace }}/{{ gitlab_repo }}_id_rsa"
    mode: 0700

- name: Config git repo safe.directory
  shell:
    cmd: |
      git config --global --add safe.directory "*"

- name: <PERSON>lone {{ gitlab_repo }}
  ignore_errors: true
  git:
    repo: *******************:developers/{{ gitlab_repo }}.git
    dest: "{{ workspace }}/{{ gitlab_repo }}"
    clone: yes
    update: yes
    key_file: "{{ workspace }}/{{ gitlab_repo }}_id_rsa"
    version: "{{ repo_branch if repo_branch is defined else omit }}"

- name: Change the owner of the cloned repository to the connected user
  ansible.builtin.file:
    path: "{{ workspace }}/{{ gitlab_repo }}"
    state: directory
    owner: "{{ ansible_ssh_user }}"
