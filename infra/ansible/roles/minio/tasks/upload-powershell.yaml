---
- name: Ensure the required NuGet package provider version is installed
  win_shell: Find-PackageProvider -Name Nuget -ForceBootstrap -IncludeDependencies -Force

- name: install powershell modules
  win_psmodule:
    name: "{{ item }}"
    skip_publisher_check: yes
    state: present
  with_items:
    - AWSPowerShell

- name: Upload file to S3
  win_shell: |
    Set-AWSCredential -AccessKey '{{ m_access_key }}' -SecretKey '{{ m_secret_key }}'
    Write-S3Object -BucketName '{{ m_bucket }}' -File '{{ m_source }}' -Key '{{ m_object }}' -EndpointUrl '{{ m_host }}'
