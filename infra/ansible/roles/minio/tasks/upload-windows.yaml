---
- name: Fetch file from Windows host to Ansible controller
  fetch:
    src: "{{ m_source }}"
    dest: "/tmp/"
    flat: yes

- name: Upload to minio
  delegate_to: localhost
  aws_s3:
    s3_url: "{{ m_host }}"
    bucket: "{{ m_bucket }}"
    object: "{{ m_object }}"
    src: "/tmp/{{ m_object }}"
    mode: put
    aws_access_key: "{{ m_access_key }}"
    aws_secret_key: "{{ m_secret_key }}"
    encrypt: false
