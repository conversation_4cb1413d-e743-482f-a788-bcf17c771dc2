---

galaxy_info:
  namespace: powerdns
  role_name: pdns
  author: PowerDNS Engineering Team
  description: Install and configure the PowerDNS Authoritative DNS Server
  company: PowerDNS.COM BV
  license: MIT
  min_ansible_version: 2.9
  platforms:
    - name: EL
      versions:
        - 7
        - 8
    - name: Debian
      versions:
        - stretch
        - buster
    - name: Ubuntu
      versions:
        - bionic
        - focal
    - name: ArchLinux
  galaxy_tags:
    - system
    - dns
    - pdns
    - powerdns
    - auth
