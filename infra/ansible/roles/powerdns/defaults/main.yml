---

# By default, no PowerDNS Authoritative Server repository will be configured by the role
pdns_install_repo: ""

# To install tje PowerDNS Authoritative Server from the 'master' official repository
# use the following playbook snippet
# - hosts: all
#   roles:
#    - { role: PowerDNS.pdns,
#        pdns_install_repo: "{{ pdns_auth_powerdns_repo_master }}"
#
# To install the PowerDNS Authoritative Server from the '4.1.x' official repository
# use the following playbook snippet
# - hosts: all
#   roles:
#    - { role: PowerDNS.pdns,
#        pdns_install_repo: "{{ pdns_auth_powerdns_repo_41 }}"

# To install the PowerDNS Authoritative pdns_backendsServer from the '4.2.x' official repository
# use the following playbook snippet
# - hosts: all
#   roles:
#    - { role: PowerDNS.pdns,
#        pdns_install_repo: "{{ pdns_auth_powerdns_repo_42 }}"
#
# To make this role configure a custom repository and install the
# PowerDNS Authoritative Server from it override the `pdns_install_repo` variable
# as follows
# - hosts: all
#   vars:
#     pdns_install_repo:
#       apt_repo_origin: "example.com"  # Pin the PowerDNS packages to the provided repository origin
#       apt_repo: "deb http://example.com/{{ ansible_distribution | lower }} {{ ansible_distribution_release | lower }}/pdns main"
#       gpg_key: "http://example.com/MYREPOGPGPUBKEY.asc" # repository public GPG key
#       gpg_key_id: "MYREPOGPGPUBKEYID" # to avoid to reimport the key each time the role is executed
#       yum_repo_baseurl: "http://example.com/centos/$basearch/$releasever/pdns"
#       name: "powerdns"       # the name of the repository
#   roles:
#    - { role: PowerDNS.pdns }

# Install the EPEL repository.
# EPEL is needed to satisfy some PowerDNS Authoritative Server dependencies like protobuf
pdns_install_epel: True

# The name of the PowerDNS Authoritative Server package
pdns_package_name: "{{ default_pdns_package_name }}"

# Install a specific version of the PowerDNS Authoritative Server package
# NB: The usage of this variable makes only sense on RedHat-like systems,
#     where each YUM repository can contains multiple versions of the same package.
pdns_package_version: ""

# Install the PowerDNS Authoritative Server debug symbols package
pdns_install_debug_symbols_package: False

# The name of the PowerDNS Authoritative Server debug symbols package
pdns_debug_symbols_package_name: "{{ default_pdns_debug_symbols_package_name }}"

# The user and group the PowerDNS Authoritative Server process will run as.
# NOTE: at the moment, we don't create a user as we assume the package creates
# a "pdns" user and group. If you change these variables, make sure to create
# the user and groups before applying this role
pdns_user: "root"
pdns_group: "pdns"
pdns_file_owner: "root"
pdns_file_group: "{{ pdns_group }}"

# Name of the PowerDNS Authoritative Server Service
pdns_service_name: "pdns"

# State of the PowerDNS Authoritative Server service
pdns_service_state: "started"
pdns_service_enabled: "yes"

# When True, disable the automated restart of the PowerDNS service
pdns_disable_handlers: False

# PowerDNS Authoritative Server configuration file and directory
pdns_config_dir: "{{ default_pdns_config_dir }}"
pdns_config_file: "pdns.conf"
pdns_bind_config_path: "/etc/named"
pdns_bind_config_file: "named.conf"

# Ddict containing all configuration options, except for backend
# configuration and the "config-dir", "setuid" and "setgid" directives.
pdns_config: {}
# pdns_config:
#  master: yes
#  slave: no
#  local-address: '**********'
#  local-ipv6: '2001:DB8:1::53'
#  local-port: '5300'

# Dict with overrides for the service (systemd only)
pdns_service_overrides: "{{ default_pdns_service_overrides }}"
# pdns_service_overrides:
#   LimitNOFILE: 10000

# Dictionary of packages that should be installed to enable the backends.
# backendname: packagename
pdns_backends_packages: "{{ default_pdns_backends_packages }}"

# A dict with all the backends you'd like to configure.
# This default starts just the bind-backend with an empty config file
pdns_backends:
  bind:
    config: '{{ pdns_bind_config_path}}/{{ pdns_bind_config_file }}'
# pdns_backends:
#   'gmysql:one':
#     'user': root
#     'host': 127.0.0.1
#     'password': root
#     'dbname': pdns
#   'gmysql:two':
#     'user': pdns_user
#     'host': **********
#     'port': 3307
#     'password': my_password
#     'dbname': dns
#   'bind':
#     'config': '/etc/named/named.conf'
#     'hybrid':  yes
#     'dnssec-db': '{{ pdns_config_dir }}/dnssec.db'

# Administrative credentials to create the PowerDNS Authoritative Server MySQL backend database and user.
pdns_mysql_databases_credentials: {}
# pdns_mysql_databases_credentials:
#   'gmysql:one':
#     'priv_user': root
#     'priv_password': my_first_password
#     'priv_host':
#       - "localhost"
#       - "%"
#   'gmysql:two':
#     'priv_user': someprivuser
#     'priv_password': my_second_password
#     'priv_host':
#       - "localhost"

# This will create the PowerDNS Authoritative Server backend SQLite database
# in the given locations.
# NOTE: Requries the SQLite CLI tools to be available in the machine and the gsqlite3
# backend to be installed on the machine.
pdns_sqlite_databases_locations: []

# This will create the PowerDNS Authoritative Server backend LMDB database
# in the given locations.
# NOTE: Requries lmdb backend to be installed on the machine.
pdns_lmdb_databases_locations: []

# Override the schema used to initialize the MySQL database
# By default, this role tries to detect the correct file
pdns_mysql_schema_file: ""

# Override the schema used to initialize the SQLite database
# By default, this role tries to detect the correct file
pdns_sqlite_schema_file: ""
