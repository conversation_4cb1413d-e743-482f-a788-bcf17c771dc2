---

# The name of the PowerDNS Authoritative Server package
default_pdns_package_name: "pdns-server"

# The name of the PowerDNS Authoritative Server debug package
default_pdns_debug_symbols_package_name: "pdns-server-dbg"

# Packages needed to install MySQL
pdns_mysql_packages:
  - default-mysql-client
  - python-mysqldb
  - python3-mysqldb

# List of PowerDNS Authoritative Server Backends packages on Debian
default_pdns_backends_packages:
  geo: pdns-backend-geo
  geoip: pdns-backend-geoip
  gmysql: pdns-backend-mysql
  gpgsql: pdns-backend-pgsql
  gsqlite3: pdns-backend-sqlite3
  ldap: pdns-backend-ldap
  lmdb: pdns-backend-lmdb
  lua: pdns-backend-lua
  mydns: pdns-backend-mydns
  pipe: pdns-backend-pipe
  remote: pdns-backend-remote
  tinydns: pdns-backend-tinydns

# The directory where the PowerDNS Authoritative Server configuration is located
default_pdns_config_dir: "/etc/powerdns"
