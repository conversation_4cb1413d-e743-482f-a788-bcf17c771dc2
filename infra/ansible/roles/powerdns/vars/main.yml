---

pdns_auth_powerdns_repo_master:
  apt_repo_origin: "repo.powerdns.com"
  apt_repo: "deb [arch=amd64] http://repo.powerdns.com/{{ ansible_distribution | lower }} {{ ansible_distribution_release | lower }}-auth-master main"
  gpg_key: "http://repo.powerdns.com/CBC8B383-pub.asc"
  gpg_key_id: "D47975F8DAE32700A563E64FFF389421CBC8B383"
  yum_repo_baseurl: "http://repo.powerdns.com/centos/$basearch/$releasever/auth-master"
  yum_debug_symbols_repo_baseurl: "http://repo.powerdns.com/centos/$basearch/$releasever/auth-master/debug"
  name: "powerdns-auth-master"

pdns_auth_powerdns_repo_43:
  apt_repo_origin: "repo.powerdns.com"
  apt_repo: "deb [arch=amd64] http://repo.powerdns.com/{{ ansible_distribution | lower }} {{ ansible_distribution_release | lower }}-auth-43 main"
  gpg_key: "http://repo.powerdns.com/FD380FBB-pub.asc"
  gpg_key_id: "9FAAA5577E8FCF62093D036C1B0C6205FD380FBB"
  yum_repo_baseurl: "http://repo.powerdns.com/centos/$basearch/$releasever/auth-43"
  yum_debug_symbols_repo_baseurl: "http://repo.powerdns.com/centos/$basearch/$releasever/auth-43/debug"
  name: "powerdns-auth-43"

pdns_auth_powerdns_repo_44:
  apt_repo_origin: "repo.powerdns.com"
  apt_repo: "deb [arch=amd64] http://repo.powerdns.com/{{ ansible_distribution | lower }} {{ ansible_distribution_release | lower }}-auth-44 main"
  gpg_key: "http://repo.powerdns.com/FD380FBB-pub.asc"
  gpg_key_id: "9FAAA5577E8FCF62093D036C1B0C6205FD380FBB"
  yum_repo_baseurl: "http://repo.powerdns.com/centos/$basearch/$releasever/auth-44"
  yum_debug_symbols_repo_baseurl: "http://repo.powerdns.com/centos/$basearch/$releasever/auth-44/debug"
  name: "powerdns-auth-44"

pdns_auth_powerdns_repo_45:
  apt_repo_origin: "repo.powerdns.com"
  apt_repo: "deb [arch=amd64] http://repo.powerdns.com/{{ ansible_distribution | lower }} {{ ansible_distribution_release | lower }}-auth-45 main"
  gpg_key: "http://repo.powerdns.com/FD380FBB-pub.asc"
  gpg_key_id: "9FAAA5577E8FCF62093D036C1B0C6205FD380FBB"
  yum_repo_baseurl: "http://repo.powerdns.com/centos/$basearch/$releasever/auth-45"
  yum_debug_symbols_repo_baseurl: "http://repo.powerdns.com/centos/$basearch/$releasever/auth-45/debug"
  name: "powerdns-auth-45"
pdns_auth_powerdns_repo_46:
  apt_repo_origin: "repo.powerdns.com"
  apt_repo: "deb [arch=amd64] http://repo.powerdns.com/{{ ansible_distribution | lower }} {{ ansible_distribution_release | lower }}-auth-46 main"
  gpg_key: "http://repo.powerdns.com/FD380FBB-pub.asc"
  gpg_key_id: "9FAAA5577E8FCF62093D036C1B0C6205FD380FBB"
  yum_repo_baseurl: "http://repo.powerdns.com/centos/$basearch/$releasever/auth-46"
  yum_debug_symbols_repo_baseurl: "http://repo.powerdns.com/centos/$basearch/$releasever/auth-46/debug"
  name: "powerdns-auth-46"

default_pdns_service_overrides: >-
  {{  { 'User'  : pdns_user
      , 'Group' : pdns_group
      }
  }}
