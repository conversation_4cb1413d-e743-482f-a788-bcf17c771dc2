config-dir={{ pdns_config_dir }}
setuid={{ pdns_user }}
setgid={{ pdns_group }}
{% for config_item, value in pdns_config.items() | sort() %}
{% if config_item not in ["config-dir", "launch", "setuid", "setgid"] %}
{% if value is sameas True %}
{{ config_item }}=yes
{% elif value is sameas False %}
{{ config_item }}=no
{% elif value == None %}
{{ config_item }}=
{% elif value is string %}
{{ config_item }}={{ value | string }}
{% elif value is sequence %}
{{ config_item }}={{ value | join(',') }}
{% else %}
{{ config_item }}={{ value | string }}
{% endif %}
{% endif %}
{% endfor %}

launch=

{% for backend in pdns_backends | sort() -%}
launch+={{ backend }}
{% set backend_string = backend | replace(':', '-') %}
{% for backend_item, value in pdns_backends[backend].items() | sort() -%}
{% if value is sameas True %}
{{ backend_string }}-{{ backend_item }}=yes
{% elif value is sameas False %}
{{ backend_string }}-{{ backend_item }}=no
{% elif value == None %}
{{ backend_string }}-{{ backend_item }}=
{% else %}
{{ backend_string }}-{{ backend_item }}={{ value | string }}
{% endif %}
{% endfor %}

{% endfor -%}

