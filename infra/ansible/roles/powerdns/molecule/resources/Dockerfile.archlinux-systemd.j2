# Molecule managed

FROM {{ item.image }}

RUN if [ $(command -v pacman) ]; then pacman -Suy --noconfirm && pacman -S --noconfirm systemd && rm -rf /var/cache/pacman/pkg/*; fi
RUN if [ ! -e /sbin/init ]; then ln -s /lib/systemd/systemd /sbin/init ; fi

ENV container docker

# Don't start the optional systemd services. 
RUN find /etc/systemd/system \
         /lib/systemd/system \
         -path '*.wants/*' \
         -not -name '*journald*' \
         -not -name '*systemd-tmpfiles*' \
         -not -name '*systemd-user-sessions*' \
         -exec rm \{} \;

RUN systemctl set-default multi-user.target

VOLUME [ "/sys/fs/cgroup" ]

CMD ["/sbin/init"]

RUN if [ $(command -v pacman) ]; then pacman -Suy && pacman -S --noconfirm python sudo bash net-tools ca-certificates awk inetutils grep && rm -rf /var/cache/pacman/pkg/*; fi
# This ensures docs are installed, which contains the schema files :)
RUN sed  -i '/usr\/share\/doc/d' /etc/pacman.conf
