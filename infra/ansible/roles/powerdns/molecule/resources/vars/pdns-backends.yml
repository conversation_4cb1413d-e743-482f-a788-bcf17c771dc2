---

##
# PowerDNS Backends
##

pdns_backends:
  gsqlite3:
    database: /var/lib/powerdns/pdns.db
    dnssec: yes
  gmysql:
    host: "mysql"                                          # This is relying on <PERSON><PERSON>'s service discovery
    dbname: "{{ ansible_hostname | replace('.', '_') }}"   # Each Platform will have its MySQL DB
    user: pdns
    password: pdns

pdns_sqlite_databases_locations:
  - '/var/lib/powerdns/pdns.db'

pdns_mysql_databases_credentials:
  gmysql:
    priv_user: root
    priv_password: "{{ ansible_env.MYSQL_ENV_MYSQL_ROOT_PASSWORD }}"  # The MySQL root password
    priv_host:                                                        # is injected by <PERSON><PERSON> into the env
      - '%'
      - 'localhost'
