---

scenario:
  name: pdns-44

driver:
  name: docker

dependency:
  name: galaxy

platforms:
  - name: centos-7
    groups: ["pdns"]
    image: centos:7
    dockerfile_tpl: centos-systemd

  - name: oraclelinux-8
    groups: ["pdns"]
    image: oraclelinux:8
    dockerfile_tpl: centos-systemd

  - name: ubuntu-1804
    groups: ["pdns"]
    image: ubuntu:18.04
    dockerfile_tpl: debian-systemd

  - name: ubuntu-2004
    groups: ["pdns"]
    image: ubuntu:20.04
    tmpfs:
      - /run
      - /tmp
    dockerfile_tpl: debian-systemd

  - name: debian-9
    groups: ["pdns"]
    image: debian:9
    dockerfile_tpl: debian-systemd

  - name: debian-10
    groups: ["pdns"]
    image: debian:10
    privileged: True
    volume_mounts:
      - "/sys/fs/cgroup:/sys/fs/cgroup:rw"
    tmpfs:
      - /run
      - /run/lock
      - /tmp
    dockerfile_tpl: debian-systemd
    environment: { container: docker }

  # In order to run the tests we need
  # a MySQL container to be up & running
  - name: mysql
    image: mysql:5.7
    env:
      MYSQL_ROOT_PASSWORD: pdns
    # Declaring the container as service,
    # will link it to the others Platforms containers
    # on creation.
    is_service: yes

provisioner:
  name: ansible
  options:
    diff: True
    v: True
  config_options:
    defaults:
      gathering: smart
      fact_caching: jsonfile
      fact_caching_connection: .ansible_cache
      fact_caching_timeout: 7200
    ssh_connection:
      pipelining: true
  inventory:
    links:
      host_vars: ../resources/host_vars/
  playbooks:
    create: ../resources/create.yml
    destroy: ../resources/destroy.yml
    prepare: ../resources/prepare.yml
    # "systemctl used in place of systemd module" "ANSIBLE0006"
    # "Tasks that run when changed should likely be handlers" "ANSIBLE0016"
    # "Shells that use pipes should set the pipefail option" "306"
  lint: ansible-lint -x ANSIBLE0006 ANSIBLE0016 306

lint: yamllint defaults tasks meta vars

verifier:
  name: testinfra
  options:
    hosts: "pdns"
    vvv: True
  directory: ../resources/tests/all
  additional_files_or_dirs:
    # path relative to 'directory'
    - ../repo-44/
    - ../backend-sqlite/
    - ../backend-mysql/
    - ../systemd-override/
  lint: flake8
