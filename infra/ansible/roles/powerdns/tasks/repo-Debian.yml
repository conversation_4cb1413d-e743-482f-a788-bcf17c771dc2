---

- name: Install gnupg
  package:
    name: gnupg
    state: present

- name: Import the PowerDNS APT Repository key from URL
  apt_key:
    url: "{{ pdns_install_repo['gpg_key'] }}"
    id: "{{ pdns_install_repo['gpg_key_id'] | default('') }}"
    state: present
  when: pdns_install_repo['gpg_key'] is regex("^[a-z]{3,}://")
  register: _pdns_apt_key

- name: Import the PowerDNS APT Repository key from File
  apt_key:
    data: "{{ lookup('file',  pdns_install_repo['gpg_key']) }}"
    id: "{{ pdns_install_repo['gpg_key_id'] | default('') }}"
    state: present
  when: not pdns_install_repo['gpg_key'] is regex("^[a-z]{3,}://")
  register: _pdns_apt_key

- name: Add the PowerDNS APT Repository
  apt_repository:
    filename: "{{ pdns_install_repo['name'] }}"
    repo: "{{ pdns_install_repo['apt_repo'] }}"
    state: present
  register: _pdns_apt_repo

- name: Update the APT cache
  apt:
    update_cache: yes
  when: "_pdns_apt_key.changed or _pdns_apt_repo.changed"

- name: Pin the PowerDNS APT Repository
  template:
    src: pdns.pin.j2
    dest: /etc/apt/preferences.d/pdns
    owner: root
    group: root
    mode: 0644
