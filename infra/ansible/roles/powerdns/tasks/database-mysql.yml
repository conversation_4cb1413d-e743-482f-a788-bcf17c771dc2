---

- name: Install the MySQL dependencies
  package:
    name: "{{ pdns_mysql_packages }}"
    state: present

- name: Create the PowerDNS MySQL databases
  mysql_db:
    login_user: "{{ item['value']['priv_user'] }}"
    login_password: "{{ item['value']['priv_password'] }}"
    login_host: "{{ item['value']['host'] }}"
    login_port: "{{ item['value']['port'] | default('3306') }}"
    name: "{{ item['value']['dbname'] }}"
    state: present
  when: "item.key.split(':')[0] == 'gmysql'"
  no_log: True
  with_dict: "{{ pdns_backends | combine(pdns_mysql_databases_credentials, recursive=True) }}"

- name: Grant PowerDNS access to the MySQL databases
  mysql_user:
    login_user: "{{ item[0]['priv_user'] }}"
    login_password: "{{ item[0]['priv_password'] }}"
    login_host: "{{ item[0]['host'] }}"
    login_port: "{{ item[0]['port'] | default('3306') }}"
    name: "{{ item[0]['user'] }}"
    password: "{{ item[0]['password'] }}"
    host: "{{ item[1] }}"
    priv: "{{ item[0]['dbname'] }}.*:ALL"
    append_privs: yes
    state: present
  with_subelements:
    - "{{ pdns_backends | combine(pdns_mysql_databases_credentials, recursive=True) }}"
    - priv_host
    - skip_missing: yes

- name: Check if the MySQL databases are empty
  command: >
    mysql --user="{{ item['value']['user'] }}" --password="{{ item['value']['password'] }}"
    --host="{{ item['value']['host'] }}" --port "{{ item['value']['port'] | default('3306') }}" --batch --skip-column-names
    --execute="SELECT COUNT(DISTINCT table_name) FROM information_schema.columns WHERE table_schema = '{{ item['value']['dbname'] }}'"
  when: item.key.split(':')[0] == 'gmysql'
  with_dict: "{{ pdns_backends }}"
  register: _pdns_check_mysql_db
  no_log: True
  changed_when: False

- name: Determine location of the SQL file
  shell:
    cmd: |
      for p in /usr/share/doc/pdns-backend-mysql-{{ _pdns_running_version }}/schema.mysql.sql /usr/share/doc/pdns-backend-mysql/schema.mysql.sql /usr/share/pdns-backend-mysql/schema/schema.mysql.sql /usr/share/dbconfig-common/data/pdns-backend-mysql/install/mysql /usr/share/doc/powerdns/schema.mysql.sql /usr/share/doc/pdns/schema.mysql.sql; do
        if [ -f $p ]; then
          echo $p
          exit 0
        fi
      done
      echo "Can't determine path to MySQL schema">&2
      exit 1
  changed_when: false
  register: pdns_mysql_schema_file_detected
  when: pdns_mysql_schema_file | length == 0

- name: Set the schema file variable
  set_fact:
    pdns_mysql_schema_file_to_use: "{% if pdns_mysql_schema_file | length == 0 %}{{ pdns_mysql_schema_file_detected.stdout }}{% else %}{{ pdns_mysql_schema_file }}{% endif %}"

- name: Import the PowerDNS MySQL schema
  mysql_db:
    login_user: "{{ item['item']['value']['user'] }}"
    login_password: "{{ item['item']['value']['password'] }}"
    login_host: "{{ item['item']['value']['host'] }}"
    login_port: "{{ item['item']['port'] | default('3306') }}"
    name: "{{ item.item['value']['dbname'] }}"
    state: import
    target: "{{ pdns_mysql_schema_file_to_use }}"
  no_log: True
  when: "item['item']['key'].split(':')[0] == 'gmysql' and item['stdout'] == '0'"
  with_items: "{{ _pdns_check_mysql_db['results'] }}"
