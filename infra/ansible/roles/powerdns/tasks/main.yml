---

- name: Include OS-specific variables
  include_vars: "{{ item }}"
  with_first_found:
    - "{{ ansible_distribution }}-{{ ansible_distribution_major_version }}.yml"
    - "{{ ansible_distribution }}.yml"
    - "{{ ansible_os_family }}-{{ ansible_distribution_major_version }}.yml"
    - "{{ ansible_os_family }}.yml"
  tags:
    - always

- include_tasks: "repo-{{ ansible_os_family }}.yml"
  when: "pdns_install_repo | length > 0"
  tags:
    - install
    - repository

- include_tasks: install.yml
  tags:
    - install

- include_tasks: inspect.yml
  tags:
    - db
    - mysql
    - sqlite
    - config

- include_tasks: database-mysql.yml
  when: "pdns_mysql_databases_credentials | length > 0"
  tags:
    - db
    - mysql

- include_tasks: database-sqlite3.yml
  when: "pdns_sqlite_databases_locations | length > 0"
  tags:
    - db
    - sqlite

- include_tasks: database-lmdb.yml
  when: "pdns_lmdb_databases_locations | length > 0"
  tags:
    - db
    - lmdb

- include_tasks: configure.yml
  tags:
    - config

- include_tasks: selinux.yml
  when: ansible_selinux is defined and ansible_selinux.status == 'enabled'
  tags:
    - selinux
    - config

- name: Start and enable the PowerDNS service
  throttle: 1
  service:
    name: "{{ pdns_service_name }}"
    state: "{{ pdns_service_state }}"
    enabled: "{{ pdns_service_enabled }}"
  tags:
    - service
