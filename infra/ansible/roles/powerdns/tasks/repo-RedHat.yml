---

- block:

  - name: Install epel-release on CentOS
    yum:
      name: epel-release
      state: present
    when: ansible_distribution in [ 'CentOS' ]

  - name: Install epel-release on RHEL
    package:
      name: "https://dl.fedoraproject.org/pub/epel/epel-release-latest-{{ ansible_distribution_major_version }}.noarch.rpm"
      state: present
    when: ansible_distribution in [ 'RedHat' ]

  # FIXME: this only works for 8
  - name: Install epel-release and hostname on OracleLinux
    package:
      name: 
      - oracle-epel-release-el8
      - hostname
      state: present
    when: ansible_distribution in [ 'OracleLinux' ]

  when: pdns_install_epel

- name: Install yum-plugin-priorities
  package:
    name: yum-plugin-priorities
    state: present
  when:
    - ansible_distribution in [ 'CentOS' ]
    - ansible_distribution_major_version | int < 8

- name: Add the PowerDNS YUM Repository
  yum_repository:
    name: "{{ pdns_install_repo['name'] }}"
    file: "{{ pdns_install_repo['name'] }}"
    description: PowerDNS Authoritative Server
    baseurl: "{{ pdns_install_repo['yum_repo_baseurl'] }}"
    gpgkey: "{{ pdns_install_repo['gpg_key'] }}"
    gpgcheck: yes
    priority: "90"
    state: present

- name: Add the PowerDNS debug symbols YUM Repository
  yum_repository:
    name: "{{ pdns_install_repo['name'] }}-debuginfo"
    file: "{{ pdns_install_repo['name'] }}"
    description: PowerDNS Authoritative Server - debug symbols
    baseurl: "{{ pdns_install_repo['yum_debug_symbols_repo_baseurl'] }}"
    gpgkey: "{{ pdns_install_repo['gpg_key'] }}"
    gpgcheck: yes
    priority: "90"
    state: present
  when: pdns_install_debug_symbols_package
