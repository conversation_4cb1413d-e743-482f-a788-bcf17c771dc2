---
- block:
    - name: Ensure the override directory exists (systemd)
      file:
        name: "/etc/systemd/system/{{ pdns_service_name }}.service.d"
        state: directory
        owner: root
        group: root

    - name: Override the PowerDNS Authoritative Server unit (systemd)
      template:
        src: "override-service.systemd.conf.j2"
        dest: "/etc/systemd/system/{{ pdns_service_name }}.service.d/override.conf"
        owner: root
        group: root
      register: _pdns_override_unit
      when: pdns_service_overrides | length > 0

    - name: Reload systemd
      systemd:
        daemon_reload: yes
      when: not pdns_disable_handlers
        and _pdns_override_unit.changed

  when: ansible_service_mgr == "systemd"

- name: Ensure that the PowerDNS configuration directory exists
  file:
    name: "{{ pdns_config_dir }}"
    state: directory
    owner: "{{ pdns_file_owner }}"
    group: "{{ pdns_file_group }}"
    mode: 0750

- name: Generate the PowerDNS configuration
  template:
    src: pdns.conf.j2
    dest: "{{ pdns_config_dir }}/{{ pdns_config_file }}"
    owner: "{{ pdns_file_owner }}"
    group: "{{ pdns_file_group }}"
    mode: 0640
  register: _pdns_configuration

- name: Creates pdns bind config directory
  file:
    path: "{{ pdns_bind_config_path }}"
    state: directory
    owner: "{{ pdns_file_owner }}"
    group: "{{ pdns_file_group }}"
    mode: 0750

- name: Create bind named configuration file
  template:
    src: named.conf.j2
    dest: "{{ pdns_bind_config_path }}/named.conf"
    owner: "{{ pdns_file_owner }}"
    group: "{{ pdns_file_group }}"
    mode: 0640
  register: _pdns_bind_configuration

- name: Create sdj corp zone file
  copy:
    src: "{{ role_path }}/files/corp.sdj.cn.zone"
    dest: "{{ pdns_bind_config_path }}"
    owner: "{{ pdns_file_owner }}"
    group: "{{ pdns_file_group }}"
    mode: 0644
    follow: no
  register: _pdns_bind_zone_file

- name: Ensure that the PowerDNS 'include-dir' directory exists
  file:
    name: "{{ pdns_config['include-dir'] }}"
    state: directory
    owner: "{{ pdns_file_owner }}"
    group: "{{ pdns_file_group }}"
    mode: 0750
  when: "pdns_config['include-dir'] is defined"

- name: Restart PowerDNS
  service:
    name: "{{ pdns_service_name }}"
    state: restarted
    sleep: 1
  when: not pdns_disable_handlers
    and pdns_service_state != 'stopped'
    and (_pdns_override_unit.changed
    or _pdns_configuration.changed
    or _pdns_bind_configuration.changed
    or _pdns_bind_zone_file.changed)
