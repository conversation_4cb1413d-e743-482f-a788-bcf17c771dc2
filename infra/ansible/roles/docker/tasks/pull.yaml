- name: <PERSON>ull {{ image_name }} from Nexus private docker registry
  docker_image:
    name: "{{ nexus_private_docker_registry }}/sdj/{{ image_name }}"
    repository: sdj/{{ image_name }}
    source: pull

- name:  Save {{ image_name }} To gz
  ignore_errors: true
  shell:
    cmd: docker save > {{ save_image_dest }}/{{ image_name }}.tar.gz  sdj/{{ image_name }}
  when: save_image_dest is defined
