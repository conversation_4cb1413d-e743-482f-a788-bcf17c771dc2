- name: Creating insecure entry for docker daemon
  copy:
    dest: '/etc/docker/daemon.json'
    owner: root
    group: root
    mode: 0644
    content: |
      {
        "insecure-registries":["{{ nexus_private_docker_registry }}"]
      }
  register: docker_registry_changed

- name: Restart docker service
  service:
    name: docker
    state: restarted
  when: docker_registry_changed

- name: Login Docker
  docker_login:
    registry_url: "{{ nexus_private_docker_registry }}"
    username: "{{ nexus_bot_docker_user }}"
    password: "{{ nexus_bot_docker_password }}"
