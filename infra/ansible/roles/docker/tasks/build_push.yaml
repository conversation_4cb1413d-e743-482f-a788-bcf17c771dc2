- name: Check if the Docker image exists
  docker_image_info:
    name: "{{ nexus_private_docker_registry }}/sdj/{{ image_name }}"
  register: docker_image_info

- name: Remove the existing Docker image
  docker_image:
    name: "{{ item }}"
    state: absent
    force_absent: yes
  with_items:
    - "{{ nexus_private_docker_registry }}/sdj/{{ image_name }}"
    - "sdj/{{ image_name }}"
  when: docker_image_info.images | length > 0

- name: Build  {{ image_name }} image and Push To Nexus docker private registry
  docker_image:
    build:
      path: "{{ build_path }}"
    name: "{{ nexus_private_docker_registry }}/sdj/{{ image_name }}"
    push: true
    source: build

