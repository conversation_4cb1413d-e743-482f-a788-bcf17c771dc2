---
galaxy_info:
  author: robe<PERSON><PERSON><PERSON><PERSON>
  role_name: mysql
  description: Install and configure mysql on your system.
  license: Apache-2.0
  company: none
  min_ansible_version: "2.12"

  platforms:
    - name: Debian
      versions:
        - bullseye
    - name: EL
      versions:
        - "8"
        - "9"
    - name: Fedora
      versions:
        - all
    - name: opensuse
      versions:
        - all
    - name: Ubuntu
      versions:
        - all

  galaxy_tags:
    - mysql
    - mariadb
    - database
    - installer
    - package

dependencies: []
