---
# handlers file for mysql

- name: Systemctl daemon-reexec
  ansible.builtin.systemd:
    daemon_reexec: yes

- name: Restart mysql server
  ansible.builtin.service:
    name: "{{ mysql_service }}"
    state: restarted

- name: Set root password
  community.mysql.mysql_user:
    login_unix_socket: "{{ mysql_socket }}"
    login_user: root
    login_password: ""
    name: root
    password: "{{ mysql_root_password }}"
    state: present
  no_log: yes
