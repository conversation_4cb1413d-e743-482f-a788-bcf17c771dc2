---
- name: Verify
  hosts: all
  become: yes
  gather_facts: yes

  vars_files: "../../vars/main.yml"

  roles:
    - role: ansible-role-mysql
      mysql_users:
        - name: my_user
      mysql_databases:
        - name: my_db
          state: dump
          target: /tmp/my_db.mysql

  vars:
    _netstat_package:
      default: net-tools
      Suse: net-tools-deprecated
    netstat_package: "{{ _netstat_package[ansible_os_family] | default(_netstat_package['default']) }}"

  tasks:
    - name: Install netstat
      ansible.builtin.package:
        name: "{{ netstat_package }}"
        state: present
      notify:
        - Remove netstat

    - name: Check if mysql is bound to 127.0.0.1
      ansible.builtin.command:
        cmd: netstat -tulpen
      register: mysql_netstat
      failed_when:
        - '"127.0.0.1" not in mysql_netstat.stdout'

    - name: Try root account
      community.mysql.mysql_db:
        name: my_test
        state: present
        login_unix_socket: "{{ mysql_socket }}"
        login_user: root
        login_password: "{{ mysql_root_password }}"

  handlers:
    - name: Remove netstat
      ansible.builtin.package:
        name: "{{ netstat_package }}"
        state: absent
