---
#
# Ansible managed
#
dependency:
  name: galaxy
  options:
    role-file: requirements.yml
    requirements-file: requirements.yml
lint: |
  set -e
  yamllint .
  ansible-lint
driver:
  name: docker
platforms:
  - name: "mysql-${image:-fedora}-${tag:-latest}${TOX_ENVNAME}"
    image: "${namespace:-robert<PERSON><PERSON>ck}/${image:-fedora}:${tag:-latest}"
    command: /sbin/init
    volumes:
      - /sys/fs/cgroup:/sys/fs/cgroup:ro
    privileged: yes
    pre_build_image: yes
provisioner:
  name: ansible
verifier:
  name: ansible
