---
# tasks file for mysql

- name: Import assert.yml
  ansible.builtin.import_tasks:
    file: assert.yml
  run_once: yes
  delegate_to: localhost

- name: Install mysql
  ansible.builtin.package:
    name: "{{ mysql_packages }}"
    state: present
  notify:
    - Set root password
    - Systemctl daemon-reexec

#- name: Configure mysql server
#  community.general.ini_file:
#    dest: "{{ mysql_configuration_destination }}/{{ mysql_name }}d.cnf"
#    section: "{{ item.section }}"
#    option: "{{ item.option }}"
#    value: "{{ item.value }}"
#    mode: "0644"
#  loop: "{{ mysqld_options }}"
#  loop_control:
#    label: "{{ item.option }}"
#  notify:
#    - Restart mysql server

- name: Configure mysql server
  community.general.ini_file:
    dest: "{{ mysql_configuration_destination }}/{{ mysql_name }}d.cnf"
    section: "{{ item.section }}"
    option: "{{ item.option }}"
    value: "{{ item.value }}"
    mode: "0644"
  loop: "{{ mysql_configuration_options }}"
  loop_control:
    label: "{{ item.option }}"
  notify:
    - Restart mysql server

- name: Configure mysql client
  community.general.ini_file:
    dest: "{{ mysql_configuration_destination }}/{{ mysql_name }}-client.cnf"
    section: client
    option: socket
    value: "{{ mysql_socket }}"
    mode: "0644"

- name: Initialize mysql
  ansible.builtin.command:
    cmd: "{{ mysql_initialize_command }}"
    creates: /var/lib/mysql/mysql
  when:
    - ansible_distribution in [ "Alpine", "Archlinux" ]

- name: Start and enable mysql
  ansible.builtin.service:
    name: "{{ mysql_service }}"
    state: started
    enabled: yes

- name: Flush handlers
  ansible.builtin.meta: flush_handlers

- name: Place my.cnf
  ansible.builtin.template:
    src: my.cnf.j2
    dest: /root/.my.cnf
    mode: "0640"

- name: Configure root user to access from any host
  mysql_user:
    login_user: root
    login_password: "{{ mysql_root_password }}"
    name: root
    host: "{{ item }}"
    password: "{{ mysql_root_password }}"
    priv: "*.*:ALL,GRANT"
    state: present
  with_items:
    - "{{ mysql_root_host }}"
  when: mysql_root_host != ""
  notify:
    - Restart mysql server

- name: Create databases
  community.mysql.mysql_db:
    name: "{{ item.name }}"
    state: "{{ item.state | default('present') }}"
    target: "{{ item.target | default(omit) }}"
    encoding: "{{ item.encoding | default(omit) }}"
    collation: "{{ item.collation | default(omit) }}"
    login_unix_socket: "{{ mysql_socket }}"
    login_user: root
    login_password: "{{ mysql_root_password }}"
  loop: "{{ mysql_databases }}"
  loop_control:
    label: "{{ item.name }}"
  when:
    - mysql_databases is defined
  no_log: yes

- name: Create users
  community.mysql.mysql_user:
    name: "{{ item.name }}"
    state: present
    password: "{{ item.password | default(omit) }}"
    priv: "{{ item.priv | default(omit) }}"
    host: "{{ item.host | default(omit) }}"
    host_all: "{{ item.host_all | default(omit) }}"
    update_password: "{{ item.update_password | default(omit) }}"
    login_unix_socket: "{{ mysql_socket }}"
    login_user: root
    login_password: "{{ mysql_root_password }}"
  loop: "{{ mysql_users }}"
  loop_control:
    label: "{{ item.name }}"
  when:
    - mysql_users is defined
  no_log: yes

- name: Flush handlers again
  ansible.builtin.meta: flush_handlers
