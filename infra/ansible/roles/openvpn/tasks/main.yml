---
- name: Include vars for OpenVPN installation
  include_vars: "{{ item }}"
  with_first_found:
    - "../vars/{{ ansible_distribution }}{{ ansible_distribution_major_version }}.yml"
    - "../vars/{{ ansible_distribution }}.yml"
    - "../vars/{{ ansible_os_family }}.yml"
    - "../vars/empty.yml"

- name: Set facts
  import_tasks: set_facts.yml

- name: Uninstall OpenVPN
  import_tasks: uninstall.yml
  when: openvpn_uninstall

- name: Install OpenVPN
  import_tasks: install.yml
  tags:
    - install

- name: Copy or Generate server keys
  import_tasks: server_keys.yml

# ignoreerrors is required for CentOS/RHEL 6
# http://serverfault.com/questions/477718/sysctl-p-etc-sysctl-conf-returns-error
- name: Enable ipv4 forwarding
  ansible.posix.sysctl:
    name: net.ipv4.ip_forward
    value: '1'
    ignoreerrors: true
  failed_when: false
  when: not ci_build

- name: Enable ipv6 forwarding
  ansible.posix.sysctl:
    name: net.ipv6.conf.all.forwarding
    value: '1'
    ignoreerrors: true
  when: openvpn_server_ipv6_network is defined and not ci_build

- name: Detect firewall type
  import_tasks: firewall.yml
  when:
    - not ci_build
    - manage_firewall_rules
  tags:
    - firewall

- name: Configure SELinux
  import_tasks: selinux.yml
  when:
    - ansible_selinux.status == "enabled"

- name: Compare existing certs against 'clients' variable
  import_tasks: cert_sync_detection.yml
  when: openvpn_sync_certs
  tags:
    - sync_certs

- name: Generate client configs
  import_tasks: client_keys.yml
  when: clients is defined

- name: Generate revocation list and clean up
  import_tasks: revocation.yml
  when: >-
    (openvpn_revoke_these_certs is defined)
    or
    (openvpn_sync_certs and cert_sync_certs_to_revoke.stdout_lines | length > 0)

- name: Configure OpenVPN server
  import_tasks: config.yml

- name: Apply rc.local template
  template:
    src: route_tables.j2
    dest: /etc/rc.local
    mode: '0755'

- name: Copy rc.local service unit
  template:
    src: route_table_service.j2
    dest: /etc/systemd/system/rc-local.service
    mode: '0644'

- name: Enable and start rc.local service
  systemd:
    name: rc-local
    enabled: yes
    state: started
