---
- name: Check systemd existence as Docker Guest
  stat:
    path: /bin/systemctl
  when: ansible_virtualization_role is defined and ansible_virtualization_type == "docker" and ansible_virtualization_role == "guest"
  register: docker_stat_result

- name: Set systemd openvpn service name
  set_fact:
    openvpn_service_name: "openvpn@{{ openvpn_config_file }}.service"
  when: ansible_service_mgr == "systemd" or (docker_stat_result.stat is defined and docker_stat_result.stat.exists)

# Fedora and CentOS 8 separate OpenVPN into client and server
- name: Set Fedora 27+ and CentOS 8 service name
  set_fact:
    openvpn_service_name: "openvpn-server@{{ openvpn_config_file }}.service"
  when: >-
    (ansible_distribution == "Fedora" and ansible_distribution_version|int >= 27)
    or
    (
      ( (ansible_distribution == "CentOS") or (ansible_distribution == "Rocky") or (ansible_distribution == "RedHat") )
      and
      (ansible_distribution_version | int) >= 8
    )

- name: Set Fedora 27+ and CentOS 8 OpenVPN base path
  set_fact:
    openvpn_base_dir: "/etc/openvpn/server"
  when: >-
    (ansible_distribution == "Fedora" and ansible_distribution_version|int >= 27)
    or
    (
      (ansible_distribution == "CentOS" or ansible_distribution == "Rocky" or ansible_distribution == "RedHat" )
      and
      (ansible_distribution_version | int) >= 8
    )
