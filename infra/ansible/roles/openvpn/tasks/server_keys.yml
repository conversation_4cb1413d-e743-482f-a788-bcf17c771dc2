---
- name: Create openvpn key directory
  file:
    path: "{{ openvpn_key_dir }}"
    state: directory
    mode: 0755

- name: Copy openssl server/ca extensions
  copy:
    src: "{{ item }}"
    dest: "{{ openvpn_key_dir }}"
    owner: root
    group: root
    mode: 0400
  with_items:
    - openssl-server.ext
    - openssl-ca.ext

- name: Copy CA key
  copy:
    content: "{{ openvpn_ca_key.key }}"
    dest: "{{ openvpn_key_dir }}/ca-key.pem"
    mode: 0400
  when: openvpn_ca_key is defined

- name: Copy CA cert
  copy:
    content: "{{ openvpn_ca_key.crt }}"
    dest: "{{ openvpn_key_dir }}/ca.crt"
    mode: 0444
  when: openvpn_ca_key is defined

- name: Generate CA key
  command: >-
    openssl req -nodes -newkey rsa:{{ openvpn_rsa_bits }} -keyout ca-key.pem -out ca-csr.pem
    -days 3650 -subj /CN=sdj/
  args:
    chdir: "{{ openvpn_key_dir }}"
    creates: ca-key.pem
  when: openvpn_ca_key is not defined

- name: Protect CA key
  file:
    path: "{{ openvpn_key_dir }}/ca-key.pem"
    mode: 0400
  when: openvpn_ca_key is not defined

- name: Sign CA key
  command: openssl x509 -req -in ca-csr.pem -out ca.crt -CAcreateserial -signkey ca-key.pem -sha256 -days 3650 -extfile openssl-ca.ext
  args:
    chdir: "{{ openvpn_key_dir }}"
    creates: ca.crt
  when: openvpn_ca_key is not defined

- name: Generate server key
  command: >-
    openssl req -nodes -newkey rsa:{{ openvpn_rsa_bits }} -keyout server.key -out server.csr
    -days 3650 -subj /CN=sdj/
  args:
    chdir: "{{ openvpn_key_dir }}"
    creates: server.key

- name: Protect server key
  file:
    path: "{{ openvpn_key_dir }}/server.key"
    mode: 0400

- name: Sign server key
  command: openssl x509 -req -in server.csr -out server.crt -CA ca.crt -CAkey ca-key.pem -sha256 -days 3650 -CAcreateserial -extfile openssl-server.ext
  args:
    chdir: "{{ openvpn_key_dir }}"
    creates: server.crt

- name: Copy tls-auth key
  copy:
    content: "{{ openvpn_tls_auth_key }}"
    dest: "{{ openvpn_key_dir }}/ta.key"
    mode: 0400
  when: openvpn_tls_auth_key is defined

- name: Generate tls-auth key
  command: openvpn --genkey --secret ta.key
  args:
    chdir: "{{ openvpn_key_dir }}"
    creates: ta.key
  when: openvpn_tls_auth_key is not defined

# not a security issue, params aren't secret, just not generated by an attacker
# per http://security.stackexchange.com/questions/42415/openvpn-dhparam/42418#42418
- name: Copy pre-generated DH params
  copy:
    src: dh.pem
    dest: "{{ openvpn_key_dir }}"
    owner: root
    group: root
    mode: 0400
  when: openvpn_use_pregenerated_dh_params|bool

# Alternatively, if you're concerned about logjam attacks
- name: Generate dh params
  command: openssl dhparam -out {{ openvpn_key_dir }}/dh.pem {{ openvpn_rsa_bits }}
  args:
    chdir: "{{ openvpn_key_dir }}"
    creates: dh.pem
  when: not (openvpn_use_pregenerated_dh_params|bool)

- name: Install ca.conf config file
  template:
    src: ca.conf.j2
    dest: "{{ openvpn_key_dir }}/ca.conf"
    owner: root
    group: root
    mode: 0744

- name: Create initial certificate revocation list squence number
  shell: "echo 00 > crl_number"
  args:
    chdir: "{{ openvpn_key_dir }}"
    creates: crl_number

- name: Generate tls-auth key
  command: openvpn --genkey --secret ta.key
  args:
    chdir: "{{ openvpn_key_dir }}"
    creates: ta.key
  when: openvpn_tls_auth_key is not defined

- name: Install revocation script
  template:
    src: revoke.sh.j2
    dest: "{{ openvpn_key_dir }}/revoke.sh"
    owner: root
    group: root
    mode: 0744

- name: Check if certificate revocation list database exists
  stat:
    path: "{{ openvpn_key_dir }}/index.txt"
  register: file_result

- name: Create certificate revocation list database if required
  file:
    path: "{{ openvpn_key_dir }}/index.txt"
    state: touch
    mode: 0644
  when: not file_result.stat.exists

- name: Set up certificate revocation list
  command: sh revoke.sh
  args:
    chdir: "{{ openvpn_key_dir }}"
    creates: "{{ openvpn_key_dir }}/ca-crl.pem"

- name: Install crl-cron script
  template:
    src: crl-cron.sh.j2
    dest: "{{ openvpn_base_dir }}/crl-cron.sh"
    owner: root
    group: root
    mode: 0744

# This should eventually be switched to use a systemd timer
# eg /usr/local/lib/systemd/system/openvpn-crl.timer
- name: Check for crontab
  command: which crontab
  register: crontab
  check_mode: false
  changed_when: false
  failed_when: false

- name: Install cronie
  ansible.builtin.package:
    name: cronie
    state: present
  when: ansible_os_family == "RedHat" and crontab.rc != 0

- name: Add cron to check every Saturday if the CRL needs to be renewed
  ansible.builtin.cron:
    name: "check if CRL will expire soon"
    special_time: weekly
    job: "sh {{ openvpn_base_dir }}/crl-cron.sh"
    cron_file: /etc/cron.d/openvpn-crl
    user: root
  when: not ci_build
