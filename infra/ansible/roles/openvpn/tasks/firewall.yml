---
- name: Check for firewalld
  command: which firewall-cmd
  register: firewalld
  check_mode: false
  changed_when: false  # Never report as changed
  failed_when: false

- name: Check for ufw
  command: which ufw
  register: ufw
  check_mode: false
  changed_when: false  # Never report as changed
  failed_when: false

- name: Check for iptables
  command: which iptables
  register: iptables
  check_mode: false
  changed_when: false  # Never report as changed
  failed_when: false

- name: <PERSON>ail on both firewalld & ufw
  fail:
    msg: "Both FirewallD and UFW are detected, firewall situation is unknown"
  when: openvpn_firewall == 'auto' and firewalld.rc == 0 and ufw.rc == 0

- name: Fail on no firewall detected
  fail:
    msg: "No firewall detected, install one before proceeding (firewalld||ufw||iptables)"
  when: firewalld.rc != 0 and ufw.rc != 0 and iptables.rc != 0

- name: Add port rules (iptables)
  include_tasks: iptables.yml
  when: >-
    (openvpn_firewall == 'iptables')
    or
    (openvpn_firewall == 'auto' and firewalld.rc != 0 and ufw.rc != 0 and iptables.rc == 0)

- name: Add port rules (firewalld)
  include_tasks: firewalld.yml
  when: >-
    (openvpn_firewall == 'firewalld')
    or
    (openvpn_firewall == 'auto' and firewalld.rc == 0 and ufw.rc != 0)

- name: Add port rules (ufw)
  include_tasks: ufw.yml
  when: >-
    (openvpn_firewall == 'ufw')
    or
    (openvpn_firewall == 'auto' and firewalld.rc != 0 and ufw.rc == 0)
