---
- name: Create openvpn ovpn file directory
  file:
    path: "{{ openvpn_ovpn_dir }}"
    state: directory
    mode: 0755

- name: Copy openssl client extensions
  copy:
    src: openssl-client.ext
    dest: "{{ openvpn_key_dir }}"
    owner: root
    group: root
    mode: 0400

- name: Generate client key
  command: >-
    openssl req -nodes -newkey rsa:{{ openvpn_rsa_bits }} -keyout {{ item }}.key -out {{ item }}.csr
    -days 3650 -subj /CN=sdj-{{ item[:24] }}/
  args:
    chdir: "{{ openvpn_key_dir }}"
    creates: "{{ item }}.key"
  with_items:
    - "{{ clients }}"

- name: Protect client keys
  file:
    path: "{{ openvpn_key_dir }}/{{ item }}.key"
    mode: 0400
  with_items:
    - "{{ clients }}"

- name: Sign client key
  command: openssl x509 -req -in {{ item }}.csr -out {{ item }}.crt -CA ca.crt -CAkey ca-key.pem -sha256 -days 3650 -extfile openssl-client.ext
  args:
    chdir: "{{ openvpn_key_dir }}"
    creates: "{{ item }}.crt"
  with_items:
    - "{{ clients }}"

- name: Register server ca key
  slurp:
    src: "{{ openvpn_key_dir }}/ca.crt"
  register: ca_cert

- name: Register tls-auth key
  slurp:
    src: "{{ openvpn_key_dir }}/ta.key"
  register: tls_auth

- name: Register client certs
  slurp:
    src: "{{ openvpn_key_dir }}/{{ item }}.crt"
  with_items:
    - "{{ clients }}"
  register: client_certs

- name: Register client keys
  slurp:
    src: "{{ openvpn_key_dir }}/{{ item }}.key"
  with_items:
    - "{{ clients }}"
  register: client_keys

- name: Generate and fetch client config
  include_tasks: client_config_tasks.yml
  loop: "{{ openvpn_client_platforms }}"
  loop_control:
    loop_var: platform
