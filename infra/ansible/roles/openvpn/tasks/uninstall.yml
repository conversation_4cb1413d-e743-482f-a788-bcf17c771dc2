---
- name: Disable openvpn auto-start & start
  service:
    name: "{{ openvpn_service_name }}"
    enabled: false
    state: stopped

- name: Wipe out config directory
  file:
    path: "{{ openvpn_base_dir }}"
    state: absent

- name: Remove openvpn logrotate config file
  file:
    path: /etc/logrotate.d/openvpn.conf
    state: absent

- name: Uninstall OpenVPN
  package:
    name: "{{ openvpn_package_name }}"
    state: absent

- name: Uninstall LDAP plugin
  package:
    name: "{{ openvpn_ldap_plugin_package_name }}"
    state: absent
  when: openvpn_use_ldap

- name: Terminate playbook
  fail:
    msg: "OpenVPN uninstalled, playbook stopped"
