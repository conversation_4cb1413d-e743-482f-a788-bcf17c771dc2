---
- name: Enable firewalld
  service:
    name: firewalld
    enabled: true
    masked: false
    state: started

- name: Install python2-firewall (Fedora)
  package:
    name: "{{ python_firewall_package_name }}"
    state: present
  when:
    - ansible_distribution == "Fedora"
    - ansible_python.version.major == 2

- name: Enable OpenVPN Port (firewalld)
  ansible.posix.firewalld:
    port: "{{ openvpn_port }}/{{ openvpn_proto }}"
    zone: "{{ firewalld_default_interface_zone }}"
    permanent: true
    immediate: true
    state: enabled

- name: Set tun0 interface to internal
  ansible.posix.firewalld:
    interface: tun0
    zone: internal
    permanent: true
    immediate: true
    state: enabled

- name: Set default interface to external
  ansible.posix.firewalld:
    interface: "{{ ansible_default_ipv4.interface }}"
    zone: "{{ firewalld_default_interface_zone }}"
    permanent: true
    immediate: true
    state: enabled

- name: Enable masquerading on external zone
  ansible.posix.firewalld:
    masquerade: true
    zone: "{{ firewalld_default_interface_zone }}"
    permanent: true
    state: enabled
    # Workaround ansible issue: https://github.com/ansible/ansible/pull/21693
    # immediate: true
  notify:
    - restart firewalld

# workaround for --permanent not working on non-NetworkManager managed ifaces
# https://bugzilla.redhat.com/show_bug.cgi?id=1112742
- name: Check if ifcfg-{{ ansible_default_ipv4.interface }} exists
  stat:
    path: "/etc/sysconfig/network-scripts/ifcfg-{{ ansible_default_ipv4.interface }}"
  register: ifcfg

- name: Persist default interface in ifcfg file
  lineinfile:
    dest: /etc/sysconfig/network-scripts/ifcfg-{{ ansible_default_ipv4.interface }}
    regexp: "^ZONE="
    line: "ZONE={{ firewalld_default_interface_zone }}"
  when: ifcfg.stat.exists
