- name: Generate client config
  template:
    src: client-{{ platform }}.ovpn.j2
    dest: "{{ openvpn_ovpn_dir }}/{{ item.0.item }}-{{ platform }}-{{ inventory_hostname }}.ovpn"
    owner: root
    group: root
    mode: 0400
  with_together:
    - "{{ client_certs.results }}"
    - "{{ client_keys.results }}"

- name: Fetch client config
  fetch:
    src: "{{ openvpn_ovpn_dir }}/{{ item }}-{{ platform }}-{{ inventory_hostname }}.ovpn"
    dest: "{{ openvpn_fetch_client_configs_dir }}//jancsivpn-{{ platform }}-{{ item }}.ovpn"
    flat: true
  when: openvpn_fetch_client_configs
  with_items:
    - "{{ clients }}"
