# Defaults for openvpn

# Networking
openvpn_client_register_dns: true
openvpn_client_to_client: true
openvpn_dualstack: true
openvpn_keepalive_ping: 5
openvpn_keepalive_timeout: 30
openvpn_port: 1194
openvpn_proto: udp
openvpn_redirect_gateway: false
openvpn_resolv_retry: 5
openvpn_server_hostname: "{{ inventory_hostname }}"
openvpn_server_netmask: *************
openvpn_server_network: ********
openvpn_tun_mtu:

# Security
openvpn_auth_alg: SHA256
openvpn_cipher: AES-256-CBC
openvpn_duplicate_cn: false
openvpn_rsa_bits: 2048
openvpn_use_crl: false
openvpn_use_hardened_tls: true
openvpn_use_modern_tls: true
openvpn_use_pregenerated_dh_params: false
openvpn_verify_cn: false
tls_auth_required: true
openvpn_script_security: 1

# Operations
openvpn_addl_client_options: []
openvpn_addl_server_options: []
openvpn_compression: lzo
openvpn_enable_management: false
openvpn_ifconfig_pool_persist_file: ipp.txt
openvpn_management_bind: /var/run/openvpn/management unix
openvpn_management_client_user: root
openvpn_push: []
openvpn_service_group: nogroup
openvpn_service_user: nobody
openvpn_status_version: 1


# Client config - settings the server will push
openvpn_client_config: true
openvpn_client_config_dir: ccd
openvpn_client_configs: {}
openvpn_client_platforms: ["windows"]
# Example:
# openvpn_client_configs:
#   client1:
#     - ifconfig-push ******** *************
#     - push "route *********** *************"
#     - push "dhcp-option DOMAIN example.com"
#     - iroute *********** *************
#
# OR
#
# openvpn_client_configs:
#   client1:
#     - ifconfig-push ******** *************
#     - push "route *********** *************"
#     - iroute *********** *************
#     - iroute *********** *************

