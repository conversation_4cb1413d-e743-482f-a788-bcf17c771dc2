client
dev tun
proto tcp
remote 115.231.107.85 500
resolv-retry infinite
nobind
persist-key
persist-tun
<ca>
-----BEGIN CERTIFICATE-----
MIIB7TCCAXOgAwIBAgIUFN5F5x+Dl5Rw6vf79fA7m3y4fUUwCgYIKoZIzj0EAwQw
ETEPMA0GA1UEAwwGamFuY3NpMB4XDTIyMTExMzE0MTY0OVoXDTMyMTExMDE0MTY0
OVowETEPMA0GA1UEAwwGamFuY3NpMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEx8zW
NM3f17xVjE97nYVQjq8BUeyHWbM27T0OgzewZvcr+2n+U55fdjRBnA/bXlxzs/ak
kkMD1S6xd9W82vZBL+hywRIBkuK/VrtUYxppGlxLr6UjWrQkwnSgfvQ8h8n7o4GL
MIGIMAwGA1UdEwQFMAMBAf8wHQYDVR0OBBYEFKsUheM3PLbpTSsu4Lw0UYqj7hng
MEwGA1UdIwRFMEOAFKsUheM3PLbpTSsu4Lw0UYqj7hngoRWkEzARMQ8wDQYDVQQD
DAZqYW5jc2mCFBTeRecfg5eUcOr3+/XwO5t8uH1FMAsGA1UdDwQEAwIBBjAKBggq
hkjOPQQDBANoADBlAjAOVR3WQ2f5GSDV/3hwNFd5fJf4CBBW4cuxX9H+JXq/sI/C
u9Rlo8xJmL7JNG4zAVACMQDo/YWGP5NvFx81sG/cvpj1MAK2SQ8pc+8uAESssKd4
+mn0oC2DuY/vBFgEbdzFed4=
-----END CERTIFICATE-----
</ca>
push "route ******** *************** vpn_gateway"
push "route ********** ************* vpn_gateway"
push "route ********* ************* vpn_gateway"
push "route ********* ************* vpn_geteway"
dhcp-option DNS ***********
dhcp-option DOMAIN corp.sdj

;tls-auth ta.key 1
cipher AES-256-GCM
verb 3
auth-user-pass {{ openvpn_base_dir }}/my-credentials.txt
auth-retry interact
#auth-nocache
persist-tun
persist-key
auth-nocache
ping 15
ping-restart 30
ping-timer-rem
