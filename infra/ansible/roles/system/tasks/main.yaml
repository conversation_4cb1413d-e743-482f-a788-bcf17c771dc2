---
- name: check if systemd-resolved config exists
  stat:
    path: /etc/systemd/resolved.conf
  register: systemd_resolved_config
  check_mode: false

- name: update DNS servers for systemd-resolvd
  include: systemd-resolved.yaml
  when: systemd_resolved_config.stat.exists | bool

- name: Copy the Nexus apt cache configuration file
  copy:
    src: '{{ role_path }}/files/sources.list'
    dest: '/etc/apt/sources.list'
    group: root
    owner: root
    mode: 0644
    follow: no
