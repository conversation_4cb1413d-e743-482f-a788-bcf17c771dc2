---
galaxy_info:
  role_name: postgresql
  namespace: galaxyproject
  author: The Galaxy Project
  description: Install and manage a PostgreSQL (http://www.postgresql.org/) server.
  company: The Galaxy Project
  license: AFL v3.0
  min_ansible_version: 2.7
  github_branch: main
  platforms:
    - name: EL
      versions:
        - all
    - name: Fedora
      versions:
        - all
    - name: Ubuntu
      versions:
        - all
    - name: Debian
      versions:
        - all
  galaxy_tags:
    - database
    - sql
    - postgres
    - postgresql
dependencies: []
