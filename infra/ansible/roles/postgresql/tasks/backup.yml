---

- name: Create backup directories
  file:
    owner: postgres
    group: postgres
    mode: 0750
    state: directory
    path: "{{ item }}"
  with_items:
    - "{{ postgresql_backup_local_dir }}"
    - "{{ postgresql_backup_local_dir }}/bin"

- name: Create backup output directory
  file:
    owner: postgres
    group: postgres
    mode: 0750
    state: directory
    path: "{{ postgresql_backup_dir }}"
  when: postgresql_backup_dir[0] == '/' and postgresql_create_backup_dir

- name: Install backup script templates
  template:
    src: "{{ item }}.j2"
    dest: "{{ postgresql_backup_local_dir }}/bin/{{ item }}"
    owner: postgres
    group: postgres
    mode: 0750
  loop:
    - archive_wal.sh

- name: Install backup script files
  copy:
    src: "{{ item }}"
    dest: "{{ postgresql_backup_local_dir }}/bin/{{ item }}"
    owner: postgres
    group: postgres
    mode: 0750
  loop:
    - backup.py

- name: Set WAL archive config options
  template:
    src: 20ansible_backup.conf.j2
    dest: "{{ postgresql_conf_dir }}/conf.d/20ansible_backup.conf"
    owner: postgres
    group: postgres
    mode: 0644
    backup: true
  notify: Reload PostgreSQL

- name: Schedule backups
  cron:
    name: "PostgreSQL Backup"
    cron_file: ansible_postgresql_backup
    user: postgres
    hour: "{{ postgresql_backup_hour | default(1) }}"
    minute: "{{ postgresql_backup_minute | default(0) }}"
    day: "{{ postgresql_backup_day | default(omit) }}"
    month: "{{ postgresql_backup_month | default(omit) }}"
    weekday: "{{ postgresql_backup_weekday | default(omit) }}"
    job: >-
      {{ postgresql_backup_command }}
      {{
        ' && ' ~ postgresql_backup_post_command if postgresql_backup_post_command is defined else ''
      }}

- name: Remove PostgreSQL working WAL backup cron job
  cron:
    name: "PostgreSQL WAL Backup"
    cron_file: ansible_postgresql_walbackup
    state: absent
