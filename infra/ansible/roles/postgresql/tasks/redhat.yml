---

- name: Set PostgreSQL dotless version fact
  set_fact:
    __postgresql_version_dotless: "{{ postgresql_version | replace('.', '') }}"
    __postgresql_command_sep: "{{ postgresql_version is version('10', '>=') | ternary('-', '') }}"

# Using the rpm URL format of the yum module causes Ansible to download the rpm
# every time to check whether it's installed, so, don't do that.
- name: Check pgdg repository package (RedHat)
  yum:
    name: "pgdg-redhat-repo"
  register: __postgresql_repo_pkg_installed_result
  ignore_errors: true

- name: Install repository key
  rpm_key:
    key: https://download.postgresql.org/pub/repos/yum/RPM-GPG-KEY-PGDG
    state: present

- name: Install pgdg repository package (RedHat)
  yum:
    name: >-
      https://download.postgresql.org/pub/repos/yum/reporpms/{{ postgresql_pgdg_shortfamilies[ansible_distribution]
        | default("EL") }}-{{ ansible_distribution_major_version }}-{{ ansible_architecture }}/pgdg-{{
        postgresql_pgdg_families[ansible_distribution] | default("redhat") }}-repo-latest.noarch.rpm
  register: __postgresql_yum_result
  until: __postgresql_yum_result is succeeded
  retries: 5
  delay: 5
  when: __postgresql_repo_pkg_installed_result is failed

# - name: Collect installed repos
#   yum:
#     list: repos
#   until: __postgresql_yum_repolist_result is succeeded
#   retries: 5
#   delay: 5
#   register: __postgresql_yum_repolist_result

# Not supported (and no good workaround) until there is a solution for https://github.com/ansible/ansible/issues/41178
# - name: Ensure that only the desired PostgreSQL version's repo is enabled
#   yum_repository:
#     name: item.repoid
#     enabled: "{{ (item.repoid == 'pgdg' ~ __postgresql_version_dotless) if item.repoid.startswith('pgdg') else item.state == 'enabled' }}"
#   # "{{ __postgresql_yum_repolist_result.results | selectattr('repoid', 'startswith', 'pgdg') | list }}" would be nice
#   # here but alas there is no `startswith` test
#   loop: "{{ __postgresql_yum_repolist_result.results }}"

- name: Install PostgreSQL (RedHat)
  yum:
    name: postgresql{{ __postgresql_version_dotless }}-server

- name: Check for pgdata directory
  stat:
    path: "{{ postgresql_pgdata }}/base"
  register: pgdata_stat
  failed_when: false

- name: Initialize database (RedHat < 7)
  command: /sbin/service postgresql-{{ postgresql_version }} initdb
  args:
    warn: false  # Use of /sbin/service is valid here, ignore lint error
  when: >-
    ansible_distribution_major_version is version(7, '<')
      and (pgdata_stat.stat.isdir is not defined or not pgdata_stat.stat.isdir)

- name: Initialize database (RedHat >= 7)
  command: >-
    /usr/pgsql-{{ postgresql_version }}/bin/postgresql{{ __postgresql_command_sep }}{{
      __postgresql_version_dotless }}-setup initdb
  when: >-
    ansible_distribution_major_version is version(7, '>=')
      and (pgdata_stat.stat.isdir is not defined or not pgdata_stat.stat.isdir)

- name: Install psycopg2
  yum:
    name: "python{{ ansible_python.version.major }}-psycopg2"
  when: postgresql_install_psycopg2
