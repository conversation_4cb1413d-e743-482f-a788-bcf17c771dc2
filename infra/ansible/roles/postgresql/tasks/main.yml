---

# Convenient ordering - debian.yml sets postgresql_version if it is unset,
# which is needed by the include_vars files.
- include_tasks: debian.yml
  when: ansible_os_family == "Debian"

# For RedHat we use the value from defaults/main.yml
- name: Set version fact
  set_fact:
    postgresql_version: "{{ postgresql_default_version }}"
  when: ansible_os_family == "RedHat" and postgresql_version is not defined

# Sets postgresql_pgdata, postgresql_conf_dir
- name: Set OS-specific variables
  include_vars: "{{ ansible_os_family | lower }}.yml"

- name: Set pgdata fact
  set_fact:
    postgresql_pgdata: "{{ postgresql_pgdata_default }}"
  when: postgresql_pgdata is not defined

- name: Set conf dir fact
  set_fact:
    postgresql_conf_dir: "{{ postgresql_conf_dir_default }}"
  when: postgresql_conf_dir is not defined

# Needs postgresql_pgdata set
- include_tasks: redhat.yml
  when: ansible_os_family == "RedHat"

- name: Create conf.d
  file:
    path: "{{ postgresql_conf_dir }}/conf.d"
    state: directory
    owner: "{{ postgresql_user_name }}"
    group: "{{ postgresql_user_name }}"
    mode: 0755

# lineinfile's behavior when `backrefs = True` is very odd. We don't want to overwrite include_dirs if it's already
# properly set, but we don't know the exact format it will be in (with or without '=', with a comment at end of line,
# etc.). So check for a match first and then add if there's no match.
- name: Check for conf.d include in postgresql.conf
  lineinfile:
    line: 'why ansible ;('
    # The '=' is optional but is present in postgresql.conf.sample, which Debian's sample is based off of (but include*
    # directive examples in the PostgreSQL docs don't use it). Also ignore comments and whitespace after the directive.
    regexp: '^include_dir(\s+|\s*=\s*)?''conf.d''\s*(#.*)?$'
    path: "{{ postgresql_conf_dir }}/postgresql.conf"
    backrefs: true
  check_mode: true
  changed_when: __postgresql_include_dir_result is not changed  # yeah...
  register: __postgresql_include_dir_result
  when: postgresql_version is version('9.3', '>=')

- name: Set conf.d include in postgresql.conf
  lineinfile:
    line: "include_dir 'conf.d'"
    path: "{{ postgresql_conf_dir }}/postgresql.conf"
    backup: true
  notify: Reload PostgreSQL
  when: postgresql_version is version('9.3', '>=') and __postgresql_include_dir_result is changed

- name: Include 25ansible_postgresql.conf in postgresql.conf
  lineinfile:
    line: "include 'conf.d/25ansible_postgresql.conf'"
    dest: "{{ postgresql_conf_dir }}/postgresql.conf"
    backup: true
  notify: Reload PostgreSQL
  when: postgresql_version is version('9.3', '>=')

- name: Set config options
  template:
    src: 25ansible_postgresql.conf.j2
    dest: "{{ postgresql_conf_dir }}/conf.d/25ansible_postgresql.conf"
    owner: "{{ postgresql_user_name }}"
    group: "{{ postgresql_user_name }}"
    mode: 0644
    backup: true
  notify: Reload PostgreSQL

- name: Install pg_hba.conf
  template:
    src: pg_hba.conf.{{ ansible_os_family | lower }}.j2
    dest: "{{ postgresql_conf_dir }}/pg_hba.conf"
    owner: "{{ postgresql_user_name }}"
    group: "{{ postgresql_user_name }}"
    mode: 0400
    backup: true
  notify: Reload PostgreSQL

- include_tasks: backup.yml
  when: postgresql_backup_dir is defined

- name: Ensure PostgreSQL is running
  service:
    name: "{{ postgresql_service_name }}"
    enabled: true
    state: started
