#!/bin/bash
##
## This file is maintained by Ansible - CHANGES WILL BE OVERWRITTEN
##
#
# this is invoked by postgres directly, and should be set as the
# archive_command thusly:
#   archive_command = '/path/to/backup/bin/archive_wal.sh "%p" "%f"'
set -euo pipefail

wal_archive_dir={{ (postgresql_backup_dir ~ '/wal_archive') | quote }}

file_path="$1"
file_name="$2"

mkdir -p "$wal_archive_dir"

# If rsync outputs anything to stdout, the destination already existed, which should not happen
if [ -n "$(rsync {{ postgresql_archive_wal_rsync_args }} "$file_path" "$wal_archive_dir")" ]; then
    echo "ERROR: ${wal_archive_dir}/${file_name} already exists, overwriting is not allowed!"
    exit 1
fi

exit 0
