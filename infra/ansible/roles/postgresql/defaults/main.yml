---

postgresql_apt_keys_dir: "/etc/apt/keyrings"
postgresql_pgdg_key_dest: "{{ postgresql_apt_keys_dir }}/pgdg.asc"
postgresql_pgdg_key_url: "https://www.postgresql.org/media/keys/ACCC4CF8.asc"
postgresql_pgdg_repo: "deb [signed-by={{ postgresql_pgdg_key_dest }}] http://apt.postgresql.org/pub/repos/apt/ {{ ansible_distribution_release }}-pgdg main"

postgresql_default_version: 15
postgresql_user_name: postgres

postgresql_install_psycopg2: true

# Point-In-Time Recovery (PITR) backup options
#   https://www.postgresql.org/docs/current/continuous-archiving.html
postgresql_backup_local_dir: >-
  {{ '/var/lib/pgsql' if ansible_os_family == 'RedHat' else (
      '/var/lib/postgresql' if ansible_os_family == 'Debian' else '~postgres') }}/backup
postgresql_create_backup_dir: true

# Options used for the WAL archive command - do not change this unless you have read the PITR documentation and
# understand how this command must work.
postgresql_archive_wal_rsync_args: '--ignore-existing -ptg --info=skip1'

# These options are passed to all calls of rsync (in addition to backups, rsync is used to clean up old backups)
postgresql_backup_rsync_connect_opts: ''
# These options are passed only to the call of rsync that performs the backup
postgresql_backup_rsync_backup_opts: '-rptg'

# Keep this many old backups
postgresql_backup_keep: 30

__postgresql_pgdg_bin_dir: "{{ '/usr/pgsql-' ~ (postgresql_version | replace('.', '')) ~ '/bin' }}"
postgresql_backup_command: >-
  {{ postgresql_backup_local_dir | quote }}/bin/backup.py
  {{ '--rsync-connect-opts ' ~ (postgresql_backup_rsync_connect_opts | quote) if postgresql_backup_rsync_connect_opts else '' }}
  --rsync-backup-opts {{ postgresql_backup_rsync_backup_opts | quote }}
  --keep {{ postgresql_backup_keep | quote }}
  {{ '--pg-bin-dir ' ~ __postgresql_pgdg_bin_dir if ansible_os_family == 'RedHat' else '' }}
  --backup --clean-archive {{ postgresql_backup_dir | quote }}

postgresql_default_auth_method: "{{ (postgresql_version is version('13', '>')) | ternary('scram-sha-256', 'md5') }}"
