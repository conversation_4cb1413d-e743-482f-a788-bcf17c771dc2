---
- hosts: dns_server
  become: true
  become_user: root
  roles:
    - powerdns
    - powerdns_recursor
  vars:
    pdns_install_repo: "{{ pdns_auth_powerdns_repo_master }}"

    pdns_config:
      # master: true
      # slave: false
      local-address: 127.0.0.1
      local-port: 5300

    pdns_rec_config:
      local-address: "{{ hostvars[inventory_hostname]['ansible_default_ipv4']['address'] }}"
      forward-zones:
        - "corp.sdj.cn=127.0.0.1:5300"
      forward-zones-recurse:
        - ".=***************:53"
