---
- name: ubuntu-bazel:22.04 image Build
  hosts: localhost
  gather_facts: no
  become: yes
  become_user: root
  vars:
    SDJWorkSpace_repo: "SDJWorkSpace"
    build_work_path: "/srv"

  tasks:
    - name: <PERSON><PERSON> repo from SDJWorkSpace
      ignore_errors: true
      include_role:
        name: git
        tasks_from: main.yaml
      vars:
        gitlab_repo: "{{ item }}"
        workspace: "{{ build_work_path }}"
      with_items:
        - "{{ SDJWorkSpace_repo }}"

    - name: Login Docker private registry
      include_role:
        name: docker
        tasks_from: login.yaml
      vars:
        nexus_bot_docker_user: docker
        nexus_bot_docker_password: docker

    - name: Build And Push Images
      include_role:
        name: docker
        tasks_from: build_push.yaml
      vars:
        image_name: "{{ item.name }}"
        build_path: "{{ item.path }}"
      with_items:
        - {
            name: ubuntu-bazel:22.04,
            path: "{{ build_work_path }}/{{ SDJWorkSpace_repo }}/gitlab_ci/",
          }

    - name: Clear Workspace
      file:
        path: "{{ build_work_path }}/{{ SDJWorkSpace_repo }}"
        state: absent
