---
- hosts: mysql_server
  gather_facts: true
  become: true
  become_user:
  vars:
    mysql_databases:
      - name: sdj
        encoding: utf8
        collation: utf8_bin
      - name: sdj_test
        encoding: utf8
        collation: utf8_bin
      - name: luxurydata
        encoding: utf8
        collation: utf8_bin
      - name: product_service
        encoding: utf8
        collation: utf8_bin
      - name: sync_service
        encoding: utf8
        collation: utf8_bin
    mysql_users:
      - name: reader
        password: reader
        priv: "sdj.*:SELECT"
        host: "%"
        update_password: on_create

      - name: editor
        password: editor
        priv: "sdj.*:SELECT,INSERT,UPDATE"
        host: "%"
        update_password: on_create

      - name: writer
        password: writer
        priv: "sdj.*:SELECT,INSERT,UPDATE,DELETE"
        host: "%"
        update_password: on_create

      - name: admin
        password: admin
        priv: "sdj.*:ALL"
        host: "%"
        update_password: on_create
      # sdj_test
      - name: test_reader
        password: reader
        priv: "sdj_test.*:SELECT"
        host: "%"
        update_password: on_create

      - name: test_editor
        password: editor
        priv: "sdj_test.*:SELECT,INSERT,UPDATE"
        host: "%"
        update_password: on_create

      - name: test_writer
        password: writer
        priv: "sdj_test.*:SELECT,INSERT,UPDATE,DELETE"
        host: "%"
        update_password: on_create

      - name: test_admin
        password: admin
        priv: "sdj_test.*:ALL"
        host: "%"
        update_password: on_create
      # luxurydata
      - name: luxurydata_reader
        password: reader
        priv: "luxurydata.*:SELECT"
        host: "%"
        update_password: on_create

      - name: luxurydata_editor
        password: editor
        priv: "luxurydata.*:SELECT,INSERT,UPDATE"
        host: "%"
        update_password: on_create

      - name: luxurydata_writer
        password: writer
        priv: "luxurydata.*:SELECT,INSERT,UPDATE,DELETE"
        host: "%"
        update_password: on_create

      - name: luxurydata_admin
        password: admin
        priv: "luxurydata.*:ALL"
        host: "%"
        update_password: on_create

      # product_service
      - name: product_service_reader
        password: reader
        priv: "product_service.*:SELECT"
        host: "%"
        update_password: on_create

      - name: product_service_editor
        password: editor
        priv: "product_service.*:SELECT,INSERT,UPDATE"
        host: "%"
        update_password: on_create

      - name: product_service_writer
        password: writer
        priv: "product_service.*:SELECT,INSERT,UPDATE,DELETE"
        host: "%"
        update_password: on_create

      - name: product_service_admin
        password: admin
        priv: "product_service.*:ALL"
        host: "%"
        update_password: on_create

      # sync_service
      - name: sync_service_reader
        password: reader
        priv: "sync_service.*:SELECT"
        host: "%"
        update_password: on_create

      - name: sync_service_editor
        password: editor
        priv: "sync_service.*:SELECT,INSERT,UPDATE"
        host: "%"
        update_password: on_create

      - name: sync_service_writer
        password: writer
        priv: "sync_service.*:SELECT,INSERT,UPDATE,DELETE"
        host: "%"
        update_password: on_create

      - name: sync_service_admin
        password: admin
        priv: "sync_service.*:ALL"
        host: "%"
        update_password: on_create
  tasks:
    - name: setup mysql software
      include_role:
        name: "{{ item }}"
      vars:
        mysql_bind_address: "0.0.0.0"
        mysql_root_password: "sdj"
        mysql_root_host: "%"
      loop:
        - mysql
