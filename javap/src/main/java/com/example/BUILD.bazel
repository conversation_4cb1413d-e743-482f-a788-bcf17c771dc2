load("@rules_java//java:defs.bzl", "java_binary")

java_binary(
    name = "hello",
    srcs = ["HelloWorld.java"],  # 指定 Java 源代码
    main_class = "com.example.HelloWorld",  # 指定 main class
    resources = ["//javap/src/main/resources:log4j2_resource"],  # 引用 src/main/resources 中的 filegroup 目标
    deps = [
        "@maven//:org_apache_logging_log4j_log4j_api",  # Log4j API
        "@maven//:org_apache_logging_log4j_log4j_core",  # Log4j 核心
    ],
)
