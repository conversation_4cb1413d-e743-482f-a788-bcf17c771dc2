{"level":"debug","timestamp":"2025-07-31T14:47:47+08:00","caller":"kernel/baseClient.go:457","content":"POST https://api.mch.weixin.qq.com/v3/pay/transactions/native request header: { Accept:*/*Content-Type:application/jsonAuthorization:WECHATPAY2-SHA256-RSA2048 mchid=\"1723592337\",nonce_str=\"WZYCKacUXgwkfPUkoH3SsJjPzNeR8NHM\",timestamp=\"1753944467\",serial_no=\"136CA165A629C60A1D048C71AAA9E3085A747F18\",signature=\"ToxSHBBoQHLM6dTQTOp6wVAc2xevnEfiqelNJc/bolWPBQ089UqAHNXfcshn8oAzsGOgN/qIzIbm4kn07U5dCxF3boYEAmAuEprO60BEkSIivPXvqgRzUMJohD3kYiVBcweN2C9JujVQR7P+LZ+I+JSHD2wn9o8KBFPu3BJCebn+BVn8GF8xcS/ohojKwGv0ANA7l+Mlb5tUX9Z4bhCFdXeFMiEkbFdfo5uYO9fBZpJegKUzs7gSvzDk1f1B6M6oUZnMADnM7VsHrzMuxdamESwqLoBlLWF+flcFCb/eWmw9im/uICF5nL8OhQAkvRrzMb776vQKUVW5Uc2FJO3qQg==\"} request body:"}
{"level":"debug","timestamp":"2025-07-31T14:47:47+08:00","caller":"kernel/baseClient.go:459","content":"------------------response content:HTTP/1.1 200 OK\r\nContent-Length: 52\r\nCache-Control: no-cache, must-revalidate\r\nConnection: keep-alive\r\nContent-Language: zh-CN\r\nContent-Type: application/json; charset=utf-8\r\nDate: Thu, 31 Jul 2025 06:47:48 GMT\r\nKeep-Alive: timeout=8\r\nRequest-Id: 0893A3ACC406109E06188ED18C5820C8FF0B2887E601-0\r\nServer: nginx\r\nWechatpay-Nonce: 9d3463fb9769faa788ac17ac785676b5\r\nWechatpay-Serial: PUB_KEY_ID_0117235923372025073100382009001007\r\nWechatpay-Signature: vai2JqHkLvgisIH7YZ24K+KBxMhVKqWe60ufrUdJue4C02SSQrPqwF1W6aMP+SaKvFJE8lhtr6c+MS5RlSSDAxoWoI2Tf80r8GHqn4kEYQHbm6tewJii/V0FEv0h6RocteBnvUKIRr2OpfOJcGX+tvS4pDw6Lp/3LeIxyAEcWCiv8KA+SusEb9l+P+hBp00NnsoaQVsLEHdEMbh+1kNjEM2HwGdupkc0RG1VIIHFaL84Wc1ktOf1o8cHcjFkyrEprnXIdVV6ZInscCOUzOBhGhyaEHtGemEqP7yG/5ya/C/0xKaz+dVf4C6m3fGezjAM2b4zmDozMOPCed3U7xgrnA==\r\nWechatpay-Signature-Type: WECHATPAY2-SHA256-RSA2048\r\nWechatpay-Timestamp: 1753944468\r\nX-Content-Type-Options: nosniff\r\n\r\n{\"code_url\":\"weixin://wxpay/bizpayurl?pr=HFwF0ihz1\"}"}
{"level":"debug","timestamp":"2025-07-31T14:49:34+08:00","caller":"kernel/baseClient.go:457","content":"POST https://api.mch.weixin.qq.com/v3/pay/transactions/native request header: { Accept:*/*Content-Type:application/jsonAuthorization:WECHATPAY2-SHA256-RSA2048 mchid=\"1723592337\",nonce_str=\"6chyfPG7uOWPgqUoS3g7RtgCz9O3ud1C\",timestamp=\"1753944573\",serial_no=\"136CA165A629C60A1D048C71AAA9E3085A747F18\",signature=\"dzdI4FLJpQuIdDMK8JBb3Zy4swrPVQ1ruTpKScNtl2Ei/R+5O2Cg8u6o1whOwRZGuiCYMDjsP2S+VnnkPMXfVpy3pMNjUo3DLhpFUt+/kLuaYG/bbpB1hiDCEeMtwavX2AU+urUURzmDcLkIdOF3etWtoXadkWABcLKhFygfDkjxxOTQMbxVeH6sGmbojkfBn6lIoLWuWr0573TdNNUBC/kVRt6VN/CJGpSEDZVV6yqVU4SYnBW18z2h4otq1qF2osLneHUYyD2QXTLiKxFbrmh1IwDui+0VWTactPi3zHOa9vhJ8dPQFrejEQi5Z+6DDsXJWHo/19R+r2Jv8Nbi+A==\"} request body:"}
{"level":"debug","timestamp":"2025-07-31T14:49:34+08:00","caller":"kernel/baseClient.go:459","content":"------------------response content:HTTP/1.1 200 OK\r\nContent-Length: 52\r\nCache-Control: no-cache, must-revalidate\r\nConnection: keep-alive\r\nContent-Language: zh-CN\r\nContent-Type: application/json; charset=utf-8\r\nDate: Thu, 31 Jul 2025 06:49:34 GMT\r\nKeep-Alive: timeout=8\r\nRequest-Id: 08FEA3ACC40610BF031886C18C5820F89E04288A8601-0\r\nServer: nginx\r\nWechatpay-Nonce: 7d7f64f255b7c3f74d3667dbb7c2b7e3\r\nWechatpay-Serial: PUB_KEY_ID_0117235923372025073100382009001007\r\nWechatpay-Signature: WBTwgPwfoo8ScKr02ntKnxBgqLiehjd9i/9wWm78qa6m2VMFJgn24Olw4ckBh6R5h6TeWP4X77h5n35/+OP+swpjbccljc6+zGZbamqHpwY8k1OPMp1uUl2V9Z4EwryL/u7DfwawRCMvzbXOpPR2scYhZ6p0/YG+8UzJ1G6XeWF3/lnUjleg1BUS6/Hwd1b500siROnZsqnnNp83qcMMeMw/XTCMnr0rQnZujos613i13f68sXEoWsc6VRgDqSPGGWOkyqTKQzQj2YK2zZ9dCOVTRf1Lp0Nc3D92M2gjVu0lxckeiTb6pmNqnzC753gWFnAw6bfRxmUVrjDfr3/s1A==\r\nWechatpay-Signature-Type: WECHATPAY2-SHA256-RSA2048\r\nWechatpay-Timestamp: 1753944574\r\nX-Content-Type-Options: nosniff\r\n\r\n{\"code_url\":\"weixin://wxpay/bizpayurl?pr=ZR6b4HOz1\"}"}
{"level":"debug","timestamp":"2025-07-31T14:52:48+08:00","caller":"kernel/baseClient.go:457","content":"POST https://api.mch.weixin.qq.com/v3/pay/transactions/native request header: { Content-Type:application/jsonAuthorization:WECHATPAY2-SHA256-RSA2048 mchid=\"1723592337\",nonce_str=\"H76dHOkDPbPzROoSf7PJ5qNH5egrjE4O\",timestamp=\"1753944767\",serial_no=\"136CA165A629C60A1D048C71AAA9E3085A747F18\",signature=\"klu9G1sRRhVoOe4aR2trUs/UxiEeDRa5/GMpHUn414SlGvwgYfnNwoHzuGCrTB9v5ygXxZobGkSNwOQCY4S3NwptyZjVnedEMYhf2DxBeIi0YuDDEZi4TuAaI6c+hnsjjEIXc6gw5d/djYDpxjNKQnWlHD+WOKCwIkupO+KFEHUEyj6D8evtfzaYhP87WQwfh1jlcoDj/R4m52xuZStJ8kqTOezh7GQuxpQ9/toPawFyiH+gv8h+BmwKfd55GFLA7QG11r6r6QxvQZ7fLM8o6AL/CtpbeGSyVv87wRxYLQ0//tp35qoAfDEQef8lAlnM81CwYB+jdOpA+Dttydd2tQ==\"Accept:*/*} request body:"}
{"level":"debug","timestamp":"2025-07-31T14:52:48+08:00","caller":"kernel/baseClient.go:459","content":"------------------response content:HTTP/1.1 200 OK\r\nContent-Length: 52\r\nCache-Control: no-cache, must-revalidate\r\nConnection: keep-alive\r\nContent-Language: zh-CN\r\nContent-Type: application/json; charset=utf-8\r\nDate: Thu, 31 Jul 2025 06:52:50 GMT\r\nKeep-Alive: timeout=8\r\nRequest-Id: 08C1A5ACC40610AA0618AEDA8C5820E4AE0128C0BD05-0\r\nServer: nginx\r\nWechatpay-Nonce: 29cab949a649483796a8008e97500a46\r\nWechatpay-Serial: PUB_KEY_ID_0117235923372025073100382009001007\r\nWechatpay-Signature: wpsCgawDnXJQP4FVASx4w3j7xkV9OOOPxTim6ITM81/+HGBkMmP/qDq23F3Q1ccrMDLt+CJ9WiIYKYH6Tc42wO5I1IDX3OCj6aqRc41SIvRiduNL/PAN0o66n0BN2Lu3dSj8bm3NFiTJqYniKJAD3P8gtqRy0HJMhBhExsdi03HC1hrGZd1FOU05k6inqxU4jgzBJL/118gT/YmUzan6oxaXreCyrpLezHnGu5gFcpC8O02NK2NECJFXa6cTKv5y12+dJI6BZfBDWTm5iu6PRMjt58MxlCAJ8pqflq51xAXbmiQEz8bMJ/JRaZ3NgEOXkC7q71gSNACooOJKjaXGLA==\r\nWechatpay-Signature-Type: WECHATPAY2-SHA256-RSA2048\r\nWechatpay-Timestamp: 1753944770\r\nX-Content-Type-Options: nosniff\r\n\r\n{\"code_url\":\"weixin://wxpay/bizpayurl?pr=UPKpnjFz1\"}"}
