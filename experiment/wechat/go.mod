module test_wap_pay

go 1.23.2

require (
	github.com/smartwalle/alipay/v3 v3.2.25
	github.com/smartwalle/xid v1.0.7
)

require (
	github.com/ArtisanCloud/PowerLibs/v3 v3.3.2 // indirect
	github.com/ArtisanCloud/PowerSocialite/v3 v3.0.8 // indirect
	github.com/ArtisanCloud/PowerWeChat/v3 v3.4.21 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/clbanning/mxj/v2 v2.7.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/redis/go-redis/v9 v9.6.3 // indirect
	github.com/smartwalle/ncrypto v1.0.4 // indirect
	github.com/smartwalle/ngx v1.0.9 // indirect
	github.com/smartwalle/nsign v1.0.9 // indirect
	github.com/wechatpay-apiv3/wechatpay-go v0.2.21 // indirect
	go.opentelemetry.io/otel v1.4.0 // indirect
	go.opentelemetry.io/otel/trace v1.4.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/crypto v0.35.0 // indirect
	gopkg.in/yaml.v2 v2.4.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
