package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/ArtisanCloud/PowerWeChat/v3/src/kernel"
	"github.com/ArtisanCloud/PowerWeChat/v3/src/payment"
	request2 "github.com/ArtisanCloud/PowerWeChat/v3/src/payment/order/request"
)

// 微信支付配置
const (
	MchID     = "1723592337"                              // 商户号
	MchKey    = ""                                        // 商户密钥
	AppID     = ""                                        // 应用ID
	NotifyURL = "https://www.weixin.qq.com/wxpay/pay.php" // 回调地址
)

// PaymentApp 全局支付应用实例
var PaymentApp *payment.Payment

// InitPaymentApp 初始化支付应用
func InitPaymentApp() error {
	config := &payment.UserConfig{
		AppID:     AppID,                                                        // 小程序、公众号或者企业微信的appid
		MchID:     MchID,                                                        // 商户号 mch_id
		Key:       MchKey,                                                       // 商户秘钥
		CertPath:  "../../golangp/apps/pointer_center/certs/apiclient_cert.pem", // 商户证书路径
		KeyPath:   "../../golangp/apps/pointer_center/certs/apiclient_key.pem",  // 商户证书秘钥路径
		SerialNo:  "136CA165A629C60A1D048C71AAA9E3085A747F18",                   // 商户证书序列号
		NotifyURL: NotifyURL,                                                    // 接收微信支付异步通知回调地址，通知url必须为直接可访问的url，不能携带参数。
		HttpDebug: true,                                                         // 是否开启http调试
		Debug:     false,                                                        // 沙箱模式，false=正式环境

		Cache: kernel.NewRedisClient(&kernel.UniversalOptions{}),
	}

	var err error
	PaymentApp, err = payment.NewPayment(config)
	if err != nil {
		return fmt.Errorf("init payment app error: %v", err)
	}

	return nil
}

// Services 服务结构体，模拟您提到的 services.PaymentApp.Order
type Services struct {
	PaymentApp *PaymentAppService
}

type PaymentAppService struct {
	Order *OrderService
}

type OrderService struct {
	app *payment.Payment
}

// TransactionNative Native支付预下单
func (o *OrderService) TransactionNative(options *request2.RequestNativePrepay) (map[string]interface{}, error) {
	// 发起Native支付预下单
	ctx := context.Background()
	response, err := o.app.Order.TransactionNative(ctx, options)
	if err != nil {
		return nil, fmt.Errorf("transaction native error: %v", err)
	}

	// 将响应转换为 map[string]interface{}
	result := map[string]interface{}{
		"code_url": response.CodeURL,
	}

	return result, nil
}

// 全局服务实例
var services *Services

// 初始化全局服务实例
func initServices() {
	services = &Services{
		PaymentApp: &PaymentAppService{
			Order: &OrderService{
				app: PaymentApp,
			},
		},
	}
}

func main() {
	// 初始化支付应用
	if err := InitPaymentApp(); err != nil {
		log.Fatalf("Failed to init payment app: %v", err)
	}

	// 初始化服务
	initServices()

	// 构建支付请求参数 - 按照您提供的格式
	options := &request2.RequestNativePrepay{
		Amount: &request2.NativeAmount{
			Total:    1,     // 1分钱，用于测试
			Currency: "CNY", // 人民币
		},
		Attach:      "12312312",
		Description: "123123",
		OutTradeNo:  fmt.Sprintf("5519778939773395659222598%03d", time.Now().Unix()%1000), // 生成唯一订单号
	}

	// 发起Native支付预下单 - 按照您提供的调用方式
	response, err := services.PaymentApp.Order.TransactionNative(options)
	if err != nil {
		log.Fatalf("Transaction native failed: %v", err)
	}

	// 输出结果
	fmt.Printf("Native支付预下单成功！\n")
	fmt.Printf("响应结果: %+v\n", response)

	// 检查是否有 code_url
	if codeURL, ok := response["code_url"].(string); ok {
		fmt.Printf("二维码链接: %s\n", codeURL)
		fmt.Printf("请使用微信扫描二维码进行支付\n")
	}

	fmt.Printf("\n注意事项：\n")
	fmt.Printf("1. 请确保已配置正确的商户号、证书和密钥\n")
	fmt.Printf("2. 订单号不能重复提交给微信\n")
	fmt.Printf("3. 二维码有效期为2小时\n")
}
