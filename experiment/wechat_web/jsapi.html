<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信支付测试 - JSAPI支付</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .title {
            color: #333;
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #07c160;
        }

        .btn {
            background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            width: 100%;
            margin-bottom: 20px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(7, 193, 96, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .payment-section {
            display: none;
            margin-top: 30px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .payment-section.show {
            display: block;
        }

        .payment-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: left;
        }

        .payment-info h3 {
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            color: #666;
            font-weight: 500;
        }

        .info-value {
            color: #333;
            font-weight: 600;
        }

        .status {
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            margin: 20px 0;
            text-align: center;
        }

        .status.waiting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.failed {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #07c160;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }

        .wechat-icon {
            color: #07c160;
            font-size: 24px;
            margin-right: 10px;
        }

        .payment-methods {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .method-btn {
            flex: 1;
            padding: 10px;
            border: 2px solid #e1e5e9;
            background: white;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .method-btn.active {
            border-color: #07c160;
            background: #f0fff4;
        }

        .method-btn:hover {
            border-color: #07c160;
        }

        .wechat-notice {
            background: #e8f5e8;
            border: 1px solid #07c160;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            color: #155724;
        }

        .wechat-notice strong {
            color: #07c160;
        }

        .pay-button {
            background: linear-gradient(135deg, #07c160 0%, #00d4aa 100%);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(7, 193, 96, 0.3);
        }

        .pay-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(7, 193, 96, 0.4);
        }

        .pay-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 4px 15px rgba(7, 193, 96, 0.3);
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">
            <span class="wechat-icon">💚</span>
            微信支付测试
        </h1>
        <p class="subtitle">JSAPI支付演示</p>

        <div class="payment-methods">
            <div class="method-btn" data-method="qrcode" onclick="window.location.href='index.html'">
                <strong>扫码支付</strong><br>
                <small>PC端推荐</small>
            </div>
            <div class="method-btn active" data-method="jsapi">
                <strong>JSAPI支付</strong><br>
                <small>微信内推荐</small>
            </div>
        </div>

        <div class="wechat-notice">
            <strong>注意：</strong>JSAPI支付需要在微信内置浏览器中使用，并且需要用户的OpenID。
            在实际应用中，OpenID通过微信授权获取。
        </div>

        <form id="paymentForm">
            <div class="form-group">
                <label for="userId">用户ID:</label>
                <input type="text" id="userId" name="userId" value="550e8400-e29b-41d4-a716-446655440000" required>
            </div>

            <div class="form-group">
                <label for="openid">用户OpenID:</label>
                <input type="text" id="openid" name="openid" value="oUpF8uMuAJO_M2pxb1Q9zNjWeS6o" required>
                <small style="color: #666;">测试用OpenID，实际使用时需要通过微信授权获取</small>
            </div>

            <div class="form-group">
                <label for="orderId">订单ID (可选):</label>
                <input type="text" id="orderId" name="orderId" placeholder="业务订单号">
            </div>

            <div class="form-group">
                <label for="totalFee">支付金额 (分):</label>
                <input type="number" id="totalFee" name="totalFee" value="100" min="1" required>
            </div>

            <div class="form-group">
                <label for="body">商品描述:</label>
                <input type="text" id="body" name="body" value="测试商品" required>
            </div>

            <div class="form-group">
                <label for="detail">商品详情 (可选):</label>
                <textarea id="detail" name="detail" rows="3" placeholder="商品详细描述"></textarea>
            </div>

            <div class="form-group">
                <label for="expireTime">过期时间 (分钟):</label>
                <input type="number" id="expireTime" name="expireTime" value="10" min="1" max="120">
            </div>

            <button type="submit" class="btn" id="createPaymentBtn">
                创建JSAPI支付
            </button>
        </form>

        <div id="paymentSection" class="payment-section">
            <div class="payment-info">
                <h3>支付信息</h3>
                <div class="info-row">
                    <span class="info-label">订单号:</span>
                    <span class="info-value" id="outTradeNo">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">支付金额:</span>
                    <span class="info-value" id="payAmount">-</span>
                </div>
                <div class="info-row">
                    <span class="info-label">过期时间:</span>
                    <span class="info-value" id="expireTimeDisplay">-</span>
                </div>
            </div>

            <button class="pay-button" id="payButton">
                <span class="wechat-icon">💰</span>
                立即支付
            </button>

            <div id="paymentStatus" class="status waiting">
                <div class="loading"></div>
                等待支付中...
            </div>

            <button class="btn" onclick="location.reload()">重新创建订单</button>
        </div>

        <div id="errorMessage" class="error-message" style="display: none;"></div>
        <div id="successMessage" class="success-message" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8012/api/v1';
        let pollingInterval = null;
        let currentOutTradeNo = null;
        let paymentData = null;

        // 检查是否在微信内置浏览器
        function isWeChatBrowser() {
            const ua = navigator.userAgent.toLowerCase();
            return ua.indexOf('micromessenger') !== -1;
        }

        // 显示微信浏览器提示
        if (!isWeChatBrowser()) {
            const notice = document.querySelector('.wechat-notice');
            notice.innerHTML = '<strong>提示：</strong>当前不在微信浏览器中，JSAPI支付可能无法正常工作。请在微信中打开此页面。';
            notice.style.background = '#fff3cd';
            notice.style.borderColor = '#ffc107';
            notice.style.color = '#856404';
        }

        // 表单提交处理
        document.getElementById('paymentForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = {
                user_id: formData.get('userId'),
                openid: formData.get('openid'),
                order_id: formData.get('orderId') || undefined,
                total_fee: parseInt(formData.get('totalFee')),
                body: formData.get('body'),
                detail: formData.get('detail') || undefined,
                expire_time: parseInt(formData.get('expireTime'))
            };

            await createJSAPIPayment(data);
        });

        // 创建JSAPI支付
        async function createJSAPIPayment(data) {
            const btn = document.getElementById('createPaymentBtn');
            const errorDiv = document.getElementById('errorMessage');
            const successDiv = document.getElementById('successMessage');
            
            btn.disabled = true;
            btn.innerHTML = '<div class="loading"></div>创建中...';
            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';

            try {
                const response = await fetch(`${API_BASE}/wechat/jsapi`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    currentOutTradeNo = result.data.out_trade_no;
                    paymentData = result.data.payment_data;
                    showPaymentSection(result.data);
                    startPolling();
                } else {
                    throw new Error(result.message || '创建支付失败');
                }
            } catch (error) {
                console.error('创建支付失败:', error);
                errorDiv.textContent = error.message;
                errorDiv.style.display = 'block';
            } finally {
                btn.disabled = false;
                btn.innerHTML = '创建JSAPI支付';
            }
        }

        // 显示支付区域
        function showPaymentSection(data) {
            const paymentSection = document.getElementById('paymentSection');
            
            // 更新支付信息
            document.getElementById('outTradeNo').textContent = data.out_trade_no;
            document.getElementById('payAmount').textContent = `¥${(document.getElementById('totalFee').value / 100).toFixed(2)}`;
            document.getElementById('expireTimeDisplay').textContent = data.expire_time;

            paymentSection.classList.add('show');
        }

        // 支付按钮点击处理
        document.getElementById('payButton').addEventListener('click', function() {
            if (!isWeChatBrowser()) {
                alert('请在微信浏览器中使用JSAPI支付功能');
                return;
            }

            // 在实际应用中，这里会调用微信JSAPI
            // 由于这是演示，我们模拟支付流程
            simulateWeChatPay();
        });

        // 模拟微信支付流程
        function simulateWeChatPay() {
            const payButton = document.getElementById('payButton');
            const statusDiv = document.getElementById('paymentStatus');
            
            payButton.disabled = true;
            payButton.innerHTML = '<div class="loading"></div>调起支付中...';
            
            // 模拟调起微信支付
            setTimeout(() => {
                // 在实际应用中，这里会调用 WeixinJSBridge.invoke('getBrandWCPayRequest', ...)
                console.log('模拟调起微信支付，支付参数:', paymentData);
                
                // 模拟用户支付操作
                const userConfirm = confirm('模拟微信支付：点击"确定"表示支付成功，点击"取消"表示支付失败');
                
                if (userConfirm) {
                    // 模拟支付成功，继续轮询等待后端确认
                    statusDiv.className = 'status waiting';
                    statusDiv.innerHTML = '<div class="loading"></div>支付处理中，请稍候...';
                } else {
                    // 模拟支付取消
                    statusDiv.className = 'status failed';
                    statusDiv.innerHTML = '❌ 支付已取消';
                    clearInterval(pollingInterval);
                }
                
                payButton.disabled = false;
                payButton.innerHTML = '<span class="wechat-icon">💰</span>立即支付';
            }, 1000);
        }

        // 开始轮询支付状态
        function startPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }

            pollingInterval = setInterval(async () => {
                if (currentOutTradeNo) {
                    await checkPaymentStatus(currentOutTradeNo);
                }
            }, 2000); // 每2秒查询一次
        }

        // 检查支付状态
        async function checkPaymentStatus(outTradeNo) {
            try {
                const response = await fetch(`${API_BASE}/wechat/status?out_trade_no=${outTradeNo}`);
                const result = await response.json();

                if (result.success) {
                    const status = result.data.status;
                    const statusDiv = document.getElementById('paymentStatus');

                    if (status === 'SUCCESS') {
                        statusDiv.className = 'status success';
                        statusDiv.innerHTML = '✅ 支付成功！';
                        clearInterval(pollingInterval);
                        
                        const successDiv = document.getElementById('successMessage');
                        successDiv.innerHTML = `
                            <strong>支付成功！</strong><br>
                            订单号: ${result.data.out_trade_no}<br>
                            微信支付单号: ${result.data.transaction_id || '暂无'}<br>
                            支付时间: ${result.data.pay_time || '刚刚'}
                        `;
                        successDiv.style.display = 'block';
                    } else if (status === 'CLOSED' || status === 'REVOKED') {
                        statusDiv.className = 'status failed';
                        statusDiv.innerHTML = '❌ 支付已关闭';
                        clearInterval(pollingInterval);
                    }
                }
            } catch (error) {
                console.error('查询支付状态失败:', error);
            }
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
        });

        // 实际的微信JSAPI支付调用示例（注释掉的代码）
        /*
        function callWeChatPay(paymentData) {
            if (typeof WeixinJSBridge == "undefined") {
                if (document.addEventListener) {
                    document.addEventListener('WeixinJSBridgeReady', function() {
                        onBridgeReady(paymentData);
                    }, false);
                } else if (document.attachEvent) {
                    document.attachEvent('WeixinJSBridgeReady', function() {
                        onBridgeReady(paymentData);
                    });
                    document.attachEvent('onWeixinJSBridgeReady', function() {
                        onBridgeReady(paymentData);
                    });
                }
            } else {
                onBridgeReady(paymentData);
            }
        }

        function onBridgeReady(paymentData) {
            WeixinJSBridge.invoke(
                'getBrandWCPayRequest', 
                JSON.parse(paymentData),
                function(res) {
                    if (res.err_msg == "get_brand_wcpay_request:ok") {
                        // 支付成功
                        console.log('支付成功');
                    } else {
                        // 支付失败
                        console.log('支付失败:', res.err_msg);
                    }
                }
            );
        }
        */
    </script>
</body>
</html>
