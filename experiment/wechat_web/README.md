# 微信支付测试页面

这个目录包含了微信支付的前端测试页面，用于测试pointer_center项目中的微信支付功能。

## 文件说明

- `index.html` - 微信扫码支付测试页面
- `jsapi.html` - 微信JSAPI支付测试页面
- `README.md` - 本说明文件

## 功能特性

### 扫码支付 (index.html)
- ✅ 创建微信支付二维码
- ✅ 实时显示二维码
- ✅ 轮询查询支付状态
- ✅ 支付成功/失败状态显示
- ✅ 响应式设计，支持移动端

### JSAPI支付 (jsapi.html)
- ✅ 创建微信JSAPI支付
- ✅ 模拟微信内支付流程
- ✅ 轮询查询支付状态
- ✅ 微信浏览器检测
- ✅ 支付成功/失败状态显示

## 使用方法

### 1. 启动后端服务

确保pointer_center服务已启动并运行在 `http://localhost:8012`

```bash
cd golangp/apps/pointer_center
go run cmd/server/main.go
```

### 2. 配置微信支付

在 `app.env` 文件中配置微信支付参数：

```env
# WeChat Pay Configuration
WECHAT_APP_ID=your_wechat_app_id
WECHAT_MCH_ID=your_wechat_mch_id
WECHAT_API_KEY=your_wechat_api_key
WECHAT_API_V3_KEY=your_wechat_api_v3_key
WECHAT_CERT_FILE=path/to/apiclient_cert.pem
WECHAT_KEY_FILE=path/to/apiclient_key.pem
WECHAT_SERIAL_NO=your_cert_serial_no
WECHAT_PLATFORM_CERT_FILE=path/to/wechatpay_cert.pem
WECHAT_PLATFORM_SERIAL_NO=your_platform_serial_no
WECHAT_API_VERSION=v3
WECHAT_SANDBOX=true
WECHAT_NOTIFY_URL=http://localhost:8012/api/wechat/notify
WECHAT_RETURN_URL=http://localhost:8012/wechat/callback
WECHAT_TIMEOUT_EXPRESS=30
```

### 3. 打开测试页面

#### 扫码支付测试
直接在浏览器中打开 `index.html`：
```
file:///path/to/golangp/apps/pointer_center/experiment/wechat_web/index.html
```

#### JSAPI支付测试
在微信内置浏览器中打开 `jsapi.html`：
```
file:///path/to/golangp/apps/pointer_center/experiment/wechat_web/jsapi.html
```

### 4. 测试流程

#### 扫码支付流程：
1. 填写支付信息（用户ID、金额、商品描述等）
2. 点击"创建支付二维码"
3. 使用微信扫描生成的二维码
4. 完成支付后页面会自动显示支付成功状态

#### JSAPI支付流程：
1. 在微信浏览器中打开页面
2. 填写支付信息（包括OpenID）
3. 点击"创建JSAPI支付"
4. 点击"立即支付"按钮
5. 在弹出的确认框中选择支付结果（模拟）
6. 页面会显示相应的支付状态

## API接口

测试页面调用以下API接口：

### 创建扫码支付
```
POST /api/wechat/qrcode
Content-Type: application/json

{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "total_fee": 100,
  "body": "测试商品",
  "expire_time": 10
}
```

### 创建JSAPI支付
```
POST /api/wechat/jsapi
Content-Type: application/json

{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "openid": "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o",
  "total_fee": 100,
  "body": "测试商品",
  "expire_time": 10
}
```

### 查询支付状态
```
GET /api/wechat/status?out_trade_no=WX1640995200
```

## 注意事项

1. **测试环境**：当前配置为沙箱环境，实际部署时需要修改为生产环境配置
2. **HTTPS要求**：生产环境中微信支付要求使用HTTPS
3. **域名配置**：需要在微信商户平台配置授权域名
4. **OpenID获取**：JSAPI支付需要真实的用户OpenID，测试时使用模拟值
5. **证书文件**：确保微信支付证书文件路径正确且可访问

## 开发说明

### 前端技术栈
- 原生HTML/CSS/JavaScript
- QRCode.js (二维码生成)
- 响应式设计

### 后端接口
- Go + Gin框架
- PowerWeChat SDK
- PostgreSQL数据库
- Redis缓存（可选）

### 支付流程
1. 前端创建支付请求
2. 后端调用微信支付API
3. 返回支付参数给前端
4. 前端轮询查询支付状态
5. 微信异步通知后端更新状态

## 故障排除

### 常见问题

1. **二维码生成失败**
   - 检查后端服务是否正常运行
   - 检查微信支付配置是否正确

2. **支付状态一直显示等待中**
   - 检查微信支付配置
   - 查看后端日志确认是否收到微信通知

3. **JSAPI支付无法调起**
   - 确保在微信浏览器中打开
   - 检查OpenID是否有效

4. **网络请求失败**
   - 检查API地址是否正确
   - 确认后端服务端口和CORS配置

### 调试方法

1. 打开浏览器开发者工具查看网络请求
2. 查看后端服务日志
3. 检查微信商户平台的交易记录
4. 使用微信支付接口调试工具

## 更新日志

- v1.0.0 - 初始版本，支持扫码支付和JSAPI支付测试
