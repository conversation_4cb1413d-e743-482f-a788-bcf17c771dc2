package main

/*
支付宝支付测试程序 - 秘钥模式

使用方法：
go run test_pay_key.go

说明：
- 使用秘钥模式，只需要配置应用私钥和支付宝公钥
- 无需下载和配置证书文件，更简单方便
- 适合沙箱环境测试和开发调试
*/

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/smartwalle/alipay/v3"
	"github.com/smartwalle/xid"
)

var client *alipay.Client

const (
	// 应用ID（沙箱环境）
	kAppId = "2021005178619320"

	// 应用私钥（用于签名）
	kPrivateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCUjXdaHAO0lO/zeQIyVBByJwWnj39fDcMosXC0Icfa8rK/RnIwNdEVA2RxV6OlsV2qQV4LNL1Hljehj8Z72MpmB9npwg+QpSM4h9aJRTdNzlUcAcfjYMBjs1JRCwmVuWPfLql+cvfzeeVQkeIM15ofhQCDCC8Bd2UIT7HqRgeHCObcs/H0XhW6lfAegI1+VRwiHLR63jHT0mxhyrWWqnT9A35rCLyXVOX0RAM+CAItWvbngg8DFmZpkDJ/dHhdQtof8myyTCTe04+6QLzlSsGgPKRykFiy5QLLkWNml6O7pGnyFhdsy2BFWxfP+q31Gjcdaq+bjZYthGrAjsoi68NPAgMBAAECggEAIZAg0iwYLjmkSRcjBun1WUU1wtjqBzjdyhjlBN3JAM/q0MqCzB+soJnGnNEXOT1skyX3sIJ27XCkGw2X461kdU76zhS5XY3h+ki+kP8hcqPWmOBSIC5g6WW/iG38+WYMiYVKaDZjjRASXnTHZO1ldg+fwPsdW7B1FD6lTPALjVd9u+/APOHa0VyD66ZczOKgrugzbdDjxae3sH7DlagkWoQY88vJUbwveJd2wlWUPIdywvH9vdaHKcprZ5PzUUADEbH0RhSEHGOWWgFb+eXwUMNKoSxucL+orwVcYJ+HPPW6qIFx+LXifJy2NuXnHW6Uei2Ac5lhSyigEQUdsEJ7sQKBgQDc11aJFCiTDNDQ1OTRxmThDMP391Y27gsOAEhSgweHwh0FjMFyqGNdOjeLCzGq57dOKyRN76Z0luNC0iqPZORtvdRP8Ka0Z8kRzuhn50KJsO5AppQmW0dY6gbU6M4fR/zSg2uU6pvtltkaEnILKcDFkroQQtTG7M4VLtdLr+kb+QKBgQCsM+qFPH8q1DuH0I/znCW7FTFqROeLTd48eScrEemJBzuLNsBczdfodeD7OBHtN/yAvUCTWWJoP3dEdnnfMsh4JRqaacTv0XxPhbl/RuysWPvuZUlmNNzvLmzY1bilRn78afqaH26wK6SbY6mxRt4+KaV+okFlPm51oR5D+JDbhwKBgQCDMXXaB1y3RvTCKscoi9igDWpZ49iW10E/9xD/25FcqAd/pYfCaqBk4NHgSy0dX3x49o4hso3LU1v89UeNWh2NyCjZuJgAMmuzpZxRJYRUsgRH6e22/6DaBGU8lHTnBmf+U05U8C7jrSqaH30afppjhO/9+wXWixU+Zk5GS2GLWQKBgCoiDbiJw8eT4/joA8CkLOsoVU3eDBUeeUisj/POzygS1/PI8qoCR1h+NwzgfCphb2uEYp9q2FQPazwaDNmakEgtM+QiCuhuhg7i9i75CIFmyEVdDOdv+bqfzkwDhskRhZJ2z7UUCb4a/4xxhxxz7X6iK/be9kcyTk6DnGcfEzpTAoGAd0peJsY5IXmqAzZpG4V+xCHjeEXkiC8IxsKYsSY1m/KyXZrskfuFL5zooFsNy7TKEloWAxeuGrF+2+IEbZKr4OwhD0rTDBg/ozXaRlooMgcggr7BH9OXUFf0G80pxn8Wo6OkHyMB4cDi5dqDPFk5pADqnhIHOQHZ9ZkEZZDNx94="

	// 支付宝公钥（沙箱环境，用于验证支付宝返回的数据）
	// 注意：这是沙箱环境的标准公钥，生产环境需要替换为实际的支付宝公钥
	// 格式：PKCS#1 RSA公钥格式
	kAliPayPublicKey = `MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs4PhTpLRIaWuCtctx4612Td4eyoEdLd9drcKukXJgQjaW19/TpfymrJ3QIeE0Rkq1k4AGDkwgvvcbcNF8fupNxZV3ag8zby58a/NvQYH7aiK0ZwfH5yOwDPDrmcHy5biFk0450ChNN7wEJJ+jcc0Ao7Uo4ISoRfDlC1anvQQ+lXzyH6ntcsO+ZEre3a/WH0j9WUGxIvZ0IW1rrhQ8SAloLOZ30szCPlm0rhQlzQXSjUaun+6UHW8tEScKn0mZVqb1Q8IFrg2ZrA2klNodUfghbA9/+pxpc1Oozw3nrvnj2Ks+cfkdOxd5dzbzIxz7OQXIbDmPVqtsVZ4r26zzWExjQIDAQAB`

	// 服务器端口
	kServerPort = "8082"

	// 回调地址域名（需要根据实际情况修改）
	kServerDomain = "https://6a757796.r26.cpolar.top"
)

func main() {
	var err error

	// 初始化支付宝客户端（秘钥模式）
	if err = initAlipayClient(); err != nil {
		log.Printf("❌ 初始化支付宝客户端失败: %v", err)
		return
	}

	// 设置路由
	setupRoutes()

	log.Printf("🚀 支付宝测试服务器启动成功（秘钥模式）")
	log.Printf("📍 服务器地址: http://localhost:%s", kServerPort)
	log.Printf("🔑 使用秘钥模式，无需证书文件")
	log.Printf("🌐 访问 http://localhost:%s 开始测试", kServerPort)
	log.Printf("🌍 外网访问地址: %s", kServerDomain)

	// 获取并显示当前服务器的出口IP（用于白名单配置）
	go func() {
		if ip, err := getCurrentServerIP(); err == nil {
			log.Printf("🔍 当前服务器出口IP: %s （请将此IP添加到支付宝白名单）", ip)
		} else {
			log.Printf("⚠️  无法获取服务器出口IP: %v", err)
			log.Printf("💡 请手动获取服务器IP并添加到支付宝白名单")
		}
	}()

	// 启动HTTP服务器
	if err := http.ListenAndServe(":"+kServerPort, nil); err != nil {
		log.Printf("❌ 服务器启动失败: %v", err)
	}
}

// initAlipayClient 初始化支付宝客户端（秘钥模式）
func initAlipayClient() error {
	var err error

	// 创建支付宝客户端 (false=沙箱环境, true=生产环境)
	isProduction := true
	if client, err = alipay.New(kAppId, kPrivateKey, isProduction); err != nil {
		return fmt.Errorf("创建支付宝客户端失败: %v", err)
	}

	if isProduction {
		log.Println("✅ 支付宝客户端创建成功（生产环境）")
		log.Printf("⚠️  注意：当前使用生产环境，应用ID: %s", kAppId)
		log.Printf("💡 如果这是沙箱应用ID，请将 isProduction 设置为 false")
	} else {
		log.Println("✅ 支付宝客户端创建成功（沙箱环境）")
	}

	// 设置支付宝公钥（秘钥模式的核心配置）
	log.Println("🔑 正在设置支付宝公钥...")
	if err = client.LoadAliPayPublicKey(kAliPayPublicKey); err != nil {
		log.Printf("❌ 设置支付宝公钥失败: %v", err)
		log.Println("💡 提示：公钥格式可能不正确，尝试不设置公钥继续运行（仅用于测试）")
		log.Println("⚠️  警告：不设置公钥将无法验证支付宝返回的签名，仅适用于开发测试")

		// 在开发测试环境中，可以选择不设置公钥继续运行
		// 但这会影响签名验证功能
		log.Println("🚧 继续初始化，但签名验证功能将受限")
	} else {
		log.Println("✅ 支付宝公钥设置成功")
	}

	// 设置内容加密密钥（可选，用于敏感信息加密）
	if err = client.SetEncryptKey("p/udwlZlkp83XYQykewreQ=="); err != nil {
		log.Printf("⚠️  设置内容加密密钥失败: %v", err)
		log.Println("💡 提示：内容加密密钥是可选的，不影响基本支付功能")
	} else {
		log.Println("✅ 内容加密密钥设置成功")
	}

	log.Println("🔑 秘钥模式初始化完成，无需加载证书文件")
	log.Println("📝 注意：如果公钥设置失败，回调验证功能可能受影响")
	return nil
}

// setupRoutes 设置HTTP路由
func setupRoutes() {
	http.HandleFunc("/", paymentPage)
	http.HandleFunc("/alipay/pay", pay)
	http.HandleFunc("/alipay/qrcode", qrcodePay)
	http.HandleFunc("/alipay/callback", callback)
	http.HandleFunc("/alipay/notify", notify)
	http.HandleFunc("/alipay/status", checkPaymentStatus)
	http.HandleFunc("/whitelist", whitelistInfo)
	http.HandleFunc("/app/check", checkAppStatus)

	log.Println("✅ HTTP路由设置完成")
}

// getCurrentServerIP 获取当前服务器的出口IP地址
func getCurrentServerIP() (string, error) {
	// 使用多个IP查询服务，提高成功率
	services := []string{
		"https://api.ipify.org",
		"https://ipinfo.io/ip",
		"https://icanhazip.com",
		"https://ident.me",
	}

	client := &http.Client{Timeout: 10 * time.Second}

	for _, service := range services {
		resp, err := client.Get(service)
		if err != nil {
			continue
		}
		defer resp.Body.Close()

		if resp.StatusCode == http.StatusOK {
			body, err := io.ReadAll(resp.Body)
			if err != nil {
				continue
			}
			ip := strings.TrimSpace(string(body))
			if ip != "" {
				return ip, nil
			}
		}
	}

	return "", fmt.Errorf("无法获取服务器出口IP")
}

// whitelistInfo 显示白名单配置信息
func whitelistInfo(writer http.ResponseWriter, request *http.Request) {
	writer.Header().Set("Content-Type", "application/json; charset=utf-8")

	// 获取服务器IP
	serverIP, err := getCurrentServerIP()
	if err != nil {
		serverIP = "无法获取"
	}

	response := map[string]interface{}{
		"success": true,
		"message": "白名单配置信息",
		"data": map[string]interface{}{
			"app_id":          kAppId,
			"server_ip":       serverIP,
			"callback_domain": kServerDomain,
			"environment":     "production", // 正式环境
			"whitelist_guide": map[string]interface{}{
				"step1": "登录支付宝开放平台 https://open.alipay.com/",
				"step2": "进入控制台 → 应用管理 → 选择应用 " + kAppId,
				"step3": "应用配置 → 接口加签方式 → 服务器白名单",
				"step4": "添加服务器IP: " + serverIP,
				"step5": "确保回调地址域名已备案: " + kServerDomain,
			},
			"common_errors": map[string]string{
				"ACCESS_FORBIDDEN":  "服务器IP未加入白名单",
				"INVALID_PARAMETER": "参数错误或应用配置问题",
				"SYSTEM_ERROR":      "系统错误，请稍后重试",
			},
		},
	}

	json.NewEncoder(writer).Encode(response)
}

// checkAppStatus 检查应用状态和权限
func checkAppStatus(writer http.ResponseWriter, request *http.Request) {
	writer.Header().Set("Content-Type", "application/json; charset=utf-8")

	// 尝试调用一个简单的API来检查应用状态
	var p = alipay.NewPayload("alipay.system.oauth.token")
	p.AddBizField("grant_type", "authorization_code")
	p.AddBizField("code", "test_code") // 这会失败，但能告诉我们应用状态

	var rsp map[string]interface{}
	err := client.Request(context.Background(), p, &rsp)

	response := map[string]interface{}{
		"success": true,
		"message": "应用状态检查",
		"data": map[string]interface{}{
			"app_id":      kAppId,
			"environment": "production",
			"test_result": map[string]interface{}{},
		},
	}

	if err != nil {
		response["data"].(map[string]interface{})["test_result"] = map[string]interface{}{
			"status": "error",
			"error":  err.Error(),
			"note":   "网络或配置错误",
		}
	} else {
		response["data"].(map[string]interface{})["test_result"] = map[string]interface{}{
			"status":   "connected",
			"response": rsp,
			"note":     "应用可以正常连接支付宝API",
		}
	}

	// 添加常见问题检查清单
	response["data"].(map[string]interface{})["checklist"] = map[string]interface{}{
		"app_online":  "应用是否已上线？",
		"permissions": "应用是否有当面付/扫码支付权限？",
		"whitelist":   "服务器IP是否已添加到白名单？",
		"signature":   "应用签名配置是否正确？",
		"domain":      "回调域名是否可访问？",
		"environment": "确认使用正式环境而非沙箱环境",
	}

	json.NewEncoder(writer).Encode(response)
}

// pay 处理网页支付请求 - 返回支付URL而不是跳转
func pay(writer http.ResponseWriter, request *http.Request) {
	// 设置CORS头
	writer.Header().Set("Access-Control-Allow-Origin", "*")
	writer.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	writer.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	writer.Header().Set("Content-Type", "application/json")

	// 处理OPTIONS预检请求
	if request.Method == "OPTIONS" {
		writer.WriteHeader(http.StatusOK)
		return
	}

	var tradeNo = fmt.Sprintf("WEB_%d", xid.Next())

	var p = alipay.TradePagePay{}
	p.NotifyURL = kServerDomain + "/alipay/notify"
	p.ReturnURL = kServerDomain + "/alipay/callback"
	p.Subject = "秘钥模式网页支付测试:" + tradeNo
	p.OutTradeNo = tradeNo
	p.TotalAmount = "0.01"
	p.ProductCode = "FAST_INSTANT_TRADE_PAY"

	// 设置二维码支付模式
	// 模式4: 订单码-可定义宽度的嵌入式二维码，商户可根据需要设定二维码的大小
	p.QRPayMode = "4"

	log.Printf("🛒 创建网页支付订单: %s, 金额: %s", tradeNo, p.TotalAmount)

	url, err := client.TradePagePay(p)
	if err != nil {
		log.Printf("❌ 创建支付订单失败: %v", err)
		response := map[string]interface{}{
			"success": false,
			"message": "创建支付订单失败",
			"error":   err.Error(),
		}
		writer.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(writer).Encode(response)
		return
	}

	log.Printf("✅ 支付订单创建成功，返回支付URL")

	// 返回JSON格式的响应
	response := map[string]interface{}{
		"success": true,
		"message": "支付订单创建成功",
		"data": map[string]interface{}{
			"payment_url":  url.String(),
			"out_trade_no": tradeNo,
			"amount":       "0.01",
			"subject":      "秘钥模式网页支付测试:" + tradeNo,
		},
	}

	json.NewEncoder(writer).Encode(response)
}

// callback 处理支付回调
func callback(writer http.ResponseWriter, request *http.Request) {
	request.ParseForm()

	// 验证签名（如果公钥设置成功的话）
	if err := client.VerifySign(request.Form); err != nil {
		log.Printf("⚠️  回调验证签名失败: %v", err)
		log.Println("💡 提示：可能是公钥配置问题，继续处理回调（仅用于测试）")
		// 在测试环境中，即使签名验证失败也继续处理
	} else {
		log.Println("✅ 回调验证签名通过")
	}

	// 查询订单状态
	var outTradeNo = request.Form.Get("out_trade_no")
	var p = alipay.TradeQuery{}
	p.OutTradeNo = outTradeNo

	rsp, err := client.TradeQuery(context.Background(), p)
	if err != nil {
		log.Printf("❌ 验证订单 %s 信息失败: %v", outTradeNo, err)
		writer.WriteHeader(http.StatusBadRequest)
		writer.Write([]byte(fmt.Sprintf("验证订单 %s 信息失败: %s", outTradeNo, err.Error())))
		return
	}

	if rsp.IsFailure() {
		log.Printf("❌ 订单 %s 验证失败: %s-%s", outTradeNo, rsp.Msg, rsp.SubMsg)
		writer.WriteHeader(http.StatusBadRequest)
		writer.Write([]byte(fmt.Sprintf("验证订单 %s 信息失败: %s-%s", outTradeNo, rsp.Msg, rsp.SubMsg)))
		return
	}

	log.Printf("✅ 订单 %s 支付成功", outTradeNo)
	writer.WriteHeader(http.StatusOK)
	writer.Write([]byte(fmt.Sprintf("🎉 订单 %s 支付成功！", outTradeNo)))
}

// notify 处理支付异步通知
func notify(writer http.ResponseWriter, request *http.Request) {
	request.ParseForm()

	// 解析异步通知
	var notification, err = client.DecodeNotification(request.Form)
	if err != nil {
		log.Printf("❌ 解析异步通知失败: %v", err)
		return
	}

	log.Printf("📨 收到异步通知: %s, 订单号: %s", notification.NotifyId, notification.OutTradeNo)

	// 验证订单信息
	var p = alipay.NewPayload("alipay.trade.query")
	p.AddBizField("out_trade_no", notification.OutTradeNo)

	var rsp *alipay.TradeQueryRsp
	if err = client.Request(context.Background(), p, &rsp); err != nil {
		log.Printf("❌ 异步通知验证订单 %s 信息失败: %v", notification.OutTradeNo, err)
		return
	}

	if rsp.IsFailure() {
		log.Printf("❌ 异步通知验证订单 %s 失败: %s-%s", notification.OutTradeNo, rsp.Msg, rsp.SubMsg)
		return
	}

	log.Printf("✅ 异步通知验证成功，订单 %s 支付完成", notification.OutTradeNo)

	// 返回成功响应给支付宝
	client.ACKNotification(writer)
}

// QRCodePayRequest 二维码支付请求结构
type QRCodePayRequest struct {
	Amount    string `json:"amount"`
	Subject   string `json:"subject"`
	Body      string `json:"body"`
	UserID    string `json:"user_id"`
	OrderID   string `json:"order_id"`
	ExtraData string `json:"extra_data"`
}

// QRCodePayResponse 二维码支付响应结构
type QRCodePayResponse struct {
	Success bool           `json:"success"`
	Message string         `json:"message"`
	Data    *QRCodePayData `json:"data,omitempty"`
	Error   string         `json:"error,omitempty"`
}

type QRCodePayData struct {
	QRCode     string `json:"qr_code"`
	OutTradeNo string `json:"out_trade_no"`
	ExpireTime int    `json:"expire_time"`
	Amount     string `json:"amount"`
	Subject    string `json:"subject"`
}

// PaymentStatusResponse 支付状态响应结构
type PaymentStatusResponse struct {
	Success bool               `json:"success"`
	Message string             `json:"message"`
	Data    *PaymentStatusData `json:"data,omitempty"`
}

type PaymentStatusData struct {
	Status     string `json:"status"` // WAIT_BUYER_PAY, TRADE_SUCCESS, TRADE_CLOSED
	OutTradeNo string `json:"out_trade_no"`
	TradeNo    string `json:"trade_no"`
	Amount     string `json:"amount"`
}

// qrcodePay 处理二维码支付请求
func qrcodePay(writer http.ResponseWriter, request *http.Request) {
	// 设置CORS头
	writer.Header().Set("Access-Control-Allow-Origin", "*")
	writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	writer.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	writer.Header().Set("Content-Type", "application/json")

	// 处理OPTIONS预检请求
	if request.Method == "OPTIONS" {
		writer.WriteHeader(http.StatusOK)
		return
	}

	if request.Method != "POST" {
		response := QRCodePayResponse{
			Success: false,
			Message: "只支持POST请求",
			Error:   "Method not allowed",
		}
		writer.WriteHeader(http.StatusMethodNotAllowed)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 解析请求参数
	var req QRCodePayRequest
	if err := json.NewDecoder(request.Body).Decode(&req); err != nil {
		response := QRCodePayResponse{
			Success: false,
			Message: "请求参数格式错误",
			Error:   err.Error(),
		}
		writer.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 验证必要参数
	if req.Amount == "" {
		response := QRCodePayResponse{
			Success: false,
			Message: "支付金额不能为空",
			Error:   "amount is required",
		}
		writer.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(writer).Encode(response)
		return
	}

	if req.Subject == "" {
		req.Subject = "秘钥模式二维码支付测试"
	}

	// 生成商户订单号
	outTradeNo := fmt.Sprintf("QR_%d_%d", time.Now().Unix(), xid.Next())

	// 创建支付宝二维码支付请求
	var p = alipay.TradePreCreate{}
	p.NotifyURL = kServerDomain + "/alipay/notify"
	p.Subject = req.Subject
	p.OutTradeNo = outTradeNo
	p.TotalAmount = req.Amount
	p.TimeoutExpress = "10m" // 10分钟过期

	// 设置二维码支付模式 - 对于TradePreCreate，使用AddBizField方法
	// 模式4: 订单码-可定义宽度的嵌入式二维码，商户可根据需要设定二维码的大小

	// 如果有商品描述，添加到请求中
	if req.Body != "" {
		p.Body = req.Body
	}

	log.Printf("🛒 创建二维码支付订单: OutTradeNo=%s, Amount=%s, Subject=%s", outTradeNo, req.Amount, req.Subject)

	// 调用支付宝API创建二维码
	rsp, err := client.TradePreCreate(context.Background(), p)
	if err != nil {
		log.Printf("❌ 创建二维码支付失败: %v", err)
		response := QRCodePayResponse{
			Success: false,
			Message: "创建支付失败",
			Error:   err.Error(),
		}
		writer.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(writer).Encode(response)
		return
	}

	if rsp.IsFailure() {
		log.Printf("❌ 支付宝返回错误: %s - %s", rsp.Msg, rsp.SubMsg)
		log.Printf("🔍 错误代码: %s", rsp.Code)
		log.Printf("🔍 子错误代码: %s", rsp.SubCode)
		log.Printf("🔍 应用ID: %s", kAppId)
		log.Printf("🔍 当前环境: 正式环境")
		log.Printf("💡 可能的解决方案:")
		log.Printf("   1. 确认应用已上线且状态正常")
		log.Printf("   2. 检查应用是否有二维码支付权限")
		log.Printf("   3. 确认服务器IP已正确添加到白名单")
		log.Printf("   4. 检查应用签名配置是否正确")

		response := QRCodePayResponse{
			Success: false,
			Message: "支付宝创建订单失败",
			Error:   fmt.Sprintf("Code: %s, SubCode: %s, Msg: %s - %s", rsp.Code, rsp.SubCode, rsp.Msg, rsp.SubMsg),
		}
		writer.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 返回成功响应
	response := QRCodePayResponse{
		Success: true,
		Message: "二维码创建成功",
		Data: &QRCodePayData{
			QRCode:     rsp.QRCode,
			OutTradeNo: outTradeNo,
			ExpireTime: 10, // 10分钟
			Amount:     req.Amount,
			Subject:    req.Subject,
		},
	}

	log.Printf("✅ 二维码支付订单创建成功: %s, 金额: %s", outTradeNo, req.Amount)
	json.NewEncoder(writer).Encode(response)
}

// checkPaymentStatus 检查支付状态
func checkPaymentStatus(writer http.ResponseWriter, request *http.Request) {
	// 设置CORS头
	writer.Header().Set("Access-Control-Allow-Origin", "*")
	writer.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	writer.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	writer.Header().Set("Content-Type", "application/json")

	// 处理OPTIONS预检请求
	if request.Method == "OPTIONS" {
		writer.WriteHeader(http.StatusOK)
		return
	}

	if request.Method != "GET" {
		response := PaymentStatusResponse{
			Success: false,
			Message: "只支持GET请求",
		}
		writer.WriteHeader(http.StatusMethodNotAllowed)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 获取订单号
	outTradeNo := request.URL.Query().Get("out_trade_no")
	if outTradeNo == "" {
		response := PaymentStatusResponse{
			Success: false,
			Message: "订单号不能为空",
		}
		writer.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 查询支付状态
	var p = alipay.TradeQuery{}
	p.OutTradeNo = outTradeNo

	rsp, err := client.TradeQuery(context.Background(), p)
	if err != nil {
		log.Printf("❌ 查询支付状态失败: %v", err)
		response := PaymentStatusResponse{
			Success: false,
			Message: "查询支付状态失败",
		}
		writer.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(writer).Encode(response)
		return
	}

	if rsp.IsFailure() {
		// 如果是订单不存在，返回等待支付状态
		if rsp.SubCode == "ACQ.TRADE_NOT_EXIST" {
			response := PaymentStatusResponse{
				Success: true,
				Message: "订单查询成功",
				Data: &PaymentStatusData{
					Status:     "WAIT_BUYER_PAY",
					OutTradeNo: outTradeNo,
					TradeNo:    "",
					Amount:     "",
				},
			}
			json.NewEncoder(writer).Encode(response)
			return
		}

		log.Printf("❌ 支付宝查询返回错误: %s - %s", rsp.Msg, rsp.SubMsg)
		response := PaymentStatusResponse{
			Success: false,
			Message: "查询支付状态失败",
		}
		writer.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 返回支付状态
	response := PaymentStatusResponse{
		Success: true,
		Message: "订单查询成功",
		Data: &PaymentStatusData{
			Status:     string(rsp.TradeStatus),
			OutTradeNo: rsp.OutTradeNo,
			TradeNo:    rsp.TradeNo,
			Amount:     rsp.TotalAmount,
		},
	}

	log.Printf("✅ 订单 %s 状态查询成功: %s", outTradeNo, rsp.TradeStatus)
	json.NewEncoder(writer).Encode(response)
}

// paymentPage 显示支付页面
func paymentPage(writer http.ResponseWriter, request *http.Request) {
	htmlTemplate := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝支付测试 - 秘钥模式</title>
    <script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background-color: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .logo {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            margin: 0 auto 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
            font-weight: bold;
        }

        h1 {
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 32px;
            font-weight: 700;
        }

        .subtitle {
            color: #718096;
            margin-bottom: 35px;
            font-size: 18px;
        }

        .mode-badge {
            display: inline-block;
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 25px;
        }

        .amount-section {
            background: #f7fafc;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 35px;
            border: 2px solid #e2e8f0;
        }

        .amount-label {
            color: #718096;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .amount {
            font-size: 42px;
            font-weight: 800;
            color: #667eea;
            margin-bottom: 8px;
        }

        .amount-desc {
            color: #a0aec0;
            font-size: 16px;
        }

        .button-group {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
        }

        .pay-button {
            flex: 1;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 18px 24px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pay-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .pay-button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .qr-button {
            flex: 1;
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 18px 24px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .qr-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(72, 187, 120, 0.4);
        }

        .qr-button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .qrcode-container {
            display: none;
            margin: 30px 0;
            padding: 30px;
            background: #f7fafc;
            border-radius: 15px;
            border: 2px solid #e2e8f0;
        }

        .qrcode-title {
            color: #2d3748;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
        }

        .qrcode {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .qrcode-tip {
            color: #718096;
            font-size: 16px;
            line-height: 1.6;
        }

        .status {
            padding: 15px 20px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .status.pending {
            background: #fef5e7;
            color: #d69e2e;
            border: 2px solid #f6e05e;
        }

        .status.success {
            background: #f0fff4;
            color: #38a169;
            border: 2px solid #68d391;
        }

        .status.error {
            background: #fed7d7;
            color: #e53e3e;
            border: 2px solid #fc8181;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            margin-top: 35px;
            padding-top: 25px;
            border-top: 2px solid #e2e8f0;
            color: #a0aec0;
            font-size: 14px;
            line-height: 1.6;
        }

        .features {
            background: #edf2f7;
            border-radius: 12px;
            padding: 20px;
            margin-top: 25px;
            text-align: left;
        }

        .features h4 {
            color: #2d3748;
            font-size: 16px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .features ul {
            list-style: none;
            padding: 0;
        }

        .features li {
            color: #4a5568;
            font-size: 14px;
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }

        .features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #48bb78;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔑</div>
        <h1>支付宝支付测试</h1>
        <div class="mode-badge">🔑 秘钥模式</div>
        <p class="subtitle">无需证书文件，简单快捷的支付测试</p>

        <div class="amount-section">
            <div class="amount-label">测试支付金额</div>
            <div class="amount">¥<span id="amount">0.01</span></div>
            <div class="amount-desc">秘钥模式支付测试订单</div>
        </div>

        <div class="button-group">
            <button class="pay-button" id="webPayBtn">网页支付</button>
            <button class="qr-button" id="qrPayBtn">扫码支付</button>
        </div>

        <div class="qrcode-container" id="qrcodeContainer">
            <div class="qrcode-title">请使用支付宝扫码支付</div>
            <div class="qrcode" id="qrcode"></div>
            <div class="qrcode-tip">
                打开支付宝APP，扫描上方二维码完成支付<br>
                支付完成后页面将自动更新状态
            </div>
        </div>

        <div id="status"></div>

        <div class="features">
            <h4>🔑 秘钥模式特点</h4>
            <ul>
                <li>无需下载证书文件</li>
                <li>只需配置应用私钥和支付宝公钥</li>
                <li>适合开发测试环境</li>
                <li>配置简单，快速上手</li>
                <li>支持网页支付和扫码支付</li>
            </ul>
        </div>

        <div class="footer">
            <p>🔒 支付过程安全可靠，请勿关闭页面</p>
            <p>💡 如有问题请检查控制台日志</p>
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        const webPayBtn = document.getElementById('webPayBtn');
        const qrPayBtn = document.getElementById('qrPayBtn');
        const qrcodeContainer = document.getElementById('qrcodeContainer');
        const qrcodeDiv = document.getElementById('qrcode');
        const statusDiv = document.getElementById('status');

        let currentOutTradeNo = null;
        let pollInterval = null;

        // 调试信息
        console.log('页面加载完成');
        console.log('webPayBtn:', webPayBtn);
        console.log('qrPayBtn:', qrPayBtn);
        console.log('API_BASE:', API_BASE);

        // 更新状态显示
        function updateStatus(message, type) {
            statusDiv.innerHTML = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
        }

        // 重置支付状态
        function resetPaymentState() {
            webPayBtn.textContent = "网页支付";
            webPayBtn.disabled = false;
            qrPayBtn.textContent = "扫码支付";
            qrPayBtn.disabled = false;
            qrcodeContainer.style.display = 'none';
            currentOutTradeNo = null;
            if (pollInterval) {
                clearInterval(pollInterval);
                pollInterval = null;
            }
        }

        // 网页支付 - 获取支付URL并在iframe中显示
        webPayBtn.addEventListener('click', async () => {
            console.log('网页支付按钮被点击');
            if (webPayBtn.disabled) return;

            webPayBtn.disabled = true;
            webPayBtn.innerHTML = '<span class="loading"></span>创建支付订单...';
            statusDiv.style.display = 'none';

            try {
                const response = await fetch(API_BASE + '/alipay/pay');
                const result = await response.json();

                if (result.success) {
                    // 显示支付URL，让用户可以在新窗口打开或在iframe中显示
                    currentOutTradeNo = result.data.out_trade_no;

                    // 创建iframe显示支付页面
                    createPaymentIframe(result.data.payment_url);

                    webPayBtn.textContent = "支付中...";
                    updateStatus("请在支付窗口中完成支付", "pending");

                    startPolling(currentOutTradeNo);
                } else {
                    updateStatus("创建支付失败，请重试", "error");
                    resetPaymentState();
                }
            } catch (error) {
                console.error('创建支付失败:', error);
                updateStatus('创建支付失败：' + error.message, "error");
                resetPaymentState();
            }
        });

        // 二维码支付
        qrPayBtn.addEventListener('click', async () => {
            console.log('扫码支付按钮被点击');
            if (qrPayBtn.disabled) return;

            qrPayBtn.disabled = true;
            qrPayBtn.innerHTML = '<span class="loading"></span>创建订单中...';
            statusDiv.style.display = 'none';

            try {
                const result = await createQRPayment('扫码支付');
                if (result.success) {
                    generateQRCode(result.qrCodeUrl);
                    currentOutTradeNo = result.outTradeNo;

                    qrPayBtn.textContent = "支付中...";
                    updateStatus("请使用支付宝扫码支付", "pending");

                    startPolling(currentOutTradeNo);
                } else {
                    updateStatus("创建订单失败，请重试", "error");
                    resetPaymentState();
                }
            } catch (error) {
                console.error('创建支付失败:', error);
                updateStatus('创建订单失败：' + error.message, "error");
                resetPaymentState();
            }
})

        // 创建支付iframe
        function createPaymentIframe(paymentUrl) {
            qrcodeContainer.style.display = 'block';
            qrcodeContainer.querySelector('.qrcode-title').textContent = '支付宝支付页面';

            // 清空二维码容器并创建iframe
            qrcodeDiv.innerHTML = '';
            const iframe = document.createElement('iframe');
            iframe.src = paymentUrl;
            iframe.style.width = '240px';  // 二维码200px + 边距
            iframe.style.height = '240px'; // 二维码200px + 标题和边距
			iframe.style.padding = '20px 0 0 20px';
            iframe.style.border = '2px solid #e2e8f0';
            iframe.style.borderRadius = '10px';
            iframe.style.margin = '0 auto';
            iframe.style.display = 'block';
            iframe.frameBorder = '0';

            qrcodeDiv.appendChild(iframe);

            // 更新提示文字
            const tipElement = qrcodeContainer.querySelector('.qrcode-tip');
            tipElement.innerHTML = '请在上方支付窗口中完成支付<br>支付完成后页面将自动更新状态';
        }

        // 创建二维码支付订单
        async function createQRPayment(paymentType = '扫码支付') {
            const response = await fetch(API_BASE + '/alipay/qrcode', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    amount: '0.01',
                    subject: '秘钥模式' + paymentType + '测试',
                    body: '这是一个秘钥模式的' + paymentType + '测试订单',
                    user_id: 'test_user_' + Date.now(),
                    order_id: 'ORDER_' + Date.now()
                })
            });

            const result = await response.json();
            if (result.success) {
                return {
                    success: true,
                    qrCodeUrl: result.data.qr_code,
                    outTradeNo: result.data.out_trade_no,
                    expireTime: result.data.expire_time
                };
            } else {
                throw new Error(result.message || '创建支付失败');
            }
        }

        // 生成二维码
        function generateQRCode(url) {
            qrcodeDiv.innerHTML = '';
            qrcodeContainer.style.display = 'block';

            try {
                if (typeof QRious !== 'undefined') {
                    const canvas = document.createElement('canvas');
                    const qr = new QRious({
                        element: canvas,
                        value: url,
                        size: 220,
                        foreground: '#667eea',
                        background: '#ffffff'
                    });
                    qrcodeDiv.appendChild(canvas);
                } else {
                    const img = document.createElement('img');
                    img.src = 'https://api.qrserver.com/v1/create-qr-code/?size=220x220&data=' + encodeURIComponent(url);
                    img.style.width = '220px';
                    img.style.height = '220px';
                    img.style.border = '2px solid #e2e8f0';
                    img.style.borderRadius = '10px';
                    img.alt = '支付二维码';
                    qrcodeDiv.appendChild(img);
                }
            } catch (error) {
                console.error('生成二维码失败:', error);
                qrcodeDiv.innerHTML = '<div style=\"padding: 20px; color: #e53e3e;\">二维码生成失败，请刷新重试</div>';
            }
        }

        // 检查支付状态
        async function checkPaymentStatus(outTradeNo) {
            try {
                const response = await fetch(API_BASE + '/alipay/status?out_trade_no=' + outTradeNo);
                const result = await response.json();
                return result.success ? result.data.status : 'UNKNOWN';
            } catch (error) {
                console.error('检查支付状态失败:', error);
                return 'ERROR';
            }
        }

        // 开始轮询支付状态
        function startPolling(outTradeNo) {
            if (pollInterval) clearInterval(pollInterval);

            pollInterval = setInterval(async () => {
                const status = await checkPaymentStatus(outTradeNo);

                if (status === 'TRADE_SUCCESS') {
                    clearInterval(pollInterval);
                    updateStatus('🎉 支付成功！', 'success');
                    qrPayBtn.textContent = "支付成功";
                    qrPayBtn.disabled = true;
                } else if (status === 'TRADE_CLOSED') {
                    clearInterval(pollInterval);
                    updateStatus('❌ 支付已关闭', 'error');
                    resetPaymentState();
                }
            }, 2000);
        }

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            if (pollInterval) clearInterval(pollInterval);
        });
    </script>
</body>
</html>
	`

	writer.Header().Set("Content-Type", "text/html; charset=utf-8")
	writer.Write([]byte(htmlTemplate))
}
