package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/smartwalle/alipay/v3"
	"github.com/smartwalle/xid"
)

var client *alipay.Client

const (
	kAppId      = "9021000150672018"
	kPrivateKey = "MIIEpAIBAAKCAQEAmFVc53xiFpwXUsgDiAR5mGeFrE1VO0pLley0qgxdBVqJtKDYTUUPVBfp9joqpTHgjUTTmCd3X71McrJIgmqp9oK/enuaBw8hsdXMfnbiDtYlv9DoZ60p/uuDglw88MfjMJ94i/vVdbLD5UrZgPqr7X7NAIOnVYyAcMghwaYUZMQ0QKYETX9OPn4cS78VwHJBxwUCYNubSV/BfodGNI4m8ZaYnVIFAhSMMmT9cwmWVSqMFgoydstr/gxpAWzv+S3HZ2FihcMZyyb4pIZPCfXWDzG77f0xzOmfZjFV2XPZFO2bgWDbeib0R0lXIrEbki3LcqdhxaR+WKvifSb0U/iYvwIDAQABAoIBAQCMeELYTzFgKgWz1y6ycXXzHJr+HL2Yk9UJZiHeoNHySt+qyZtL/X+6Cuc6n76NYGXxvuLjbpk8lqF1LoOAh309DKom9u08zy5B/6W3hEbGaaCbtbdpSxvrrx8wyX+EhL4yKwuNAed/xh2wRYo1znFV6lR9wek/EPWrilDechhiJcGCmFw9IJoAUB1eNhV2CtheLl9WGxuWf4GEPi3WHFV0MGY/vZCWNqUuAbTOmlWHX04k0sJL551jwBooHn0Z7NwAXwyQO2PgcCToCzfHmEmxk2ZSQg/QMtLqBNtE/8BiNUQdw82ppxOXKj9IstUQ1nCBae0Muihh6i+cHx4GNk1pAoGBANcpWqaK4GS/D7eRbacV3Ae5iU849JyHN+w0ZAPzUOcffmBtitoydzap+SyG+kMEQuVv+DRuPLtaswfBKrgMqJ9MMDzB+XD3Qi2FYs+LAKcl+C5I+u04k4q9T9auiAmWGC+On0SWZ7RATMTJ7p9hHBYAZoSrQSw4VgMgtBR6uYM1AoGBALU/NRs0cgE0Dbs5KFkINjOB/m+vf3UxX7hMf59P6qbJBpdaq1SYxv+qGlO9a3stzz2KykzUmh85XCRoMrWMPghtfrCFa32YR3Ad5Z9DYYmBOEQWEZKWByp+QhqbirnezuNjnEJV0VmUEgrjqSq+4yX8vxjuUaj5dVgvnXNnNZajAoGBAJPFMR8EbkyC8/ZtCNQiLTWY9XbVdRozu/KboL0d1MiHVrI/SYfF9R4z6o/sO+DfAlq1ZdAMjeVWy3Jd9krlVsHg/5ga2If/KRI+c7h6jP4/E44mTT+zBZHZpwWYZJHA5edzmmruqVagepkE3r/TYAi0SbSAMYh/QINxaHxQNSypAoGAMmhw6a8NYf5LL133NcPN50dpL0C9Dyf7OjO0bud4GIjiV2f1LKMM/YAesFmHMgwMjk4yc2nRr4txcAK2z71AZ3GkgY2p1pVqkOkPutqvnuGcd3s3yfAF8DCLTAEkdQtduOo0RFK/1c0Ts4mbu/K5FBCWSWXGulse4rbjcHws8EcCgYA6u/50RuGk+ZiZx3i6WNbFb2C3IcZH1kqiEmHJuyS2KV4jnGsn5eUKC/bZ8rhkzlCF4Hg47Ftv8WNftBl/BG83jTr4xtXWsnpmJxaL/G7GMT5tbk/9BHpMkVjrgEU6v4qIIFSLezOe0Pw07NA72xf5A4rRK+9kZbsm7foeAaXoQw=="
	kServerPort = "8082"
	// TODO 设置回调地址域名
	kServerDomain = "https://6a757796.r26.cpolar.top"
)

func main() {
	// 检查启动参数，决定使用证书模式还是秘钥模式
	useKeyMode := false
	if len(os.Args) > 1 && os.Args[1] == "key" {
		useKeyMode = true
	}

	var err error
	if useKeyMode {
		err = initClientWithKey()
	} else {
		err = initClientWithCert()
	}

	if err != nil {
		log.Printf("❌ 初始化支付宝客户端失败: %v", err)
		return
	}

	http.HandleFunc("/", paymentPage)
	http.HandleFunc("/test_qr.html", testQRPage)
	http.HandleFunc("/alipay/pay", pay)
	http.HandleFunc("/alipay/qrcode", qrcodePay)
	http.HandleFunc("/alipay/callback", callback)
	http.HandleFunc("/alipay/notify", notify)
	http.HandleFunc("/alipay/status", checkPaymentStatus)

	log.Printf("服务器启动在端口 %s", kServerPort)
	http.ListenAndServe(":"+kServerPort, nil)
}

// initClientWithKey 使用秘钥模式初始化支付宝客户端
func initClientWithKey() error {
	var err error

	// 支付宝公钥（用于验证支付宝返回的数据）
	// 注意：这里需要替换为你的支付宝公钥
	const kAliPayPublicKey = `MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuWJKrQ6SWvS6niI+4vEiRjhdOAoP+0Dua2pkuKlOiKXo3GUl2lcZpTBdHtPgHtbBHSu3ehrFax2FS3jOqXet8uXjy8UF2RPp/cjyOAsCarratkmr/YgAuNgXVcGVKdVRWPC2NOdxOzpYmN0TiCBFEHPAJWpf8BRf7WoWGUIp1PLFGUxpDHKCYaC4ciFFyFPeHd9b1SiS2vDFpFVgE8YqidOdVekEKhnB8SUJKXVhDBSo+NdUKhTgCZ4RzDobNC8+CqsgQu1QnNWbbQUon5eO+4Lm1galLDIfzVN6+5B0raqs0Q+G/xF+uKn/nV175dWPKHx+9cd3qMX+MO0CIwIDAQAB`

	// 初始化支付宝客户端 (true=生产环境, false=沙箱环境)
	if client, err = alipay.New(kAppId, kPrivateKey, false); err != nil {
		return fmt.Errorf("初始化支付宝客户端失败: %v", err)
	}
	log.Println("✅ 支付宝客户端初始化成功（沙箱模式 - 秘钥模式）")

	// 设置支付宝公钥（秘钥模式）
	if err = client.LoadAliPayPublicKey(kAliPayPublicKey); err != nil {
		log.Printf("⚠️  设置支付宝公钥失败: %v", err)
		log.Println("💡 提示：请确保支付宝公钥正确")
	} else {
		log.Println("✅ 支付宝公钥设置成功")
	}

	// 设置内容加密密钥（可选）
	if err = client.SetEncryptKey("d3es4Zw8rGs5sVKtEgLfuA=="); err != nil {
		log.Printf("⚠️  设置内容加密密钥失败: %v", err)
	} else {
		log.Println("✅ 内容加密密钥设置成功")
	}

	log.Println("🔑 使用秘钥模式，无需加载证书文件")
	return nil
}

// initClientWithCert 使用证书模式初始化支付宝客户端
func initClientWithCert() error {
	var err error

	// 初始化支付宝客户端 (true=生产环境, false=沙箱环境)
	// 建议先使用沙箱环境测试
	if client, err = alipay.New(kAppId, kPrivateKey, false); err != nil {
		return fmt.Errorf("初始化支付宝客户端失败: %v", err)
	}
	log.Println("✅ 支付宝客户端初始化成功（沙箱模式 - 证书模式）")

	// 加载证书（用于生产环境）
	log.Println("尝试加载证书文件...")
	if err = client.LoadAppCertPublicKeyFromFile("testpay/certs/appPublicCert.crt"); err != nil {
		log.Printf("⚠️  加载应用证书失败: %v", err)
		log.Println("💡 提示：如果是测试环境，可以忽略此错误")
	} else {
		log.Println("✅ 应用证书加载成功")
	}

	if err = client.LoadAliPayRootCertFromFile("testpay/certs/alipayRootCert.crt"); err != nil {
		log.Printf("⚠️  加载支付宝根证书失败: %v", err)
	} else {
		log.Println("✅ 支付宝根证书加载成功")
	}

	if err = client.LoadAlipayCertPublicKeyFromFile("testpay/certs/alipayPublicCert.crt"); err != nil {
		log.Printf("⚠️  加载支付宝公钥证书失败: %v", err)
	} else {
		log.Println("✅ 支付宝公钥证书加载成功")
	}

	if err = client.SetEncryptKey("d3es4Zw8rGs5sVKtEgLfuA=="); err != nil {
		log.Printf("⚠️  加载内容加密密钥失败: %v", err)
	} else {
		log.Println("✅ 内容加密密钥设置成功")
	}

	return nil
}

func pay(writer http.ResponseWriter, request *http.Request) {
	var tradeNo = fmt.Sprintf("%d", xid.Next())

	var p = alipay.TradePagePay{}
	p.NotifyURL = kServerDomain + "/alipay/notify"
	p.ReturnURL = kServerDomain + "/alipay/callback"
	p.Subject = "支付测试:" + tradeNo
	p.OutTradeNo = tradeNo
	p.TotalAmount = "0.01"
	p.ProductCode = "FAST_INSTANT_TRADE_PAY"
	p.QRPayMode = "4"

	url, _ := client.TradePagePay(p)
	http.Redirect(writer, request, url.String(), http.StatusTemporaryRedirect)
}

func callback(writer http.ResponseWriter, request *http.Request) {
	request.ParseForm()

	if err := client.VerifySign(request.Form); err != nil {
		log.Println("回调验证签名发生错误", err)
		writer.WriteHeader(http.StatusBadRequest)
		writer.Write([]byte("回调验证签名发生错误"))
		return
	}

	log.Println("回调验证签名通过")

	// 示例一：使用已有接口进行查询
	var outTradeNo = request.Form.Get("out_trade_no")
	var p = alipay.TradeQuery{}
	p.OutTradeNo = outTradeNo

	rsp, err := client.TradeQuery(context.Background(), p)
	if err != nil {
		writer.WriteHeader(http.StatusBadRequest)
		writer.Write([]byte(fmt.Sprintf("验证订单 %s 信息发生错误: %s", outTradeNo, err.Error())))
		return
	}

	if rsp.IsFailure() {
		writer.WriteHeader(http.StatusBadRequest)
		writer.Write([]byte(fmt.Sprintf("验证订单 %s 信息发生错误: %s-%s", outTradeNo, rsp.Msg, rsp.SubMsg)))
		return
	}
	writer.WriteHeader(http.StatusOK)
	writer.Write([]byte(fmt.Sprintf("订单 %s 支付成功", outTradeNo)))
}

func notify(writer http.ResponseWriter, request *http.Request) {
	request.ParseForm()

	var notification, err = client.DecodeNotification(request.Form)
	if err != nil {
		log.Println("解析异步通知发生错误", err)
		return
	}

	log.Println("解析异步通知成功:", notification.NotifyId)

	// 示例一：使用自定义请求进行查询
	var p = alipay.NewPayload("alipay.trade.query")
	p.AddBizField("out_trade_no", notification.OutTradeNo)

	var rsp *alipay.TradeQueryRsp
	if err = client.Request(context.Background(), p, &rsp); err != nil {
		log.Printf("异步通知验证订单 %s 信息发生错误: %s \n", notification.OutTradeNo, err.Error())
		return
	}
	if rsp.IsFailure() {
		log.Printf("异步通知验证订单 %s 信息发生错误: %s-%s \n", notification.OutTradeNo, rsp.Msg, rsp.SubMsg)
		return
	}

	log.Printf("订单 %s 支付成功 \n", notification.OutTradeNo)

	client.ACKNotification(writer)
}

// QRCodePayRequest 二维码支付请求结构
type QRCodePayRequest struct {
	Amount    string `json:"amount"`
	Subject   string `json:"subject"`
	Body      string `json:"body"`
	UserID    string `json:"user_id"`
	OrderID   string `json:"order_id"`
	ExtraData string `json:"extra_data"`
}

// QRCodePayResponse 二维码支付响应结构
type QRCodePayResponse struct {
	Success bool           `json:"success"`
	Message string         `json:"message"`
	Data    *QRCodePayData `json:"data,omitempty"`
	Error   string         `json:"error,omitempty"`
}

type QRCodePayData struct {
	QRCode     string `json:"qr_code"`
	OutTradeNo string `json:"out_trade_no"`
	ExpireTime int    `json:"expire_time"`
	Amount     string `json:"amount"`
	Subject    string `json:"subject"`
}

// PaymentStatusResponse 支付状态响应结构
type PaymentStatusResponse struct {
	Success bool               `json:"success"`
	Message string             `json:"message"`
	Data    *PaymentStatusData `json:"data,omitempty"`
}

type PaymentStatusData struct {
	Status     string `json:"status"` // WAIT_BUYER_PAY, TRADE_SUCCESS, TRADE_CLOSED
	OutTradeNo string `json:"out_trade_no"`
	TradeNo    string `json:"trade_no"`
	Amount     string `json:"amount"`
}

// paymentPage 显示支付页面
func paymentPage(writer http.ResponseWriter, request *http.Request) {
	htmlTemplate := `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝扫码支付</title>
    <script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #e4edf5 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background-color: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 480px;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1677ff, #69c0ff);
        }

        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #1677ff, #69c0ff);
            border-radius: 12px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        h1 {
            color: #1f2937;
            margin-bottom: 8px;
            font-size: 28px;
            font-weight: 600;
        }

        .subtitle {
            color: #6b7280;
            margin-bottom: 32px;
            font-size: 16px;
        }

        .amount-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
            border: 1px solid #e2e8f0;
        }

        .amount-label {
            color: #64748b;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .amount {
            font-size: 36px;
            font-weight: 700;
            color: #1677ff;
            margin-bottom: 4px;
        }

        .amount-desc {
            color: #94a3b8;
            font-size: 14px;
        }

        .pay-button {
            width: 100%;
            background: linear-gradient(135deg, #1677ff, #69c0ff);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 24px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 24px;
        }

        .pay-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(22, 119, 255, 0.3);
        }

        .pay-button:disabled {
            background: #94a3b8;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .qrcode-container {
            display: none;
            margin: 24px 0;
            padding: 24px;
            background: #f8fafc;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }

        .qrcode-title {
            color: #1f2937;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
        }

        .qrcode {
            display: flex;
            justify-content: center;
            margin-bottom: 16px;
        }

        .qrcode-tip {
            color: #64748b;
            font-size: 14px;
            line-height: 1.5;
        }

        .status {
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 16px;
        }

        .status.pending {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fbbf24;
        }

        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }

        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #1677ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #e2e8f0;
            color: #94a3b8;
            font-size: 12px;
            line-height: 1.5;
        }

        .order-info {
            background: #f1f5f9;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
            text-align: left;
        }

        .order-info h4 {
            color: #1f2937;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .order-info p {
            color: #64748b;
            font-size: 12px;
            margin: 4px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">💳</div>
        <h1>支付宝扫码支付</h1>
        <p class="subtitle">安全便捷的移动支付体验</p>

        <div class="amount-section">
            <div class="amount-label">支付金额</div>
            <div class="amount">¥<span id="amount">0.01</span></div>
            <div class="amount-desc">测试支付订单</div>
        </div>

        <button class="pay-button" id="payBtn">立即支付</button>

        <div class="qrcode-container" id="qrcodeContainer">
            <div class="qrcode-title">请使用支付宝扫码支付</div>
            <div class="qrcode" id="qrcode"></div>
            <div class="qrcode-tip">
                打开支付宝APP，扫描上方二维码完成支付<br>
                支付完成后页面将自动跳转
            </div>
        </div>

        <div id="status"></div>

        <div class="footer">
            <p>支付过程中请勿关闭页面</p>
            <p>如有问题请联系客服</p>
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        const payBtn = document.getElementById('payBtn');
        const qrcodeContainer = document.getElementById('qrcodeContainer');
        const qrcodeDiv = document.getElementById('qrcode');
        const statusDiv = document.getElementById('status');

        let currentOutTradeNo = null;
        let pollInterval = null;
        let qrcodeGenerated = false;

        // 更新状态显示
        function updateStatus(message, type) {
            statusDiv.innerHTML = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
        }

        // 重置支付状态
        function resetPaymentState() {
            payBtn.textContent = "立即支付";
            payBtn.disabled = false;
            qrcodeContainer.style.display = 'none';
            qrcodeGenerated = false;
            currentOutTradeNo = null;
            if (pollInterval) {
                clearInterval(pollInterval);
                pollInterval = null;
            }
        }

        // 生成二维码
        function generateQRCode(url) {
            qrcodeDiv.innerHTML = '';
            qrcodeContainer.style.display = 'block';

            try {
                // 方法1: 使用QRious库生成二维码
                if (typeof QRious !== 'undefined') {
                    const canvas = document.createElement('canvas');
                    const qr = new QRious({
                        element: canvas,
                        value: url,
                        size: 200,
                        foreground: '#1677ff',
                        background: '#ffffff'
                    });

                    qrcodeDiv.appendChild(canvas);
                    qrcodeGenerated = true;

                    // 添加提示文字
                    const tip = document.createElement('p');
                    tip.style.marginTop = '10px';
                    tip.style.fontSize = '14px';
                    tip.style.color = '#666';
                    tip.textContent = '请使用支付宝APP扫描二维码完成支付';
                    qrcodeDiv.appendChild(tip);

                    console.log('二维码生成成功 (QRious)');
                    return;
                }

                // 方法2: 使用在线二维码生成服务
                const img = document.createElement('img');
                img.src = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(url);
                img.style.width = '200px';
                img.style.height = '200px';
                img.style.border = '1px solid #ddd';
                img.alt = '支付二维码';

                img.onload = function() {
                    console.log('二维码生成成功 (在线服务)');
                };

                img.onerror = function() {
                    console.error('在线二维码生成失败，使用备用方案');
                    showQRCodeFallback(url);
                    return;
                };

                qrcodeDiv.appendChild(img);
                qrcodeGenerated = true;

                // 添加提示文字
                const tip = document.createElement('p');
                tip.style.marginTop = '10px';
                tip.style.fontSize = '14px';
                tip.style.color = '#666';
                tip.textContent = '请使用支付宝APP扫描二维码完成支付';
                qrcodeDiv.appendChild(tip);

            } catch (error) {
                console.error('生成二维码失败:', error);
                qrcodeDiv.innerHTML =
                    '<div style="padding: 20px; text-align: center; border: 2px dashed #ccc; border-radius: 8px; background: #f9f9f9;">' +
                        '<p style="margin: 0 0 10px 0; font-size: 16px; color: #666;">二维码生成失败</p>' +
                        '<p style="margin: 0; font-size: 12px; color: #999;">错误: ' + error.message + '</p>' +
                        '<p style="margin: 10px 0 0 0; font-size: 12px; color: #999;">请刷新页面重试</p>' +
                    '</div>';
                qrcodeContainer.style.display = 'block';
            }
        }

        // 备用二维码显示方案
        function showQRCodeFallback(url) {
            qrcodeDiv.innerHTML =
                '<div style="padding: 20px; text-align: center; border: 2px solid #1677ff; border-radius: 8px; background: #f0f8ff;">' +
                    '<p style="margin: 0 0 15px 0; font-size: 16px; color: #1677ff; font-weight: bold;">支付二维码</p>' +
                    '<div style="margin: 15px 0; padding: 15px; background: white; border-radius: 4px; word-break: break-all; font-family: monospace; font-size: 12px; color: #333;">' + url + '</div>' +
                    '<p style="margin: 15px 0 5px 0; font-size: 14px; color: #666;">请复制上方链接到支付宝中打开</p>' +
                    '<p style="margin: 0; font-size: 12px; color: #999;">或者使用支付宝扫一扫功能扫描此链接</p>' +
                    '<button onclick="copyToClipboard(\'' + url + '\')" style="margin-top: 10px; padding: 8px 16px; background: #1677ff; color: white; border: none; border-radius: 4px; cursor: pointer;">复制链接</button>' +
                '</div>';
            qrcodeGenerated = true;
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('链接已复制到剪贴板');
                }).catch(() => {
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                fallbackCopyTextToClipboard(text);
            }
        }

        function fallbackCopyTextToClipboard(text) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                document.execCommand('copy');
                alert('链接已复制到剪贴板');
            } catch (err) {
                alert('复制失败，请手动复制链接');
            }
            document.body.removeChild(textArea);
        }

        // 创建支付订单
        async function createPayment() {
            try {
                const response = await fetch(API_BASE + '/alipay/qrcode', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: '0.01',
                        subject: '测试商品支付',
                        body: '这是一个测试支付订单',
                        user_id: 'test_user_' + Date.now(),
                        order_id: 'ORDER_' + Date.now()
                    })
                });

                const result = await response.json();

                if (result.success) {
                    return {
                        success: true,
                        qrCodeUrl: result.data.qr_code,
                        outTradeNo: result.data.out_trade_no,
                        expireTime: result.data.expire_time
                    };
                } else {
                    throw new Error(result.message || '创建支付失败');
                }
            } catch (error) {
                throw new Error('网络错误: ' + error.message);
            }
        }

        // 检查支付状态
        async function checkPaymentStatus(outTradeNo) {
            try {
                const response = await fetch(API_BASE + '/alipay/status?out_trade_no=' + outTradeNo);
                const result = await response.json();

                if (result.success) {
                    return result.data.status;
                }
                return 'UNKNOWN';
            } catch (error) {
                console.error('检查支付状态失败:', error);
                return 'ERROR';
            }
        }

        // 开始轮询支付状态
        function startPolling(outTradeNo) {
            if (pollInterval) {
                clearInterval(pollInterval);
            }

            pollInterval = setInterval(async () => {
                const status = await checkPaymentStatus(outTradeNo);

                if (status === 'TRADE_SUCCESS') {
                    clearInterval(pollInterval);
                    updateStatus('✅ 支付成功！', 'success');
                    payBtn.textContent = "支付成功";
                    payBtn.disabled = true;
                } else if (status === 'TRADE_CLOSED') {
                    clearInterval(pollInterval);
                    updateStatus('❌ 支付已关闭', 'error');
                    resetPaymentState();
                }
            }, 2000); // 每2秒检查一次
        }

        // 支付按钮点击事件
        payBtn.addEventListener('click', async () => {
            if (payBtn.disabled) return;

            payBtn.disabled = true;
            payBtn.innerHTML = '<span class="loading"></span>创建订单中...';
            statusDiv.style.display = 'none';

            try {
                // 创建支付订单
                const result = await createPayment();

                if (result.success) {
                    // 显示二维码
                    generateQRCode(result.qrCodeUrl);
                    currentOutTradeNo = result.outTradeNo;

                    // 更新UI
                    payBtn.textContent = "支付中...";
                    updateStatus("请使用支付宝扫码支付", "pending");

                    // 开始轮询支付状态
                    startPolling(currentOutTradeNo);
                } else {
                    updateStatus("创建订单失败，请重试", "error");
                    resetPaymentState();
                }
            } catch (error) {
                console.error('创建支付失败:', error);
                updateStatus('创建订单失败：' + error.message, "error");
                resetPaymentState();
            }
        });

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            if (pollInterval) {
                clearInterval(pollInterval);
            }
        });
    </script>
</body>
</html>
	`

	writer.Header().Set("Content-Type", "text/html; charset=utf-8")
	writer.Write([]byte(htmlTemplate))
}

// qrcodePay 处理二维码支付请求
func qrcodePay(writer http.ResponseWriter, request *http.Request) {
	// 设置CORS头
	writer.Header().Set("Access-Control-Allow-Origin", "*")
	writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	writer.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	writer.Header().Set("Content-Type", "application/json")

	// 处理OPTIONS预检请求
	if request.Method == "OPTIONS" {
		writer.WriteHeader(http.StatusOK)
		return
	}

	if request.Method != "POST" {
		response := QRCodePayResponse{
			Success: false,
			Message: "只支持POST请求",
			Error:   "Method not allowed",
		}
		writer.WriteHeader(http.StatusMethodNotAllowed)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 解析请求参数
	var req QRCodePayRequest
	if err := json.NewDecoder(request.Body).Decode(&req); err != nil {
		response := QRCodePayResponse{
			Success: false,
			Message: "请求参数格式错误",
			Error:   err.Error(),
		}
		writer.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 验证必要参数
	if req.Amount == "" {
		response := QRCodePayResponse{
			Success: false,
			Message: "支付金额不能为空",
			Error:   "amount is required",
		}
		writer.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(writer).Encode(response)
		return
	}

	if req.Subject == "" {
		req.Subject = "测试支付订单"
	}

	// 生成商户订单号
	outTradeNo := fmt.Sprintf("QR_%d_%d", time.Now().Unix(), xid.Next())

	// 创建支付宝二维码支付请求
	var p = alipay.TradePreCreate{}
	p.NotifyURL = kServerDomain + "/alipay/notify"
	p.Subject = req.Subject
	p.OutTradeNo = outTradeNo
	p.TotalAmount = req.Amount
	p.TimeoutExpress = "10m" // 10分钟过期

	// 设置商品码（必需参数）- 扫码支付使用此产品码
	// 注意：沙箱环境可能不需要此参数，但生产环境需要
	// p.ProductCode = "FACE_TO_FACE_PAYMENT"

	// 如果有商品描述，添加到请求中
	if req.Body != "" {
		p.Body = req.Body
	}

	log.Printf("创建支付订单参数: OutTradeNo=%s, Amount=%s, Subject=%s", outTradeNo, req.Amount, req.Subject)

	// 调用支付宝API创建二维码
	rsp, err := client.TradePreCreate(context.Background(), p)
	if err != nil {
		log.Printf("创建二维码支付失败: %v", err)
		response := QRCodePayResponse{
			Success: false,
			Message: "创建支付失败",
			Error:   err.Error(),
		}
		writer.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(writer).Encode(response)
		return
	}

	if rsp.IsFailure() {
		log.Printf("支付宝返回错误: %s - %s", rsp.Msg, rsp.SubMsg)
		response := QRCodePayResponse{
			Success: false,
			Message: "支付宝创建订单失败",
			Error:   fmt.Sprintf("%s - %s", rsp.Msg, rsp.SubMsg),
		}
		writer.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 返回成功响应
	response := QRCodePayResponse{
		Success: true,
		Message: "二维码创建成功",
		Data: &QRCodePayData{
			QRCode:     rsp.QRCode,
			OutTradeNo: outTradeNo,
			ExpireTime: 10, // 10分钟
			Amount:     req.Amount,
			Subject:    req.Subject,
		},
	}

	log.Printf("二维码支付订单创建成功: %s, 金额: %s", outTradeNo, req.Amount)
	json.NewEncoder(writer).Encode(response)
}

// checkPaymentStatus 检查支付状态
func checkPaymentStatus(writer http.ResponseWriter, request *http.Request) {
	// 设置CORS头
	writer.Header().Set("Access-Control-Allow-Origin", "*")
	writer.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	writer.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	writer.Header().Set("Content-Type", "application/json")

	// 处理OPTIONS预检请求
	if request.Method == "OPTIONS" {
		writer.WriteHeader(http.StatusOK)
		return
	}

	if request.Method != "GET" {
		response := PaymentStatusResponse{
			Success: false,
			Message: "只支持GET请求",
		}
		writer.WriteHeader(http.StatusMethodNotAllowed)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 获取订单号
	outTradeNo := request.URL.Query().Get("out_trade_no")
	if outTradeNo == "" {
		response := PaymentStatusResponse{
			Success: false,
			Message: "订单号不能为空",
		}
		writer.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 查询支付状态
	var p = alipay.TradeQuery{}
	p.OutTradeNo = outTradeNo

	rsp, err := client.TradeQuery(context.Background(), p)
	if err != nil {
		log.Printf("查询支付状态失败: %v", err)
		response := PaymentStatusResponse{
			Success: false,
			Message: "查询支付状态失败",
		}
		writer.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(writer).Encode(response)
		return
	}

	if rsp.IsFailure() {
		// 如果是订单不存在，返回等待支付状态
		if rsp.SubCode == "ACQ.TRADE_NOT_EXIST" {
			response := PaymentStatusResponse{
				Success: true,
				Message: "订单查询成功",
				Data: &PaymentStatusData{
					Status:     "WAIT_BUYER_PAY",
					OutTradeNo: outTradeNo,
					TradeNo:    "",
					Amount:     "",
				},
			}
			json.NewEncoder(writer).Encode(response)
			return
		}

		log.Printf("支付宝查询返回错误: %s - %s", rsp.Msg, rsp.SubMsg)
		response := PaymentStatusResponse{
			Success: false,
			Message: "查询支付状态失败",
		}
		writer.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(writer).Encode(response)
		return
	}

	// 返回支付状态
	response := PaymentStatusResponse{
		Success: true,
		Message: "订单查询成功",
		Data: &PaymentStatusData{
			Status:     string(rsp.TradeStatus),
			OutTradeNo: rsp.OutTradeNo,
			TradeNo:    rsp.TradeNo,
			Amount:     rsp.TotalAmount,
		},
	}

	log.Printf("订单 %s 状态查询成功: %s", outTradeNo, rsp.TradeStatus)
	json.NewEncoder(writer).Encode(response)
}

// testQRPage 提供二维码测试页面
func testQRPage(writer http.ResponseWriter, request *http.Request) {
	writer.Header().Set("Content-Type", "text/html; charset=utf-8")
	http.ServeFile(writer, request, "test_qr.html")
}
