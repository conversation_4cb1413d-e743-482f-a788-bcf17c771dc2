<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pointer Center - 支付宝支付</title>
    <script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background-color: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            margin: 0 auto 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }

        h1 {
            color: #2d3748;
            margin-bottom: 12px;
            font-size: 32px;
            font-weight: 700;
        }

        .subtitle {
            color: #718096;
            margin-bottom: 40px;
            font-size: 18px;
        }

        .payment-section {
            background: #f7fafc;
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 32px;
            border: 2px solid #e2e8f0;
        }

        .amount-display {
            font-size: 48px;
            font-weight: 800;
            color: #667eea;
            margin-bottom: 8px;
        }

        .amount-label {
            color: #a0aec0;
            font-size: 16px;
            margin-bottom: 24px;
        }

        .payment-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 32px;
        }

        .payment-btn {
            padding: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
            font-weight: 600;
            color: #4a5568;
        }

        .payment-btn:hover {
            border-color: #667eea;
            background: #f7fafc;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .payment-btn.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .payment-btn .icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #4a5568;
            font-weight: 600;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .pay-button {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 18px 24px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 24px;
        }

        .pay-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .pay-button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .qrcode-container {
            display: none;
            margin: 24px 0;
            padding: 32px;
            background: #f7fafc;
            border-radius: 16px;
            border: 2px solid #e2e8f0;
        }

        .qrcode-title {
            color: #2d3748;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .qrcode {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        .qrcode-tip {
            color: #718096;
            font-size: 14px;
            line-height: 1.6;
        }

        .status {
            padding: 16px 20px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 20px;
        }

        .status.pending {
            background: #fef5e7;
            color: #d69e2e;
            border: 2px solid #f6e05e;
        }

        .status.success {
            background: #f0fff4;
            color: #38a169;
            border: 2px solid #68d391;
        }

        .status.error {
            background: #fed7d7;
            color: #e53e3e;
            border: 2px solid #fc8181;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .footer {
            margin-top: 32px;
            padding-top: 24px;
            border-top: 2px solid #e2e8f0;
            color: #a0aec0;
            font-size: 14px;
            line-height: 1.6;
        }

        @media (max-width: 600px) {
            .payment-methods {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 24px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .amount-display {
                font-size: 36px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">💳</div>
        <h1>Pointer Center</h1>
        <p class="subtitle">安全便捷的支付体验</p>

        <div class="payment-section">
            <div class="amount-display">¥<span id="amount">0.01</span></div>
            <div class="amount-label">支付金额</div>
            
            <div class="payment-methods">
                <button class="payment-btn active" id="qrBtn" data-method="qr">
                    <span class="icon">📱</span>
                    扫码支付
                </button>
                <button class="payment-btn" id="webBtn" data-method="web">
                    <span class="icon">🌐</span>
                    网页支付
                </button>
            </div>
        </div>

        <div class="form-group">
            <label class="form-label" for="userIdInput">用户ID</label>
            <input type="text" id="userIdInput" class="form-input" placeholder="请输入用户ID" value="test-user-123">
        </div>

        <div class="form-group">
            <label class="form-label" for="subjectInput">商品名称</label>
            <input type="text" id="subjectInput" class="form-input" placeholder="请输入商品名称" value="测试商品">
        </div>

        <div class="form-group">
            <label class="form-label" for="amountInput">支付金额</label>
            <input type="number" id="amountInput" class="form-input" placeholder="请输入支付金额" value="0.01" step="0.01" min="0.01">
        </div>

        <button class="pay-button" id="payBtn">立即支付</button>

        <div class="qrcode-container" id="qrcodeContainer">
            <div class="qrcode-title">请使用支付宝扫码支付</div>
            <div class="qrcode" id="qrcode"></div>
            <div class="qrcode-tip">
                打开支付宝APP，扫描上方二维码完成支付<br>
                支付完成后页面将自动更新状态
            </div>
        </div>

        <div id="status"></div>

        <div class="footer">
            <p>支付过程中请勿关闭页面</p>
            <p>如有问题请联系技术支持</p>
            <p>Powered by Pointer Center & Alipay</p>
        </div>
    </div>

    <script>
        // 配置
        const API_BASE = window.location.origin; // 使用当前页面的域名，通过代理访问API
        
        // DOM 元素
        const qrBtn = document.getElementById('qrBtn');
        const webBtn = document.getElementById('webBtn');
        const payBtn = document.getElementById('payBtn');
        const qrcodeContainer = document.getElementById('qrcodeContainer');
        const qrcodeDiv = document.getElementById('qrcode');
        const statusDiv = document.getElementById('status');
        const userIdInput = document.getElementById('userIdInput');
        const subjectInput = document.getElementById('subjectInput');
        const amountInput = document.getElementById('amountInput');
        const amountDisplay = document.getElementById('amount');

        // 状态变量
        let currentPaymentMethod = 'qr';
        let currentOutTradeNo = null;
        let pollInterval = null;

        // 初始化
        init();

        function init() {
            // 绑定事件
            qrBtn.addEventListener('click', () => selectPaymentMethod('qr'));
            webBtn.addEventListener('click', () => selectPaymentMethod('web'));
            payBtn.addEventListener('click', handlePayment);
            amountInput.addEventListener('input', updateAmountDisplay);
            
            // 页面卸载时清理定时器
            window.addEventListener('beforeunload', () => {
                if (pollInterval) {
                    clearInterval(pollInterval);
                }
            });
        }

        function selectPaymentMethod(method) {
            currentPaymentMethod = method;
            
            // 更新按钮状态
            qrBtn.classList.toggle('active', method === 'qr');
            webBtn.classList.toggle('active', method === 'web');
            
            // 隐藏二维码容器
            qrcodeContainer.style.display = 'none';
            statusDiv.style.display = 'none';
            
            // 重置支付状态
            resetPaymentState();
        }

        function updateAmountDisplay() {
            const amount = amountInput.value || '0.01';
            amountDisplay.textContent = amount;
        }

        function resetPaymentState() {
            payBtn.textContent = "立即支付";
            payBtn.disabled = false;
            currentOutTradeNo = null;
            if (pollInterval) {
                clearInterval(pollInterval);
                pollInterval = null;
            }
        }

        function updateStatus(message, type) {
            statusDiv.innerHTML = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
        }

        async function handlePayment() {
            if (payBtn.disabled) return;

            // 验证输入
            const userId = userIdInput.value.trim();
            const subject = subjectInput.value.trim();
            const amount = amountInput.value.trim();

            if (!userId || !subject || !amount) {
                updateStatus('请填写完整的支付信息', 'error');
                return;
            }

            if (parseFloat(amount) <= 0) {
                updateStatus('支付金额必须大于0', 'error');
                return;
            }

            payBtn.disabled = true;
            payBtn.innerHTML = '<span class="loading"></span>创建订单中...';
            statusDiv.style.display = 'none';

            try {
                if (currentPaymentMethod === 'qr') {
                    await handleQRPayment(userId, subject, amount);
                } else {
                    await handleWebPayment(userId, subject, amount);
                }
            } catch (error) {
                console.error('支付失败:', error);
                updateStatus('支付失败：' + error.message, 'error');
                resetPaymentState();
            }
        }

        async function handleQRPayment(userId, subject, amount) {
            const response = await fetch(`${API_BASE}/api/v1/alipay/qrcode`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    amount: amount,
                    subject: subject,
                    body: '通过Pointer Center支付',
                    user_id: userId,
                    order_id: 'ORDER_' + Date.now()
                })
            });

            const result = await response.json();

            if (result.success) {
                // 显示二维码
                generateQRCode(result.data.qr_code);
                currentOutTradeNo = result.data.out_trade_no;
                
                // 更新UI
                payBtn.textContent = "支付中...";
                updateStatus("请使用支付宝扫码支付", "pending");
                
                // 开始轮询支付状态
                startPolling(currentOutTradeNo);
            } else {
                throw new Error(result.message || '创建二维码失败');
            }
        }

        async function handleWebPayment(userId, subject, amount) {
            const response = await fetch(`${API_BASE}/api/v1/alipay/web`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    amount: amount,
                    subject: subject,
                    body: '通过Pointer Center支付',
                    user_id: userId,
                    order_id: 'ORDER_' + Date.now()
                })
            });

            const result = await response.json();

            if (result.success) {
                currentOutTradeNo = result.data.out_trade_no;
                
                // 更新UI
                updateStatus("正在跳转到支付宝...", "pending");
                
                // 跳转到支付宝
                setTimeout(() => {
                    window.open(result.data.payment_url, '_blank');
                    
                    // 开始轮询支付状态
                    payBtn.textContent = "支付中...";
                    updateStatus("请在新窗口中完成支付", "pending");
                    startPolling(currentOutTradeNo);
                }, 1000);
            } else {
                throw new Error(result.message || '创建支付链接失败');
            }
        }

        function generateQRCode(url) {
            qrcodeDiv.innerHTML = '';
            qrcodeContainer.style.display = 'block';

            try {
                if (typeof QRious !== 'undefined') {
                    const canvas = document.createElement('canvas');
                    const qr = new QRious({
                        element: canvas,
                        value: url,
                        size: 200,
                        foreground: '#667eea',
                        background: '#ffffff'
                    });

                    qrcodeDiv.appendChild(canvas);
                } else {
                    // 备用方案：使用在线二维码生成服务
                    const img = document.createElement('img');
                    img.src = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(url);
                    img.style.width = '200px';
                    img.style.height = '200px';
                    img.alt = '支付二维码';
                    qrcodeDiv.appendChild(img);
                }
            } catch (error) {
                console.error('生成二维码失败:', error);
                qrcodeDiv.innerHTML = '<div style="padding: 20px; color: #e53e3e;">二维码生成失败，请刷新页面重试</div>';
            }
        }

        async function checkPaymentStatus(outTradeNo) {
            try {
                const response = await fetch(`${API_BASE}/api/v1/alipay/status?out_trade_no=${outTradeNo}`);
                const result = await response.json();

                if (result.success) {
                    return result.data.status;
                }
                return 'UNKNOWN';
            } catch (error) {
                console.error('检查支付状态失败:', error);
                return 'ERROR';
            }
        }

        function startPolling(outTradeNo) {
            if (pollInterval) {
                clearInterval(pollInterval);
            }

            pollInterval = setInterval(async () => {
                const status = await checkPaymentStatus(outTradeNo);

                if (status === 'TRADE_SUCCESS') {
                    clearInterval(pollInterval);
                    updateStatus('✅ 支付成功！', 'success');
                    payBtn.textContent = "支付成功";
                    payBtn.disabled = true;
                    qrcodeContainer.style.display = 'none';
                } else if (status === 'TRADE_CLOSED') {
                    clearInterval(pollInterval);
                    updateStatus('❌ 支付已关闭', 'error');
                    resetPaymentState();
                    qrcodeContainer.style.display = 'none';
                }
            }, 2000); // 每2秒检查一次
        }
    </script>
</body>
</html>
