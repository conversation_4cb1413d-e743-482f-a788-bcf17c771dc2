#!/usr/bin/env python3
"""
Pointer Center CORS Proxy Server
解决前端页面访问后端API的跨域问题
"""

import http.server
import socketserver
import urllib.request
import urllib.parse
import json
import os
import sys
from urllib.error import URLError, HTTPError

class CORSProxyHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的代理HTTP请求处理器"""
    
    # 后端API服务器地址
    API_TARGET = "http://localhost:8012"
    
    def __init__(self, *args, **kwargs):
        # 设置静态文件服务目录为当前目录
        super().__init__(*args, directory=".", **kwargs)
    
    def end_headers(self):
        """添加CORS头"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        self.send_header('Access-Control-Allow-Credentials', 'true')
        super().end_headers()
    
    def do_OPTIONS(self):
        """处理预检请求"""
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        """处理GET请求"""
        if self.path.startswith('/api/'):
            self.proxy_request()
        elif self.path == '/':
            # 根路径重定向到支付页面
            self.send_response(302)
            self.send_header('Location', '/pointer_center_payment.html')
            self.end_headers()
        elif self.path == '/health':
            self.send_health_check()
        else:
            # 静态文件服务
            super().do_GET()
    
    def do_POST(self):
        """处理POST请求"""
        if self.path.startswith('/api/'):
            self.proxy_request()
        else:
            self.send_error(404, "Not Found")
    
    def proxy_request(self):
        """代理API请求到后端服务器"""
        try:
            # 构建目标URL
            target_url = self.API_TARGET + self.path

            # 获取请求体数据
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length) if content_length > 0 else None

            # 创建请求
            req = urllib.request.Request(target_url, data=post_data)

            # 复制请求头（排除一些不需要的头）
            skip_headers = {'host', 'content-length', 'connection'}
            for header, value in self.headers.items():
                if header.lower() not in skip_headers:
                    req.add_header(header, value)

            # 设置Content-Type
            if post_data and 'Content-Type' not in self.headers:
                req.add_header('Content-Type', 'application/json')

            print(f"[{self.log_date_time_string()}] Proxying {self.command} {self.path} -> {target_url}")
            if post_data:
                print(f"[{self.log_date_time_string()}] Request body: {post_data.decode('utf-8', errors='ignore')[:200]}...")

            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                response_data = response.read()
                response_code = response.getcode()

                print(f"[{self.log_date_time_string()}] Backend response: {response_code}")
                print(f"[{self.log_date_time_string()}] Response body: {response_data.decode('utf-8', errors='ignore')[:200]}...")

                # 发送响应状态
                self.send_response(response_code)

                # 复制响应头
                for header, value in response.headers.items():
                    if header.lower() not in {'connection', 'transfer-encoding'}:
                        self.send_header(header, value)

                self.end_headers()

                # 发送响应体
                self.wfile.write(response_data)

        except HTTPError as e:
            print(f"[{self.log_date_time_string()}] HTTP Error {e.code}: {e.reason}")
            # 读取错误响应体
            try:
                error_body = e.read().decode('utf-8', errors='ignore')
                print(f"[{self.log_date_time_string()}] Error response body: {error_body[:200]}...")
            except:
                pass
            self.send_error_json(e.code, f"Backend HTTP Error: {e.reason}")
        except URLError as e:
            print(f"[{self.log_date_time_string()}] URL Error: {e.reason}")
            self.send_error_json(502, f"Backend service unavailable: {e.reason}")
        except Exception as e:
            print(f"[{self.log_date_time_string()}] Proxy Error: {str(e)}")
            self.send_error_json(500, f"Proxy error: {str(e)}")

    def send_error_json(self, code, message):
        """发送JSON格式的错误响应"""
        error_data = {
            "success": False,
            "message": message,
            "error": "proxy_error",
            "timestamp": self.log_date_time_string()
        }

        response_data = json.dumps(error_data, indent=2).encode('utf-8')

        self.send_response(code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(response_data)))
        self.end_headers()
        self.wfile.write(response_data)
    
    def send_health_check(self):
        """发送健康检查响应"""
        health_data = {
            "status": "ok",
            "timestamp": self.log_date_time_string(),
            "proxy_target": self.API_TARGET,
            "server": "Python CORS Proxy"
        }
        
        response_data = json.dumps(health_data, indent=2).encode('utf-8')
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(response_data)))
        self.end_headers()
        self.wfile.write(response_data)
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.log_date_time_string()}] {format % args}")

def main():
    """主函数"""
    # 默认配置
    PORT = int(os.environ.get('PORT', 8000))
    API_TARGET = os.environ.get('API_TARGET', 'http://localhost:8012')
    
    # 更新API目标地址
    CORSProxyHandler.API_TARGET = API_TARGET
    
    # 检查HTML文件是否存在
    current_dir = os.path.dirname(__file__) if __file__ else '.'
    html_files = ['pointer_center_payment.html', 'test_pointer_center.html']
    missing_files = []

    for html_file in html_files:
        file_path = os.path.join(current_dir, html_file)
        if not os.path.exists(file_path):
            missing_files.append(html_file)

    if missing_files:
        print(f"⚠️  Warning: Some HTML files not found: {', '.join(missing_files)}")
        print("Make sure the HTML files are in the same directory as this script")
    
    try:
        # 创建服务器
        with socketserver.TCPServer(("", PORT), CORSProxyHandler) as httpd:
            print(f"""
🚀 Pointer Center CORS Proxy Server Started!

📍 Server Info:
   • Port: {PORT}
   • API Target: {API_TARGET}
   • Static Files: current directory

🌐 Available URLs:
   • Main Page: http://localhost:{PORT}/
   • Payment Page: http://localhost:{PORT}/pointer_center_payment.html
   • Test Page: http://localhost:{PORT}/test_pointer_center.html
   • Health Check: http://localhost:{PORT}/health

🔧 API Proxy:
   • All /api/* requests will be proxied to {API_TARGET}
   • CORS is enabled for all origins

💡 Usage:
   1. Make sure your backend service is running on {API_TARGET}
   2. Open http://localhost:{PORT}/ in your browser
   3. The proxy will handle CORS issues automatically

🛑 Press Ctrl+C to stop the server
            """)
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Error: Port {PORT} is already in use")
            print(f"Try using a different port: PORT={PORT+1} python3 {sys.argv[0]}")
        else:
            print(f"❌ Error starting server: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
