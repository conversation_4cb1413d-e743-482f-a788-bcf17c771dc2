# Pointer Center Python CORS Proxy

这是一个用Python编写的CORS代理服务器，用于解决 Pointer Center 前端页面跨域问题。

## 功能特性

- ✅ 解决前端访问后端API的跨域问题
- ✅ 静态文件服务（HTML页面）
- ✅ API请求代理转发
- ✅ 完整的CORS支持
- ✅ 错误处理和日志记录
- ✅ 健康检查端点
- ✅ 纯Python实现，无需额外依赖

## 快速开始

### 方法1：直接启动代理服务器

```bash
cd experiment/web
python3 cors_proxy_server.py
```

### 方法2：使用启动脚本

```bash
cd experiment/web
python3 start_server.py
```

### 方法3：替代原有的http.server命令

原来使用：
```bash
python3 -m http.server 8000
```

现在使用：
```bash
python3 cors_proxy_server.py
```

## 访问页面

服务器启动后，可以访问以下URL：

- 主页面: http://localhost:8000/
- 支付页面: http://localhost:8000/pointer_center_payment.html
- 测试页面: http://localhost:8000/test_pointer_center.html
- 健康检查: http://localhost:8000/health

## 配置选项

可以通过环境变量配置服务器：

```bash
# 设置代理服务器端口（默认8000）
PORT=8000 python3 cors_proxy_server.py

# 设置后端API地址（默认http://localhost:8012）
API_TARGET=http://localhost:8012 python3 cors_proxy_server.py

# 同时设置多个参数
PORT=9000 API_TARGET=http://localhost:9012 python3 cors_proxy_server.py
```

## 工作原理

1. **静态文件服务**: 服务器会服务 `../alipay/` 目录下的HTML文件
2. **API代理**: 所有 `/api/*` 请求会被代理转发到后端服务器
3. **CORS处理**: 自动添加必要的CORS头，解决跨域问题
4. **错误处理**: 提供友好的错误信息和详细的日志记录

## 使用示例

### 基本使用

```bash
# 1. 确保后端服务运行在 localhost:8012
# 2. 启动代理服务器
cd experiment/web
python3 cors_proxy_server.py

# 3. 打开浏览器访问 http://localhost:8000/
```

### 自定义配置

```bash
# 使用不同端口
PORT=9000 python3 cors_proxy_server.py

# 使用不同的后端API地址
API_TARGET=http://*************:8012 python3 cors_proxy_server.py
```

## 目录结构

```
experiment/web/
├── cors_proxy_server.py    # 主要的CORS代理服务器
├── start_server.py         # 启动脚本
├── README.md              # 说明文档
└── ../alipay/             # 静态HTML文件目录
    ├── pointer_center_payment.html
    └── test_pointer_center.html
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :8000
   # 或使用不同端口
   PORT=8001 python3 cors_proxy_server.py
   ```

2. **后端服务连接失败**
   - 确保后端服务正在运行
   - 检查 API_TARGET 配置是否正确
   - 查看代理服务器日志

3. **静态文件找不到**
   - 确保在 `experiment/web/` 目录下运行脚本
   - 检查 `../alipay/` 目录是否存在

4. **CORS问题仍然存在**
   - 确保通过代理服务器访问页面（http://localhost:8000）
   - 不要直接打开HTML文件
   - 检查浏览器开发者工具的网络请求

### 日志查看

代理服务器会输出详细的请求日志：
```
[20/Jan/2024 10:30:00] Proxying POST /api/v1/alipay/qrcode -> http://localhost:8012/api/v1/alipay/qrcode
[20/Jan/2024 10:30:01] "POST /api/v1/alipay/qrcode HTTP/1.1" 200 -
```

## 与原http.server的区别

| 功能 | python3 -m http.server | cors_proxy_server.py |
|------|------------------------|---------------------|
| 静态文件服务 | ✅ | ✅ |
| CORS支持 | ❌ | ✅ |
| API代理 | ❌ | ✅ |
| 错误处理 | 基础 | 详细 |
| 日志记录 | 基础 | 详细 |
| 健康检查 | ❌ | ✅ |

## 技术实现

- 基于Python标准库 `http.server` 和 `urllib`
- 继承 `SimpleHTTPRequestHandler` 添加代理功能
- 自动处理CORS预检请求（OPTIONS）
- 支持GET和POST请求的代理转发
- 智能的错误处理和超时机制
