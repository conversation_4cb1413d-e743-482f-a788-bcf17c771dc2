<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pointer Center 支付测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        button {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-qr {
            background-color: #1890ff;
            color: white;
        }
        .btn-qr:hover {
            background-color: #40a9ff;
        }
        .btn-web {
            background-color: #52c41a;
            color: white;
        }
        .btn-web:hover {
            background-color: #73d13d;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }
        .error {
            background-color: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }
        .info {
            background-color: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0958d9;
        }
        .qrcode {
            text-align: center;
            margin: 20px 0;
        }
        .qrcode img {
            max-width: 200px;
            border: 1px solid #ddd;
            padding: 10px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Pointer Center 支付宝支付测试</h1>
        
        <div class="form-group">
            <label for="apiBase">API 基础地址:</label>
            <input type="text" id="apiBase" value="" placeholder="留空使用代理服务器">
        </div>

        <div class="form-group">
            <label for="userId">用户ID:</label>
            <input type="text" id="userId" value="test-user-123" placeholder="用户ID">
        </div>

        <div class="form-group">
            <label for="amount">支付金额 (元):</label>
            <input type="number" id="amount" value="0.01" step="0.01" min="0.01" placeholder="0.01">
        </div>

        <div class="form-group">
            <label for="subject">商品标题:</label>
            <input type="text" id="subject" value="测试商品支付" placeholder="商品标题">
        </div>

        <div class="form-group">
            <label for="body">商品描述:</label>
            <textarea id="body" rows="3" placeholder="商品描述（可选）">通过Pointer Center进行的测试支付</textarea>
        </div>

        <div class="form-group">
            <label for="orderId">订单ID (可选):</label>
            <input type="text" id="orderId" placeholder="留空将自动生成">
        </div>

        <div class="button-group">
            <button class="btn-qr" onclick="createQRPayment()">📱 创建扫码支付</button>
            <button class="btn-web" onclick="createWebPayment()">🌐 创建网页支付</button>
        </div>

        <div class="button-group">
            <button onclick="queryPaymentStatus()" style="background-color: #722ed1; color: white;">🔍 查询支付状态</button>
            <button onclick="clearResult()" style="background-color: #8c8c8c; color: white;">🗑️ 清空结果</button>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let currentOutTradeNo = null;

        function getFormData() {
            return {
                user_id: document.getElementById('userId').value.trim(),
                amount: document.getElementById('amount').value.trim(),
                subject: document.getElementById('subject').value.trim(),
                body: document.getElementById('body').value.trim(),
                order_id: document.getElementById('orderId').value.trim() || 'ORDER_' + Date.now()
            };
        }

        function getApiBase() {
            return document.getElementById('apiBase').value.trim() || window.location.origin;
        }

        function showResult(content, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = content;
            resultDiv.className = `result ${type}`;
        }

        function clearResult() {
            document.getElementById('result').innerHTML = '';
            currentOutTradeNo = null;
        }

        async function createQRPayment() {
            const data = getFormData();
            const apiBase = getApiBase();

            if (!data.user_id || !data.amount || !data.subject) {
                showResult('请填写必要的字段：用户ID、支付金额、商品标题', 'error');
                return;
            }

            try {
                showResult('正在创建扫码支付...', 'info');

                const response = await fetch(`${apiBase}/api/v1/alipay/qrcode`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    currentOutTradeNo = result.data.out_trade_no;
                    const qrCodeHtml = `
<strong>✅ 扫码支付创建成功！</strong>

📋 订单信息:
• 订单号: ${result.data.out_trade_no}
• 过期时间: ${result.data.expire_time}

📱 二维码:
<div class="qrcode">
    <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(result.data.qr_code)}" alt="支付二维码">
</div>

🔗 支付链接:
${result.data.qr_code}

💡 使用支付宝APP扫描上方二维码完成支付
                    `;
                    showResult(qrCodeHtml, 'success');
                } else {
                    showResult(`❌ 创建失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        async function createWebPayment() {
            const data = getFormData();
            const apiBase = getApiBase();

            if (!data.user_id || !data.amount || !data.subject) {
                showResult('请填写必要的字段：用户ID、支付金额、商品标题', 'error');
                return;
            }

            try {
                showResult('正在创建网页支付...', 'info');

                const response = await fetch(`${apiBase}/api/v1/alipay/web`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    currentOutTradeNo = result.data.out_trade_no;
                    const webPaymentHtml = `
<strong>✅ 网页支付创建成功！</strong>

📋 订单信息:
• 订单号: ${result.data.out_trade_no}
• 过期时间: ${result.data.expire_time}

🌐 支付链接:
${result.data.payment_url}

💡 点击下方按钮跳转到支付宝完成支付:
<div style="text-align: center; margin: 20px 0;">
    <a href="${result.data.payment_url}" target="_blank" style="display: inline-block; padding: 15px 30px; background-color: #1890ff; color: white; text-decoration: none; border-radius: 5px; font-weight: bold;">🚀 前往支付宝支付</a>
</div>
                    `;
                    showResult(webPaymentHtml, 'success');
                } else {
                    showResult(`❌ 创建失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        async function queryPaymentStatus() {
            if (!currentOutTradeNo) {
                showResult('请先创建支付订单', 'error');
                return;
            }

            const apiBase = getApiBase();

            try {
                showResult('正在查询支付状态...', 'info');

                const response = await fetch(`${apiBase}/api/v1/alipay/status?out_trade_no=${currentOutTradeNo}`);
                const result = await response.json();

                if (result.success) {
                    const statusHtml = `
<strong>📊 支付状态查询结果</strong>

📋 订单信息:
• 订单号: ${result.data.out_trade_no}
• 支付宝交易号: ${result.data.trade_no || '暂无'}
• 支付状态: ${result.data.status}
• 订单金额: ${result.data.total_amount || '暂无'}

📅 时间信息:
• 查询时间: ${new Date().toLocaleString()}

💡 状态说明:
• WAIT_BUYER_PAY: 等待买家付款
• TRADE_SUCCESS: 交易支付成功
• TRADE_CLOSED: 交易关闭
• TRADE_FINISHED: 交易完成
                    `;
                    showResult(statusHtml, result.data.status === 'TRADE_SUCCESS' ? 'success' : 'info');
                } else {
                    showResult(`❌ 查询失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 页面加载完成后显示使用说明
        window.onload = function() {
            showResult(`
🎯 Pointer Center 支付宝支付测试工具

📝 使用说明:
1. 确保 Pointer Center 服务正在运行
2. 修改 API 基础地址（如果需要）
3. 填写测试数据
4. 选择支付方式进行测试
5. 使用查询功能检查支付状态

⚠️ 注意事项:
• 请使用测试环境和测试金额
• 确保支付宝配置正确
• 网页支付会在新窗口打开
            `, 'info');
        };
    </script>
</body>
</html>
