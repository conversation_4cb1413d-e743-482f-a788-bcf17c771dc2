#!/usr/bin/env python3
"""
启动脚本 - 自动启动CORS代理服务器
可以替代 python3 -m http.server 8000 命令
"""

import subprocess
import sys
import os

def main():
    """启动CORS代理服务器"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    proxy_script = os.path.join(script_dir, 'cors_proxy_server.py')
    
    if not os.path.exists(proxy_script):
        print("❌ Error: cors_proxy_server.py not found")
        sys.exit(1)
    
    try:
        # 启动代理服务器
        print("🚀 Starting Pointer Center CORS Proxy Server...")
        subprocess.run([sys.executable, proxy_script], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
