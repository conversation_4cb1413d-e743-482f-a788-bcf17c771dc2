{"__AUTOGENERATED_FILE_DO_NOT_MODIFY_THIS_FILE_MANUALLY": "THERE_IS_NO_DATA_ONLY_ZUUL", "__INPUT_ARTIFACTS_HASH": -522077468, "__RESOLVED_ARTIFACTS_HASH": -2009400575, "conflict_resolution": {"com.google.errorprone:error_prone_annotations:2.30.0": "com.google.errorprone:error_prone_annotations:2.36.0", "com.google.guava:failureaccess:1.0.1": "com.google.guava:failureaccess:1.0.2", "com.google.guava:guava:32.0.1-jre": "com.google.guava:guava:33.4.0-jre", "com.google.guava:guava:33.3.1-android": "com.google.guava:guava:33.4.0-jre", "com.google.protobuf:protobuf-java:3.21.12": "com.google.protobuf:protobuf-java:3.25.5", "io.opencensus:opencensus-api:0.31.0": "io.opencensus:opencensus-api:0.31.1", "org.checkerframework:checker-qual:3.12.0": "org.checkerframework:checker-qual:3.43.0"}, "artifacts": {"com.beust:jcommander": {"shasums": {"jar": "deeac157c8de6822878d85d0c7bc8467a19cc8484d37788f7804f039dde280b1", "sources": "cc39d22f3cf29c2033fb526e5600ae8fec36e316274b0c07fa14c1a4a38eca3b"}, "version": "1.82"}, "com.fasterxml.jackson.core:jackson-annotations": {"shasums": {"jar": "581bd61000ef7648943f781ca05689e56d03f6052748365a8e2b3a9b5d3fa32f", "sources": "43bdeaa3510e49a035f6ff670882a0c1a6df21c255199a37f6ed660e4839e19f"}, "version": "2.18.2"}, "com.fasterxml.jackson.core:jackson-core": {"shasums": {"jar": "d8054ae7c0d1c2d2f55d28e46026ebe5892881f3fab5f439233184381c3b4a1f", "sources": "ab5a6bf525586bee936b7127c0d0d6f0d61bb15d1b49edff845c9924810bd91c"}, "version": "2.18.2"}, "com.fasterxml.jackson.core:jackson-databind": {"shasums": {"jar": "4b364e6850dc89172fcf1d4dd26b8ff5488eda44ff4657e22dd265203dd5ab3c", "sources": "a543f696cc9db5a009f2c548270ced3ac0232c5d947b0ca3e62605dfccc279a2"}, "version": "2.18.2"}, "com.fasterxml.jackson.datatype:jackson-datatype-jdk8": {"shasums": {"jar": "f30d77f5826b9e9813342e84ab412095ed4ed5cf4fef6f93cebb848cb0fd0294", "sources": "82cc3b5d41bf8b3df9492d81fecab5af4aa8cd7663f7fc6d5b9801f66f76fe74"}, "version": "2.18.2"}, "com.fasterxml.jackson.datatype:jackson-datatype-jsr310": {"shasums": {"jar": "e2d202d4606e23aeaf8a5a9632db06f5fefd5b63d251c3f503f9faaa78530e5c", "sources": "40abc5db4e4a68da4a1841d7b3cd68bd60fbf7db00bb05dd6aa4617ea39856ee"}, "version": "2.18.2"}, "com.github.spotbugs:spotbugs-annotations": {"shasums": {"jar": "4548b74a815ed44f5480ca4f06204a8b00809dc7e5f6a825a9edf18f40377b65", "sources": "b5d0110b70b9c44915f2c3375d1b700acb6d409152baf70030787d17a684469b"}, "version": "4.8.6"}, "com.google.android:annotations": {"shasums": {"jar": "ba734e1e84c09d615af6a09d33034b4f0442f8772dec120efb376d86a565ae15", "sources": "e9b667aa958df78ea1ad115f7bbac18a5869c3128b1d5043feb360b0cfce9d40"}, "version": "4.1.1.4"}, "com.google.api.grpc:proto-google-common-protos": {"shasums": {"jar": "0b27938f3d28ccd6884945d7e4f75f4e26a677bbf3cd39bbcb694f130f782aa9", "sources": "e58038bd20d37c93583185013eb38de50f6da4a6bf0ace1f8ebb911f14bccea5"}, "version": "2.51.0"}, "com.google.auth:google-auth-library-credentials": {"shasums": {"jar": "5dbf1207d14e093f67995f457cb69c3cf49bed1364150b23465e09acada65d96", "sources": "edc2499f614f01050b044b046789d28079302e8f9af141bca00ba71188b7c5e4"}, "version": "1.24.1"}, "com.google.auth:google-auth-library-oauth2-http": {"shasums": {"jar": "88a75cd4448ea2f3b46e48a89497a6cf0985a5fa4e21274af4940e07f59f6eaf", "sources": "0c91f45329bb9acc5db7aca708ae57f4501e1ce6b18c1c7aa5967b19b7e57294"}, "version": "1.24.1"}, "com.google.auto.value:auto-value": {"shasums": {"jar": "aaf8d637bfed3c420436b9facf1b7a88d12c8785374e4202382783005319c2c3", "sources": "4bff06fe077d68f964bd5e05f020ed78fd7870730441e403a2eb306360c4890a"}, "version": "1.11.0"}, "com.google.auto.value:auto-value-annotations": {"shasums": {"jar": "5a055ce4255333b3346e1a8703da5bf8ff049532286fdcd31712d624abe111dd", "sources": "d7941e5f19bb38afcfa85350d57e5245856c23c98c2bbe32f6d31b5577f2bc33"}, "version": "1.11.0"}, "com.google.code.findbugs:jsr305": {"shasums": {"jar": "766ad2a0783f2687962c8ad74ceecc38a28b9f72a2d085ee438b7813e928d0c7", "sources": "1c9e85e272d0708c6a591dc74828c71603053b48cc75ae83cce56912a2aa063b"}, "version": "3.0.2"}, "com.google.code.gson:gson": {"shasums": {"jar": "57928d6e5a6edeb2abd3770a8f95ba44dce45f3b23b7a9dc2b309c581552a78b", "sources": "49a853f71bc874ee1898a4ad5009b57d0c536e5a998b3890253ffbf4b7276ad3"}, "version": "2.11.0"}, "com.google.dagger:dagger": {"shasums": {"jar": "7ab0d8278a532888de19025dc6ee70a80ecd4567778a6a7e7ec256a2073e40a3", "sources": "acb531af3d31c3a0c82caae55d146ab3c7315bc0c48aa19919e3e66bb0fc0a96"}, "version": "2.54"}, "com.google.dagger:dagger-compiler": {"shasums": {"jar": "f3eab7ab8298f5d4816331b7e591aa461f3f90690ccd0d190f7d8a6084bd064d", "sources": "d0b539ffb94280fb47c5fc1f7d52c40725ae51b57ace7048400d00a0e4841677"}, "version": "2.54"}, "com.google.dagger:dagger-producers": {"shasums": {"jar": "027e638c3c541966e1fd535ea7f6dcb67dd9f3df846589e0ec001060372287b9", "sources": "3b5c44d429f29a5207cf7b7b346d347e8fe58bfbf4b520c480508b8fbfe13c51"}, "version": "2.54"}, "com.google.dagger:dagger-spi": {"shasums": {"jar": "7a990e528ea259b439cdd12ec8164519d8b85a57939a8d7c56a3aaa707e3b269", "sources": "35ffcf86483d9fc1c6cad91c420ed46340d899cc76eed89844dcf90a35138502"}, "version": "2.54"}, "com.google.devtools.ksp:symbol-processing-api": {"shasums": {"jar": "e38dd8199c65aae4bce9929bf39a563bf7658c600be5066c7e042cdd8de3137c", "sources": "6dbcfd749f6d489d999652801e20e8ee6c4a60c53a0e22bd89947e672f47ff6e"}, "version": "2.0.21-1.0.28"}, "com.google.errorprone:error_prone_annotations": {"shasums": {"jar": "77440e270b0bc9a249903c5a076c36a722c4886ca4f42675f2903a1c53ed61a5", "sources": "7e117e0931cb2cb4226372af336189b49edb79969d120ec958a6df0beacb0612"}, "version": "2.36.0"}, "com.google.googlejavaformat:google-java-format": {"shasums": {"jar": "938d0321c3aadc8e45a51e4334f61ca9f71137908e0fe8853925ee2125e98c6f", "sources": "d77cfc63467e20ffd863775063aac7822d8519d1cd74b5b57e2c7cf6a89f82ec"}, "version": "1.25.2"}, "com.google.guava:failureaccess": {"shasums": {"jar": "8a8f81cf9b359e3f6dfa691a1e776985c061ef2f223c9b2c80753e1b458e8064", "sources": "dd3bfa5e2ec5bc5397efb2c3cef044c192313ff77089573667ff97a60c6978e0"}, "version": "1.0.2"}, "com.google.guava:guava": {"shasums": {"jar": "b918c98a7e44dbe94ebd9fe3e40cddaadb5a93e6a78eb6008b42df237241e538", "sources": "55ef6603b6ab1f6e3ae810b127561650ed682eb5f3fb50a212a658a74087b457"}, "version": "33.4.0-jre"}, "com.google.guava:listenablefuture": {"shasums": {"jar": "b372a037d4230aa57fbeffdef30fd6123f9c0c2db85d0aced00c91b974f33f99", "sources": null}, "version": "9999.0-empty-to-avoid-conflict-with-guava"}, "com.google.http-client:google-http-client": {"shasums": {"jar": "390618d7b51704240b8fd28e1230fa35d220f93f4b4ba80f63e38db00dacb09e", "sources": "9419537a2973195619b43f76be92388b1e37a785503717d76afff5764884ebc2"}, "version": "1.44.2"}, "com.google.http-client:google-http-client-gson": {"shasums": {"jar": "1119b66685195310375b717de2215d6c5d14fa8ed9f57e07b4fecd461e7b9db7", "sources": "3bac061bdac5c5c67713b8db689a1d6342afcb07a87c2f7285dffc1729fc4825"}, "version": "1.44.2"}, "com.google.j2objc:j2objc-annotations": {"shasums": {"jar": "88241573467ddca44ffd4d74aa04c2bbfd11bf7c17e0c342c94c9de7a70a7c64", "sources": "bd60019a0423c3a025ef6ab24fe0761f5f45ffb48a8cca74a01b678de1105d38"}, "version": "3.0.0"}, "com.google.protobuf:protobuf-java": {"shasums": {"jar": "8540247fad9e06baefa8fb45eb313802d019f485f14300e0f9d6b556ed88e753", "sources": "d686e802329e42954e72e9b3b148b67eeb4f6f3ed327abc4508b79fda4937c3b"}, "version": "3.25.5"}, "com.google.re2j:re2j": {"shasums": {"jar": "7b52c72156dd7f98b3237a5b35c1d34fba381b21048c89208913ad80a45dfbd7", "sources": "4378db846db2d91c8028fe32efcb183a1f1b4157339d0a3b4c46b2c6b0655d7c"}, "version": "1.8"}, "com.google.truth:truth": {"shasums": {"jar": "14c297bc64ca8bc15b6baf67f160627e4562ec91624797e312e907b431113508", "sources": "9e6e49a3d2eefcadc0294878cb19fa6c6da305f2939c422f3cbd8caf9efe80bb"}, "version": "1.4.2"}, "com.squareup.okhttp:okhttp": {"shasums": {"jar": "88ac9fd1bb51f82bcc664cc1eb9c225c90dc4389d660231b4cc737bebfe7d0aa", "sources": "edfafce3b9abb0fc31b5c72cc5f533516a08f7b86754138f36595a6837746ddf"}, "version": "2.7.5"}, "com.squareup.okio:okio": {"shasums": {"jar": "a27f091d34aa452e37227e2cfa85809f29012a8ef2501a9b5a125a978e4fcbc1", "sources": "4f255d11829d7e1949be042efa70f933650a7d929e68d594c1608f97884a0535"}, "version": "2.10.0"}, "com.squareup:javapoet": {"shasums": {"jar": "4c7517e848a71b36d069d12bb3bf46a70fd4cda3105d822b0ed2e19c00b69291", "sources": "d1699067787846453fdcc104aeba3946f070fb2c167cfb3445838e4c86bb1f11"}, "version": "1.13.0"}, "com.squareup:kotlinpoet": {"shasums": {"jar": "2887ada1ca03dd83baa2758640d87e840d1907564db0ef88d2289c868a980492", "sources": "240a885acd6e3f3852644dc9e5820f4330fa2656b2c2c68ce67b04ae829dc89d"}, "version": "1.11.0"}, "commons-codec:commons-codec": {"shasums": {"jar": "e599d5318e97aa48f42136a2927e6dfa4e8881dff0e6c8e3109ddbbff51d7b7d", "sources": "901cb5d1f7c2877017c95d3c5efd5a497738d0162ef72cdf58e9cb13f50b2e9c"}, "version": "1.11"}, "io.grpc:grpc-api": {"shasums": {"jar": "2e896944cf513e0e5cfd32bcd72c89601a27c6ca56916f84b20f3a13bacf1b1f", "sources": "aa2974982805cc998f79e7c4d5d536744fd5520b56eb15b0179f9331c1edb3b7"}, "version": "1.62.2"}, "io.grpc:grpc-context": {"shasums": {"jar": "9959747df6a753119e1c1a3dff01aa766d2455f5e4860acaa305359e1d533a05", "sources": "c656b874e58c84ca975c3708f2e001dba76233385b6a5b7cb098868bd6ce38b1"}, "version": "1.62.2"}, "io.grpc:grpc-core": {"shasums": {"jar": "18439902c473a2c1511e517d13b8ae796378850a8eda43787c6ba778fa90fcc5", "sources": "351325425f07abc1d274d5afea1a3b8f48cf49b6f07a128ebe7e71a732188f92"}, "version": "1.62.2"}, "io.grpc:grpc-netty-shaded": {"shasums": {"jar": "b3f1823ef30ca02ac721020f4b6492248efdbd0548c78e893d5d245cbca2cc60", "sources": "c656b874e58c84ca975c3708f2e001dba76233385b6a5b7cb098868bd6ce38b1"}, "version": "1.62.2"}, "io.grpc:grpc-protobuf": {"shasums": {"jar": "66a0b196318bdfd817d965d2d82b9c81dfced8eb08c0f7510fcb728d2994237a", "sources": "4020d5c7485d6dd261f07b3deeabfe1d06fcd13e8a20fc147683926a03c38ef1"}, "version": "1.62.2"}, "io.grpc:grpc-protobuf-lite": {"shasums": {"jar": "79997989a8c2b5bf4dd18182a2df2e2f668703d68ba7c317e7a07809d33f91f4", "sources": "fd38569d1c610d12e0844873ea18542503334b5f4db8c2239b68553ccc58942b"}, "version": "1.62.2"}, "io.grpc:grpc-stub": {"shasums": {"jar": "fb4ca679a4214143406c65ac4167b2b5e2ee2cab1fc101566bb1c4695d105e36", "sources": "da613a25d08f3915ab1d54634c6dc4ffa7441fea74d53fcd46e68afe53b1b29a"}, "version": "1.62.2"}, "io.grpc:grpc-util": {"shasums": {"jar": "3c7103e6f3738571e3aeda420fe2a6ac68e354534d8b66f41897b6755b48b735", "sources": "eea606bb4b3b6df7863604fd82321f8713bc1e13e8d124c8ae1374fba174052e"}, "version": "1.62.2"}, "io.netty:netty-buffer": {"shasums": {"jar": "46d74e79125aacc055c31f18152fdc5d4a569aa8d60091203d0baa833973ac3c", "sources": "b4e7d5c0d3cbbed2bfc2c26922e6e8989330e7d2ec653a39ab40635d24ee5850"}, "version": "4.1.110.Final"}, "io.netty:netty-codec": {"shasums": {"jar": "9eccce9a8d827bb8ce84f9c3183fec58bd1c96a51010cf711297746034af3701", "sources": "da6a146add7a60a96ee363d59db2fc79d4858d0c73ec9afe995a13f4bbd4a110"}, "version": "4.1.110.Final"}, "io.netty:netty-codec-http": {"shasums": {"jar": "dc0d6af5054630a70ff0ef354f20aa7a6e46738c9fc5636ed3d4fe77e38bd48d", "sources": "b03b477f0607bbb6693ea1de41e4db3dbec1de616bea6feeba096fbf9fca1209"}, "version": "4.1.110.Final"}, "io.netty:netty-codec-http2": {"shasums": {"jar": "b546c75445a487bb7bcd5a94779caecce33582cf7be31b8b39fc0e65b1ee26fc", "sources": "555279bfb4ddd1fede5afaced0973bc763a6b643de7c6b006280f79d92e30889"}, "version": "4.1.110.Final"}, "io.netty:netty-codec-socks": {"shasums": {"jar": "976052a3c9bb280bc6d99f3a29e6404677cf958c3de05b205093d38c006b880c", "sources": "aedb4ffb91584e1b60d4c64f96c6033c204fcbca64b97ebf895df05ec6700dd3"}, "version": "4.1.110.Final"}, "io.netty:netty-common": {"shasums": {"jar": "9851ec66548b9e0d41164ce98943cdd4bbe305f68ddbd24eae52e4501a0d7b1a", "sources": "ca8799963fdfc712f0b87120f1b6a5e728444eeb41feaf2ecca52435b03bf099"}, "version": "4.1.110.Final"}, "io.netty:netty-handler": {"shasums": {"jar": "d5a08d7de364912e4285968de4d4cce3f01da4bb048d5c6937e5f2af1f8e148a", "sources": "3e8186c75527436a7fb99d0686cf138676df9498724aa787f7894d2ad6c4caa6"}, "version": "4.1.110.Final"}, "io.netty:netty-handler-proxy": {"shasums": {"jar": "ad54ab4fe9c47ef3e723d71251126db53e8db543871adb9eafc94446539eff52", "sources": "b1411f4864e9f76d435aca6adfe93bf8745718e1c0e1b7d73d0d1926e2e92a8a"}, "version": "4.1.110.Final"}, "io.netty:netty-resolver": {"shasums": {"jar": "a2e9b4ae7caa92fc5bd747e11d1dec20d81b18fc00959554302244ac5c56ce70", "sources": "792d057545fc3f1aa72c5571f6caf44d4166527322003cfb7b621c15bcd8d9e7"}, "version": "4.1.110.Final"}, "io.netty:netty-tcnative-boringssl-static": {"shasums": {"jar": "3f7b4c3a51737965cd5b53777782c125784420458d96513cfac7412e4d1fa0c3", "linux-aarch_64": "523c43f67ad9040d70f9494fc28eebf711d8c54e2aa30e3fd1a199c38740f53b", "linux-x86_64": "3d773aac73fe40f5d04de37ce14a1f7abd27caf0b3bd8275884f5d2968b3e254", "osx-aarch_64": "0454c53e65da6e253b2104d1ae26ecc79df4faf999e8924b659846b5bf41e996", "osx-x86_64": "9c6a23335f296689fb3538bc49e4e280ff163675212c6fe01c9cf2a9273ee19a", "sources": "0014a922b27f0db593210d8e4d2aa52205992efdffdb3b9940c18d8c0a18b63d", "windows-x86_64": "b3e3e0559df29a5624bcf529cb8e2bd9375c6d68164dda338e841677586a14c4"}, "version": "2.0.70.Final"}, "io.netty:netty-tcnative-classes": {"shasums": {"jar": "a79c1579313d4ad48a3ecc1d01a25da06d22d6449c3bcc369c2318749bcf55bc", "sources": "5ce3f7abc379d5d3193e01651f7ddcb6a572afa392c85a0f5a6e26241af332f1"}, "version": "2.0.70.Final"}, "io.netty:netty-transport": {"shasums": {"jar": "a42dd68390ca14b4ff2d40628a096c76485b4adb7c19602d5289321a0669e704", "sources": "8b30a2af314e828040b7736f2134192fcf932e9f34756c6e6168753dfae32cb5"}, "version": "4.1.110.Final"}, "io.netty:netty-transport-classes-epoll": {"shasums": {"jar": "8e59cec67de3b9f8afe4eccec11ed8ce4423948eeaf4ca512bf69324052ed510", "sources": "77ddaafe69b00258c8c342c98b76a7fef14e0f91124780b24e49ee49be9b896a"}, "version": "4.1.110.Final"}, "io.netty:netty-transport-native-epoll": {"shasums": {"linux-x86_64": "dcd60c6b3076af307ab877201a136e1f1066c9be809aaed827391a23909f9135", "sources": "156c8e44e9034fc82eff318b125391f456ae519c06c6a2001a4b20c0bd5abd6b"}, "version": "4.1.110.Final"}, "io.netty:netty-transport-native-unix-common": {"shasums": {"jar": "51717bb7471141950390c6713a449fdb1054d07e60737ee7dda7083796cdee48", "sources": "05c3989c61518b7dd6225a445401d1e8a9508a0d99a839847dac373e305e784d"}, "version": "4.1.110.Final"}, "io.opencensus:opencensus-api": {"shasums": {"jar": "f1474d47f4b6b001558ad27b952e35eda5cc7146788877fc52938c6eba24b382", "sources": "6748d57aaae81995514ad3e2fb11a95aa88e158b3f93450288018eaccf31e86b"}, "version": "0.31.1"}, "io.opencensus:opencensus-contrib-grpc-metrics": {"shasums": {"jar": "b28fc72490edd49c4c40a3c216b709200833fb361f7f602f1c7c9a527f7b7f63", "sources": "c45a239e4c4fa4ad564018c0ba0a94d669dbc9b1ed561fc0464b79ebb3c0d6e3"}, "version": "0.31.0"}, "io.opencensus:opencensus-contrib-http-util": {"shasums": {"jar": "3ea995b55a4068be22989b70cc29a4d788c2d328d1d50613a7a9afd13fdd2d0a", "sources": "d55afd5f96dc724bd903a77a38b0a344d0e59f02a64b9ab2f32618bc582ea924"}, "version": "0.31.1"}, "io.perfmark:perfmark-api": {"shasums": {"jar": "c7b478503ec524e55df19b424d46d27c8a68aeb801664fadd4f069b71f52d0f6", "sources": "311551ab29cf51e5a8abee6a019e88dee47d1ea71deb9fcd3649db9c51b237bc"}, "version": "0.27.0"}, "jakarta.inject:jakarta.inject-api": {"shasums": {"jar": "f7dc98062fccf14126abb751b64fab12c312566e8cbdc8483598bffcea93af7c", "sources": "44f4c73fda69f8b7d87136f0f789f042f54e8ff506d40aa126199baf3752d1c9"}, "version": "2.0.1"}, "javax.annotation:javax.annotation-api": {"shasums": {"jar": "e04ba5195bcd555dc95650f7cc614d151e4bcd52d29a10b8aa2197f3ab89ab9b", "sources": "128971e52e0d84a66e3b6e049dab8ad7b2c58b7e1ad37fa2debd3d40c2947b95"}, "version": "1.3.2"}, "javax.inject:javax.inject": {"shasums": {"jar": "91c77044a50c481636c32d916fd89c9118a72195390452c81065080f957de7ff", "sources": "c4b87ee2911c139c3daf498a781967f1eb2e75bc1a8529a2e7b328a15d0e433e"}, "version": "1"}, "junit:junit": {"shasums": {"jar": "8e495b634469d64fb8acfa3495a065cbacc8a0fff55ce1e31007be4c16dc57d3", "sources": "34181df6482d40ea4c046b063cb53c7ffae94bdf1b1d62695bdf3adf9dea7e3a"}, "version": "4.13.2"}, "net.ltgt.gradle.incap:incap": {"shasums": {"jar": "b625b9806b0f1e4bc7a2e3457119488de3cd57ea20feedd513db070a573a4ffd", "sources": "15c3cd213a214c94ef7ed262e00ab10c75d1680b0b9203b47801e7068de1cf5c"}, "version": "0.2"}, "org.apache.httpcomponents:httpclient": {"shasums": {"jar": "c8bc7e1c51a6d4ce72f40d2ebbabf1c4b68bfe76e732104b04381b493478e9d6", "sources": "55b01f9f4cbec9ac646866a4b64b176570d79e293a556796b5b0263d047ef8e6"}, "version": "4.5.14"}, "org.apache.httpcomponents:httpcore": {"shasums": {"jar": "6c9b3dd142a09dc468e23ad39aad6f75a0f2b85125104469f026e52a474e464f", "sources": "705f8cf3671093b6c1db16bbf6971a7ef400e3819784f1af53e5bc3e67b5a9a0"}, "version": "4.4.16"}, "org.apache.logging.log4j:log4j-api": {"shasums": {"jar": "5b4a0a0cd0e751ded431c162442bdbdd53328d1f8bb2bae5fc1bbeee0f66d80f", "sources": "a7e2290fd22242d85d8423efb8eff5fd7e14f80fed94bc959415d61cb8aa0d11"}, "version": "2.24.3"}, "org.apache.logging.log4j:log4j-core": {"shasums": {"jar": "7eb4084596ae25bd3c61698e48e8d0ab65a9260758884ed5cbb9c6e55c44a56a", "sources": "909158f99135ee7d3cb583e00eac314a22d873aca87440a2a0f20c7e3dc85440"}, "version": "2.24.3"}, "org.apache.tomcat:annotations-api": {"shasums": {"jar": "253829d3c12b7381d1044fc22c6436cff025fe0d459e4a329413e560a7d0dd13", "sources": null}, "version": "6.0.53"}, "org.checkerframework:checker-compat-qual": {"shasums": {"jar": "11d134b245e9cacc474514d2d66b5b8618f8039a1465cdc55bbc0b34e0008b7a", "sources": "7c63a4a46b2ef903f941aeac63da87dd345be3243b472796aa945fa715bf3ca9"}, "version": "2.5.5"}, "org.checkerframework:checker-qual": {"shasums": {"jar": "3fbc2e98f05854c3df16df9abaa955b91b15b3ecac33623208ed6424640ef0f6", "sources": "d6bdee58964cd05aabfca4e44947d3cbdada6bf617ed618b62b3b0d5a21de339"}, "version": "3.43.0"}, "org.codehaus.mojo:animal-sniffer-annotations": {"shasums": {"jar": "c720e6e5bcbe6b2f48ded75a47bccdb763eede79d14330102e0d352e3d89ed92", "sources": "4270ce5531ed0f12e4234e08f240ef3b45ee3ceeb16e28d44abc61c12cf522ca"}, "version": "1.24"}, "org.hamcrest:hamcrest-core": {"shasums": {"jar": "66fdef91e9739348df7a096aa384a5685f4e875584cce89386a7a47251c4d8e9", "sources": "e223d2d8fbafd66057a8848cc94222d63c3cedd652cc48eddc0ab5c39c0f84df"}, "version": "1.3"}, "org.jetbrains.kotlin:kotlin-reflect": {"shasums": {"jar": "3277ac102ae17aad10a55abec75ff5696c8d109790396434b496e75087854203", "sources": "39242dd149249c66edda7aa9822947e62aeff6194326039d4e8b5d528db5f006"}, "version": "1.6.10"}, "org.jetbrains.kotlin:kotlin-stdlib": {"shasums": {"jar": "f31cc53f105a7e48c093683bbd5437561d1233920513774b470805641bedbc09", "sources": "5995c780c3ac742fb277ced561ebd7b0739227ea7c93a6bd9c7cee6593493fce"}, "version": "2.0.21"}, "org.jetbrains.kotlin:kotlin-stdlib-common": {"shasums": {"jar": "a7112c9b3cefee418286c9c9372f7af992bd1e6e030691d52f60cb36dbec8320", "sources": "2ee47b54b4a20257d2ec73ddf34c44f72f4c7f6e5625b1a13de77c115a0e2afc"}, "version": "1.4.20"}, "org.jetbrains.kotlin:kotlin-stdlib-jdk7": {"shasums": {"jar": "2aedcdc6b69b33bdf5cc235bcea88e7cf6601146bb6bcdffdb312bbacd7be261", "sources": "01950537506f314570b0867e02da2a7a1d0cc4106a91ad43c9dc35f510b78a9e"}, "version": "1.6.10"}, "org.jetbrains.kotlin:kotlin-stdlib-jdk8": {"shasums": {"jar": "1456d82d039ea30d8485b032901f52bbf07e7cdbe8bb1f8708ad32a8574c41ce", "sources": "5520b4f2dfafdea57219d9c2ca219924897f0315d41282eda848a7c52c4477de"}, "version": "1.6.10"}, "org.jetbrains:annotations": {"shasums": {"jar": "ace2a10dc8e2d5fd34925ecac03e4988b2c0f851650c94b8cef49ba1bd111478", "sources": "42a5e144b8e81d50d6913d1007b695e62e614705268d8cf9f13dbdc478c2c68e"}, "version": "13.0"}, "org.jspecify:jspecify": {"shasums": {"jar": "1fad6e6be7557781e4d33729d49ae1cdc8fdda6fe477bb0cc68ce351eafdfbab", "sources": "adf0898191d55937fb3192ba971826f4f294292c4a960740f3c27310e7b70296"}, "version": "1.0.0"}, "org.ow2.asm:asm": {"shasums": {"jar": "3c6fac2424db3d4a853b669f4e3d1d9c3c552235e19a319673f887083c2303a1", "sources": "2b6e12f0da3d065ba628a024a8851ab0d5b5d3501dacfcc18769243250f4f77e"}, "version": "9.6"}}, "dependencies": {"com.fasterxml.jackson.core:jackson-databind": ["com.fasterxml.jackson.core:jackson-annotations", "com.fasterxml.jackson.core:jackson-core"], "com.fasterxml.jackson.datatype:jackson-datatype-jdk8": ["com.fasterxml.jackson.core:jackson-core", "com.fasterxml.jackson.core:jackson-databind"], "com.fasterxml.jackson.datatype:jackson-datatype-jsr310": ["com.fasterxml.jackson.core:jackson-annotations", "com.fasterxml.jackson.core:jackson-core", "com.fasterxml.jackson.core:jackson-databind"], "com.github.spotbugs:spotbugs-annotations": ["com.google.code.findbugs:jsr305"], "com.google.api.grpc:proto-google-common-protos": ["com.google.protobuf:protobuf-java"], "com.google.auth:google-auth-library-oauth2-http": ["com.google.auth:google-auth-library-credentials", "com.google.auto.value:auto-value-annotations", "com.google.code.findbugs:jsr305", "com.google.errorprone:error_prone_annotations", "com.google.guava:guava", "com.google.http-client:google-http-client", "com.google.http-client:google-http-client-gson"], "com.google.code.gson:gson": ["com.google.errorprone:error_prone_annotations"], "com.google.dagger:dagger": ["jakarta.inject:jakarta.inject-api", "javax.inject:javax.inject", "org.jspecify:jspecify"], "com.google.dagger:dagger-compiler": ["com.google.code.findbugs:jsr305", "com.google.dagger:dagger", "com.google.dagger:dagger-spi", "com.google.devtools.ksp:symbol-processing-api", "com.google.googlejavaformat:google-java-format", "com.google.guava:failureaccess", "com.google.guava:guava", "com.squareup:javapoet", "com.squareup:kotlinpoet", "javax.inject:javax.inject", "net.ltgt.gradle.incap:incap", "org.checkerframework:checker-compat-qual", "org.jetbrains.kotlin:kotlin-stdlib"], "com.google.dagger:dagger-producers": ["com.google.dagger:dagger", "com.google.guava:failureaccess", "com.google.guava:guava", "javax.inject:javax.inject", "org.checkerframework:checker-compat-qual"], "com.google.dagger:dagger-spi": ["com.google.code.findbugs:jsr305", "com.google.dagger:dagger", "com.google.devtools.ksp:symbol-processing-api", "com.google.guava:failureaccess", "com.google.guava:guava", "com.squareup:javapoet", "javax.inject:javax.inject"], "com.google.devtools.ksp:symbol-processing-api": ["org.jetbrains.kotlin:kotlin-stdlib"], "com.google.googlejavaformat:google-java-format": ["com.google.guava:guava"], "com.google.guava:guava": ["com.google.code.findbugs:jsr305", "com.google.errorprone:error_prone_annotations", "com.google.guava:failureaccess", "com.google.guava:listenablefuture", "com.google.j2objc:j2objc-annotations", "org.checkerframework:checker-qual"], "com.google.http-client:google-http-client": ["com.google.code.findbugs:jsr305", "com.google.errorprone:error_prone_annotations", "com.google.guava:guava", "com.google.j2objc:j2objc-annotations", "io.grpc:grpc-context", "io.opencensus:opencensus-api", "io.opencensus:opencensus-contrib-http-util", "org.apache.httpcomponents:httpclient", "org.apache.httpcomponents:httpcore"], "com.google.http-client:google-http-client-gson": ["com.google.code.gson:gson", "com.google.http-client:google-http-client"], "com.google.truth:truth": ["com.google.auto.value:auto-value-annotations", "com.google.errorprone:error_prone_annotations", "com.google.guava:guava", "junit:junit", "org.checkerframework:checker-qual", "org.ow2.asm:asm"], "com.squareup.okhttp:okhttp": ["com.squareup.okio:okio"], "com.squareup.okio:okio": ["org.jetbrains.kotlin:kotlin-stdlib", "org.jetbrains.kotlin:kotlin-stdlib-common"], "com.squareup:kotlinpoet": ["org.jetbrains.kotlin:kotlin-reflect", "org.jetbrains.kotlin:kotlin-stdlib-jdk8"], "io.grpc:grpc-api": ["com.google.code.findbugs:jsr305", "com.google.errorprone:error_prone_annotations", "com.google.guava:guava"], "io.grpc:grpc-context": ["io.grpc:grpc-api"], "io.grpc:grpc-core": ["com.google.android:annotations", "com.google.code.gson:gson", "com.google.errorprone:error_prone_annotations", "com.google.guava:guava", "io.grpc:grpc-api", "io.grpc:grpc-context", "io.perfmark:perfmark-api", "org.codehaus.mojo:animal-sniffer-annotations"], "io.grpc:grpc-netty-shaded": ["com.google.errorprone:error_prone_annotations", "com.google.guava:guava", "io.grpc:grpc-api", "io.grpc:grpc-core", "io.grpc:grpc-util", "io.perfmark:perfmark-api"], "io.grpc:grpc-protobuf": ["com.google.api.grpc:proto-google-common-protos", "com.google.code.findbugs:jsr305", "com.google.guava:guava", "com.google.protobuf:protobuf-java", "io.grpc:grpc-api", "io.grpc:grpc-protobuf-lite"], "io.grpc:grpc-protobuf-lite": ["com.google.code.findbugs:jsr305", "com.google.guava:guava", "io.grpc:grpc-api"], "io.grpc:grpc-stub": ["com.google.errorprone:error_prone_annotations", "com.google.guava:guava", "io.grpc:grpc-api"], "io.grpc:grpc-util": ["com.google.guava:guava", "io.grpc:grpc-api", "io.grpc:grpc-core", "org.codehaus.mojo:animal-sniffer-annotations"], "io.netty:netty-buffer": ["io.netty:netty-common"], "io.netty:netty-codec": ["io.netty:netty-buffer", "io.netty:netty-common", "io.netty:netty-transport"], "io.netty:netty-codec-http": ["io.netty:netty-buffer", "io.netty:netty-codec", "io.netty:netty-common", "io.netty:netty-handler", "io.netty:netty-transport"], "io.netty:netty-codec-http2": ["io.netty:netty-buffer", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-common", "io.netty:netty-handler", "io.netty:netty-transport"], "io.netty:netty-codec-socks": ["io.netty:netty-buffer", "io.netty:netty-codec", "io.netty:netty-common", "io.netty:netty-transport"], "io.netty:netty-handler": ["io.netty:netty-buffer", "io.netty:netty-codec", "io.netty:netty-common", "io.netty:netty-resolver", "io.netty:netty-transport", "io.netty:netty-transport-native-unix-common"], "io.netty:netty-handler-proxy": ["io.netty:netty-buffer", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-codec-socks", "io.netty:netty-common", "io.netty:netty-transport"], "io.netty:netty-resolver": ["io.netty:netty-common"], "io.netty:netty-tcnative-boringssl-static": ["io.netty:netty-tcnative-boringssl-static:jar:linux-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:linux-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:windows-x86_64", "io.netty:netty-tcnative-classes"], "io.netty:netty-tcnative-boringssl-static:jar:linux-aarch_64": ["io.netty:netty-tcnative-boringssl-static:jar:linux-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:windows-x86_64", "io.netty:netty-tcnative-classes"], "io.netty:netty-tcnative-boringssl-static:jar:linux-x86_64": ["io.netty:netty-tcnative-boringssl-static:jar:linux-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:windows-x86_64", "io.netty:netty-tcnative-classes"], "io.netty:netty-tcnative-boringssl-static:jar:osx-aarch_64": ["io.netty:netty-tcnative-boringssl-static:jar:linux-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:linux-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:windows-x86_64", "io.netty:netty-tcnative-classes"], "io.netty:netty-tcnative-boringssl-static:jar:osx-x86_64": ["io.netty:netty-tcnative-boringssl-static:jar:linux-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:linux-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:windows-x86_64", "io.netty:netty-tcnative-classes"], "io.netty:netty-tcnative-boringssl-static:jar:windows-x86_64": ["io.netty:netty-tcnative-boringssl-static:jar:linux-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:linux-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-x86_64", "io.netty:netty-tcnative-classes"], "io.netty:netty-transport": ["io.netty:netty-buffer", "io.netty:netty-common", "io.netty:netty-resolver"], "io.netty:netty-transport-classes-epoll": ["io.netty:netty-buffer", "io.netty:netty-common", "io.netty:netty-transport", "io.netty:netty-transport-native-unix-common"], "io.netty:netty-transport-native-epoll:jar:linux-x86_64": ["io.netty:netty-buffer", "io.netty:netty-common", "io.netty:netty-transport", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-native-unix-common"], "io.netty:netty-transport-native-unix-common": ["io.netty:netty-buffer", "io.netty:netty-common", "io.netty:netty-transport"], "io.opencensus:opencensus-api": ["io.grpc:grpc-context"], "io.opencensus:opencensus-contrib-grpc-metrics": ["com.google.guava:guava", "io.opencensus:opencensus-api"], "io.opencensus:opencensus-contrib-http-util": ["com.google.guava:guava", "io.opencensus:opencensus-api"], "junit:junit": ["org.hamcrest:hamcrest-core"], "org.apache.httpcomponents:httpclient": ["commons-codec:commons-codec", "org.apache.httpcomponents:httpcore"], "org.apache.logging.log4j:log4j-core": ["org.apache.logging.log4j:log4j-api"], "org.jetbrains.kotlin:kotlin-reflect": ["org.jetbrains.kotlin:kotlin-stdlib"], "org.jetbrains.kotlin:kotlin-stdlib": ["org.jetbrains:annotations"], "org.jetbrains.kotlin:kotlin-stdlib-jdk7": ["org.jetbrains.kotlin:kotlin-stdlib"], "org.jetbrains.kotlin:kotlin-stdlib-jdk8": ["org.jetbrains.kotlin:kotlin-stdlib", "org.jetbrains.kotlin:kotlin-stdlib-jdk7"]}, "packages": {"com.beust:jcommander": ["com.beust.ah", "com.beust.jcommander", "com.beust.jcommander.converters", "com.beust.jcommander.defaultprovider", "com.beust.jcommander.internal", "com.beust.jcommander.parser", "com.beust.jcommander.validators"], "com.fasterxml.jackson.core:jackson-annotations": ["com.fasterxml.jackson.annotation"], "com.fasterxml.jackson.core:jackson-core": ["com.fasterxml.jackson.core", "com.fasterxml.jackson.core.async", "com.fasterxml.jackson.core.base", "com.fasterxml.jackson.core.exc", "com.fasterxml.jackson.core.filter", "com.fasterxml.jackson.core.format", "com.fasterxml.jackson.core.internal.shaded.fdp.v2_18_2", "com.fasterxml.jackson.core.io", "com.fasterxml.jackson.core.io.schubfach", "com.fasterxml.jackson.core.json", "com.fasterxml.jackson.core.json.async", "com.fasterxml.jackson.core.sym", "com.fasterxml.jackson.core.type", "com.fasterxml.jackson.core.util"], "com.fasterxml.jackson.core:jackson-databind": ["com.fasterxml.jackson.databind", "com.fasterxml.jackson.databind.annotation", "com.fasterxml.jackson.databind.cfg", "com.fasterxml.jackson.databind.deser", "com.fasterxml.jackson.databind.deser.impl", "com.fasterxml.jackson.databind.deser.std", "com.fasterxml.jackson.databind.exc", "com.fasterxml.jackson.databind.ext", "com.fasterxml.jackson.databind.introspect", "com.fasterxml.jackson.databind.jdk14", "com.fasterxml.jackson.databind.json", "com.fasterxml.jackson.databind.jsonFormatVisitors", "com.fasterxml.jackson.databind.jsonschema", "com.fasterxml.jackson.databind.jsontype", "com.fasterxml.jackson.databind.jsontype.impl", "com.fasterxml.jackson.databind.module", "com.fasterxml.jackson.databind.node", "com.fasterxml.jackson.databind.ser", "com.fasterxml.jackson.databind.ser.impl", "com.fasterxml.jackson.databind.ser.std", "com.fasterxml.jackson.databind.type", "com.fasterxml.jackson.databind.util", "com.fasterxml.jackson.databind.util.internal"], "com.fasterxml.jackson.datatype:jackson-datatype-jdk8": ["com.fasterxml.jackson.datatype.jdk8"], "com.fasterxml.jackson.datatype:jackson-datatype-jsr310": ["com.fasterxml.jackson.datatype.jsr310", "com.fasterxml.jackson.datatype.jsr310.deser", "com.fasterxml.jackson.datatype.jsr310.deser.key", "com.fasterxml.jackson.datatype.jsr310.ser", "com.fasterxml.jackson.datatype.jsr310.ser.key", "com.fasterxml.jackson.datatype.jsr310.util"], "com.github.spotbugs:spotbugs-annotations": ["edu.umd.cs.findbugs.annotations"], "com.google.android:annotations": ["android.annotation"], "com.google.api.grpc:proto-google-common-protos": ["com.google.api", "com.google.apps.card.v1", "com.google.cloud", "com.google.cloud.audit", "com.google.cloud.location", "com.google.geo.type", "com.google.logging.type", "com.google.longrunning", "com.google.rpc", "com.google.rpc.context", "com.google.shopping.type", "com.google.type"], "com.google.auth:google-auth-library-credentials": ["com.google.auth"], "com.google.auth:google-auth-library-oauth2-http": ["com.google.auth.http", "com.google.auth.oauth2"], "com.google.auto.value:auto-value": ["autovalue.shaded.com.google.auto.common", "autovalue.shaded.com.google.auto.service", "autovalue.shaded.com.google.common.annotations", "autovalue.shaded.com.google.common.base", "autovalue.shaded.com.google.common.cache", "autovalue.shaded.com.google.common.collect", "autovalue.shaded.com.google.common.escape", "autovalue.shaded.com.google.common.eventbus", "autovalue.shaded.com.google.common.graph", "autovalue.shaded.com.google.common.hash", "autovalue.shaded.com.google.common.html", "autovalue.shaded.com.google.common.io", "autovalue.shaded.com.google.common.math", "autovalue.shaded.com.google.common.net", "autovalue.shaded.com.google.common.primitives", "autovalue.shaded.com.google.common.reflect", "autovalue.shaded.com.google.common.util.concurrent", "autovalue.shaded.com.google.common.xml", "autovalue.shaded.com.google.errorprone.annotations", "autovalue.shaded.com.google.errorprone.annotations.concurrent", "autovalue.shaded.com.google.escapevelocity", "autovalue.shaded.com.google.j2objc.annotations", "autovalue.shaded.com.squareup.javapoet", "autovalue.shaded.net.ltgt.gradle.incap", "autovalue.shaded.org.checkerframework.checker.nullness.qual", "autovalue.shaded.org.checkerframework.framework.qual", "autovalue.shaded.org.objectweb.asm", "com.google.auto.value.extension", "com.google.auto.value.extension.memoized.processor", "com.google.auto.value.extension.serializable.processor", "com.google.auto.value.extension.serializable.serializer", "com.google.auto.value.extension.serializable.serializer.impl", "com.google.auto.value.extension.serializable.serializer.interfaces", "com.google.auto.value.extension.serializable.serializer.runtime", "com.google.auto.value.extension.toprettystring.processor", "com.google.auto.value.processor"], "com.google.auto.value:auto-value-annotations": ["com.google.auto.value", "com.google.auto.value.extension.memoized", "com.google.auto.value.extension.serializable", "com.google.auto.value.extension.toprettystring"], "com.google.code.findbugs:jsr305": ["javax.annotation", "javax.annotation.concurrent", "javax.annotation.meta"], "com.google.code.gson:gson": ["com.google.gson", "com.google.gson.annotations", "com.google.gson.internal", "com.google.gson.internal.bind", "com.google.gson.internal.bind.util", "com.google.gson.internal.reflect", "com.google.gson.internal.sql", "com.google.gson.reflect", "com.google.gson.stream"], "com.google.dagger:dagger": ["dagger", "dagger.assisted", "dagger.internal", "dagger.multibindings"], "com.google.dagger:dagger-compiler": ["dagger.internal.codegen", "dagger.internal.codegen.base", "dagger.internal.codegen.binding", "dagger.internal.codegen.bindinggraphvalidation", "dagger.internal.codegen.compileroption", "dagger.internal.codegen.componentgenerator", "dagger.internal.codegen.javapoet", "dagger.internal.codegen.kotlin", "dagger.internal.codegen.langmodel", "dagger.internal.codegen.model", "dagger.internal.codegen.processingstep", "dagger.internal.codegen.validation", "dagger.internal.codegen.writing", "dagger.internal.codegen.xprocessing"], "com.google.dagger:dagger-producers": ["dagger.producers", "dagger.producers.internal", "dagger.producers.monitoring", "dagger.producers.monitoring.internal"], "com.google.dagger:dagger-spi": ["dagger.internal.codegen.extension", "dagger.model", "dagger.spi", "dagger.spi.internal.shaded.androidx.room.compiler.codegen", "dagger.spi.internal.shaded.androidx.room.compiler.codegen.compat", "dagger.spi.internal.shaded.androidx.room.compiler.codegen.impl", "dagger.spi.internal.shaded.androidx.room.compiler.codegen.java", "dagger.spi.internal.shaded.androidx.room.compiler.codegen.kotlin", "dagger.spi.internal.shaded.androidx.room.compiler.processing", "dagger.spi.internal.shaded.androidx.room.compiler.processing.compat", "dagger.spi.internal.shaded.androidx.room.compiler.processing.javac", "dagger.spi.internal.shaded.androidx.room.compiler.processing.javac.kotlin", "dagger.spi.internal.shaded.androidx.room.compiler.processing.ksp", "dagger.spi.internal.shaded.androidx.room.compiler.processing.ksp.synthetic", "dagger.spi.internal.shaded.androidx.room.compiler.processing.util", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal.common", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal.extensions", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal.metadata", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal.metadata.builtins", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal.metadata.deserialization", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal.metadata.jvm", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal.metadata.jvm.deserialization", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal.metadata.jvm.serialization", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal.metadata.serialization", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal.protobuf", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.jvm", "dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.jvm.internal", "dagger.spi.internal.shaded.auto.common", "dagger.spi.internal.shaded.kotlin.metadata", "dagger.spi.internal.shaded.kotlin.metadata.internal", "dagger.spi.internal.shaded.kotlin.metadata.internal.common", "dagger.spi.internal.shaded.kotlin.metadata.internal.extensions", "dagger.spi.internal.shaded.kotlin.metadata.internal.metadata", "dagger.spi.internal.shaded.kotlin.metadata.internal.metadata.builtins", "dagger.spi.internal.shaded.kotlin.metadata.internal.metadata.deserialization", "dagger.spi.internal.shaded.kotlin.metadata.internal.metadata.jvm", "dagger.spi.internal.shaded.kotlin.metadata.internal.metadata.jvm.deserialization", "dagger.spi.internal.shaded.kotlin.metadata.internal.metadata.jvm.serialization", "dagger.spi.internal.shaded.kotlin.metadata.internal.metadata.serialization", "dagger.spi.internal.shaded.kotlin.metadata.internal.protobuf", "dagger.spi.internal.shaded.kotlin.metadata.jvm", "dagger.spi.internal.shaded.kotlin.metadata.jvm.internal", "dagger.spi.model"], "com.google.devtools.ksp:symbol-processing-api": ["com.google.devtools.ksp", "com.google.devtools.ksp.processing", "com.google.devtools.ksp.symbol", "com.google.devtools.ksp.visitor"], "com.google.errorprone:error_prone_annotations": ["com.google.errorprone.annotations", "com.google.errorprone.annotations.concurrent"], "com.google.googlejavaformat:google-java-format": ["com.google.googlejavaformat", "com.google.googlejavaformat.java", "com.google.googlejavaformat.java.filer", "com.google.googlejavaformat.java.java21", "com.google.googlejavaformat.java.javadoc"], "com.google.guava:failureaccess": ["com.google.common.util.concurrent.internal"], "com.google.guava:guava": ["com.google.common.annotations", "com.google.common.base", "com.google.common.base.internal", "com.google.common.cache", "com.google.common.collect", "com.google.common.escape", "com.google.common.eventbus", "com.google.common.graph", "com.google.common.hash", "com.google.common.html", "com.google.common.io", "com.google.common.math", "com.google.common.net", "com.google.common.primitives", "com.google.common.reflect", "com.google.common.util.concurrent", "com.google.common.xml", "com.google.thirdparty.publicsuffix"], "com.google.http-client:google-http-client": ["com.google.api.client.http", "com.google.api.client.http.apache", "com.google.api.client.http.javanet", "com.google.api.client.http.json", "com.google.api.client.json", "com.google.api.client.json.rpc2", "com.google.api.client.json.webtoken", "com.google.api.client.testing.http", "com.google.api.client.testing.http.apache", "com.google.api.client.testing.http.javanet", "com.google.api.client.testing.json", "com.google.api.client.testing.json.webtoken", "com.google.api.client.testing.util", "com.google.api.client.util", "com.google.api.client.util.escape", "com.google.api.client.util.store"], "com.google.http-client:google-http-client-gson": ["com.google.api.client.json.gson"], "com.google.j2objc:j2objc-annotations": ["com.google.j2objc.annotations"], "com.google.protobuf:protobuf-java": ["com.google.protobuf", "com.google.protobuf.compiler"], "com.google.re2j:re2j": ["com.google.re2j"], "com.google.truth:truth": ["com.google.common.truth"], "com.squareup.okhttp:okhttp": ["com.squareup.okhttp", "com.squareup.okhttp.internal", "com.squareup.okhttp.internal.framed", "com.squareup.okhttp.internal.http", "com.squareup.okhttp.internal.io", "com.squareup.okhttp.internal.tls"], "com.squareup.okio:okio": ["okio", "okio.internal"], "com.squareup:javapoet": ["com.squareup.javapoet"], "com.squareup:kotlinpoet": ["com.squareup.kotlinpoet", "com.squareup.kotlinpoet.jvm", "com.squareup.kotlinpoet.tags"], "commons-codec:commons-codec": ["org.apache.commons.codec", "org.apache.commons.codec.binary", "org.apache.commons.codec.cli", "org.apache.commons.codec.digest", "org.apache.commons.codec.language", "org.apache.commons.codec.language.bm", "org.apache.commons.codec.net"], "io.grpc:grpc-api": ["io.grpc"], "io.grpc:grpc-core": ["io.grpc.internal"], "io.grpc:grpc-netty-shaded": ["io.grpc.netty.shaded.io.grpc.netty", "io.grpc.netty.shaded.io.netty.bootstrap", "io.grpc.netty.shaded.io.netty.buffer", "io.grpc.netty.shaded.io.netty.buffer.search", "io.grpc.netty.shaded.io.netty.channel", "io.grpc.netty.shaded.io.netty.channel.embedded", "io.grpc.netty.shaded.io.netty.channel.epoll", "io.grpc.netty.shaded.io.netty.channel.group", "io.grpc.netty.shaded.io.netty.channel.internal", "io.grpc.netty.shaded.io.netty.channel.local", "io.grpc.netty.shaded.io.netty.channel.nio", "io.grpc.netty.shaded.io.netty.channel.oio", "io.grpc.netty.shaded.io.netty.channel.pool", "io.grpc.netty.shaded.io.netty.channel.socket", "io.grpc.netty.shaded.io.netty.channel.socket.nio", "io.grpc.netty.shaded.io.netty.channel.socket.oio", "io.grpc.netty.shaded.io.netty.channel.unix", "io.grpc.netty.shaded.io.netty.handler.address", "io.grpc.netty.shaded.io.netty.handler.codec", "io.grpc.netty.shaded.io.netty.handler.codec.base64", "io.grpc.netty.shaded.io.netty.handler.codec.bytes", "io.grpc.netty.shaded.io.netty.handler.codec.compression", "io.grpc.netty.shaded.io.netty.handler.codec.http", "io.grpc.netty.shaded.io.netty.handler.codec.http.cookie", "io.grpc.netty.shaded.io.netty.handler.codec.http.cors", "io.grpc.netty.shaded.io.netty.handler.codec.http.multipart", "io.grpc.netty.shaded.io.netty.handler.codec.http.websocketx", "io.grpc.netty.shaded.io.netty.handler.codec.http.websocketx.extensions", "io.grpc.netty.shaded.io.netty.handler.codec.http.websocketx.extensions.compression", "io.grpc.netty.shaded.io.netty.handler.codec.http2", "io.grpc.netty.shaded.io.netty.handler.codec.json", "io.grpc.netty.shaded.io.netty.handler.codec.marshalling", "io.grpc.netty.shaded.io.netty.handler.codec.protobuf", "io.grpc.netty.shaded.io.netty.handler.codec.rtsp", "io.grpc.netty.shaded.io.netty.handler.codec.serialization", "io.grpc.netty.shaded.io.netty.handler.codec.socks", "io.grpc.netty.shaded.io.netty.handler.codec.socksx", "io.grpc.netty.shaded.io.netty.handler.codec.socksx.v4", "io.grpc.netty.shaded.io.netty.handler.codec.socksx.v5", "io.grpc.netty.shaded.io.netty.handler.codec.spdy", "io.grpc.netty.shaded.io.netty.handler.codec.string", "io.grpc.netty.shaded.io.netty.handler.codec.xml", "io.grpc.netty.shaded.io.netty.handler.flow", "io.grpc.netty.shaded.io.netty.handler.flush", "io.grpc.netty.shaded.io.netty.handler.ipfilter", "io.grpc.netty.shaded.io.netty.handler.logging", "io.grpc.netty.shaded.io.netty.handler.pcap", "io.grpc.netty.shaded.io.netty.handler.proxy", "io.grpc.netty.shaded.io.netty.handler.ssl", "io.grpc.netty.shaded.io.netty.handler.ssl.ocsp", "io.grpc.netty.shaded.io.netty.handler.ssl.util", "io.grpc.netty.shaded.io.netty.handler.stream", "io.grpc.netty.shaded.io.netty.handler.timeout", "io.grpc.netty.shaded.io.netty.handler.traffic", "io.grpc.netty.shaded.io.netty.internal.tcnative", "io.grpc.netty.shaded.io.netty.resolver", "io.grpc.netty.shaded.io.netty.util", "io.grpc.netty.shaded.io.netty.util.collection", "io.grpc.netty.shaded.io.netty.util.concurrent", "io.grpc.netty.shaded.io.netty.util.internal", "io.grpc.netty.shaded.io.netty.util.internal.logging", "io.grpc.netty.shaded.io.netty.util.internal.shaded.org.jctools.queues", "io.grpc.netty.shaded.io.netty.util.internal.shaded.org.jctools.queues.atomic", "io.grpc.netty.shaded.io.netty.util.internal.shaded.org.jctools.util", "io.grpc.netty.shaded.io.netty.util.internal.svm"], "io.grpc:grpc-protobuf": ["io.grpc.protobuf"], "io.grpc:grpc-protobuf-lite": ["io.grpc.protobuf.lite"], "io.grpc:grpc-stub": ["io.grpc.stub", "io.grpc.stub.annotations"], "io.grpc:grpc-util": ["io.grpc.util"], "io.netty:netty-buffer": ["io.netty.buffer", "io.netty.buffer.search"], "io.netty:netty-codec": ["io.netty.handler.codec", "io.netty.handler.codec.base64", "io.netty.handler.codec.bytes", "io.netty.handler.codec.compression", "io.netty.handler.codec.json", "io.netty.handler.codec.marshalling", "io.netty.handler.codec.protobuf", "io.netty.handler.codec.serialization", "io.netty.handler.codec.string", "io.netty.handler.codec.xml"], "io.netty:netty-codec-http": ["io.netty.handler.codec.http", "io.netty.handler.codec.http.cookie", "io.netty.handler.codec.http.cors", "io.netty.handler.codec.http.multipart", "io.netty.handler.codec.http.websocketx", "io.netty.handler.codec.http.websocketx.extensions", "io.netty.handler.codec.http.websocketx.extensions.compression", "io.netty.handler.codec.rtsp", "io.netty.handler.codec.spdy"], "io.netty:netty-codec-http2": ["io.netty.handler.codec.http2"], "io.netty:netty-codec-socks": ["io.netty.handler.codec.socks", "io.netty.handler.codec.socksx", "io.netty.handler.codec.socksx.v4", "io.netty.handler.codec.socksx.v5"], "io.netty:netty-common": ["io.netty.util", "io.netty.util.collection", "io.netty.util.concurrent", "io.netty.util.internal", "io.netty.util.internal.logging", "io.netty.util.internal.shaded.org.jctools.queues", "io.netty.util.internal.shaded.org.jctools.queues.atomic", "io.netty.util.internal.shaded.org.jctools.util", "io.netty.util.internal.svm"], "io.netty:netty-handler": ["io.netty.handler.address", "io.netty.handler.flow", "io.netty.handler.flush", "io.netty.handler.ipfilter", "io.netty.handler.logging", "io.netty.handler.pcap", "io.netty.handler.ssl", "io.netty.handler.ssl.ocsp", "io.netty.handler.ssl.util", "io.netty.handler.stream", "io.netty.handler.timeout", "io.netty.handler.traffic"], "io.netty:netty-handler-proxy": ["io.netty.handler.proxy"], "io.netty:netty-resolver": ["io.netty.resolver"], "io.netty:netty-tcnative-classes": ["io.netty.internal.tcnative"], "io.netty:netty-transport": ["io.netty.bootstrap", "io.netty.channel", "io.netty.channel.embedded", "io.netty.channel.group", "io.netty.channel.internal", "io.netty.channel.local", "io.netty.channel.nio", "io.netty.channel.oio", "io.netty.channel.pool", "io.netty.channel.socket", "io.netty.channel.socket.nio", "io.netty.channel.socket.oio"], "io.netty:netty-transport-classes-epoll": ["io.netty.channel.epoll"], "io.netty:netty-transport-native-unix-common": ["io.netty.channel.unix"], "io.opencensus:opencensus-api": ["io.opencensus.common", "io.opencensus.internal", "io.opencensus.metrics", "io.opencensus.metrics.data", "io.opencensus.metrics.export", "io.opencensus.resource", "io.opencensus.stats", "io.opencensus.tags", "io.opencensus.tags.propagation", "io.opencensus.tags.unsafe", "io.opencensus.trace", "io.opencensus.trace.config", "io.opencensus.trace.export", "io.opencensus.trace.internal", "io.opencensus.trace.propagation", "io.opencensus.trace.samplers", "io.opencensus.trace.unsafe"], "io.opencensus:opencensus-contrib-grpc-metrics": ["io.opencensus.contrib.grpc.metrics"], "io.opencensus:opencensus-contrib-http-util": ["io.opencensus.contrib.http", "io.opencensus.contrib.http.util"], "io.perfmark:perfmark-api": ["io.perfmark"], "jakarta.inject:jakarta.inject-api": ["jakarta.inject"], "javax.annotation:javax.annotation-api": ["javax.annotation", "javax.annotation.security", "javax.annotation.sql"], "javax.inject:javax.inject": ["javax.inject"], "junit:junit": ["junit.extensions", "junit.framework", "junit.runner", "junit.textui", "org.junit", "org.junit.experimental", "org.junit.experimental.categories", "org.junit.experimental.max", "org.junit.experimental.results", "org.junit.experimental.runners", "org.junit.experimental.theories", "org.junit.experimental.theories.internal", "org.junit.experimental.theories.suppliers", "org.junit.function", "org.junit.internal", "org.junit.internal.builders", "org.junit.internal.management", "org.junit.internal.matchers", "org.junit.internal.requests", "org.junit.internal.runners", "org.junit.internal.runners.model", "org.junit.internal.runners.rules", "org.junit.internal.runners.statements", "org.junit.matchers", "org.junit.rules", "org.junit.runner", "org.junit.runner.manipulation", "org.junit.runner.notification", "org.junit.runners", "org.junit.runners.model", "org.junit.runners.parameterized", "org.junit.validator"], "net.ltgt.gradle.incap:incap": ["net.ltgt.gradle.incap"], "org.apache.httpcomponents:httpclient": ["org.apache.http.auth", "org.apache.http.auth.params", "org.apache.http.client", "org.apache.http.client.config", "org.apache.http.client.entity", "org.apache.http.client.methods", "org.apache.http.client.params", "org.apache.http.client.protocol", "org.apache.http.client.utils", "org.apache.http.conn", "org.apache.http.conn.params", "org.apache.http.conn.routing", "org.apache.http.conn.scheme", "org.apache.http.conn.socket", "org.apache.http.conn.ssl", "org.apache.http.conn.util", "org.apache.http.cookie", "org.apache.http.cookie.params", "org.apache.http.impl.auth", "org.apache.http.impl.client", "org.apache.http.impl.conn", "org.apache.http.impl.conn.tsccm", "org.apache.http.impl.cookie", "org.apache.http.impl.execchain"], "org.apache.httpcomponents:httpcore": ["org.apache.http", "org.apache.http.annotation", "org.apache.http.concurrent", "org.apache.http.config", "org.apache.http.entity", "org.apache.http.impl", "org.apache.http.impl.bootstrap", "org.apache.http.impl.entity", "org.apache.http.impl.io", "org.apache.http.impl.pool", "org.apache.http.io", "org.apache.http.message", "org.apache.http.params", "org.apache.http.pool", "org.apache.http.protocol", "org.apache.http.ssl", "org.apache.http.util"], "org.apache.logging.log4j:log4j-api": ["org.apache.logging.log4j", "org.apache.logging.log4j.internal", "org.apache.logging.log4j.internal.map", "org.apache.logging.log4j.message", "org.apache.logging.log4j.simple", "org.apache.logging.log4j.simple.internal", "org.apache.logging.log4j.spi", "org.apache.logging.log4j.status", "org.apache.logging.log4j.util", "org.apache.logging.log4j.util.internal"], "org.apache.logging.log4j:log4j-core": ["org.apache.logging.log4j.core", "org.apache.logging.log4j.core.appender", "org.apache.logging.log4j.core.appender.db", "org.apache.logging.log4j.core.appender.db.jdbc", "org.apache.logging.log4j.core.appender.mom", "org.apache.logging.log4j.core.appender.mom.jeromq", "org.apache.logging.log4j.core.appender.mom.kafka", "org.apache.logging.log4j.core.appender.nosql", "org.apache.logging.log4j.core.appender.rewrite", "org.apache.logging.log4j.core.appender.rolling", "org.apache.logging.log4j.core.appender.rolling.action", "org.apache.logging.log4j.core.appender.routing", "org.apache.logging.log4j.core.async", "org.apache.logging.log4j.core.config", "org.apache.logging.log4j.core.config.arbiters", "org.apache.logging.log4j.core.config.builder.api", "org.apache.logging.log4j.core.config.builder.impl", "org.apache.logging.log4j.core.config.composite", "org.apache.logging.log4j.core.config.json", "org.apache.logging.log4j.core.config.plugins", "org.apache.logging.log4j.core.config.plugins.convert", "org.apache.logging.log4j.core.config.plugins.processor", "org.apache.logging.log4j.core.config.plugins.util", "org.apache.logging.log4j.core.config.plugins.validation", "org.apache.logging.log4j.core.config.plugins.validation.constraints", "org.apache.logging.log4j.core.config.plugins.validation.validators", "org.apache.logging.log4j.core.config.plugins.visitors", "org.apache.logging.log4j.core.config.properties", "org.apache.logging.log4j.core.config.status", "org.apache.logging.log4j.core.config.xml", "org.apache.logging.log4j.core.config.yaml", "org.apache.logging.log4j.core.context.internal", "org.apache.logging.log4j.core.filter", "org.apache.logging.log4j.core.filter.mutable", "org.apache.logging.log4j.core.impl", "org.apache.logging.log4j.core.jackson", "org.apache.logging.log4j.core.jmx", "org.apache.logging.log4j.core.jmx.internal", "org.apache.logging.log4j.core.layout", "org.apache.logging.log4j.core.layout.internal", "org.apache.logging.log4j.core.lookup", "org.apache.logging.log4j.core.message", "org.apache.logging.log4j.core.net", "org.apache.logging.log4j.core.net.ssl", "org.apache.logging.log4j.core.osgi", "org.apache.logging.log4j.core.parser", "org.apache.logging.log4j.core.pattern", "org.apache.logging.log4j.core.script", "org.apache.logging.log4j.core.selector", "org.apache.logging.log4j.core.time", "org.apache.logging.log4j.core.time.internal", "org.apache.logging.log4j.core.tools", "org.apache.logging.log4j.core.tools.picocli", "org.apache.logging.log4j.core.util", "org.apache.logging.log4j.core.util.datetime", "org.apache.logging.log4j.core.util.internal"], "org.apache.tomcat:annotations-api": ["javax.annotation", "javax.annotation.security", "javax.ejb", "javax.persistence", "javax.xml.ws"], "org.checkerframework:checker-compat-qual": ["org.checkerframework.checker.nullness.compatqual"], "org.checkerframework:checker-qual": ["org.checkerframework.checker.builder.qual", "org.checkerframework.checker.calledmethods.qual", "org.checkerframework.checker.compilermsgs.qual", "org.checkerframework.checker.fenum.qual", "org.checkerframework.checker.formatter.qual", "org.checkerframework.checker.guieffect.qual", "org.checkerframework.checker.i18n.qual", "org.checkerframework.checker.i18nformatter.qual", "org.checkerframework.checker.index.qual", "org.checkerframework.checker.initialization.qual", "org.checkerframework.checker.interning.qual", "org.checkerframework.checker.lock.qual", "org.checkerframework.checker.mustcall.qual", "org.checkerframework.checker.nullness.qual", "org.checkerframework.checker.optional.qual", "org.checkerframework.checker.propkey.qual", "org.checkerframework.checker.regex.qual", "org.checkerframework.checker.signature.qual", "org.checkerframework.checker.signedness.qual", "org.checkerframework.checker.tainting.qual", "org.checkerframework.checker.units.qual", "org.checkerframework.common.aliasing.qual", "org.checkerframework.common.initializedfields.qual", "org.checkerframework.common.reflection.qual", "org.checkerframework.common.returnsreceiver.qual", "org.checkerframework.common.subtyping.qual", "org.checkerframework.common.util.count.report.qual", "org.checkerframework.common.value.qual", "org.checkerframework.dataflow.qual", "org.checkerframework.framework.qual"], "org.codehaus.mojo:animal-sniffer-annotations": ["org.codehaus.mojo.animal_sniffer"], "org.hamcrest:hamcrest-core": ["org.hamcrest", "org.hamcrest.core", "org.hamcrest.internal"], "org.jetbrains.kotlin:kotlin-reflect": ["kotlin.reflect.full", "kotlin.reflect.jvm", "kotlin.reflect.jvm.internal", "kotlin.reflect.jvm.internal.calls", "kotlin.reflect.jvm.internal.impl", "kotlin.reflect.jvm.internal.impl.builtins", "kotlin.reflect.jvm.internal.impl.builtins.functions", "kotlin.reflect.jvm.internal.impl.builtins.jvm", "kotlin.reflect.jvm.internal.impl.descriptors", "kotlin.reflect.jvm.internal.impl.descriptors.annotations", "kotlin.reflect.jvm.internal.impl.descriptors.deserialization", "kotlin.reflect.jvm.internal.impl.descriptors.impl", "kotlin.reflect.jvm.internal.impl.descriptors.java", "kotlin.reflect.jvm.internal.impl.descriptors.runtime.components", "kotlin.reflect.jvm.internal.impl.descriptors.runtime.structure", "kotlin.reflect.jvm.internal.impl.incremental", "kotlin.reflect.jvm.internal.impl.incremental.components", "kotlin.reflect.jvm.internal.impl.load.java", "kotlin.reflect.jvm.internal.impl.load.java.components", "kotlin.reflect.jvm.internal.impl.load.java.descriptors", "kotlin.reflect.jvm.internal.impl.load.java.lazy", "kotlin.reflect.jvm.internal.impl.load.java.lazy.descriptors", "kotlin.reflect.jvm.internal.impl.load.java.lazy.types", "kotlin.reflect.jvm.internal.impl.load.java.sources", "kotlin.reflect.jvm.internal.impl.load.java.structure", "kotlin.reflect.jvm.internal.impl.load.java.typeEnhancement", "kotlin.reflect.jvm.internal.impl.load.kotlin", "kotlin.reflect.jvm.internal.impl.load.kotlin.header", "kotlin.reflect.jvm.internal.impl.metadata", "kotlin.reflect.jvm.internal.impl.metadata.builtins", "kotlin.reflect.jvm.internal.impl.metadata.deserialization", "kotlin.reflect.jvm.internal.impl.metadata.jvm", "kotlin.reflect.jvm.internal.impl.metadata.jvm.deserialization", "kotlin.reflect.jvm.internal.impl.name", "kotlin.reflect.jvm.internal.impl.platform", "kotlin.reflect.jvm.internal.impl.protobuf", "kotlin.reflect.jvm.internal.impl.renderer", "kotlin.reflect.jvm.internal.impl.resolve", "kotlin.reflect.jvm.internal.impl.resolve.calls.inference", "kotlin.reflect.jvm.internal.impl.resolve.constants", "kotlin.reflect.jvm.internal.impl.resolve.deprecation", "kotlin.reflect.jvm.internal.impl.resolve.descriptorUtil", "kotlin.reflect.jvm.internal.impl.resolve.jvm", "kotlin.reflect.jvm.internal.impl.resolve.sam", "kotlin.reflect.jvm.internal.impl.resolve.scopes", "kotlin.reflect.jvm.internal.impl.resolve.scopes.receivers", "kotlin.reflect.jvm.internal.impl.serialization", "kotlin.reflect.jvm.internal.impl.serialization.deserialization", "kotlin.reflect.jvm.internal.impl.serialization.deserialization.builtins", "kotlin.reflect.jvm.internal.impl.serialization.deserialization.descriptors", "kotlin.reflect.jvm.internal.impl.storage", "kotlin.reflect.jvm.internal.impl.types", "kotlin.reflect.jvm.internal.impl.types.checker", "kotlin.reflect.jvm.internal.impl.types.error", "kotlin.reflect.jvm.internal.impl.types.model", "kotlin.reflect.jvm.internal.impl.types.typeUtil", "kotlin.reflect.jvm.internal.impl.types.typesApproximation", "kotlin.reflect.jvm.internal.impl.util", "kotlin.reflect.jvm.internal.impl.util.capitalizeDecapitalize", "kotlin.reflect.jvm.internal.impl.util.collectionUtils", "kotlin.reflect.jvm.internal.impl.utils", "kotlin.reflect.jvm.internal.pcollections"], "org.jetbrains.kotlin:kotlin-stdlib": ["kotlin", "kotlin.annotation", "kotlin.collections", "kotlin.collections.builders", "kotlin.collections.jdk8", "kotlin.collections.unsigned", "kotlin.comparisons", "kotlin.concurrent", "kotlin.contracts", "kotlin.coroutines", "kotlin.coroutines.cancellation", "kotlin.coroutines.intrinsics", "kotlin.coroutines.jvm.internal", "kotlin.enums", "kotlin.experimental", "kotlin.internal", "kotlin.internal.jdk7", "kotlin.internal.jdk8", "kotlin.io", "kotlin.io.encoding", "kotlin.io.path", "kotlin.jdk7", "kotlin.js", "kotlin.jvm", "kotlin.jvm.functions", "kotlin.jvm.internal", "kotlin.jvm.internal.markers", "kotlin.jvm.internal.unsafe", "kotlin.jvm.jdk8", "kotlin.jvm.optionals", "kotlin.math", "kotlin.properties", "kotlin.random", "kotlin.random.jdk8", "kotlin.ranges", "kotlin.reflect", "kotlin.sequences", "kotlin.streams.jdk8", "kotlin.system", "kotlin.text", "kotlin.text.jdk8", "kotlin.time", "kotlin.time.jdk8", "kotlin.uuid"], "org.jetbrains.kotlin:kotlin-stdlib-jdk7": ["kotlin.internal.jdk7", "kotlin.io.path", "kotlin.jdk7"], "org.jetbrains.kotlin:kotlin-stdlib-jdk8": ["kotlin.collections.jdk8", "kotlin.internal.jdk8", "kotlin.jvm.jdk8", "kotlin.random.jdk8", "kotlin.streams.jdk8", "kotlin.text.jdk8", "kotlin.time.jdk8"], "org.jetbrains:annotations": ["org.intellij.lang.annotations", "org.jetbrains.annotations"], "org.jspecify:jspecify": ["org.jspecify.annotations"], "org.ow2.asm:asm": ["org.objectweb.asm", "org.objectweb.asm.signature"]}, "repositories": {"https://repo1.maven.org/maven2/": ["com.beust:jcommander", "com.beust:jcommander:jar:sources", "com.fasterxml.jackson.core:jackson-annotations", "com.fasterxml.jackson.core:jackson-annotations:jar:sources", "com.fasterxml.jackson.core:jackson-core", "com.fasterxml.jackson.core:jackson-core:jar:sources", "com.fasterxml.jackson.core:jackson-databind", "com.fasterxml.jackson.core:jackson-databind:jar:sources", "com.fasterxml.jackson.datatype:jackson-datatype-jdk8", "com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:sources", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:sources", "com.github.spotbugs:spotbugs-annotations", "com.github.spotbugs:spotbugs-annotations:jar:sources", "com.google.android:annotations", "com.google.android:annotations:jar:sources", "com.google.api.grpc:proto-google-common-protos", "com.google.api.grpc:proto-google-common-protos:jar:sources", "com.google.auth:google-auth-library-credentials", "com.google.auth:google-auth-library-credentials:jar:sources", "com.google.auth:google-auth-library-oauth2-http", "com.google.auth:google-auth-library-oauth2-http:jar:sources", "com.google.auto.value:auto-value", "com.google.auto.value:auto-value-annotations", "com.google.auto.value:auto-value-annotations:jar:sources", "com.google.auto.value:auto-value:jar:sources", "com.google.code.findbugs:jsr305", "com.google.code.findbugs:jsr305:jar:sources", "com.google.code.gson:gson", "com.google.code.gson:gson:jar:sources", "com.google.dagger:dagger", "com.google.dagger:dagger-compiler", "com.google.dagger:dagger-compiler:jar:sources", "com.google.dagger:dagger-producers", "com.google.dagger:dagger-producers:jar:sources", "com.google.dagger:dagger-spi", "com.google.dagger:dagger-spi:jar:sources", "com.google.dagger:dagger:jar:sources", "com.google.devtools.ksp:symbol-processing-api", "com.google.devtools.ksp:symbol-processing-api:jar:sources", "com.google.errorprone:error_prone_annotations", "com.google.errorprone:error_prone_annotations:jar:sources", "com.google.googlejavaformat:google-java-format", "com.google.googlejavaformat:google-java-format:jar:sources", "com.google.guava:failureaccess", "com.google.guava:failureaccess:jar:sources", "com.google.guava:guava", "com.google.guava:guava:jar:sources", "com.google.guava:listenablefuture", "com.google.http-client:google-http-client", "com.google.http-client:google-http-client-gson", "com.google.http-client:google-http-client-gson:jar:sources", "com.google.http-client:google-http-client:jar:sources", "com.google.j2objc:j2objc-annotations", "com.google.j2objc:j2objc-annotations:jar:sources", "com.google.protobuf:protobuf-java", "com.google.protobuf:protobuf-java:jar:sources", "com.google.re2j:re2j", "com.google.re2j:re2j:jar:sources", "com.google.truth:truth", "com.google.truth:truth:jar:sources", "com.squareup.okhttp:okhttp", "com.squareup.okhttp:okhttp:jar:sources", "com.squareup.okio:okio", "com.squareup.okio:okio:jar:sources", "com.squareup:javapoet", "com.squareup:javapoet:jar:sources", "com.squareup:kotlinpoet", "com.squareup:kotlinpoet:jar:sources", "commons-codec:commons-codec", "commons-codec:commons-codec:jar:sources", "io.grpc:grpc-api", "io.grpc:grpc-api:jar:sources", "io.grpc:grpc-context", "io.grpc:grpc-context:jar:sources", "io.grpc:grpc-core", "io.grpc:grpc-core:jar:sources", "io.grpc:grpc-netty-shaded", "io.grpc:grpc-netty-shaded:jar:sources", "io.grpc:grpc-protobuf", "io.grpc:grpc-protobuf-lite", "io.grpc:grpc-protobuf-lite:jar:sources", "io.grpc:grpc-protobuf:jar:sources", "io.grpc:grpc-stub", "io.grpc:grpc-stub:jar:sources", "io.grpc:grpc-util", "io.grpc:grpc-util:jar:sources", "io.netty:netty-buffer", "io.netty:netty-buffer:jar:sources", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-codec-http2:jar:sources", "io.netty:netty-codec-http:jar:sources", "io.netty:netty-codec-socks", "io.netty:netty-codec-socks:jar:sources", "io.netty:netty-codec:jar:sources", "io.netty:netty-common", "io.netty:netty-common:jar:sources", "io.netty:netty-handler", "io.netty:netty-handler-proxy", "io.netty:netty-handler-proxy:jar:sources", "io.netty:netty-handler:jar:sources", "io.netty:netty-resolver", "io.netty:netty-resolver:jar:sources", "io.netty:netty-tcnative-boringssl-static", "io.netty:netty-tcnative-boringssl-static:jar:linux-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:linux-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:sources", "io.netty:netty-tcnative-boringssl-static:jar:windows-x86_64", "io.netty:netty-tcnative-classes", "io.netty:netty-tcnative-classes:jar:sources", "io.netty:netty-transport", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-classes-epoll:jar:sources", "io.netty:netty-transport-native-epoll:jar:linux-x86_64", "io.netty:netty-transport-native-epoll:jar:sources", "io.netty:netty-transport-native-unix-common", "io.netty:netty-transport-native-unix-common:jar:sources", "io.netty:netty-transport:jar:sources", "io.opencensus:opencensus-api", "io.opencensus:opencensus-api:jar:sources", "io.opencensus:opencensus-contrib-grpc-metrics", "io.opencensus:opencensus-contrib-grpc-metrics:jar:sources", "io.opencensus:opencensus-contrib-http-util", "io.opencensus:opencensus-contrib-http-util:jar:sources", "io.perfmark:perfmark-api", "io.perfmark:perfmark-api:jar:sources", "jakarta.inject:jakarta.inject-api", "jakarta.inject:jakarta.inject-api:jar:sources", "javax.annotation:javax.annotation-api", "javax.annotation:javax.annotation-api:jar:sources", "javax.inject:javax.inject", "javax.inject:javax.inject:jar:sources", "junit:junit", "junit:junit:jar:sources", "net.ltgt.gradle.incap:incap", "net.ltgt.gradle.incap:incap:jar:sources", "org.apache.httpcomponents:httpclient", "org.apache.httpcomponents:httpclient:jar:sources", "org.apache.httpcomponents:httpcore", "org.apache.httpcomponents:httpcore:jar:sources", "org.apache.logging.log4j:log4j-api", "org.apache.logging.log4j:log4j-api:jar:sources", "org.apache.logging.log4j:log4j-core", "org.apache.logging.log4j:log4j-core:jar:sources", "org.apache.tomcat:annotations-api", "org.checkerframework:checker-compat-qual", "org.checkerframework:checker-compat-qual:jar:sources", "org.checkerframework:checker-qual", "org.checkerframework:checker-qual:jar:sources", "org.codehaus.mojo:animal-sniffer-annotations", "org.codehaus.mojo:animal-sniffer-annotations:jar:sources", "org.hamcrest:hamcrest-core", "org.hamcrest:hamcrest-core:jar:sources", "org.jetbrains.kotlin:kotlin-reflect", "org.jetbrains.kotlin:kotlin-reflect:jar:sources", "org.jetbrains.kotlin:kotlin-stdlib", "org.jetbrains.kotlin:kotlin-stdlib-common", "org.jetbrains.kotlin:kotlin-stdlib-common:jar:sources", "org.jetbrains.kotlin:kotlin-stdlib-jdk7", "org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:sources", "org.jetbrains.kotlin:kotlin-stdlib-jdk8", "org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:sources", "org.jetbrains.kotlin:kotlin-stdlib:jar:sources", "org.jetbrains:annotations", "org.jetbrains:annotations:jar:sources", "org.jspecify:jspecify", "org.jspecify:jspecify:jar:sources", "org.ow2.asm:asm", "org.ow2.asm:asm:jar:sources"], "https://maven.google.com/": ["com.beust:jcommander", "com.beust:jcommander:jar:sources", "com.fasterxml.jackson.core:jackson-annotations", "com.fasterxml.jackson.core:jackson-annotations:jar:sources", "com.fasterxml.jackson.core:jackson-core", "com.fasterxml.jackson.core:jackson-core:jar:sources", "com.fasterxml.jackson.core:jackson-databind", "com.fasterxml.jackson.core:jackson-databind:jar:sources", "com.fasterxml.jackson.datatype:jackson-datatype-jdk8", "com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:sources", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:sources", "com.github.spotbugs:spotbugs-annotations", "com.github.spotbugs:spotbugs-annotations:jar:sources", "com.google.android:annotations", "com.google.android:annotations:jar:sources", "com.google.api.grpc:proto-google-common-protos", "com.google.api.grpc:proto-google-common-protos:jar:sources", "com.google.auth:google-auth-library-credentials", "com.google.auth:google-auth-library-credentials:jar:sources", "com.google.auth:google-auth-library-oauth2-http", "com.google.auth:google-auth-library-oauth2-http:jar:sources", "com.google.auto.value:auto-value", "com.google.auto.value:auto-value-annotations", "com.google.auto.value:auto-value-annotations:jar:sources", "com.google.auto.value:auto-value:jar:sources", "com.google.code.findbugs:jsr305", "com.google.code.findbugs:jsr305:jar:sources", "com.google.code.gson:gson", "com.google.code.gson:gson:jar:sources", "com.google.dagger:dagger", "com.google.dagger:dagger-compiler", "com.google.dagger:dagger-compiler:jar:sources", "com.google.dagger:dagger-producers", "com.google.dagger:dagger-producers:jar:sources", "com.google.dagger:dagger-spi", "com.google.dagger:dagger-spi:jar:sources", "com.google.dagger:dagger:jar:sources", "com.google.devtools.ksp:symbol-processing-api", "com.google.devtools.ksp:symbol-processing-api:jar:sources", "com.google.errorprone:error_prone_annotations", "com.google.errorprone:error_prone_annotations:jar:sources", "com.google.googlejavaformat:google-java-format", "com.google.googlejavaformat:google-java-format:jar:sources", "com.google.guava:failureaccess", "com.google.guava:failureaccess:jar:sources", "com.google.guava:guava", "com.google.guava:guava:jar:sources", "com.google.guava:listenablefuture", "com.google.http-client:google-http-client", "com.google.http-client:google-http-client-gson", "com.google.http-client:google-http-client-gson:jar:sources", "com.google.http-client:google-http-client:jar:sources", "com.google.j2objc:j2objc-annotations", "com.google.j2objc:j2objc-annotations:jar:sources", "com.google.protobuf:protobuf-java", "com.google.protobuf:protobuf-java:jar:sources", "com.google.re2j:re2j", "com.google.re2j:re2j:jar:sources", "com.google.truth:truth", "com.google.truth:truth:jar:sources", "com.squareup.okhttp:okhttp", "com.squareup.okhttp:okhttp:jar:sources", "com.squareup.okio:okio", "com.squareup.okio:okio:jar:sources", "com.squareup:javapoet", "com.squareup:javapoet:jar:sources", "com.squareup:kotlinpoet", "com.squareup:kotlinpoet:jar:sources", "commons-codec:commons-codec", "commons-codec:commons-codec:jar:sources", "io.grpc:grpc-api", "io.grpc:grpc-api:jar:sources", "io.grpc:grpc-context", "io.grpc:grpc-context:jar:sources", "io.grpc:grpc-core", "io.grpc:grpc-core:jar:sources", "io.grpc:grpc-netty-shaded", "io.grpc:grpc-netty-shaded:jar:sources", "io.grpc:grpc-protobuf", "io.grpc:grpc-protobuf-lite", "io.grpc:grpc-protobuf-lite:jar:sources", "io.grpc:grpc-protobuf:jar:sources", "io.grpc:grpc-stub", "io.grpc:grpc-stub:jar:sources", "io.grpc:grpc-util", "io.grpc:grpc-util:jar:sources", "io.netty:netty-buffer", "io.netty:netty-buffer:jar:sources", "io.netty:netty-codec", "io.netty:netty-codec-http", "io.netty:netty-codec-http2", "io.netty:netty-codec-http2:jar:sources", "io.netty:netty-codec-http:jar:sources", "io.netty:netty-codec-socks", "io.netty:netty-codec-socks:jar:sources", "io.netty:netty-codec:jar:sources", "io.netty:netty-common", "io.netty:netty-common:jar:sources", "io.netty:netty-handler", "io.netty:netty-handler-proxy", "io.netty:netty-handler-proxy:jar:sources", "io.netty:netty-handler:jar:sources", "io.netty:netty-resolver", "io.netty:netty-resolver:jar:sources", "io.netty:netty-tcnative-boringssl-static", "io.netty:netty-tcnative-boringssl-static:jar:linux-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:linux-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-aarch_64", "io.netty:netty-tcnative-boringssl-static:jar:osx-x86_64", "io.netty:netty-tcnative-boringssl-static:jar:sources", "io.netty:netty-tcnative-boringssl-static:jar:windows-x86_64", "io.netty:netty-tcnative-classes", "io.netty:netty-tcnative-classes:jar:sources", "io.netty:netty-transport", "io.netty:netty-transport-classes-epoll", "io.netty:netty-transport-classes-epoll:jar:sources", "io.netty:netty-transport-native-epoll:jar:linux-x86_64", "io.netty:netty-transport-native-epoll:jar:sources", "io.netty:netty-transport-native-unix-common", "io.netty:netty-transport-native-unix-common:jar:sources", "io.netty:netty-transport:jar:sources", "io.opencensus:opencensus-api", "io.opencensus:opencensus-api:jar:sources", "io.opencensus:opencensus-contrib-grpc-metrics", "io.opencensus:opencensus-contrib-grpc-metrics:jar:sources", "io.opencensus:opencensus-contrib-http-util", "io.opencensus:opencensus-contrib-http-util:jar:sources", "io.perfmark:perfmark-api", "io.perfmark:perfmark-api:jar:sources", "jakarta.inject:jakarta.inject-api", "jakarta.inject:jakarta.inject-api:jar:sources", "javax.annotation:javax.annotation-api", "javax.annotation:javax.annotation-api:jar:sources", "javax.inject:javax.inject", "javax.inject:javax.inject:jar:sources", "junit:junit", "junit:junit:jar:sources", "net.ltgt.gradle.incap:incap", "net.ltgt.gradle.incap:incap:jar:sources", "org.apache.httpcomponents:httpclient", "org.apache.httpcomponents:httpclient:jar:sources", "org.apache.httpcomponents:httpcore", "org.apache.httpcomponents:httpcore:jar:sources", "org.apache.logging.log4j:log4j-api", "org.apache.logging.log4j:log4j-api:jar:sources", "org.apache.logging.log4j:log4j-core", "org.apache.logging.log4j:log4j-core:jar:sources", "org.apache.tomcat:annotations-api", "org.checkerframework:checker-compat-qual", "org.checkerframework:checker-compat-qual:jar:sources", "org.checkerframework:checker-qual", "org.checkerframework:checker-qual:jar:sources", "org.codehaus.mojo:animal-sniffer-annotations", "org.codehaus.mojo:animal-sniffer-annotations:jar:sources", "org.hamcrest:hamcrest-core", "org.hamcrest:hamcrest-core:jar:sources", "org.jetbrains.kotlin:kotlin-reflect", "org.jetbrains.kotlin:kotlin-reflect:jar:sources", "org.jetbrains.kotlin:kotlin-stdlib", "org.jetbrains.kotlin:kotlin-stdlib-common", "org.jetbrains.kotlin:kotlin-stdlib-common:jar:sources", "org.jetbrains.kotlin:kotlin-stdlib-jdk7", "org.jetbrains.kotlin:kotlin-stdlib-jdk7:jar:sources", "org.jetbrains.kotlin:kotlin-stdlib-jdk8", "org.jetbrains.kotlin:kotlin-stdlib-jdk8:jar:sources", "org.jetbrains.kotlin:kotlin-stdlib:jar:sources", "org.jetbrains:annotations", "org.jetbrains:annotations:jar:sources", "org.jspecify:jspecify", "org.jspecify:jspecify:jar:sources", "org.ow2.asm:asm", "org.ow2.asm:asm:jar:sources"]}, "services": {"com.fasterxml.jackson.core:jackson-core": {"com.fasterxml.jackson.core.JsonFactory": ["com.fasterxml.jackson.core.JsonFactory"]}, "com.fasterxml.jackson.core:jackson-core:jar:sources": {"com.fasterxml.jackson.core.JsonFactory": ["com.fasterxml.jackson.core.JsonFactory"]}, "com.fasterxml.jackson.core:jackson-databind": {"com.fasterxml.jackson.core.ObjectCodec": ["com.fasterxml.jackson.databind.ObjectMapper"]}, "com.fasterxml.jackson.core:jackson-databind:jar:sources": {"com.fasterxml.jackson.core.ObjectCodec": ["com.fasterxml.jackson.databind.ObjectMapper"]}, "com.fasterxml.jackson.datatype:jackson-datatype-jdk8": {"com.fasterxml.jackson.databind.Module": ["com.fasterxml.jackson.datatype.jdk8.Jdk8Module"]}, "com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:sources": {"com.fasterxml.jackson.databind.Module": ["com.fasterxml.jackson.datatype.jdk8.Jdk8Module"]}, "com.fasterxml.jackson.datatype:jackson-datatype-jsr310": {"com.fasterxml.jackson.databind.Module": ["com.fasterxml.jackson.datatype.jsr310.JavaTimeModule"]}, "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:sources": {"com.fasterxml.jackson.databind.Module": ["com.fasterxml.jackson.datatype.jsr310.JavaTimeModule"]}, "com.google.auto.value:auto-value": {"com.google.auto.value.extension.AutoValueExtension": ["com.google.auto.value.extension.memoized.processor.MemoizeExtension", "com.google.auto.value.extension.serializable.processor.SerializableAutoValueExtension", "com.google.auto.value.extension.toprettystring.processor.ToPrettyStringExtension"], "com.google.auto.value.extension.serializable.serializer.interfaces.SerializerExtension": ["com.google.auto.value.extension.serializable.serializer.impl.ImmutableListSerializerExtension", "com.google.auto.value.extension.serializable.serializer.impl.ImmutableMapSerializerExtension", "com.google.auto.value.extension.serializable.serializer.impl.OptionalSerializerExtension"], "javax.annotation.processing.Processor": ["com.google.auto.value.extension.memoized.processor.MemoizedValidator", "com.google.auto.value.extension.toprettystring.processor.ToPrettyStringValidator", "com.google.auto.value.processor.AutoAnnotationProcessor", "com.google.auto.value.processor.AutoBuilderProcessor", "com.google.auto.value.processor.AutoOneOfProcessor", "com.google.auto.value.processor.AutoValueBuilderProcessor", "com.google.auto.value.processor.AutoValueProcessor"]}, "com.google.dagger:dagger-compiler": {"com.google.devtools.ksp.processing.SymbolProcessorProvider": ["dagger.internal.codegen.KspComponentProcessor$Provider"], "javax.annotation.processing.Processor": ["dagger.internal.codegen.ComponentProcessor"]}, "com.google.dagger:dagger-spi": {"dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.internal.extensions.MetadataExtensions": ["dagger.spi.internal.shaded.androidx.room.jarjarred.kotlin.metadata.jvm.internal.JvmMetadataExtensions"], "dagger.spi.internal.shaded.kotlin.metadata.internal.extensions.MetadataExtensions": ["dagger.spi.internal.shaded.kotlin.metadata.jvm.internal.JvmMetadataExtensions"]}, "com.google.googlejavaformat:google-java-format": {"java.util.spi.ToolProvider": ["com.google.googlejavaformat.java.GoogleJavaFormatToolProvider"], "javax.tools.Tool": ["com.google.googlejavaformat.java.GoogleJavaFormatTool"]}, "io.grpc:grpc-core": {"io.grpc.LoadBalancerProvider": ["io.grpc.internal.PickFirstLoadBalancerProvider"], "io.grpc.NameResolverProvider": ["io.grpc.internal.DnsNameResolverProvider"]}, "io.grpc:grpc-core:jar:sources": {"io.grpc.LoadBalancerProvider": ["io.grpc.internal.PickFirstLoadBalancerProvider"], "io.grpc.NameResolverProvider": ["io.grpc.internal.DnsNameResolverProvider"]}, "io.grpc:grpc-netty-shaded": {"io.grpc.ManagedChannelProvider": ["io.grpc.netty.shaded.io.grpc.netty.NettyChannelProvider", "io.grpc.netty.shaded.io.grpc.netty.UdsNettyChannelProvider"], "io.grpc.NameResolverProvider": ["io.grpc.netty.shaded.io.grpc.netty.UdsNameResolverProvider"], "io.grpc.ServerProvider": ["io.grpc.netty.shaded.io.grpc.netty.NettyServerProvider"], "reactor.blockhound.integration.BlockHoundIntegration": ["io.grpc.netty.shaded.io.netty.util.internal.Hidden$NettyBlockHoundIntegration"]}, "io.grpc:grpc-util": {"io.grpc.LoadBalancerProvider": ["io.grpc.util.OutlierDetectionLoadBalancerProvider", "io.grpc.util.SecretRoundRobinLoadBalancerProvider$Provider"]}, "io.grpc:grpc-util:jar:sources": {"io.grpc.LoadBalancerProvider": ["io.grpc.util.OutlierDetectionLoadBalancerProvider", "io.grpc.util.SecretRoundRobinLoadBalancerProvider$Provider"]}, "io.netty:netty-common": {"reactor.blockhound.integration.BlockHoundIntegration": ["io.netty.util.internal.Hidden$NettyBlockHoundIntegration"]}, "io.netty:netty-common:jar:sources": {"reactor.blockhound.integration.BlockHoundIntegration": ["io.netty.util.internal.Hidden$NettyBlockHoundIntegration"]}, "org.apache.logging.log4j:log4j-api": {"org.apache.logging.log4j.util.PropertySource": ["org.apache.logging.log4j.util.EnvironmentPropertySource", "org.apache.logging.log4j.util.SystemPropertiesPropertySource"]}, "org.apache.logging.log4j:log4j-core": {"javax.annotation.processing.Processor": ["org.apache.logging.log4j.core.config.plugins.processor.PluginProcessor"], "org.apache.logging.log4j.core.util.ContextDataProvider": ["org.apache.logging.log4j.core.impl.ThreadContextDataProvider"], "org.apache.logging.log4j.message.ThreadDumpMessage$ThreadInfoFactory": ["org.apache.logging.log4j.core.message.ExtendedThreadInfoFactory"], "org.apache.logging.log4j.spi.Provider": ["org.apache.logging.log4j.core.impl.Log4jProvider"]}, "org.jetbrains.kotlin:kotlin-reflect": {"kotlin.reflect.jvm.internal.impl.builtins.BuiltInsLoader": ["kotlin.reflect.jvm.internal.impl.serialization.deserialization.builtins.BuiltInsLoaderImpl"], "kotlin.reflect.jvm.internal.impl.resolve.ExternalOverridabilityCondition": ["kotlin.reflect.jvm.internal.impl.load.java.ErasedOverridabilityCondition", "kotlin.reflect.jvm.internal.impl.load.java.FieldOverridabilityCondition", "kotlin.reflect.jvm.internal.impl.load.java.JavaIncompatibilityRulesOverridabilityCondition"]}}, "skipped": ["com.google.guava:listenablefuture:jar:sources", "org.apache.tomcat:annotations-api:jar:sources"], "version": "2"}