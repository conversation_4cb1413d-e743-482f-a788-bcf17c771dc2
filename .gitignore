# Prerequisites
*.d

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
*.so
*.dylib
*.dll

# Fortran module files
*.smod

# Compiled Static libraries
*.lai
*.la
*.a
*.lib

# Executables
*.exe
*.out
*.app

# Bazel related ignore
.clwb
bazel-bin
bazel-out
bazel-testlogs
bazel-*
bazel-PointerWorkSpace
MODULE.bazel.lock

# Miscs
*cmake-build-debug*
.vscode
*.idea*
*.vs*


*CMakeSettings.json
*.onnx
*.ijwb*

*node_modules*

__pycache__/
.DS_Store
command_log.txt
MODULE.bazel.lock

*.tar.gz
.history
docker-data/
.env
app.env