package logging_test

import (
	"fmt"
	"os"
	"path/filepath"
	"pointer/golangp/common/logging"
	"strings"
	"testing"
	"time"
)

func init() {
	// 设置测试日志路径
	tempDir := os.TempDir()
	logPath := filepath.Join(tempDir, "test_logs")
	os.MkdirAll(logPath, 0755)

	// 初始化日志配置
	loggerConfig := &logging.LoggerConfig{
		Level:    logging.DEBUG,
		MaxSize:  20 * 1024 * 1024,
		LogPath:  logPath,
		FileName: "test",
	}
	logging.InitLogger(loggerConfig)
}

func TestLoggerInitialization(t *testing.T) {
	fmt.Println("Running TestLoggerInitialization")

	// 测试默认日志记录器
	defaultLogger := logging.GetLogger("test")
	if defaultLogger == nil {
		t.Error("Default logger should not be nil")
	}

	// 验证日志文件是否存在
	logPath := filepath.Join(os.TempDir(), "test_logs", "test.log")
	if _, err := os.Stat(logPath); os.IsNotExist(err) {
		t.<PERSON>rf("Log file does not exist: %s", logPath)
	} else {
		fmt.Println("Log file exists")
	}
}

func TestLogFunctions(t *testing.T) {
	fmt.Println("Running TestLogFunctions")
	logPath := filepath.Join(os.TempDir(), "test_logs", "test.log")
	fmt.Printf("Using log file: %s\n", logPath)

	// 测试每个日志函数
	testCases := []struct {
		name     string
		logFunc  func(string, ...interface{})
		prefix   string
		message  string
		contains string
	}{
		{
			name:     "Debug",
			logFunc:  logging.Debug,
			prefix:   "[DEBUG]",
			message:  "test debug message",
			contains: "[DEBUG]",
		},
		{
			name:     "Info",
			logFunc:  logging.Info,
			prefix:   "[INFO]",
			message:  "test info message",
			contains: "[INFO]",
		},
		{
			name:     "Warning",
			logFunc:  logging.Warning,
			prefix:   "[WARNING]",
			message:  "test warning message",
			contains: "[WARNING]",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			fmt.Printf("Running test case: %s\n", tc.name)

			// 清空日志文件
			if err := os.Truncate(logPath, 0); err != nil {
				t.Fatalf("Failed to truncate log file: %v", err)
			}

			// 写入测试日志
			tc.logFunc(tc.message)

			// 等待日志写入
			time.Sleep(100 * time.Millisecond)

			// 读取日志内容
			content, err := os.ReadFile(logPath)
			if err != nil {
				t.Fatalf("Failed to read log file: %v", err)
			}

			logContent := string(content)
			fmt.Printf("Log content: %s\n", logContent)

			// 检查日志是否包含预期的前缀
			if !strings.Contains(logContent, tc.contains) {
				t.Errorf("Log content does not contain expected prefix %s", tc.contains)
			}

			// 检查日志是否包含消息
			if !strings.Contains(logContent, tc.message) {
				t.Errorf("Log content does not contain message: %s", tc.message)
			}

			// 检查时间戳格式
			timestamp := time.Now().Format("2006/01/02 15:04:05")
			if !strings.Contains(logContent, timestamp[:10]) { // 检查日期部分
				t.Errorf("Log content does not contain valid timestamp")
			}
		})
	}
}

func TestMultipleLoggers(t *testing.T) {
	fmt.Println("Running TestMultipleLoggers")

	// 测试多个日志记录器
	loggers := []string{"logger1", "logger2", "logger3"}
	for _, name := range loggers {
		logger := logging.GetLogger(name)
		if logger == nil {
			t.Errorf("Logger %s should not be nil", name)
		}

		// 验证日志文件是否存在
		logPath := filepath.Join(os.TempDir(), "test_logs", name+".log")
		if _, err := os.Stat(logPath); os.IsNotExist(err) {
			t.Errorf("Log file for %s does not exist: %s", name, logPath)
		}

		// 写入测试日志
		logger.Info("Test message for %s", name)

		// 等待日志写入
		time.Sleep(100 * time.Millisecond)

		// 验证日志内容
		content, err := os.ReadFile(logPath)
		if err != nil {
			t.Errorf("Failed to read log file for %s: %v", name, err)
		}

		if !strings.Contains(string(content), "Test message for "+name) {
			t.Errorf("Log file for %s does not contain expected message", name)
		}
	}
}

func TestErrorLogging(t *testing.T) {
	fmt.Println("Running TestErrorLogging")

	// 创建测试日志记录器
	logger := logging.GetLogger("error_test")

	// 测试错误日志
	message := "test error message"
	logger.Error(message)

	// 等待日志写入
	time.Sleep(100 * time.Millisecond)

	// 验证日志文件
	logPath := filepath.Join(os.TempDir(), "test_logs", "error_test.log")
	content, err := os.ReadFile(logPath)
	if err != nil {
		t.Fatalf("Failed to read error log file: %v", err)
	}

	// 检查错误日志内容
	if !strings.Contains(string(content), "[ERROR]") {
		t.Error("Error log does not contain ERROR prefix")
	}
	if !strings.Contains(string(content), message) {
		t.Error("Error log does not contain error message")
	}
}
