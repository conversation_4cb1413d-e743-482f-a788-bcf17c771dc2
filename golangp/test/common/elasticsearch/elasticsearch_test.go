/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-15 10:04:49
 */
package elasticsearch_test

import (
	"context"
	"testing"
	"time"

	"pointer/golangp/common/elasticsearch"
)

func TestElasticsearchClient(t *testing.T) {
	// 创建测试配置
	config := &elasticsearch.Config{
		URLs:        []string{"http://*************:9200"},
		Username:    "elastic",
		Password:    "admin123",
		Sniff:       false,
		HealthCheck: true,
	}

	// 创建客户端
	client, err := elasticsearch.NewClient(config)
	if err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}
	defer client.Close()

	// 测试连接
	ctx := context.Background()
	if err := client.Ping(ctx); err != nil {
		t.Fatalf("Failed to ping Elasticsearch: %v", err)
	}

	// 测试索引操作
	testIndex := "test_index_" + time.Now().Format("20060102150405")

	// 创建索引
	settings := &elasticsearch.IndexSettings{
		NumberOfShards:   1,
		NumberOfReplicas: 1,
	}

	mappings := map[string]interface{}{
		"properties": map[string]interface{}{
			"title": map[string]interface{}{
				"type": "text",
			},
			"content": map[string]interface{}{
				"type": "text",
			},
			"created_at": map[string]interface{}{
				"type": "date",
			},
		},
	}

	if err := client.CreateIndex(ctx, testIndex, settings, mappings); err != nil {
		t.Fatalf("Failed to create index: %v", err)
	}

	// 检查索引是否存在
	exists, err := client.IndexExists(ctx, testIndex)
	if err != nil {
		t.Fatalf("Failed to check index existence: %v", err)
	}
	if !exists {
		t.Fatalf("Index should exist but doesn't")
	}

	// 获取索引设置
	indexSettings, err := client.GetIndexSettings(ctx, testIndex)
	if err != nil {
		t.Fatalf("Failed to get index settings: %v", err)
	}
	if indexSettings == nil {
		t.Fatalf("Index settings should not be nil")
	}

	// 获取索引映射
	indexMappings, err := client.GetIndexMappings(ctx, testIndex)
	if err != nil {
		t.Fatalf("Failed to get index mappings: %v", err)
	}
	if indexMappings == nil {
		t.Fatalf("Index mappings should not be nil")
	}

	// 清理：删除测试索引
	if err := client.DeleteIndex(ctx, testIndex); err != nil {
		t.Fatalf("Failed to delete index: %v", err)
	}
}
