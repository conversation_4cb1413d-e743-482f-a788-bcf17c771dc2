package storage_test

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"strings"
	"testing"
	"time"

	"github.com/minio/minio-go/v7"

	"pointer/golangp/common/storage"
)

// MockMinioClient can be developed further if deeper testing of MinIO interactions is needed.
// For now, we are testing the parts of MinioS3Service that don't heavily rely on live client responses,
// or we are testing the constructor.

// TestNewMinioS3Service tests the constructor for MinioS3Service.
func TestNewMinioS3Service(t *testing.T) {
	// Test with valid parameters
	_, err := storage.NewMinioS3Service("minio", "localhost:9000", "accesskey", "secretkey", "us-east-1", false)
	if err != nil {
		t.<PERSON>rrorf("NewMinioS3Service() with valid params failed: %v", err)
	}

	// Test with OSS provider
	_, err = storage.NewMinioS3Service("oss", "oss-cn-hangzhou.aliyuncs.com", "accesskey", "secretkey", "", true)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("NewMinioS3Service() with OSS provider failed: %v", err)
	}

	// Test with S3 provider
	_, err = storage.NewMinioS3Service("s3", "s3.amazonaws.com", "accesskey", "secretkey", "us-east-1", true)
	if err != nil {
		t.Errorf("NewMinioS3Service() with S3 provider failed: %v", err)
	}
}

// TestNewStorageService tests the factory function
func TestNewStorageService(t *testing.T) {
	tests := []struct {
		name        string
		config      storage.Config
		expectError bool
	}{
		{
			name: "Valid MinIO Config",
			config: storage.Config{
				Provider:        "minio",
				Endpoint:        "localhost:9000",
				AccessKeyID:     "accesskey",
				SecretAccessKey: "secretkey",
				UseSSL:          false,
				Region:          "us-east-1",
			},
			expectError: false,
		},
		{
			name: "Valid OSS Config",
			config: storage.Config{
				Provider:        "oss",
				Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
				AccessKeyID:     "accesskey",
				SecretAccessKey: "secretkey",
				UseSSL:          true,
				Region:          "",
			},
			expectError: false,
		},
		{
			name: "Missing Endpoint",
			config: storage.Config{
				Provider:        "minio",
				AccessKeyID:     "accesskey",
				SecretAccessKey: "secretkey",
				UseSSL:          false,
			},
			expectError: true,
		},
		{
			name: "Missing AccessKeyID",
			config: storage.Config{
				Provider:        "minio",
				Endpoint:        "localhost:9000",
				SecretAccessKey: "secretkey",
				UseSSL:          false,
			},
			expectError: true,
		},
		{
			name: "Missing SecretAccessKey",
			config: storage.Config{
				Provider:    "minio",
				Endpoint:    "localhost:9000",
				AccessKeyID: "accesskey",
				UseSSL:      false,
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := storage.NewStorageService(tt.config)
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if service == nil {
					t.Errorf("expected service but got nil")
				}
			}
		})
	}
}

// TestGeneratePreviewBaseURL tests the URL generation utility
func TestGeneratePreviewBaseURL(t *testing.T) {
	tests := []struct {
		name        string
		provider    string
		endpoint    string
		region      string
		bucketName  string
		useSSL      bool
		expectedURL string
	}{
		{
			name:        "MinIO HTTP",
			provider:    "minio",
			endpoint:    "localhost:9000",
			region:      "",
			bucketName:  "mybucket",
			useSSL:      false,
			expectedURL: "http://localhost:9000",
		},
		{
			name:        "MinIO HTTPS",
			provider:    "minio",
			endpoint:    "localhost:9000",
			region:      "",
			bucketName:  "mybucket",
			useSSL:      true,
			expectedURL: "https://localhost:9000",
		},
		{
			name:        "OSS",
			provider:    "oss",
			endpoint:    "oss-cn-hangzhou.aliyuncs.com",
			region:      "",
			bucketName:  "mybucket",
			useSSL:      true,
			expectedURL: "https://mybucket.oss-cn-hangzhou.aliyuncs.com",
		},
		{
			name:        "S3 with region",
			provider:    "s3",
			endpoint:    "s3.amazonaws.com",
			region:      "us-east-1",
			bucketName:  "mybucket",
			useSSL:      true,
			expectedURL: "https://mybucket.s3.us-east-1.amazonaws.com",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := storage.GeneratePreviewBaseURL(tt.provider, tt.endpoint, tt.region, tt.bucketName, tt.useSSL)
			if result != tt.expectedURL {
				t.Errorf("expected %s, got %s", tt.expectedURL, result)
			}
		})
	}
}

// TestGenerateObjectPath tests the object path generation utility
func TestGenerateObjectPath(t *testing.T) {
	tests := []struct {
		name         string
		bucketName   string
		objectKey    string
		provider     string
		expectedPath string
	}{
		{
			name:         "MinIO path",
			bucketName:   "mybucket",
			objectKey:    "folder/file.txt",
			provider:     "minio",
			expectedPath: "mybucket/folder/file.txt",
		},
		{
			name:         "OSS path",
			bucketName:   "mybucket",
			objectKey:    "folder/file.txt",
			provider:     "oss",
			expectedPath: "folder/file.txt",
		},
		{
			name:         "S3 path",
			bucketName:   "mybucket",
			objectKey:    "folder/file.txt",
			provider:     "s3",
			expectedPath: "mybucket/folder/file.txt",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := storage.GenerateObjectPath(tt.bucketName, tt.objectKey, tt.provider)
			if result != tt.expectedPath {
				t.Errorf("expected %s, got %s", tt.expectedPath, result)
			}
		})
	}
}

// TestGenerateBucketPolicy tests the bucket policy generation
func TestGenerateBucketPolicy(t *testing.T) {
	tests := []struct {
		name        string
		bucketName  string
		provider    string
		access      string
		prefix      string
		expectError bool
	}{
		{
			name:        "MinIO readonly",
			bucketName:  "mybucket",
			provider:    "minio",
			access:      "readonly",
			prefix:      "",
			expectError: false,
		},
		{
			name:        "OSS readonly",
			bucketName:  "mybucket",
			provider:    "oss",
			access:      "readonly",
			prefix:      "",
			expectError: false,
		},
		{
			name:        "Invalid access",
			bucketName:  "mybucket",
			provider:    "minio",
			access:      "invalid",
			prefix:      "",
			expectError: true,
		},
		{
			name:        "Empty bucket name",
			bucketName:  "",
			provider:    "minio",
			access:      "readonly",
			prefix:      "",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			policy, err := storage.GenerateBucketPolicy(tt.bucketName, tt.provider, tt.access, tt.prefix)
			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if policy == "" {
					t.Errorf("expected policy but got empty string")
				}
				// Basic validation that it's valid JSON
				if !strings.Contains(policy, `"Version"`) {
					t.Errorf("policy should contain Version field")
				}
			}
		})
	}
}

// TestMinioS3Service_GetPreviewURL tests the GetPreviewURL method with different providers
func TestMinioS3Service_GetPreviewURL(t *testing.T) {
	tests := []struct {
		name              string
		provider          string
		endpoint          string
		region            string
		bucketName        string
		objectKey         string
		configuredBaseURL string
		expectedURL       string
		expectError       bool
	}{
		{
			name:              "MinIO with configured base URL",
			provider:          "minio",
			endpoint:          "localhost:9000",
			region:            "",
			bucketName:        "mybucket",
			objectKey:         "folder/file.txt",
			configuredBaseURL: "http://localhost:9000",
			expectedURL:       "http://localhost:9000/mybucket/folder/file.txt",
			expectError:       false,
		},
		{
			name:              "OSS with configured base URL",
			provider:          "oss",
			endpoint:          "oss-cn-hangzhou.aliyuncs.com",
			region:            "",
			bucketName:        "mybucket",
			objectKey:         "folder/file.txt",
			configuredBaseURL: "https://mybucket.oss-cn-hangzhou.aliyuncs.com",
			expectedURL:       "https://mybucket.oss-cn-hangzhou.aliyuncs.com/folder/file.txt",
			expectError:       false,
		},
		{
			name:              "S3 with configured base URL",
			provider:          "s3",
			endpoint:          "s3.amazonaws.com",
			region:            "us-east-1",
			bucketName:        "mybucket",
			objectKey:         "folder/file.txt",
			configuredBaseURL: "https://mybucket.s3.us-east-1.amazonaws.com",
			expectedURL:       "https://mybucket.s3.us-east-1.amazonaws.com/folder/file.txt",
			expectError:       false,
		},
		{
			name:              "MinIO with dynamic base URL",
			provider:          "minio",
			endpoint:          "localhost:9000",
			region:            "",
			bucketName:        "mybucket",
			objectKey:         "folder/file.txt",
			configuredBaseURL: "",
			expectedURL:       "http://localhost:9000/mybucket/folder/file.txt",
			expectError:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service, err := storage.NewMinioS3Service(tt.provider, tt.endpoint, "dummy", "dummy", tt.region, strings.HasPrefix(tt.expectedURL, "https"))
			if err != nil {
				t.Fatalf("failed to create service: %v", err)
			}

			urlStr, err := service.GetPreviewURL(tt.bucketName, tt.objectKey, tt.configuredBaseURL)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected an error, but got nil")
				}
			} else {
				if err != nil {
					t.Errorf("did not expect an error, but got: %v", err)
				}
				if urlStr != tt.expectedURL {
					t.Errorf("expected URL: %s, got: %s", tt.expectedURL, urlStr)
				}
				// Validate if the URL is parsable
				_, parseErr := url.Parse(urlStr)
				if parseErr != nil {
					t.Errorf("generated URL is not parsable: %s, error: %v", urlStr, parseErr)
				}
			}
		})
	}
}

// TestMinioS3Service_RealServer tests with real storage server
func TestMinioS3Service_RealServer(t *testing.T) {
	// Skip if not running integration tests
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Test configuration - you can modify these for your test environment
	config := storage.Config{
		Provider:        "minio", // Change to "oss" or "s3" for different providers
		Endpoint:        "************:9000",
		AccessKeyID:     "shedangjia",
		SecretAccessKey: "123456789",
		UseSSL:          false,
		Region:          "us-east-1",
	}

	// Create service using factory
	service, err := storage.NewStorageService(config)
	if err != nil {
		t.Fatalf("Failed to create storage service: %v", err)
	}

	// Test bucket name
	testBucket := "test-bucket-" + fmt.Sprintf("%d", time.Now().Unix())
	testObjectKey := "test-object.txt"
	testContent := "Hello, Storage Service!"

	t.Run("Upload Test", func(t *testing.T) {
		// Upload a test file
		reader := strings.NewReader(testContent)
		objectKey, objectPath, err := service.Upload(testBucket, testObjectKey, reader, int64(len(testContent)), "text/plain")
		if err != nil {
			t.Fatalf("Upload failed: %v", err)
		}
		t.Logf("Successfully uploaded object: %s", objectKey)

		// Test object path generation
		t.Logf("Object path: %s", objectPath)
	})

	t.Run("Download Test", func(t *testing.T) {
		// Download the test file
		reader, err := service.Download(testBucket, testObjectKey)
		if err != nil {
			t.Fatalf("Download failed: %v", err)
		}
		defer reader.Close()

		// Read content
		content, err := io.ReadAll(reader)
		if err != nil {
			t.Fatalf("Failed to read downloaded content: %v", err)
		}

		if string(content) != testContent {
			t.Errorf("Downloaded content mismatch. Expected: %s, Got: %s", testContent, string(content))
		}
		t.Logf("Successfully downloaded and verified content")
	})

	t.Run("GetPreviewURL Test", func(t *testing.T) {
		// Test preview URL generation
		previewURL, err := service.GetPreviewURL(testBucket, testObjectKey, "")
		if err != nil {
			t.Fatalf("GetPreviewURL failed: %v", err)
		}

		t.Logf("Generated preview URL: %s", previewURL)

		// Validate URL format
		_, parseErr := url.Parse(previewURL)
		if parseErr != nil {
			t.Errorf("Generated URL is not parsable: %v", parseErr)
		}
	})

	t.Run("SetAnonymousAccessPolicy Test", func(t *testing.T) {
		prefix := "test-policy/"

		// Test setting readonly
		t.Run("SetReadOnly", func(t *testing.T) {
			err := service.SetAnonymousAccessPolicy(testBucket, prefix, "readonly")
			if err != nil {
				t.Fatalf("SetAnonymousAccessPolicy(readonly) failed: %v", err)
			}
			t.Log("✓ readonly policy set successfully")
		})

		// Test getting bucket policy after setting readonly
		t.Run("GetBucketPolicy After ReadOnly", func(t *testing.T) {
			policy, err := service.GetBucketPolicy(testBucket)
			if err != nil {
				t.Fatalf("GetBucketPolicy failed: %v", err)
			}
			if policy == "" {
				t.Log("✓ No bucket policy set (empty string returned)")
			} else {
				t.Logf("✓ Current bucket policy:\n%s", policy)
				// Basic validation that it's valid JSON
				if !strings.Contains(policy, `"Version"`) {
					t.Errorf("policy should contain Version field")
				}
				if !strings.Contains(policy, `"Statement"`) {
					t.Errorf("policy should contain Statement field")
				}
			}
		})

		// Test setting none
		t.Run("SetNone", func(t *testing.T) {
			err := service.SetAnonymousAccessPolicy(testBucket, prefix, "none")
			if err != nil {
				t.Fatalf("SetAnonymousAccessPolicy(none) failed: %v", err)
			}
			t.Log("✓ none policy set successfully")
		})

		// Test getting bucket policy after setting none
		t.Run("GetBucketPolicy After None", func(t *testing.T) {
			policy, err := service.GetBucketPolicy(testBucket)
			if err != nil {
				t.Fatalf("GetBucketPolicy failed: %v", err)
			}
			if policy == "" {
				t.Log("✓ No bucket policy set (empty string returned) - expected for 'none' access")
			} else {
				t.Logf("✓ Current bucket policy:\n%s", policy)
			}
		})
	})

	// Cleanup
	t.Cleanup(func() {
		t.Logf("Test completed. Test bucket: %s", testBucket)
	})
}

// TestMinioS3Service_Upload_Placeholder is a placeholder for future Upload tests.
// Meaningful tests would require mocking the MinIO client or using a test MinIO server.
func TestMinioS3Service_Upload_Placeholder(t *testing.T) {
	t.Skip("Skipping Upload test: requires MinIO client mocking or a live test server.")

	// Example structure for a mocked test:
	// mockClient := newMockMinioClient() // Your mock implementation
	// service := &MinioS3Service{client: mockClient, region: "us-east-1"}
	// _, err := service.Upload("testbucket", "testobject", strings.NewReader("content"), 7, "text/plain")
	// if err != nil { t.Errorf("Upload failed: %v", err) }
	// Assert that mockClient.PutObject was called with expected parameters.
}

// TestMinioS3Service_Download_Placeholder is a placeholder for future Download tests.
func TestMinioS3Service_Download_Placeholder(t *testing.T) {
	t.Skip("Skipping Download test: requires MinIO client mocking or a live test server.")

	// Example structure for a mocked test:
	// mockClient := newMockMinioClient()
	// mockClient.expectGetObject("testbucket", "testobject", "mocked content") // Configure mock
	// service := &MinioS3Service{client: mockClient}
	// reader, err := service.Download("testbucket", "testobject")
	// if err != nil { t.Errorf("Download failed: %v", err) }
	// // Read content and assert
	// defer reader.Close()
}

// --- Mocking MinIO Client (Illustrative - needs full implementation if used) ---
// This is a very basic sketch. A real mock would implement minio.Client interfaces
// or use a library like mockery or testcontainers.

type mockMinioOperations interface {
	PutObject(ctx context.Context, bucketName, objectName string, reader io.Reader, objectSize int64, opts minio.PutObjectOptions) (minio.UploadInfo, error)
	GetObject(ctx context.Context, bucketName, objectName string, opts minio.GetObjectOptions) (*minio.Object, error)
	BucketExists(ctx context.Context, bucketName string) (bool, error)
	MakeBucket(ctx context.Context, bucketName string, opts minio.MakeBucketOptions) error
	// ... other methods used by MinioS3Service
}

// A simple mock structure
type simpleMockMinioClient struct {
	// Store expected calls and responses here
	putObjectFunc func(bucketName, objectName string) (minio.UploadInfo, error)
	getObjectFunc func(bucketName, objectName string) (*minio.Object, error)
	// ...
}

// Implement methods from mockMinioOperations
// func (m *simpleMockMinioClient) PutObject(...) { ... return m.putObjectFunc(...) }
// func (m *simpleMockMinioClient) GetObject(...) { ... return m.getObjectFunc(...) }

// This is non-trivial to do correctly without either:
// 1. Defining an interface that *minio.Client happens to satisfy, and coding to that interface.
// 2. Using a library like testify/mock and generating mocks.
// 3. Using testcontainers to spin up a real MinIO instance for integration tests.

// For now, the tests above focus on GetPreviewURL's logic which is less client-dependent.
// and the constructor.
func TestMain(m *testing.M) {
	// Setup global test resources here if needed
	m.Run()
	// Teardown global test resources here
}
