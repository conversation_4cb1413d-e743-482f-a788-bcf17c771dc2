package redis

import (
	"context"
	"time"

	"github.com/go-redsync/redsync/v4"
	"github.com/redis/go-redis/v9"
)

// <PERSON> tests the Redis connection
func (c *Client) Ping(ctx context.Context) error {
	return c.client.Ping(ctx).Err()
}

// Set sets a key-value pair with optional expiration
func (c *Client) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return c.client.Set(ctx, key, value, expiration).Err()
}

// Get retrieves the value for a key
func (c *Client) Get(ctx context.Context, key string) (string, error) {
	return c.client.Get(ctx, key).Result()
}

// Del deletes one or more keys
func (c *Client) Del(ctx context.Context, keys ...string) error {
	return c.client.Del(ctx, keys...).Err()
}

// Exists checks if one or more keys exist
func (c *Client) Exists(ctx context.Context, keys ...string) (int64, error) {
	return c.client.Exists(ctx, keys...).Result()
}

// Expire sets the expiration time for a key
func (c *Client) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return c.client.Expire(ctx, key, expiration).Err()
}

// TTL returns the remaining time to live of a key
func (c *Client) TTL(ctx context.Context, key string) (time.Duration, error) {
	return c.client.TTL(ctx, key).Result()
}

// HSet sets the hash field to the specified value
func (c *Client) HSet(ctx context.Context, key string, values ...interface{}) error {
	return c.client.HSet(ctx, key, values...).Err()
}

// HGet retrieves the value of a hash field
func (c *Client) HGet(ctx context.Context, key, field string) (string, error) {
	return c.client.HGet(ctx, key, field).Result()
}

// HGetAll retrieves all fields and values of a hash
func (c *Client) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	return c.client.HGetAll(ctx, key).Result()
}

// HDel deletes one or more hash fields
func (c *Client) HDel(ctx context.Context, key string, fields ...string) error {
	return c.client.HDel(ctx, key, fields...).Err()
}

// LPush prepends one or multiple values to a list
func (c *Client) LPush(ctx context.Context, key string, values ...interface{}) error {
	return c.client.LPush(ctx, key, values...).Err()
}

// RPush appends one or multiple values to a list
func (c *Client) RPush(ctx context.Context, key string, values ...interface{}) error {
	return c.client.RPush(ctx, key, values...).Err()
}

// LRange returns the specified elements of the list
func (c *Client) LRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	return c.client.LRange(ctx, key, start, stop).Result()
}

// SAdd adds one or more members to a set
func (c *Client) SAdd(ctx context.Context, key string, members ...interface{}) error {
	return c.client.SAdd(ctx, key, members...).Err()
}

// SMembers returns all the members of the set
func (c *Client) SMembers(ctx context.Context, key string) ([]string, error) {
	return c.client.SMembers(ctx, key).Result()
}

// SRem removes one or more members from a set
func (c *Client) SRem(ctx context.Context, key string, members ...interface{}) error {
	return c.client.SRem(ctx, key, members...).Err()
}

// ZAdd adds one or more members to a sorted set
func (c *Client) ZAdd(ctx context.Context, key string, members ...redis.Z) error {
	return c.client.ZAdd(ctx, key, members...).Err()
}

// ZRange returns the specified range of elements in the sorted set
func (c *Client) ZRange(ctx context.Context, key string, start, stop int64) ([]string, error) {
	return c.client.ZRange(ctx, key, start, stop).Result()
}

// ZRem removes one or more members from a sorted set
func (c *Client) ZRem(ctx context.Context, key string, members ...interface{}) error {
	return c.client.ZRem(ctx, key, members...).Err()
}

// BitOpAnd performs a bitwise AND operation on multiple keys and stores the result in a destination key
func (c *Client) BitOpAnd(ctx context.Context, destKey string, keys ...string) (int64, error) {
	return c.client.BitOpAnd(ctx, destKey, keys...).Result()
}

// BitPos returns the position of the first bit set to 1 in a string
func (c *Client) BitPos(ctx context.Context, key string, bit, start int64) (int64, error) {
	return c.client.BitPos(ctx, key, bit, start, -1).Result()
}

// GetBit retrieves the value of a bit at a specific offset in a string
func (c *Client) GetBit(ctx context.Context, key string, offset int64) (int64, error) {
	return c.client.GetBit(ctx, key, offset).Result()
}

// Incr increments the integer value of a key by one
func (c *Client) Incr(ctx context.Context, key string) (int64, error) {
	return c.client.Incr(ctx, key).Result()
}

// Lock acquires a distributed lock with a specified expiration
func (c *Client) Lock(ctx context.Context, lockKey string, expiry time.Duration) (func(), error) {
	mutex := c.rs.NewMutex(lockKey, redsync.WithExpiry(expiry))

	if err := mutex.Lock(); err != nil {
		return nil, err
	}

	// 返回一个用于释放锁的函数
	return func() {
		if ok, err := mutex.Unlock(); !ok || err != nil {
			// 可记录日志或处理异常
		}
	}, nil
}

// GetRange retrieves a substring of a string stored at key
func (c *Client) GetRange(ctx context.Context, key string, start, end int64) (string, error) {
	return c.client.GetRange(ctx, key, start, end).Result()
}
