package redis

// Config holds the Redis client configuration
type Config struct {
	// Addr is the Redis server address in the format "host:port"
	Addr string

	// Password is the Redis server password
	Password string

	// DB is the Redis database number
	DB int

	// PoolSize is the maximum number of socket connections
	PoolSize int

	// MinIdleConns is the minimum number of idle connections
	MinIdleConns int
}

// DefaultConfig returns a default Redis configuration
func DefaultConfig() *Config {
	return &Config{
		Addr:         "localhost:6379",
		Password:     "", // 生产环境建议配置密码
		DB:           0,  // 逻辑数据库编号
		PoolSize:     50, // 最大连接数
		MinIdleConns: 10, // 最小空闲连接数
	}
}
