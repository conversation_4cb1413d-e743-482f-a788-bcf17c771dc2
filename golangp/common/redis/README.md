# Redis Client Module

这个模块提供了一个简单的 Redis 客户端封装，支持基本的键值操作、哈希表操作、列表操作和集合操作。

## 功能特性

- 客户端连接管理
- 键值操作（设置、获取、删除）
- 哈希表操作（HSET, HGET, HDEL 等）
- 列表操作（LPUSH, RPUSH, LPOP, RPOP 等）
- 集合操作（SADD, SREM, SMEMBERS 等）
- 过期时间管理
- 错误处理和自定义错误类型

## 安装

确保你的项目中已经添加了以下依赖：

```go
import "github.com/redis/go-redis/v9"
```

## 使用方法

### 1. 创建客户端

```go
config := &redis.Config{
    Addr:     "localhost:6379",
    Password: "", // 如果有密码，在这里设置
    DB:       0,  // 使用默认数据库
}

client, err := redis.NewClient(config)
if err != nil {
    log.Fatalf("Failed to create client: %v", err)
}
defer client.Close()
```

### 2. 键值操作

```go
// 设置键值
err := client.Set(ctx, "key", "value", 0)

// 获取键值
value, err := client.Get(ctx, "key")

// 删除键
err := client.Del(ctx, "key")

// 设置过期时间
err := client.Expire(ctx, "key", 24*time.Hour)
```

### 3. 哈希表操作

```go
// 设置哈希表字段
err := client.HSet(ctx, "hash", "field1", "value1", "field2", "value2")

// 获取哈希表字段
value, err := client.HGet(ctx, "hash", "field1")

// 获取所有字段
fields, err := client.HGetAll(ctx, "hash")

// 删除哈希表字段
err := client.HDel(ctx, "hash", "field1")
```

### 4. 列表操作

```go
// 从左侧推入元素
err := client.LPush(ctx, "list", "value1", "value2")

// 从右侧推入元素
err := client.RPush(ctx, "list", "value3", "value4")

// 从左侧弹出元素
value, err := client.LPop(ctx, "list")

// 从右侧弹出元素
value, err := client.RPop(ctx, "list")

// 获取列表范围
values, err := client.LRange(ctx, "list", 0, -1)
```

### 5. 集合操作

```go
// 添加集合元素
err := client.SAdd(ctx, "set", "member1", "member2")

// 移除集合元素
err := client.SRem(ctx, "set", "member1")

// 获取集合所有成员
members, err := client.SMembers(ctx, "set")

// 检查元素是否存在
exists, err := client.SIsMember(ctx, "set", "member1")
```

## 错误处理

模块定义了以下自定义错误类型：

- `ErrKeyNotFound`: 键不存在
- `ErrInvalidValue`: 无效的值类型
- `ErrConnectionFailed`: 连接失败

## 配置说明

### Config 结构体

```go
type Config struct {
    Addr     string // Redis 服务器地址
    Password string // 密码
    DB       int    // 数据库编号
}
```

## 注意事项

1. 确保 Redis 服务器已启动并可访问
2. 正确配置认证信息（如果需要密码）
3. 在生产环境中适当设置连接池大小
4. 注意处理错误情况，特别是网络错误和认证失败
5. 合理使用过期时间，避免内存泄漏

## 示例

完整的示例代码可以参考 `golangp/test/common/redis/redis_test.go` 文件。 