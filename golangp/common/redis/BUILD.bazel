load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "redis",
    srcs = [
        "config.go",
        "connection.go",
        "operations.go",
    ],
    importpath = "pointer/golangp/common/redis",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_go_redsync_redsync_v4//:redsync",
        "@com_github_go_redsync_redsync_v4//redis/goredis/v9:goredis",
        "@com_github_redis_go_redis_v9//:go-redis",
    ],
)
