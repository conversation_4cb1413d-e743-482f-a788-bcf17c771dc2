package logging

import (
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"
)

// LogLevel 定义日志级别
type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARNING
	ERROR
	FATAL
)

var levelNames = map[LogLevel]string{
	DEBUG:   "DEBUG",
	INFO:    "INFO",
	WARNING: "WARNING",
	ERROR:   "ERROR",
	FATAL:   "FATAL",
}

// LoggerConfig 定义日志配置
type LoggerConfig struct {
	Level      LogLevel
	MaxSize    int64  // 单个日志文件最大大小（字节）
	LogPath    string // 日志文件路径
	FileName   string // 日志文件名
	MaxBackups *int   // 最大备份文件数，可选
	MaxAge     *int   // 日志文件保留天数，可选
	Compress   *bool  // 是否压缩备份文件，可选
	Env        string // 环境配置：dev, test, prod
}

var (
	defaultConfig = LoggerConfig{
		Level:    INFO,
		MaxSize:  20 * 1024 * 1024, // 20MB
		LogPath:  "logs",
		FileName: "app", // 默认文件名
		Env:      "dev", // 默认环境为dev
	}

	defaultLogger *Logger
	loggers       = make(map[string]*Logger)
	once          sync.Once
	mutex         sync.RWMutex
)

// Logger 封装日志记录器
type Logger struct {
	*log.Logger
	config LoggerConfig
	name   string
}

func InitLogger(config *LoggerConfig) {
	once.Do(func() {
		if config != nil {
			defaultConfig = *config
		}
		defaultLogger = createLogger("default", defaultConfig)
	})
}

// GetLogger 获取或创建指定名称的日志记录器
func GetLogger(name string) *Logger {
	mutex.RLock()
	logger, exists := loggers[name]
	mutex.RUnlock()
	if exists {
		return logger
	}

	mutex.Lock()
	defer mutex.Unlock()

	// Double check
	if logger, exists := loggers[name]; exists {
		return logger
	}

	// 为每个logger创建独立的配置
	config := defaultConfig
	config.FileName = name // 使用name作为文件名
	logger = createLogger(name, config)
	loggers[name] = logger
	return logger
}

func createLogger(name string, config LoggerConfig) *Logger {
	var writers []io.Writer

	// 只在非生产环境下添加控制台输出
	env := strings.ToLower(config.Env)
	if env != "prod" && env != "production" && config.Level <= INFO {
		writers = append(writers, os.Stdout)
	}

	// 添加文件输出
	if config.LogPath != "" {
		if err := os.MkdirAll(config.LogPath, 0755); err != nil {
			log.Fatalf("Failed to create log directory: %v", err)
		}

		// 使用配置的文件名，如果没有则使用name
		fileName := config.FileName
		if fileName == "" {
			fileName = name
		}
		logFileName := fmt.Sprintf("%s.log", fileName)
		logFilePath := filepath.Join(config.LogPath, logFileName)

		file, err := os.OpenFile(logFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
		if err != nil {
			log.Fatalf("Failed to open log file: %v", err)
		}
		writers = append(writers, file)
	}

	if len(writers) == 0 {
		writers = append(writers, os.Stdout)
	}

	return &Logger{
		Logger: log.New(io.MultiWriter(writers...), "", 0),
		config: config,
		name:   name,
	}
}

func (l *Logger) log(level LogLevel, format string, args ...interface{}) {
	if level < l.config.Level {
		return
	}

	_, file, line, ok := runtime.Caller(2)
	if !ok {
		file = "???"
		line = 0
	}

	message := fmt.Sprintf(format, args...)
	timestamp := time.Now().Format("2006/01/02 15:04:05.000")
	levelName := levelNames[level]

	l.Printf("[%s] %s [%s:%d] %s", levelName, timestamp, file, line, message)
}

// 默认日志记录器方法
func Debug(format string, args ...interface{}) {
	defaultLogger.log(DEBUG, format, args...)
}

func Info(format string, args ...interface{}) {
	defaultLogger.log(INFO, format, args...)
}

func Warning(format string, args ...interface{}) {
	defaultLogger.log(WARNING, format, args...)
}

func Error(format string, args ...interface{}) {
	defaultLogger.log(ERROR, format, args...)
}

func Fatal(format string, args ...interface{}) {
	defaultLogger.log(FATAL, format, args...)
	os.Exit(1)
}

// 命名日志记录器方法
func (l *Logger) Debug(format string, args ...interface{}) {
	l.log(DEBUG, format, args...)
}

func (l *Logger) Info(format string, args ...interface{}) {
	l.log(INFO, format, args...)
}

func (l *Logger) Warning(format string, args ...interface{}) {
	l.log(WARNING, format, args...)
}

func (l *Logger) Error(format string, args ...interface{}) {
	l.log(ERROR, format, args...)
}

func (l *Logger) Fatal(format string, args ...interface{}) {
	l.log(FATAL, format, args...)
	os.Exit(1)
}
