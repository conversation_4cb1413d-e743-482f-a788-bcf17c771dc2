# Logging Module

这个模块提供了一个简单而强大的日志记录器封装，支持多级别日志记录、文件输出、环境配置和命名日志记录器。

## 功能特性

- 多级别日志记录（DEBUG, INFO, WARNING, ERROR, FATAL）
- 文件和控制台双重输出
- 环境配置支持（dev, test, prod）
- 命名日志记录器管理
- 自动文件路径和行号记录
- 线程安全的单例模式
- 灵活的配置选项

## 安装

确保你的项目中已经添加了标准库依赖，无需额外安装第三方包。

## 使用方法

### 1. 初始化默认日志记录器

```go
import "your-project/golangp/common/logging"

// 使用默认配置
logging.InitLogger(nil)

// 或者使用自定义配置
config := &logging.LoggerConfig{
    Level:    logging.INFO,
    MaxSize:  50 * 1024 * 1024, // 50MB
    LogPath:  "logs",
    FileName: "myapp",
    Env:      "dev",
}
logging.InitLogger(config)
```

### 2. 使用默认日志记录器

```go
// 基本日志记录
logging.Debug("这是调试信息: %s", "debug message")
logging.Info("应用启动成功，端口: %d", 8080)
logging.Warning("这是警告信息: %s", "warning message")
logging.Error("发生错误: %v", err)
logging.Fatal("致命错误，程序退出: %v", err) // 会调用 os.Exit(1)
```

### 3. 使用命名日志记录器

```go
// 创建或获取命名日志记录器
dbLogger := logging.GetLogger("database")
apiLogger := logging.GetLogger("api")
authLogger := logging.GetLogger("auth")

// 使用命名日志记录器
dbLogger.Info("数据库连接成功")
apiLogger.Warning("API 请求超时: %s", "/api/users")
authLogger.Error("用户认证失败: %s", username)
```

### 4. 高级配置示例

```go
// 生产环境配置
prodConfig := &logging.LoggerConfig{
    Level:    logging.WARNING, // 只记录警告及以上级别
    MaxSize:  100 * 1024 * 1024, // 100MB
    LogPath:  "/var/log/myapp",
    FileName: "production",
    Env:      "prod", // 生产环境不输出到控制台
}
logging.InitLogger(prodConfig)

// 开发环境配置
devConfig := &logging.LoggerConfig{
    Level:    logging.DEBUG, // 记录所有级别
    MaxSize:  20 * 1024 * 1024, // 20MB
    LogPath:  "logs",
    FileName: "development",
    Env:      "dev", // 开发环境输出到控制台和文件
}
logging.InitLogger(devConfig)
```

## 配置说明

### LoggerConfig 结构体

```go
type LoggerConfig struct {
    Level      LogLevel // 日志级别
    MaxSize    int64    // 单个日志文件最大大小（字节）
    LogPath    string   // 日志文件路径
    FileName   string   // 日志文件名
    MaxBackups *int     // 最大备份文件数（预留字段）
    MaxAge     *int     // 日志文件保留天数（预留字段）
    Compress   *bool    // 是否压缩备份文件（预留字段）
    Env        string   // 环境配置：dev, test, prod
}
```

### 日志级别

```go
const (
    DEBUG LogLevel = iota  // 调试信息
    INFO                   // 一般信息
    WARNING               // 警告信息
    ERROR                 // 错误信息
    FATAL                 // 致命错误
)
```

## 输出格式

日志输出格式为：
```
[LEVEL] YYYY/MM/DD HH:MM:SS.mmm [file:line] message
```

示例：
```
[INFO] 2025/07/01 14:30:25.123 [main.go:25] 应用启动成功，端口: 8080
[ERROR] 2025/07/01 14:30:26.456 [database.go:45] 数据库连接失败: connection refused
```

## 环境配置

- **dev/development**: 输出到控制台和文件
- **test**: 输出到控制台和文件
- **prod/production**: 仅输出到文件（INFO级别及以上）

## 注意事项

1. 日志记录器使用单例模式，确保线程安全
2. 命名日志记录器会创建独立的日志文件
3. 生产环境建议设置较高的日志级别以减少I/O开销
4. 日志文件会自动创建目录结构
5. Fatal级别的日志会导致程序退出

## 示例

完整的示例代码：

```go
package main

import (
    "errors"
    "your-project/golangp/common/logging"
)

func main() {
    // 初始化日志记录器
    config := &logging.LoggerConfig{
        Level:    logging.DEBUG,
        LogPath:  "logs",
        FileName: "example",
        Env:      "dev",
    }
    logging.InitLogger(config)

    // 使用默认日志记录器
    logging.Info("应用程序启动")
    
    // 创建命名日志记录器
    dbLogger := logging.GetLogger("database")
    apiLogger := logging.GetLogger("api")
    
    // 模拟一些日志记录
    dbLogger.Info("连接到数据库")
    apiLogger.Debug("处理API请求: GET /users")
    
    // 错误处理
    err := errors.New("模拟错误")
    if err != nil {
        logging.Error("发生错误: %v", err)
    }
    
    logging.Info("应用程序结束")
}
```
