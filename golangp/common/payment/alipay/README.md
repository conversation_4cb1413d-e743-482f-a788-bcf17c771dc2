# 支付宝支付服务

这是一个基于 Golang 的支付宝支付服务封装，使用 Bazel 构建系统，支持通过配置文件传入参数。

## 功能特性

- 支持多种支付方式（网页支付、手机网页支付、APP 支付、扫码支付）
- 支付状态查询
- 退款功能
- 异步通知处理
- 完整的数据库记录
- 支付统计功能
- **使用密钥模式**（推荐，配置简单）

## 快速开始

### 1\. 配置环境变量

在你的应用配置文件（如 `app.env`）中添加以下配置：

```env
# 支付宝配置（密钥模式）
ALIPAY_APP_ID=your_app_id
ALIPAY_PRIVATE_KEY=your_private_key
ALIPAY_PUBLIC_KEY=alipay_public_key
ALIPAY_GATEWAY_URL=https://openapi.alipaydev.com/gateway.do
ALIPAY_SIGN_TYPE=RSA2
ALIPAY_CHARSET=utf-8
ALIPAY_FORMAT=JSON
ALIPAY_VERSION=1.0
ALIPAY_RETURN_URL=http://your-domain.com/alipay/return
ALIPAY_NOTIFY_URL=http://your-domain.com/alipay/notify
ALIPAY_TIMEOUT_EXPRESS=30
ALIPAY_SANDBOX=true
```

### 2\. 初始化服务

```go
package main

import (
    "pointer/golangp/apps/pointer_center/internal/config"
    "pointer/golangp/common/payment/alipay"
    "gorm.io/gorm"
)

func main() {
    // 加载配置
    cfg := config.Load()

    // 获取支付宝配置
    alipayConfig := cfg.GetAlipayConfig()

    // 初始化数据库连接
    var db *gorm.DB // 你的数据库连接

    // 创建支付宝服务
    alipayService, err := alipay.NewService(alipayConfig, db)
    if err != nil {
        panic(err)
    }

    // 使用服务...
}
```

### 3\. 创建支付

```go
// 创建支付请求
req := &alipay.CreatePaymentRequest{
    OutTradeNo:    "ORDER_" + time.Now().Format("20060102150405"),
    TotalAmount:   "0.01",
    Subject:       "测试商品",
    Body:          "这是一个测试商品",
    PaymentMethod: alipay.PaymentMethodWeb,
    TimeoutExpress: 30,
    GoodsDetail: []alipay.GoodsDetail{
        {
            GoodsID:       "goods_001",
            GoodsName:     "测试商品",
            Quantity:      1,
            Price:         "0.01",
            GoodsCategory: "digital",
        },
    },
}

// 创建支付
resp, err := alipayService.CreatePayment(req)
if err != nil {
    log.Printf("创建支付失败: %v", err)
    return
}

log.Printf("支付创建成功: %+v", resp)
```

### 4\. 查询支付状态

```go
// 查询支付状态
queryReq := &alipay.QueryPaymentRequest{
    OutTradeNo: "ORDER_20240101120000",
}

queryResp, err := alipayService.QueryPayment(queryReq)
if err != nil {
    log.Printf("查询支付失败: %v", err)
    return
}

log.Printf("支付状态: %s", queryResp.TradeStatus)
```

### 5\. 申请退款

```go
// 申请退款
refundReq := &alipay.RefundRequest{
    OutTradeNo:   "ORDER_20240101120000",
    RefundAmount: "0.01",
    RefundReason: "用户申请退款",
}

refundResp, err := alipayService.Refund(refundReq)
if err != nil {
    log.Printf("申请退款失败: %v", err)
    return
}

log.Printf("退款成功: %+v", refundResp)
```

### 6\. 处理异步通知

```go
// 在你的HTTP处理器中
func handleAlipayNotify(c *gin.Context) {
    // 获取通知数据
    notifyData := make(map[string]string)
    c.Request.ParseForm()
    for key, values := range c.Request.Form {
        if len(values) > 0 {
            notifyData[key] = values[0]
        }
    }

    // 验证通知
    isValid, err := alipayService.VerifyNotify(notifyData)
    if err != nil || !isValid {
        c.String(http.StatusBadRequest, "fail")
        return
    }

    // 处理通知
    err = alipayService.ProcessNotify(notifyData)
    if err != nil {
        log.Printf("处理通知失败: %v", err)
        c.String(http.StatusInternalServerError, "fail")
        return
    }

    c.String(http.StatusOK, "success")
}
```

## 数据库迁移

服务使用以下数据表：

- `payment_records` - 支付记录
- `refund_records` - 退款记录
- `notify_records` - 异步通知记录
- `payment_statistics` - 支付统计

确保在使用前运行数据库迁移：

```go
// 自动迁移数据表
err := db.AutoMigrate(
    &alipay.PaymentRecord{},
    &alipay.RefundRecord{},
    &alipay.NotifyRecord{},
    &alipay.PaymentStatistics{},
)
if err != nil {
    panic(err)
}
```

## 配置说明

| 配置项                 | 说明           | 必填 | 默认值               |
| ---------------------- | -------------- | ---- | -------------------- |
| ALIPAY_APP_ID          | 支付宝应用 ID  | 是   | \-                   |
| ALIPAY_PRIVATE_KEY     | 应用私钥       | 是   | \-                   |
| ALIPAY_PUBLIC_KEY      | 支付宝公钥     | 是   | \-                   |
| ALIPAY_GATEWAY_URL     | 网关地址       | 否   | 根据沙箱模式自动设置 |
| ALIPAY_SIGN_TYPE       | 签名类型       | 否   | RSA2                 |
| ALIPAY_CHARSET         | 字符编码       | 否   | utf-8                |
| ALIPAY_FORMAT          | 数据格式       | 否   | JSON                 |
| ALIPAY_VERSION         | 版本号         | 否   | 1.0                  |
| ALIPAY_RETURN_URL      | 同步回调地址   | 否   | \-                   |
| ALIPAY_NOTIFY_URL      | 异步回调地址   | 否   | \-                   |
| ALIPAY_TIMEOUT_EXPRESS | 超时时间(分钟) | 否   | 30                   |
| ALIPAY_SANDBOX         | 是否沙箱模式   | 否   | false                |

## 支付方式

- `PaymentMethodWeb` - 网页支付
- `PaymentMethodWap` - 手机网页支付
- `PaymentMethodApp` - APP 支付
- `PaymentMethodQR` - 扫码支付

## 支付状态

- `PaymentStatusWaitBuyerPay` - 等待买家付款
- `PaymentStatusTradeClosed` - 交易关闭
- `PaymentStatusTradeSuccess` - 交易成功
- `PaymentStatusTradeFinished` - 交易完成

## 构建

使用 Bazel 构建：

```bash
# 构建支付宝包
bazel build //golangp/common/payment/alipay:alipay

# 运行测试
bazel test //golangp/common/payment/alipay:alipay_test
```

## 注意事项

1. 生产环境请设置 `ALIPAY_SANDBOX=false`
2. 确保异步通知 URL 可以被支付宝服务器访问
3. 私钥和公钥请妥善保管，不要提交到代码仓库
4. 建议使用 HTTPS 协议处理支付相关请求
5. 当前实现为简化版本，实际生产使用时需要完善具体的支付宝 SDK 调用

## 许可证

MIT License
