package alipay

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/smartwalle/alipay/v3"
	"gorm.io/gorm"
)

// Service 支付宝支付服务
type Service struct {
	client       *alipay.Client
	config       *Config
	db           *gorm.DB
	cacheManager *CacheManager
}

// NewService 创建支付宝支付服务实例
func NewService(config *Config, db *gorm.DB) (*Service, error) {
	return NewServiceWithRedis(config, db, nil)
}

// NewServiceWithRedis 创建支付宝支付服务实例（带 Redis 支持）
func NewServiceWithRedis(config *Config, db *gorm.DB, redisClient interface{}) (*Service, error) {
	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid alipay config: %w", err)
	}

	// 设置默认值
	config.SetDefaults()

	// 打印配置信息用于调试
	fmt.Printf("🔍 支付宝配置调试信息:\n")
	fmt.Printf("   AppID: %s\n", config.AppID)
	fmt.Printf("   Sandbox: %t\n", config.Sandbox)
	fmt.Printf("   GatewayURL: %s\n", config.GatewayURL)
	fmt.Printf("   UseCertMode: %t\n", config.UseCertMode)
	fmt.Printf("   PrivateKey长度: %d\n", len(config.PrivateKey))
	if config.UseCertMode {
		fmt.Printf("   AppCertPath: %s\n", config.AppCertPath)
		fmt.Printf("   AlipayCertPath: %s\n", config.AlipayCertPath)
		fmt.Printf("   RootCertPath: %s\n", config.RootCertPath)
	} else {
		fmt.Printf("   PublicKey长度: %d\n", len(config.PublicKey))
	}

	// 创建支付宝客户端（直接使用原始密钥，SDK会自动处理格式）
	client, err := alipay.New(config.AppID, config.PrivateKey, config.Sandbox)
	if err != nil {
		return nil, fmt.Errorf("failed to create alipay client: %w", err)
	}

	// 根据配置选择证书模式或密钥模式
	if config.UseCertMode {
		// 使用证书模式
		fmt.Printf("🔐 使用证书模式初始化...\n")
		if err := client.LoadAppCertPublicKeyFromFile(config.AppCertPath); err != nil {
			return nil, fmt.Errorf("failed to load app cert: %w", err)
		}
		if err := client.LoadAlipayCertPublicKeyFromFile(config.AlipayCertPath); err != nil {
			return nil, fmt.Errorf("failed to load alipay cert: %w", err)
		}
		if err := client.LoadAliPayRootCertFromFile(config.RootCertPath); err != nil {
			return nil, fmt.Errorf("failed to load root cert: %w", err)
		}
		fmt.Printf("✅ 证书模式初始化成功\n")
	} else {
		// 使用公钥模式
		fmt.Printf("🔑 使用公钥模式初始化...\n")
		if err := client.LoadAliPayPublicKey(config.PublicKey); err != nil {
			return nil, fmt.Errorf("failed to load alipay public key: %w", err)
		}
		fmt.Printf("✅ 公钥模式初始化成功\n")
	}

	// 设置内容加密密钥（可选，用于敏感信息加密）
	if config.EncryptKey != "" {
		fmt.Printf("🔐 设置内容加密密钥...\n")
		if err := client.SetEncryptKey(config.EncryptKey); err != nil {
			fmt.Printf("⚠️  设置内容加密密钥失败: %v\n", err)
			fmt.Printf("💡 提示：内容加密密钥是可选的，不影响基本支付功能\n")
		} else {
			fmt.Printf("✅ 内容加密密钥设置成功\n")
		}
	}

	// 初始化缓存管理器
	var cacheManager *CacheManager
	if redisClient != nil {
		// 尝试类型断言为 *redis.Client
		if rdb, ok := redisClient.(*redis.Client); ok {
			cacheManager = NewCacheManager(rdb)
			fmt.Printf("✅ Redis 缓存管理器初始化成功\n")
		} else {
			fmt.Printf("⚠️  Redis 客户端类型不匹配，缓存功能将被禁用\n")
		}
	} else {
		fmt.Printf("💡 未提供 Redis 客户端，缓存功能将被禁用\n")
	}

	return &Service{
		client:       client,
		config:       config,
		db:           db,
		cacheManager: cacheManager,
	}, nil
}

// CreatePayment 创建支付
func (s *Service) CreatePayment(req *CreatePaymentRequest) (*CreatePaymentResponse, error) {
	// 检查订单号是否已存在
	var existingRecord PaymentRecord
	if err := s.db.Where("out_trade_no = ?", req.OutTradeNo).First(&existingRecord).Error; err == nil {
		return nil, fmt.Errorf("order number %s already exists", req.OutTradeNo)
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing order: %w", err)
	}

	// 验证金额格式
	if req.TotalAmount == "" {
		return nil, fmt.Errorf("total amount is required")
	}

	// 验证金额是否为有效数字
	if _, err := strconv.ParseFloat(req.TotalAmount, 64); err != nil {
		return nil, fmt.Errorf("invalid total amount format: %s", req.TotalAmount)
	}

	// 创建支付记录
	record := &PaymentRecord{
		ID:             uuid.New(),
		OutTradeNo:     req.OutTradeNo,
		Subject:        req.Subject,
		Body:           req.Body,
		TotalAmount:    req.TotalAmount,
		PaymentMethod:  req.PaymentMethod,
		Status:         PaymentStatusWaitBuyerPay,
		TimeoutExpress: req.TimeoutExpress,
		PassbackParams: req.PassbackParams,
		// 初始化decimal字段为"0.00"，避免空字符串导致的数据库错误
		BuyerPayAmount: "0.00",
		ReceiptAmount:  "0.00",
		InvoiceAmount:  "0.00",
	}

	// 设置超时时间
	if record.TimeoutExpress == 0 {
		record.TimeoutExpress = s.config.TimeoutExpress
	}

	now := time.Now()
	record.GmtCreate = &now

	// 保存到数据库
	if err := s.db.Create(record).Error; err != nil {
		return nil, fmt.Errorf("failed to save payment record: %w", err)
	}

	// 根据支付方式生成支付链接
	var paymentData, qrCode string
	var err error

	switch req.PaymentMethod {
	case PaymentMethodQR:
		// 生成扫码支付链接（预下单模式）
		paymentData, qrCode, err = s.createQRPayment(record)
		if err != nil {
			return nil, fmt.Errorf("failed to create QR payment: %w", err)
		}
	case PaymentMethodWebQR:
		// 生成网站支付二维码（网页支付+二维码模式）
		paymentData, qrCode, err = s.createWebQRPayment(record)
		if err != nil {
			return nil, fmt.Errorf("failed to create web QR payment: %w", err)
		}
	case PaymentMethodWap:
		// 生成手机网页支付链接
		paymentData, err = s.createWapPayment(record)
		if err != nil {
			return nil, fmt.Errorf("failed to create WAP payment: %w", err)
		}
	case PaymentMethodWeb:
		// 生成电脑网页支付链接
		paymentData, err = s.createWebPayment(record)
		if err != nil {
			return nil, fmt.Errorf("failed to create page payment: %w", err)
		}
	default:
		return nil, fmt.Errorf("unsupported payment method: %s", req.PaymentMethod)
	}

	// 更新支付记录中的支付数据
	if err := s.db.Model(record).Updates(map[string]interface{}{
		"payment_data": paymentData,
		"qr_code":      qrCode,
	}).Error; err != nil {
		return nil, fmt.Errorf("failed to update payment data: %w", err)
	}

	// 设置 Redis 缓存（如果可用）
	if s.cacheManager != nil {
		ctx := context.Background()
		if err := s.cacheManager.SetPaymentCache(ctx, record.OutTradeNo, record.Status, record.TradeNo, record.TimeoutExpress); err != nil {
			fmt.Printf("⚠️  设置支付缓存失败: %v\n", err)
		}
	}

	return &CreatePaymentResponse{
		TradeNo:     record.TradeNo,
		OutTradeNo:  record.OutTradeNo,
		PaymentData: paymentData,
		QRCode:      qrCode,
	}, nil
}

// QueryPayment 查询支付状态
func (s *Service) QueryPayment(req *QueryPaymentRequest) (*QueryPaymentResponse, error) {
	if req.TradeNo == "" && req.OutTradeNo == "" {
		return nil, fmt.Errorf("trade_no or out_trade_no is required")
	}

	// 从数据库查询支付记录
	var record PaymentRecord
	query := s.db
	if req.TradeNo != "" {
		query = query.Where("trade_no = ?", req.TradeNo)
	} else {
		query = query.Where("out_trade_no = ?", req.OutTradeNo)
	}

	if err := query.First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("payment record not found")
		}
		return nil, fmt.Errorf("failed to find payment record: %w", err)
	}

	return &QueryPaymentResponse{
		TradeNo:        record.TradeNo,
		OutTradeNo:     record.OutTradeNo,
		BuyerLogonID:   record.BuyerLogonID,
		TradeStatus:    record.Status,
		TotalAmount:    record.TotalAmount,
		ReceiptAmount:  record.ReceiptAmount,
		BuyerPayAmount: record.BuyerPayAmount,
		GmtCreate:      *record.GmtCreate,
		GmtPayment:     record.GmtPayment,
		Subject:        record.Subject,
		Body:           record.Body,
	}, nil
}

// Refund 申请退款
func (s *Service) Refund(req *RefundRequest) (*RefundResponse, error) {
	if req.TradeNo == "" && req.OutTradeNo == "" {
		return nil, fmt.Errorf("trade_no or out_trade_no is required")
	}

	// 查找支付记录
	var paymentRecord PaymentRecord
	query := s.db
	if req.TradeNo != "" {
		query = query.Where("trade_no = ?", req.TradeNo)
	} else {
		query = query.Where("out_trade_no = ?", req.OutTradeNo)
	}

	if err := query.First(&paymentRecord).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("payment record not found")
		}
		return nil, fmt.Errorf("failed to find payment record: %w", err)
	}

	// 检查支付状态
	if paymentRecord.Status != PaymentStatusTradeSuccess && paymentRecord.Status != PaymentStatusTradeFinished {
		return nil, fmt.Errorf("payment status %s cannot be refunded", paymentRecord.Status)
	}

	// 生成退款单号
	outRequestNo := req.OutRequestNo
	if outRequestNo == "" {
		outRequestNo = fmt.Sprintf("RF%d%s", time.Now().Unix(), uuid.New().String()[:8])
	}

	// 创建退款记录
	now := time.Now()
	refundRecord := &RefundRecord{
		ID:              uuid.New(),
		PaymentRecordID: paymentRecord.ID,
		OutRequestNo:    outRequestNo,
		RefundAmount:    req.RefundAmount,
		RefundReason:    req.RefundReason,
		RefundStatus:    RefundStatusSuccess,
		TradeNo:         paymentRecord.TradeNo,
		OutTradeNo:      paymentRecord.OutTradeNo,
		GmtRefundPay:    &now,
	}

	// 保存退款记录
	if err := s.db.Create(refundRecord).Error; err != nil {
		return nil, fmt.Errorf("failed to save refund record: %w", err)
	}

	return &RefundResponse{
		TradeNo:      paymentRecord.TradeNo,
		OutTradeNo:   paymentRecord.OutTradeNo,
		RefundFee:    req.RefundAmount,
		GmtRefundPay: now,
		OutRequestNo: outRequestNo,
	}, nil
}

// VerifyNotify 验证支付宝异步通知
func (s *Service) VerifyNotify(notifyData map[string]string) (bool, error) {
	// 使用支付宝SDK验证签名
	// 注意：这里需要将map转换为url.Values格式
	values := make(map[string][]string)
	for k, v := range notifyData {
		values[k] = []string{v}
	}

	// 使用SDK验证签名
	err := s.client.VerifySign(values)
	if err != nil {
		fmt.Printf("⚠️  异步通知签名验证失败: %v\n", err)
		return false, err
	}

	fmt.Printf("✅ 异步通知签名验证成功\n")
	return true, nil
}

// VerifyCallback 验证支付宝同步回调
func (s *Service) VerifyCallback(callbackData map[string]string) (bool, error) {
	// 使用支付宝SDK验证签名
	// 注意：这里需要将map转换为url.Values格式
	values := make(map[string][]string)
	for k, v := range callbackData {
		values[k] = []string{v}
	}

	// 使用SDK验证签名
	err := s.client.VerifySign(values)
	if err != nil {
		fmt.Printf("⚠️  同步回调签名验证失败: %v\n", err)
		return false, err
	}

	fmt.Printf("✅ 同步回调签名验证成功\n")
	return true, nil
}

// ProcessNotify 处理支付宝异步通知
func (s *Service) ProcessNotify(notifyData map[string]string) error {
	// 保存通知记录
	notifyRecord := &NotifyRecord{
		ID:          uuid.New(),
		NotifyID:    notifyData["notify_id"],
		NotifyType:  notifyData["notify_type"],
		NotifyTime:  notifyData["notify_time"],
		TradeNo:     notifyData["trade_no"],
		OutTradeNo:  notifyData["out_trade_no"],
		TradeStatus: notifyData["trade_status"],
		RawData:     fmt.Sprintf("%v", notifyData),
		IsVerified:  true,
		IsProcessed: true,
		ProcessedAt: func() *time.Time { t := time.Now(); return &t }(),
	}

	if err := s.db.Create(notifyRecord).Error; err != nil {
		return fmt.Errorf("failed to save notify record: %w", err)
	}

	// 更新支付记录
	if notifyData["trade_no"] != "" || notifyData["out_trade_no"] != "" {
		var record PaymentRecord
		query := s.db
		if notifyData["trade_no"] != "" {
			query = query.Where("trade_no = ?", notifyData["trade_no"])
		} else {
			query = query.Where("out_trade_no = ?", notifyData["out_trade_no"])
		}

		if err := query.First(&record).Error; err != nil {
			if err != gorm.ErrRecordNotFound {
				return fmt.Errorf("failed to find payment record: %w", err)
			}
			// 记录不存在，可能是先收到通知
			return nil
		}

		// 更新支付状态
		updates := map[string]interface{}{
			"status": notifyData["trade_status"],
		}

		if notifyData["trade_no"] != "" && record.TradeNo == "" {
			updates["trade_no"] = notifyData["trade_no"]
		}

		if notifyData["buyer_logon_id"] != "" {
			updates["buyer_logon_id"] = notifyData["buyer_logon_id"]
		}

		if notifyData["buyer_id"] != "" {
			updates["buyer_user_id"] = notifyData["buyer_id"]
		}

		if notifyData["buyer_pay_amount"] != "" {
			updates["buyer_pay_amount"] = notifyData["buyer_pay_amount"]
		}

		if notifyData["receipt_amount"] != "" {
			updates["receipt_amount"] = notifyData["receipt_amount"]
		}

		if notifyData["gmt_payment"] != "" {
			if t, err := time.Parse("2006-01-02 15:04:05", notifyData["gmt_payment"]); err == nil {
				updates["gmt_payment"] = t
			}
		}

		// 更新数据库
		if err := s.db.Model(&record).Updates(updates).Error; err != nil {
			return err
		}

		// 同时更新 Redis 缓存（如果可用）
		if s.cacheManager != nil {
			ctx := context.Background()
			newStatus := PaymentStatus(notifyData["trade_status"])
			tradeNo := notifyData["trade_no"]
			if tradeNo == "" {
				tradeNo = record.TradeNo
			}

			if err := s.cacheManager.UpdatePaymentCache(ctx, record.OutTradeNo, newStatus, tradeNo); err != nil {
				fmt.Printf("⚠️  更新支付缓存失败: %v\n", err)
			}
		}

		return nil
	}

	return nil
}

// QueryPaymentWithCache 带缓存的支付状态查询
func (s *Service) QueryPaymentWithCache(ctx context.Context, outTradeNo string) (*PaymentPollingResponse, error) {
	if s.cacheManager == nil {
		// 没有缓存管理器，直接查询数据库
		return s.queryPaymentFromDB(outTradeNo)
	}

	// 使用缓存管理器的统一查询方法
	return s.cacheManager.QueryPaymentWithCache(ctx, outTradeNo,
		func() (*PaymentRecord, error) {
			// 数据库查询函数
			var paymentRecord PaymentRecord
			if err := s.db.Where("out_trade_no = ?", outTradeNo).First(&paymentRecord).Error; err != nil {
				return nil, err
			}
			return &paymentRecord, nil
		},
		func() error {
			// 支付宝查询函数
			queryReq := &QueryPaymentRequest{
				OutTradeNo: outTradeNo,
			}
			_, err := s.QueryPayment(queryReq)
			return err
		},
	)
}

// queryPaymentFromDB 从数据库查询支付状态（无缓存）
func (s *Service) queryPaymentFromDB(outTradeNo string) (*PaymentPollingResponse, error) {
	var paymentRecord PaymentRecord
	if err := s.db.Where("out_trade_no = ?", outTradeNo).First(&paymentRecord).Error; err != nil {
		return nil, err
	}

	// 如果订单还在等待支付，尝试从支付宝查询最新状态
	if paymentRecord.Status == PaymentStatusWaitBuyerPay {
		queryReq := &QueryPaymentRequest{
			OutTradeNo: outTradeNo,
		}
		if _, err := s.QueryPayment(queryReq); err != nil {
			fmt.Printf("⚠️  支付宝查询失败: %v\n", err)
		} else {
			// 重新查询数据库获取最新状态
			s.db.Where("out_trade_no = ?", outTradeNo).First(&paymentRecord)
		}
	}

	return &PaymentPollingResponse{
		OutTradeNo: paymentRecord.OutTradeNo,
		IsPaid:     IsPaymentSuccess(paymentRecord.Status),
		Status:     string(paymentRecord.Status),
	}, nil
}

// createQRPayment 创建扫码支付（预下单模式）
// TODO: 将来可以替换 pointer_center 路由使用此方法而不是 createWebQRPayment
func (s *Service) createQRPayment(record *PaymentRecord) (paymentData, qrCode string, err error) {
	fmt.Printf("🔍 创建扫码支付调试信息:\n")
	fmt.Printf("   使用的AppID: %s\n", s.config.AppID)
	fmt.Printf("   订单号: %s\n", record.OutTradeNo)
	fmt.Printf("   金额: %s\n", record.TotalAmount)
	fmt.Printf("   沙箱模式: %t\n", s.config.Sandbox)

	// 构建支付宝扫码支付参数
	var p = alipay.TradePreCreate{}
	p.OutTradeNo = record.OutTradeNo
	p.TotalAmount = record.TotalAmount
	p.Subject = record.Subject
	p.Body = record.Body
	p.TimeoutExpress = fmt.Sprintf("%dm", record.TimeoutExpress)

	// 设置回调地址
	if s.config.NotifyURL != "" {
		p.NotifyURL = s.config.NotifyURL
	}

	// 设置回传参数
	if record.PassbackParams != "" {
		p.PassbackParams = record.PassbackParams
	}

	// 设置应用授权令牌（如果配置了）
	if s.config.AppAuthToken != "" {
		p.AppAuthToken = s.config.AppAuthToken
		fmt.Printf("   AppAuthToken: %s\n", s.config.AppAuthToken)
	}

	// 调用支付宝API
	fmt.Printf("🚀 调用支付宝API: TradePreCreate\n")
	fmt.Printf("📋 请求参数:\n")
	fmt.Printf("   OutTradeNo: %s\n", p.OutTradeNo)
	fmt.Printf("   TotalAmount: %s\n", p.TotalAmount)
	fmt.Printf("   Subject: %s\n", p.Subject)
	fmt.Printf("   TimeoutExpress: %s\n", p.TimeoutExpress)
	fmt.Printf("   NotifyURL: %s\n", p.NotifyURL)

	resp, err := s.client.TradePreCreate(context.Background(), p)
	if err != nil {
		fmt.Printf("❌ 支付宝API调用失败: %v\n", err)
		return "", "", fmt.Errorf("alipay API error: %w", err)
	}

	fmt.Printf("📋 支付宝API响应:\n")
	fmt.Printf("   Success: %t\n", resp.IsSuccess())
	fmt.Printf("   Code: %s\n", resp.Code)
	fmt.Printf("   Msg: %s\n", resp.Msg)
	fmt.Printf("   SubCode: %s\n", resp.SubCode)
	fmt.Printf("   SubMsg: %s\n", resp.SubMsg)

	if !resp.IsSuccess() {
		fmt.Printf("❌ 支付宝API业务失败: %s - %s (SubCode: %s, SubMsg: %s)\n",
			resp.Code, resp.Msg, resp.SubCode, resp.SubMsg)
		return "", "", fmt.Errorf("alipay API failed: %s - %s (SubCode: %s, SubMsg: %s)",
			resp.Code, resp.Msg, resp.SubCode, resp.SubMsg)
	}

	// 返回支付数据和二维码链接
	paymentData = resp.QRCode
	qrCode = resp.QRCode

	return paymentData, qrCode, nil
}

// createWapPayment 创建手机网页支付
func (s *Service) createWapPayment(record *PaymentRecord) (paymentData string, err error) {
	// 构建支付宝手机网页支付参数
	var p = alipay.TradeWapPay{}
	p.OutTradeNo = record.OutTradeNo
	p.TotalAmount = record.TotalAmount
	p.Subject = record.Subject
	p.Body = record.Body
	p.TimeoutExpress = fmt.Sprintf("%dm", record.TimeoutExpress)

	// 设置回调地址
	if s.config.NotifyURL != "" {
		p.NotifyURL = s.config.NotifyURL
	}
	if s.config.ReturnURL != "" {
		p.ReturnURL = s.config.ReturnURL
	}

	// 设置回传参数
	if record.PassbackParams != "" {
		p.PassbackParams = record.PassbackParams
	}

	// 设置应用授权令牌（如果配置了）
	if s.config.AppAuthToken != "" {
		p.AppAuthToken = s.config.AppAuthToken
	}

	// 生成支付链接
	paymentURL, err := s.client.TradeWapPay(p)
	if err != nil {
		return "", fmt.Errorf("failed to create WAP payment: %w", err)
	}

	return paymentURL.String(), nil
}

// createWebPayment 创建电脑网页支付
func (s *Service) createWebPayment(record *PaymentRecord) (paymentData string, err error) {
	// 构建支付宝电脑网页支付参数
	var p = alipay.TradePagePay{}
	p.OutTradeNo = record.OutTradeNo
	p.TotalAmount = record.TotalAmount
	p.Subject = record.Subject
	p.Body = record.Body
	p.TimeoutExpress = fmt.Sprintf("%dm", record.TimeoutExpress)

	// 设置回调地址
	if s.config.NotifyURL != "" {
		p.NotifyURL = s.config.NotifyURL
	}
	if s.config.ReturnURL != "" {
		p.ReturnURL = s.config.ReturnURL
	}

	// 设置回传参数
	if record.PassbackParams != "" {
		p.PassbackParams = record.PassbackParams
	}

	// 设置应用授权令牌（如果配置了）
	if s.config.AppAuthToken != "" {
		p.AppAuthToken = s.config.AppAuthToken
	}

	// 生成支付链接
	paymentURL, err := s.client.TradePagePay(p)
	if err != nil {
		return "", fmt.Errorf("failed to create web payment: %w", err)
	}

	return paymentURL.String(), nil
}

// createWebQRPayment 创建网站支付二维码（使用TradePagePay + QRPayMode）
func (s *Service) createWebQRPayment(record *PaymentRecord) (paymentData, qrCode string, err error) {
	fmt.Printf("🔍 创建网站支付二维码调试信息:\n")
	fmt.Printf("   使用的AppID: %s\n", s.config.AppID)
	fmt.Printf("   订单号: %s\n", record.OutTradeNo)
	fmt.Printf("   金额: %s\n", record.TotalAmount)
	fmt.Printf("   沙箱模式: %t\n", s.config.Sandbox)

	// 构建支付宝网站支付二维码参数
	var p = alipay.TradePagePay{}
	p.OutTradeNo = record.OutTradeNo
	p.TotalAmount = record.TotalAmount
	p.Subject = record.Subject
	p.Body = record.Body
	p.TimeoutExpress = fmt.Sprintf("%dm", record.TimeoutExpress)
	p.ProductCode = "FAST_INSTANT_TRADE_PAY"

	// 设置二维码支付模式
	// 模式4: 订单码-可定义宽度的嵌入式二维码，商户可根据需要设定二维码的大小
	p.QRPayMode = "4"

	// 设置回调地址
	if s.config.NotifyURL != "" {
		p.NotifyURL = s.config.NotifyURL
	}
	if s.config.ReturnURL != "" {
		p.ReturnURL = s.config.ReturnURL
	}

	// 设置回传参数
	if record.PassbackParams != "" {
		p.PassbackParams = record.PassbackParams
	}

	// 设置应用授权令牌（如果配置了）
	if s.config.AppAuthToken != "" {
		p.AppAuthToken = s.config.AppAuthToken
		fmt.Printf("   AppAuthToken: %s\n", s.config.AppAuthToken)
	}

	// 调用支付宝API
	fmt.Printf("🚀 调用支付宝API: TradePagePay (QR Mode)\n")
	fmt.Printf("📋 请求参数:\n")
	fmt.Printf("   OutTradeNo: %s\n", p.OutTradeNo)
	fmt.Printf("   TotalAmount: %s\n", p.TotalAmount)
	fmt.Printf("   Subject: %s\n", p.Subject)
	fmt.Printf("   TimeoutExpress: %s\n", p.TimeoutExpress)
	fmt.Printf("   QRPayMode: %s\n", p.QRPayMode)
	fmt.Printf("   NotifyURL: %s\n", p.NotifyURL)

	paymentURL, err := s.client.TradePagePay(p)
	if err != nil {
		fmt.Printf("❌ 支付宝API调用失败: %v\n", err)
		return "", "", fmt.Errorf("alipay API error: %w", err)
	}

	// 网站支付二维码返回的是支付链接，这个链接可以生成二维码
	paymentData = paymentURL.String()
	qrCode = paymentURL.String()

	fmt.Printf("✅ 网站支付二维码创建成功\n")
	fmt.Printf("   支付链接: %s\n", paymentData)

	return paymentData, qrCode, nil
}
