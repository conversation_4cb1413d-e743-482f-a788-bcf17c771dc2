package alipay

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
)

// CreateQRCodeRequest 创建二维码请求
type CreateQRCodeRequest struct {
	Amount     string `json:"amount" binding:"required"`  // 支付金额
	Subject    string `json:"subject" binding:"required"` // 订单标题
	Body       string `json:"body"`                       // 订单描述
	UserID     string `json:"user_id" binding:"required"` // 用户ID
	OrderID    string `json:"order_id"`                   // 业务订单ID（可选）
	ExpireTime int    `json:"expire_time"`                // 过期时间（分钟，可选）
	ExtraData  string `json:"extra_data"`                 // 额外数据（可选）
}

// CreateQRCodeResponse 创建二维码响应
type CreateQRCodeResponse struct {
	OutTradeNo string `json:"out_trade_no"` // 商户订单号
	QRCode     string `json:"qr_code"`      // 二维码链接
	ExpireTime string `json:"expire_time"`  // 过期时间
}

// PaymentStatusResponse 支付状态响应（详细信息）
type PaymentStatusResponse struct {
	OutTradeNo string `json:"out_trade_no"` // 商户订单号
	TradeNo    string `json:"trade_no"`     // 支付宝交易号
	Status     string `json:"status"`       // 支付状态
	Amount     string `json:"amount"`       // 支付金额
	PaidAmount string `json:"paid_amount"`  // 实付金额
	PayTime    string `json:"pay_time"`     // 支付时间
	UserID     string `json:"user_id"`      // 用户ID
	OrderID    string `json:"order_id"`     // 业务订单ID
	ExtraData  string `json:"extra_data"`   // 额外数据
}

// PaymentPollingResponse 支付状态轮询响应（简化版）
type PaymentPollingResponse struct {
	OutTradeNo string `json:"out_trade_no"` // 商户订单号
	IsPaid     bool   `json:"is_paid"`      // 是否已支付
	Status     string `json:"status"`       // 支付状态
}

// GenerateOutTradeNo 生成商户订单号
func GenerateOutTradeNo(prefix string) string {
	return fmt.Sprintf("%s_%d_%s", prefix, time.Now().Unix(), uuid.New().String()[:8])
}

// BuildPassbackParams 构建回传参数
func BuildPassbackParams(userID, orderID, extraData string) string {
	params := fmt.Sprintf("user_id=%s", userID)
	if orderID != "" {
		params += fmt.Sprintf("&order_id=%s", orderID)
	}
	if extraData != "" {
		params += fmt.Sprintf("&extra_data=%s", extraData)
	}
	return params
}

// ParsePassbackParams 解析回传参数
func ParsePassbackParams(params string) (userID, orderID, extraData string) {
	if params == "" {
		return "", "", ""
	}

	userID = ExtractParam(params, "user_id")
	orderID = ExtractParam(params, "order_id")
	extraData = ExtractParam(params, "extra_data")
	return
}

// ExtractParam 从参数字符串中提取指定参数的值
func ExtractParam(params, key string) string {
	// 简单的参数解析，格式：key1=value1&key2=value2
	if params == "" {
		return ""
	}

	// 确保参数字符串以&开头，方便统一处理
	if !strings.HasPrefix(params, "&") {
		params = "&" + params
	}

	// 查找参数
	searchKey := "&" + key + "="
	startIdx := strings.Index(params, searchKey)
	if startIdx == -1 {
		return ""
	}

	// 计算值的起始位置
	valueStart := startIdx + len(searchKey)
	if valueStart >= len(params) {
		return ""
	}

	// 查找下一个&的位置，确定值的结束位置
	endIdx := strings.Index(params[valueStart:], "&")
	if endIdx == -1 {
		// 如果没有找到下一个&，说明这是最后一个参数
		return params[valueStart:]
	}

	return params[valueStart : valueStart+endIdx]
}

// ValidateUserID 验证用户ID格式
func ValidateUserID(userID string) (uuid.UUID, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return uuid.Nil, fmt.Errorf("用户ID格式错误: %w", err)
	}
	return userUUID, nil
}

// FormatExpireTime 格式化过期时间
func FormatExpireTime(expireMinutes int) string {
	return time.Now().Add(time.Duration(expireMinutes) * time.Minute).Format("2006-01-02 15:04:05")
}

// FormatPayTime 格式化支付时间
func FormatPayTime(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// IsPaymentSuccess 检查支付状态是否为成功
func IsPaymentSuccess(status PaymentStatus) bool {
	return status == PaymentStatusTradeSuccess || status == PaymentStatusTradeFinished
}

// IsPaymentPending 检查支付状态是否为等待中
func IsPaymentPending(status PaymentStatus) bool {
	return status == PaymentStatusWaitBuyerPay
}

// IsPaymentClosed 检查支付状态是否为已关闭
func IsPaymentClosed(status PaymentStatus) bool {
	return status == PaymentStatusTradeClosed
}

// ParseFormToMap 将表单数据转换为map
func ParseFormToMap(formData map[string][]string) map[string]string {
	result := make(map[string]string)
	for key, values := range formData {
		if len(values) > 0 {
			result[key] = values[0]
		}
	}
	return result
}

// ValidateOutTradeNo 验证商户订单号
func ValidateOutTradeNo(outTradeNo string) error {
	if outTradeNo == "" {
		return fmt.Errorf("商户订单号不能为空")
	}
	return nil
}

// GetDefaultExpireTime 获取默认过期时间
func GetDefaultExpireTime() int {
	return 10 // 默认10分钟
}
