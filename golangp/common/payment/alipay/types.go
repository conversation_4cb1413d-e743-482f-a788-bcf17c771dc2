package alipay

import (
	"time"
)

// PaymentMethod 支付方式
type PaymentMethod string

const (
	// PaymentMethodWeb 网页支付
	PaymentMethodWeb PaymentMethod = "web"
	// PaymentMethodWap 手机网页支付
	PaymentMethodWap PaymentMethod = "wap"
	// PaymentMethodApp APP支付
	PaymentMethodApp PaymentMethod = "app"
	// PaymentMethodQR 扫码支付（预下单模式）
	PaymentMethodQR PaymentMethod = "qr"
	// PaymentMethodWebQR 网站支付二维码（网页支付+二维码模式）
	PaymentMethodWebQR PaymentMethod = "web_qr"
)

// PaymentStatus 支付状态
type PaymentStatus string

const (
	// PaymentStatusWaitBuyerPay 交易创建，等待买家付款
	PaymentStatusWaitBuyerPay PaymentStatus = "WAIT_BUYER_PAY"
	// PaymentStatusTradeClosed 未付款交易超时关闭，或支付完成后全额退款
	PaymentStatusTradeClosed PaymentStatus = "TRADE_CLOSED"
	// PaymentStatusTradeSuccess 交易支付成功
	PaymentStatusTradeSuccess PaymentStatus = "TRADE_SUCCESS"
	// PaymentStatusTradeFinished 交易结束，不可退款
	PaymentStatusTradeFinished PaymentStatus = "TRADE_FINISHED"
)

// RefundStatus 退款状态
type RefundStatus string

const (
	// RefundStatusSuccess 退款成功
	RefundStatusSuccess RefundStatus = "REFUND_SUCCESS"
	// RefundStatusFail 退款失败
	RefundStatusFail RefundStatus = "REFUND_FAIL"
)

// CreatePaymentRequest 创建支付请求
type CreatePaymentRequest struct {
	// 商户订单号，64个字符以内、只能包含字母、数字、下划线；需保证在商户端不重复
	OutTradeNo string `json:"out_trade_no" binding:"required"`

	// 订单总金额，单位为元，精确到小数点后两位
	TotalAmount string `json:"total_amount" binding:"required"`

	// 订单标题
	Subject string `json:"subject" binding:"required"`

	// 订单描述
	Body string `json:"body,omitempty"`

	// 支付方式
	PaymentMethod PaymentMethod `json:"payment_method" binding:"required"`

	// 超时时间（分钟）
	TimeoutExpress int `json:"timeout_express,omitempty"`

	// 商品详情
	GoodsDetail []GoodsDetail `json:"goods_detail,omitempty"`

	// 扩展参数
	PassbackParams string `json:"passback_params,omitempty"`
}

// GoodsDetail 商品详情
type GoodsDetail struct {
	// 商品的编号
	GoodsID string `json:"goods_id"`

	// 商品名称
	GoodsName string `json:"goods_name"`

	// 商品数量
	Quantity int `json:"quantity"`

	// 商品单价，单位为元
	Price string `json:"price"`

	// 商品类目
	GoodsCategory string `json:"goods_category,omitempty"`

	// 商品描述信息
	Body string `json:"body,omitempty"`
}

// CreatePaymentResponse 创建支付响应
type CreatePaymentResponse struct {
	// 支付宝交易号
	TradeNo string `json:"trade_no"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no"`

	// 支付链接或支付字符串（根据支付方式不同）
	PaymentData string `json:"payment_data"`

	// 二维码链接（扫码支付时）
	QRCode string `json:"qr_code,omitempty"`
}

// QueryPaymentRequest 查询支付请求
type QueryPaymentRequest struct {
	// 支付宝交易号
	TradeNo string `json:"trade_no,omitempty"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no,omitempty"`
}

// QueryPaymentResponse 查询支付响应
type QueryPaymentResponse struct {
	// 支付宝交易号
	TradeNo string `json:"trade_no"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no"`

	// 买家支付宝账号
	BuyerLogonID string `json:"buyer_logon_id"`

	// 交易状态
	TradeStatus PaymentStatus `json:"trade_status"`

	// 交易金额
	TotalAmount string `json:"total_amount"`

	// 实收金额
	ReceiptAmount string `json:"receipt_amount"`

	// 买家实付金额
	BuyerPayAmount string `json:"buyer_pay_amount"`

	// 交易创建时间
	GmtCreate time.Time `json:"gmt_create"`

	// 交易付款时间
	GmtPayment *time.Time `json:"gmt_payment,omitempty"`

	// 订单标题
	Subject string `json:"subject"`

	// 订单描述
	Body string `json:"body"`
}

// RefundRequest 退款请求
type RefundRequest struct {
	// 支付宝交易号
	TradeNo string `json:"trade_no,omitempty"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no,omitempty"`

	// 退款金额
	RefundAmount string `json:"refund_amount" binding:"required"`

	// 退款原因
	RefundReason string `json:"refund_reason,omitempty"`

	// 商户退款单号
	OutRequestNo string `json:"out_request_no,omitempty"`
}

// RefundResponse 退款响应
type RefundResponse struct {
	// 支付宝交易号
	TradeNo string `json:"trade_no"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no"`

	// 买家支付宝用户号
	BuyerUserID string `json:"buyer_user_id"`

	// 退款金额
	RefundFee string `json:"refund_fee"`

	// 退款时间
	GmtRefundPay time.Time `json:"gmt_refund_pay"`

	// 商户退款单号
	OutRequestNo string `json:"out_request_no"`
}

// NotifyRequest 异步通知请求
type NotifyRequest struct {
	// 通知时间
	NotifyTime string `json:"notify_time"`

	// 通知类型
	NotifyType string `json:"notify_type"`

	// 通知校验ID
	NotifyID string `json:"notify_id"`

	// 应用ID
	AppID string `json:"app_id"`

	// 编码格式
	Charset string `json:"charset"`

	// 版本
	Version string `json:"version"`

	// 签名类型
	SignType string `json:"sign_type"`

	// 签名
	Sign string `json:"sign"`

	// 支付宝交易号
	TradeNo string `json:"trade_no"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no"`

	// 商户业务号
	OutBizNo string `json:"out_biz_no"`

	// 买家支付宝用户号
	BuyerID string `json:"buyer_id"`

	// 买家支付宝账号
	BuyerLogonID string `json:"buyer_logon_id"`

	// 卖家支付宝用户号
	SellerID string `json:"seller_id"`

	// 卖家支付宝账号
	SellerEmail string `json:"seller_email"`

	// 交易状态
	TradeStatus string `json:"trade_status"`

	// 订单金额
	TotalAmount string `json:"total_amount"`

	// 实收金额
	ReceiptAmount string `json:"receipt_amount"`

	// 开票金额
	InvoiceAmount string `json:"invoice_amount"`

	// 买家付款金额
	BuyerPayAmount string `json:"buyer_pay_amount"`

	// 交易创建时间
	GmtCreate string `json:"gmt_create"`

	// 交易付款时间
	GmtPayment string `json:"gmt_payment"`

	// 交易关闭时间
	GmtClose string `json:"gmt_close"`

	// 订单标题
	Subject string `json:"subject"`

	// 商品描述
	Body string `json:"body"`

	// 回传参数
	PassbackParams string `json:"passback_params"`
}
