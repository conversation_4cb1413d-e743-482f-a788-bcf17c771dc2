package alipay

import (
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
)

// Example 支付宝支付服务使用示例
type Example struct {
	service *Service
}

// NewExample 创建示例实例
func NewExample(config *Config, db *gorm.DB) (*Example, error) {
	service, err := NewService(config, db)
	if err != nil {
		return nil, err
	}
	
	return &Example{
		service: service,
	}, nil
}

// CreateWebPaymentExample 创建网页支付示例
func (e *Example) CreateWebPaymentExample() {
	fmt.Println("=== 创建网页支付示例 ===")
	
	req := &CreatePaymentRequest{
		OutTradeNo:    fmt.Sprintf("WEB_%d", time.Now().Unix()),
		TotalAmount:   "0.01",
		Subject:       "测试网页支付",
		Body:          "这是一个测试网页支付订单",
		PaymentMethod: PaymentMethodWeb,
		TimeoutExpress: 30,
		GoodsDetail: []GoodsDetail{
			{
				GoodsID:       "goods_web_001",
				GoodsName:     "测试商品",
				Quantity:      1,
				Price:         "0.01",
				GoodsCategory: "digital",
				Body:          "数字商品",
			},
		},
		PassbackParams: "custom_data_web",
	}

	resp, err := e.service.CreatePayment(req)
	if err != nil {
		log.Printf("创建网页支付失败: %v", err)
		return
	}

	fmt.Printf("网页支付创建成功:\n")
	fmt.Printf("  订单号: %s\n", resp.OutTradeNo)
	fmt.Printf("  支付宝交易号: %s\n", resp.TradeNo)
	fmt.Printf("  支付链接: %s\n", resp.PaymentData)
	fmt.Println()
}

// CreateAppPaymentExample 创建APP支付示例
func (e *Example) CreateAppPaymentExample() {
	fmt.Println("=== 创建APP支付示例 ===")
	
	req := &CreatePaymentRequest{
		OutTradeNo:    fmt.Sprintf("APP_%d", time.Now().Unix()),
		TotalAmount:   "9.99",
		Subject:       "测试APP支付",
		Body:          "这是一个测试APP支付订单",
		PaymentMethod: PaymentMethodApp,
		TimeoutExpress: 15,
		GoodsDetail: []GoodsDetail{
			{
				GoodsID:       "goods_app_001",
				GoodsName:     "移动应用内商品",
				Quantity:      1,
				Price:         "9.99",
				GoodsCategory: "virtual",
				Body:          "虚拟商品",
			},
		},
	}

	resp, err := e.service.CreatePayment(req)
	if err != nil {
		log.Printf("创建APP支付失败: %v", err)
		return
	}

	fmt.Printf("APP支付创建成功:\n")
	fmt.Printf("  订单号: %s\n", resp.OutTradeNo)
	fmt.Printf("  支付字符串: %s\n", resp.PaymentData)
	fmt.Println()
}

// CreateQRPaymentExample 创建扫码支付示例
func (e *Example) CreateQRPaymentExample() {
	fmt.Println("=== 创建扫码支付示例 ===")
	
	req := &CreatePaymentRequest{
		OutTradeNo:    fmt.Sprintf("QR_%d", time.Now().Unix()),
		TotalAmount:   "19.99",
		Subject:       "测试扫码支付",
		Body:          "这是一个测试扫码支付订单",
		PaymentMethod: PaymentMethodQR,
		TimeoutExpress: 10,
		GoodsDetail: []GoodsDetail{
			{
				GoodsID:       "goods_qr_001",
				GoodsName:     "线下商品",
				Quantity:      2,
				Price:         "9.99",
				GoodsCategory: "physical",
				Body:          "实体商品",
			},
		},
	}

	resp, err := e.service.CreatePayment(req)
	if err != nil {
		log.Printf("创建扫码支付失败: %v", err)
		return
	}

	fmt.Printf("扫码支付创建成功:\n")
	fmt.Printf("  订单号: %s\n", resp.OutTradeNo)
	fmt.Printf("  二维码链接: %s\n", resp.QRCode)
	fmt.Println()
}

// QueryPaymentExample 查询支付状态示例
func (e *Example) QueryPaymentExample(outTradeNo string) {
	fmt.Println("=== 查询支付状态示例 ===")
	
	req := &QueryPaymentRequest{
		OutTradeNo: outTradeNo,
	}

	resp, err := e.service.QueryPayment(req)
	if err != nil {
		log.Printf("查询支付状态失败: %v", err)
		return
	}

	fmt.Printf("支付状态查询结果:\n")
	fmt.Printf("  订单号: %s\n", resp.OutTradeNo)
	fmt.Printf("  支付宝交易号: %s\n", resp.TradeNo)
	fmt.Printf("  支付状态: %s\n", resp.TradeStatus)
	fmt.Printf("  订单金额: %s\n", resp.TotalAmount)
	fmt.Printf("  实收金额: %s\n", resp.ReceiptAmount)
	fmt.Printf("  买家账号: %s\n", resp.BuyerLogonID)
	fmt.Printf("  创建时间: %s\n", resp.GmtCreate.Format("2006-01-02 15:04:05"))
	if resp.GmtPayment != nil {
		fmt.Printf("  支付时间: %s\n", resp.GmtPayment.Format("2006-01-02 15:04:05"))
	}
	fmt.Println()
}

// RefundExample 退款示例
func (e *Example) RefundExample(outTradeNo string) {
	fmt.Println("=== 申请退款示例 ===")
	
	req := &RefundRequest{
		OutTradeNo:   outTradeNo,
		RefundAmount: "0.01",
		RefundReason: "用户申请退款",
		OutRequestNo: fmt.Sprintf("REFUND_%d", time.Now().Unix()),
	}

	resp, err := e.service.Refund(req)
	if err != nil {
		log.Printf("申请退款失败: %v", err)
		return
	}

	fmt.Printf("退款申请成功:\n")
	fmt.Printf("  原订单号: %s\n", resp.OutTradeNo)
	fmt.Printf("  退款单号: %s\n", resp.OutRequestNo)
	fmt.Printf("  退款金额: %s\n", resp.RefundFee)
	fmt.Printf("  退款时间: %s\n", resp.GmtRefundPay.Format("2006-01-02 15:04:05"))
	fmt.Println()
}

// NotifyExample 异步通知处理示例
func (e *Example) NotifyExample() {
	fmt.Println("=== 异步通知处理示例 ===")
	
	// 模拟支付宝异步通知数据
	notifyData := map[string]string{
		"notify_time":      time.Now().Format("2006-01-02 15:04:05"),
		"notify_type":      "trade_status_sync",
		"notify_id":        fmt.Sprintf("notify_%d", time.Now().Unix()),
		"app_id":           "2021000000000000",
		"charset":          "utf-8",
		"version":          "1.0",
		"sign_type":        "RSA2",
		"sign":             "mock_signature",
		"trade_no":         "2024010122001400000000000001",
		"out_trade_no":     "WEB_1704067200",
		"trade_status":     "TRADE_SUCCESS",
		"total_amount":     "0.01",
		"receipt_amount":   "0.01",
		"buyer_pay_amount": "0.01",
		"buyer_id":         "2088000000000001",
		"buyer_logon_id":   "test***@example.com",
		"seller_id":        "2088000000000002",
		"seller_email":     "<EMAIL>",
		"gmt_create":       "2024-01-01 12:00:00",
		"gmt_payment":      "2024-01-01 12:01:00",
		"subject":          "测试网页支付",
		"body":             "这是一个测试网页支付订单",
	}

	// 验证通知
	isValid, err := e.service.VerifyNotify(notifyData)
	if err != nil {
		log.Printf("验证通知失败: %v", err)
		return
	}

	if !isValid {
		log.Printf("通知验证失败")
		return
	}

	// 处理通知
	err = e.service.ProcessNotify(notifyData)
	if err != nil {
		log.Printf("处理通知失败: %v", err)
		return
	}

	fmt.Printf("异步通知处理成功:\n")
	fmt.Printf("  通知ID: %s\n", notifyData["notify_id"])
	fmt.Printf("  订单号: %s\n", notifyData["out_trade_no"])
	fmt.Printf("  支付宝交易号: %s\n", notifyData["trade_no"])
	fmt.Printf("  交易状态: %s\n", notifyData["trade_status"])
	fmt.Println()
}

// RunAllExamples 运行所有示例
func (e *Example) RunAllExamples() {
	fmt.Println("支付宝支付服务示例演示")
	fmt.Println("========================")
	
	// 创建支付示例
	e.CreateWebPaymentExample()
	e.CreateAppPaymentExample()
	e.CreateQRPaymentExample()
	
	// 查询支付示例（使用第一个创建的订单）
	webOrderNo := fmt.Sprintf("WEB_%d", time.Now().Unix()-10)
	e.QueryPaymentExample(webOrderNo)
	
	// 退款示例
	e.RefundExample(webOrderNo)
	
	// 异步通知示例
	e.NotifyExample()
	
	fmt.Println("所有示例演示完成！")
}
