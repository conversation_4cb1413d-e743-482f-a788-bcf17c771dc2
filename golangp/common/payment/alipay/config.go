package alipay

import (
	"fmt"
	"time"
)

// Config holds all configuration for Alipay payment service
type Config struct {
	// 应用ID
	AppID string `mapstructure:"ALIPAY_APP_ID"`

	// 应用私钥
	PrivateKey string `mapstructure:"ALIPAY_PRIVATE_KEY"`

	// 支付宝公钥
	PublicKey string `mapstructure:"ALIPAY_PUBLIC_KEY"`

	// 支付宝根证书
	RootCert string `mapstructure:"ALIPAY_ROOT_CERT"`

	// 应用公钥证书
	AppCert string `mapstructure:"ALIPAY_APP_CERT"`

	// 支付宝公钥证书
	AlipayCert string `mapstructure:"ALIPAY_CERT"`

	// 证书文件路径配置
	AppCertPath    string `mapstructure:"ALIPAY_APP_CERT_PATH"`
	AlipayCertPath string `mapstructure:"ALIPAY_ALIPAY_CERT_PATH"`
	RootCertPath   string `mapstructure:"ALIPAY_ROOT_CERT_PATH"`

	// 是否使用证书模式
	UseCertMode bool `mapstructure:"ALIPAY_USE_CERT_MODE"`

	// 应用授权令牌（第三方应用授权时需要）
	AppAuthToken string `mapstructure:"ALIPAY_APP_AUTH_TOKEN"`

	// 网关地址 (沙箱: https://openapi.alipaydev.com/gateway.do, 正式: https://openapi.alipay.com/gateway.do)
	GatewayURL string `mapstructure:"ALIPAY_GATEWAY_URL"`

	// 签名类型 (RSA2)
	SignType string `mapstructure:"ALIPAY_SIGN_TYPE"`

	// 字符编码 (utf-8)
	Charset string `mapstructure:"ALIPAY_CHARSET"`

	// 数据格式 (JSON)
	Format string `mapstructure:"ALIPAY_FORMAT"`

	// 版本号 (1.0)
	Version string `mapstructure:"ALIPAY_VERSION"`

	// 同步回调地址
	ReturnURL string `mapstructure:"ALIPAY_RETURN_URL"`

	// 异步回调地址
	NotifyURL string `mapstructure:"ALIPAY_NOTIFY_URL"`

	// 超时时间 (分钟)
	TimeoutExpress int `mapstructure:"ALIPAY_TIMEOUT_EXPRESS"`

	// 是否启用沙箱模式
	Sandbox bool `mapstructure:"ALIPAY_SANDBOX"`

	// 内容加密密钥（可选，用于敏感信息加密）
	EncryptKey string `mapstructure:"ALIPAY_ENCRYPT_KEY"`
}

// Validate validates the Alipay configuration
func (c *Config) Validate() error {
	if c.AppID == "" {
		return fmt.Errorf("alipay app_id is required")
	}

	if c.PrivateKey == "" {
		return fmt.Errorf("alipay private_key is required")
	}

	// if c.GatewayURL == "" {
	// 	return fmt.Errorf("alipay gateway_url is required")
	// }

	// 验证证书模式配置
	if c.UseCertMode {
		if c.AppCertPath == "" {
			return fmt.Errorf("alipay app_cert_path is required for certificate mode")
		}
		if c.AlipayCertPath == "" {
			return fmt.Errorf("alipay alipay_cert_path is required for certificate mode")
		}
		if c.RootCertPath == "" {
			return fmt.Errorf("alipay root_cert_path is required for certificate mode")
		}
		return nil
	}

	// 验证密钥模式配置
	if c.PublicKey == "" {
		return fmt.Errorf("alipay public_key is required for key mode")
	}

	return nil
}

// SetDefaults sets default values for optional configuration fields
func (c *Config) SetDefaults() {
	if c.SignType == "" {
		c.SignType = "RSA2"
	}

	if c.Charset == "" {
		c.Charset = "utf-8"
	}

	if c.Format == "" {
		c.Format = "JSON"
	}

	if c.Version == "" {
		c.Version = "1.0"
	}

	if c.TimeoutExpress == 0 {
		c.TimeoutExpress = 30 // 默认30分钟
	}

	// 根据沙箱模式设置默认网关地址
	// if c.GatewayURL == "" {
	// 	if c.Sandbox {
	// 		c.GatewayURL = "https://openapi-sandbox.dl.alipaydev.com/gateway.do"
	// 	} else {
	// 		c.GatewayURL = "https://openapi.alipay.com/gateway.do"
	// 	}
	// }
}

// GetTimeoutDuration returns timeout duration
func (c *Config) GetTimeoutDuration() time.Duration {
	return time.Duration(c.TimeoutExpress) * time.Minute
}
