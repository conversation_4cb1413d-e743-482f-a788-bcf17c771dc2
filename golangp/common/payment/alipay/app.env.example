# 支付宝支付配置示例
# 复制此文件为 app.env 并填入真实的配置值

# ================================
# 支付宝基础配置
# ================================

# 支付宝应用ID（必填）
# 在支付宝开放平台创建应用后获得
ALIPAY_APP_ID=2021000000000000

# 应用私钥（必填）
# 使用支付宝密钥生成工具生成的应用私钥
# 注意：这里应该是完整的私钥字符串，包含-----BEGIN PRIVATE KEY-----等标识
ALIPAY_PRIVATE_KEY=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...

# 支付宝公钥（必填）
# 从支付宝开放平台获取的支付宝公钥
ALIPAY_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...

# ================================
# 证书模式配置（可选）
# ================================

# 如果使用证书模式，需要配置以下三个证书
# 应用公钥证书
ALIPAY_APP_CERT=

# 支付宝公钥证书
ALIPAY_CERT=

# 支付宝根证书
ALIPAY_ROOT_CERT=

# ================================
# 网关和基础参数配置
# ================================

# 支付宝网关地址
# 沙箱环境: https://openapi.alipaydev.com/gateway.do
# 正式环境: https://openapi.alipay.com/gateway.do
ALIPAY_GATEWAY_URL=https://openapi.alipaydev.com/gateway.do

# 签名类型（推荐使用RSA2）
ALIPAY_SIGN_TYPE=RSA2

# 字符编码
ALIPAY_CHARSET=utf-8

# 数据格式
ALIPAY_FORMAT=JSON

# 接口版本
ALIPAY_VERSION=1.0

# ================================
# 回调地址配置
# ================================

# 同步回调地址（用户支付完成后跳转的页面）
# 注意：必须是完整的URL，支持HTTPS
ALIPAY_RETURN_URL=https://your-domain.com/alipay/return

# 异步回调地址（支付宝服务器通知地址）
# 注意：必须是可以被支付宝服务器访问的URL，支持HTTPS
ALIPAY_NOTIFY_URL=https://your-domain.com/api/alipay/notify

# ================================
# 业务配置
# ================================

# 订单超时时间（分钟）
# 建议设置为5-120分钟之间
ALIPAY_TIMEOUT_EXPRESS=30

# 是否启用沙箱模式
# 开发测试时设置为true，生产环境设置为false
ALIPAY_SANDBOX=true

# ================================
# 其他应用配置示例
# ================================

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME=your_app_db
DB_SSL_MODE=disable

# 服务器配置
SERVER_PORT=8080
ENVIRONMENT=development

# 日志配置
LOG_LEVEL=info
LOG_PATH=/tmp/logs

# ================================
# 安全注意事项
# ================================

# 1. 私钥和证书信息绝对不能泄露，不要提交到代码仓库
# 2. 生产环境必须使用HTTPS协议
# 3. 回调地址必须能被支付宝服务器访问
# 4. 建议定期更换密钥
# 5. 沙箱环境仅用于开发测试，不能用于生产

# ================================
# 支付宝沙箱账号信息（仅供测试）
# ================================

# 沙箱买家账号: <EMAIL>
# 沙箱买家密码: 111111
# 沙箱买家支付密码: 111111

# 注意：以上是支付宝提供的公共测试账号，仅用于开发测试
