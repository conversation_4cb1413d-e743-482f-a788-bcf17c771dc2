
package alipay

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PaymentRecord 支付记录模型
type PaymentRecord struct {
	ID          uuid.UUID     `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt   time.Time     `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time     `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 基本信息
	OutTradeNo    string        `gorm:"type:varchar(64);uniqueIndex;not null" json:"out_trade_no"`    // 商户订单号
	TradeNo       string        `gorm:"type:varchar(64);index" json:"trade_no"`                       // 支付宝交易号
	Subject       string        `gorm:"type:varchar(256);not null" json:"subject"`                    // 订单标题
	Body          string        `gorm:"type:text" json:"body"`                                        // 订单描述
	TotalAmount   string        `gorm:"type:decimal(10,2);not null" json:"total_amount"`             // 订单总金额
	PaymentMethod PaymentMethod `gorm:"type:varchar(20);not null" json:"payment_method"`             // 支付方式

	// 支付状态
	Status        PaymentStatus `gorm:"type:varchar(50);not null;default:'WAIT_BUYER_PAY'" json:"status"`
	PaymentData   string        `gorm:"type:text" json:"payment_data"`                               // 支付数据（支付链接或支付字符串）
	QRCode        string        `gorm:"type:text" json:"qr_code"`                                    // 二维码链接

	// 买家信息
	BuyerLogonID   string `gorm:"type:varchar(100)" json:"buyer_logon_id"`   // 买家支付宝账号
	BuyerUserID    string `gorm:"type:varchar(32)" json:"buyer_user_id"`     // 买家支付宝用户号
	BuyerPayAmount string `gorm:"type:decimal(10,2)" json:"buyer_pay_amount"` // 买家实付金额

	// 收款信息
	ReceiptAmount string `gorm:"type:decimal(10,2)" json:"receipt_amount"` // 实收金额
	InvoiceAmount string `gorm:"type:decimal(10,2)" json:"invoice_amount"` // 开票金额

	// 时间信息
	TimeoutExpress int        `gorm:"not null;default:30" json:"timeout_express"`     // 超时时间（分钟）
	GmtCreate      *time.Time `gorm:"index" json:"gmt_create"`                        // 交易创建时间
	GmtPayment     *time.Time `gorm:"index" json:"gmt_payment"`                       // 交易付款时间
	GmtClose       *time.Time `gorm:"index" json:"gmt_close"`                         // 交易关闭时间

	// 扩展信息
	PassbackParams string `gorm:"type:text" json:"passback_params"` // 回传参数
	NotifyData     string `gorm:"type:text" json:"notify_data"`     // 异步通知数据

	// 关联信息
	UserID   *uuid.UUID `gorm:"type:uuid;index" json:"user_id"`   // 关联用户ID
	OrderID  *uuid.UUID `gorm:"type:uuid;index" json:"order_id"`  // 关联订单ID
	
	// 索引
	_ struct{} `gorm:"index:idx_payment_status"`
	_ struct{} `gorm:"index:idx_payment_created_at"`
	_ struct{} `gorm:"index:idx_payment_user_id"`
}

// TableName returns the table name for PaymentRecord
func (PaymentRecord) TableName() string {
	return "payment_records"
}

// BeforeCreate sets the ID before creating
func (p *PaymentRecord) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}

// RefundRecord 退款记录模型
type RefundRecord struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联支付记录
	PaymentRecordID uuid.UUID `gorm:"type:uuid;not null;index" json:"payment_record_id"`
	PaymentRecord   PaymentRecord `gorm:"foreignKey:PaymentRecordID" json:"payment_record"`

	// 退款信息
	OutRequestNo  string       `gorm:"type:varchar(64);uniqueIndex;not null" json:"out_request_no"` // 商户退款单号
	RefundAmount  string       `gorm:"type:decimal(10,2);not null" json:"refund_amount"`            // 退款金额
	RefundReason  string       `gorm:"type:varchar(256)" json:"refund_reason"`                      // 退款原因
	RefundStatus  RefundStatus `gorm:"type:varchar(50);not null" json:"refund_status"`              // 退款状态

	// 支付宝返回信息
	TradeNo       string     `gorm:"type:varchar(64)" json:"trade_no"`         // 支付宝交易号
	OutTradeNo    string     `gorm:"type:varchar(64)" json:"out_trade_no"`     // 商户订单号
	BuyerUserID   string     `gorm:"type:varchar(32)" json:"buyer_user_id"`    // 买家支付宝用户号
	RefundFee     string     `gorm:"type:decimal(10,2)" json:"refund_fee"`     // 实际退款金额
	GmtRefundPay  *time.Time `gorm:"index" json:"gmt_refund_pay"`              // 退款时间

	// 关联信息
	UserID *uuid.UUID `gorm:"type:uuid;index" json:"user_id"` // 操作用户ID
	
	// 索引
	_ struct{} `gorm:"index:idx_refund_status"`
	_ struct{} `gorm:"index:idx_refund_created_at"`
}

// TableName returns the table name for RefundRecord
func (RefundRecord) TableName() string {
	return "refund_records"
}

// BeforeCreate sets the ID before creating
func (r *RefundRecord) BeforeCreate(tx *gorm.DB) error {
	if r.ID == uuid.Nil {
		r.ID = uuid.New()
	}
	return nil
}

// NotifyRecord 异步通知记录模型
type NotifyRecord struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 通知信息
	NotifyID   string `gorm:"type:varchar(128);uniqueIndex;not null" json:"notify_id"`   // 通知校验ID
	NotifyType string `gorm:"type:varchar(50);not null" json:"notify_type"`              // 通知类型
	NotifyTime string `gorm:"type:varchar(50);not null" json:"notify_time"`              // 通知时间
	
	// 交易信息
	TradeNo    string `gorm:"type:varchar(64);index" json:"trade_no"`     // 支付宝交易号
	OutTradeNo string `gorm:"type:varchar(64);index" json:"out_trade_no"` // 商户订单号
	TradeStatus string `gorm:"type:varchar(50)" json:"trade_status"`       // 交易状态

	// 原始数据
	RawData    string `gorm:"type:text;not null" json:"raw_data"`    // 原始通知数据
	Signature  string `gorm:"type:text;not null" json:"signature"`   // 签名
	IsVerified bool   `gorm:"not null;default:false" json:"is_verified"` // 是否验证通过
	IsProcessed bool  `gorm:"not null;default:false" json:"is_processed"` // 是否已处理

	// 处理结果
	ProcessResult string     `gorm:"type:text" json:"process_result"` // 处理结果
	ProcessedAt   *time.Time `gorm:"index" json:"processed_at"`       // 处理时间
	
	// 索引
	_ struct{} `gorm:"index:idx_notify_status"`
	_ struct{} `gorm:"index:idx_notify_created_at"`
}

// TableName returns the table name for NotifyRecord
func (NotifyRecord) TableName() string {
	return "notify_records"
}

// BeforeCreate sets the ID before creating
func (n *NotifyRecord) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	return nil
}

// PaymentStatistics 支付统计模型
type PaymentStatistics struct {
	Date         time.Time `gorm:"type:date;primaryKey" json:"date"`
	TotalCount   int       `gorm:"not null;default:0" json:"total_count"`    // 总支付笔数
	SuccessCount int       `gorm:"not null;default:0" json:"success_count"`  // 成功支付笔数
	TotalAmount  string    `gorm:"type:decimal(15,2);not null;default:0" json:"total_amount"`  // 总支付金额
	SuccessAmount string   `gorm:"type:decimal(15,2);not null;default:0" json:"success_amount"` // 成功支付金额
	RefundCount  int       `gorm:"not null;default:0" json:"refund_count"`   // 退款笔数
	RefundAmount string    `gorm:"type:decimal(15,2);not null;default:0" json:"refund_amount"`  // 退款金额
	CreatedAt    time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt    time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName returns the table name for PaymentStatistics
func (PaymentStatistics) TableName() string {
	return "payment_statistics"
}
