package alipay

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// PaymentCache 支付缓存结构
type PaymentCache struct {
	OutTradeNo string        `json:"out_trade_no"` // 商户订单号
	Status     PaymentStatus `json:"status"`       // 支付状态
	TradeNo    string        `json:"trade_no"`     // 支付宝交易号
	UpdatedAt  time.Time     `json:"updated_at"`   // 更新时间
}

// CacheManager 缓存管理器
type CacheManager struct {
	redis *redis.Client
}

// NewCacheManager 创建缓存管理器
func NewCacheManager(redisClient *redis.Client) *CacheManager {
	return &CacheManager{
		redis: redisClient,
	}
}

// GetPaymentCacheKey 获取支付缓存键
func GetPaymentCacheKey(outTradeNo string) string {
	return fmt.Sprintf("alipay:payment:%s", outTradeNo)
}

// SetPaymentCache 设置支付缓存
func (c *CacheManager) SetPaymentCache(ctx context.Context, outTradeNo string, status PaymentStatus, tradeNo string, expireMinutes int) error {
	if c.redis == nil {
		return nil // Redis 不可用时静默失败
	}

	cache := PaymentCache{
		OutTradeNo: outTradeNo,
		Status:     status,
		TradeNo:    tradeNo,
		UpdatedAt:  time.Now(),
	}

	data, err := json.Marshal(cache)
	if err != nil {
		return fmt.Errorf("marshal payment cache failed: %w", err)
	}

	key := GetPaymentCacheKey(outTradeNo)
	expiration := time.Duration(expireMinutes) * time.Minute

	err = c.redis.Set(ctx, key, data, expiration).Err()
	if err != nil {
		return fmt.Errorf("set payment cache failed: %w", err)
	}

	fmt.Printf("✅ 设置支付缓存成功: %s, 状态: %s, 过期时间: %d分钟\n", outTradeNo, status, expireMinutes)
	return nil
}

// GetPaymentCache 获取支付缓存
func (c *CacheManager) GetPaymentCache(ctx context.Context, outTradeNo string) (*PaymentCache, error) {
	if c.redis == nil {
		return nil, fmt.Errorf("redis client not available")
	}

	key := GetPaymentCacheKey(outTradeNo)
	data, err := c.redis.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存不存在
		}
		return nil, fmt.Errorf("get payment cache failed: %w", err)
	}

	var cache PaymentCache
	if err := json.Unmarshal([]byte(data), &cache); err != nil {
		return nil, fmt.Errorf("unmarshal payment cache failed: %w", err)
	}

	fmt.Printf("✅ 获取支付缓存成功: %s, 状态: %s\n", outTradeNo, cache.Status)
	return &cache, nil
}

// UpdatePaymentCache 更新支付缓存
func (c *CacheManager) UpdatePaymentCache(ctx context.Context, outTradeNo string, status PaymentStatus, tradeNo string) error {
	if c.redis == nil {
		return nil // Redis 不可用时静默失败
	}

	key := GetPaymentCacheKey(outTradeNo)

	// 检查缓存是否存在
	exists, err := c.redis.Exists(ctx, key).Result()
	if err != nil {
		return fmt.Errorf("check cache exists failed: %w", err)
	}

	if exists == 0 {
		// 缓存不存在，不更新
		fmt.Printf("⚠️  支付缓存不存在，跳过更新: %s\n", outTradeNo)
		return nil
	}

	// 获取当前 TTL
	ttl, err := c.redis.TTL(ctx, key).Result()
	if err != nil {
		return fmt.Errorf("get cache ttl failed: %w", err)
	}

	// 更新缓存内容
	cache := PaymentCache{
		OutTradeNo: outTradeNo,
		Status:     status,
		TradeNo:    tradeNo,
		UpdatedAt:  time.Now(),
	}

	data, err := json.Marshal(cache)
	if err != nil {
		return fmt.Errorf("marshal payment cache failed: %w", err)
	}

	// 保持原有的过期时间
	err = c.redis.Set(ctx, key, data, ttl).Err()
	if err != nil {
		return fmt.Errorf("update payment cache failed: %w", err)
	}

	fmt.Printf("✅ 更新支付缓存成功: %s, 状态: %s\n", outTradeNo, status)
	return nil
}

// DeletePaymentCache 删除支付缓存
func (c *CacheManager) DeletePaymentCache(ctx context.Context, outTradeNo string) error {
	if c.redis == nil {
		return nil // Redis 不可用时静默失败
	}

	key := GetPaymentCacheKey(outTradeNo)
	err := c.redis.Del(ctx, key).Err()
	if err != nil {
		return fmt.Errorf("delete payment cache failed: %w", err)
	}

	fmt.Printf("✅ 删除支付缓存成功: %s\n", outTradeNo)
	return nil
}

// IsRedisAvailable 检查 Redis 是否可用
func (c *CacheManager) IsRedisAvailable(ctx context.Context) bool {
	if c.redis == nil {
		return false
	}

	err := c.redis.Ping(ctx).Err()
	return err == nil
}

// QueryPaymentWithCache 带缓存的支付状态查询
func (c *CacheManager) QueryPaymentWithCache(ctx context.Context, outTradeNo string, dbQuery func() (*PaymentRecord, error), alipayQuery func() error) (*PaymentPollingResponse, error) {
	// 1. 先尝试从 Redis 获取
	if c.IsRedisAvailable(ctx) {
		cache, err := c.GetPaymentCache(ctx, outTradeNo)
		if err == nil && cache != nil {
			// Redis 中存在，直接返回
			return &PaymentPollingResponse{
				OutTradeNo: cache.OutTradeNo,
				IsPaid:     IsPaymentSuccess(cache.Status),
				Status:     string(cache.Status),
			}, nil
		}
		if err != nil {
			fmt.Printf("⚠️  Redis 查询失败，降级到数据库查询: %v\n", err)
		}
	}

	// 2. Redis 不存在或不可用，查询数据库
	paymentRecord, err := dbQuery()
	if err != nil {
		return nil, err
	}

	// 3. 如果订单还在等待支付，尝试从支付宝查询最新状态
	if paymentRecord.Status == PaymentStatusWaitBuyerPay {
		if err := alipayQuery(); err != nil {
			fmt.Printf("⚠️  支付宝查询失败: %v\n", err)
		} else {
			// 重新查询数据库获取最新状态
			paymentRecord, err = dbQuery()
			if err != nil {
				return nil, err
			}
		}
	}

	// 4. 更新 Redis 缓存（如果可用）
	if c.IsRedisAvailable(ctx) {
		// 计算剩余过期时间（默认10分钟，如果已经过期则设置为1分钟）
		expireMinutes := 10
		if paymentRecord.GmtCreate != nil {
			elapsed := time.Since(*paymentRecord.GmtCreate)
			remaining := time.Duration(paymentRecord.TimeoutExpress)*time.Minute - elapsed
			if remaining > 0 {
				expireMinutes = int(remaining.Minutes()) + 1 // 多加1分钟缓冲
			} else {
				expireMinutes = 1 // 已过期，短时间缓存
			}
		}

		if err := c.SetPaymentCache(ctx, paymentRecord.OutTradeNo, paymentRecord.Status, paymentRecord.TradeNo, expireMinutes); err != nil {
			fmt.Printf("⚠️  设置 Redis 缓存失败: %v\n", err)
		}
	}

	// 5. 返回结果
	return &PaymentPollingResponse{
		OutTradeNo: paymentRecord.OutTradeNo,
		IsPaid:     IsPaymentSuccess(paymentRecord.Status),
		Status:     string(paymentRecord.Status),
	}, nil
}
