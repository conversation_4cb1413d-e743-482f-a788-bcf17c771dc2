package wechat

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PaymentRecord 微信支付记录模型
type PaymentRecord struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 基本信息
	OutTradeNo    string        `gorm:"type:varchar(64);uniqueIndex;not null" json:"out_trade_no"`    // 商户订单号
	TransactionID string        `gorm:"type:varchar(64);index" json:"transaction_id"`                 // 微信支付订单号
	Body          string        `gorm:"type:varchar(256);not null" json:"body"`                       // 商品描述
	Detail        string        `gorm:"type:text" json:"detail"`                                      // 商品详情
	TotalFee      int64         `gorm:"not null" json:"total_fee"`                                    // 订单总金额（分）
	PaymentMethod PaymentMethod `gorm:"type:varchar(20);not null" json:"payment_method"`             // 支付方式

	// 支付状态
	TradeState     PaymentStatus `gorm:"type:varchar(50);not null;default:'NOTPAY'" json:"trade_state"`
	TradeStateDesc string        `gorm:"type:varchar(256)" json:"trade_state_desc"`                   // 交易状态描述
	PaymentData    string        `gorm:"type:text" json:"payment_data"`                               // 支付数据
	CodeURL        string        `gorm:"type:text" json:"code_url"`                                   // 二维码链接
	PrepayID       string        `gorm:"type:varchar(64)" json:"prepay_id"`                           // 预支付交易会话标识
	MWebURL        string        `gorm:"type:text" json:"mweb_url"`                                   // H5支付跳转链接

	// 用户信息
	OpenID   string `gorm:"type:varchar(128)" json:"openid"`    // 用户标识
	BankType string `gorm:"type:varchar(32)" json:"bank_type"`  // 付款银行
	CashFee  int64  `gorm:"default:0" json:"cash_fee"`          // 现金支付金额

	// 时间信息
	TimeoutExpress int        `gorm:"not null;default:30" json:"timeout_express"` // 超时时间（分钟）
	TimeStart      *time.Time `gorm:"index" json:"time_start"`                    // 交易创建时间
	TimeEnd        *time.Time `gorm:"index" json:"time_end"`                      // 交易结束时间

	// 扩展信息
	Attach    string `gorm:"type:text" json:"attach"`     // 附加数据
	GoodsTag  string `gorm:"type:varchar(32)" json:"goods_tag"`  // 商品标签
	LimitPay  string `gorm:"type:varchar(32)" json:"limit_pay"`  // 指定支付方式
	SceneInfo string `gorm:"type:text" json:"scene_info"` // 场景信息

	// 关联信息
	UserID  *uuid.UUID `gorm:"type:uuid;index" json:"user_id"`  // 关联用户ID
	OrderID *uuid.UUID `gorm:"type:uuid;index" json:"order_id"` // 关联订单ID

	// 索引
	_ struct{} `gorm:"index:idx_wechat_payment_status"`
	_ struct{} `gorm:"index:idx_wechat_payment_created_at"`
	_ struct{} `gorm:"index:idx_wechat_payment_user_id"`
}

// TableName returns the table name for PaymentRecord
func (PaymentRecord) TableName() string {
	return "wechat_payment_records"
}

// BeforeCreate sets the ID before creating
func (p *PaymentRecord) BeforeCreate(tx *gorm.DB) error {
	if p.ID == uuid.Nil {
		p.ID = uuid.New()
	}
	return nil
}

// RefundRecord 微信退款记录模型
type RefundRecord struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 关联支付记录
	PaymentRecordID uuid.UUID     `gorm:"type:uuid;not null;index" json:"payment_record_id"`
	PaymentRecord   PaymentRecord `gorm:"foreignKey:PaymentRecordID" json:"payment_record"`

	// 退款信息
	OutRefundNo   string       `gorm:"type:varchar(64);uniqueIndex;not null" json:"out_refund_no"` // 商户退款单号
	RefundID      string       `gorm:"type:varchar(64);index" json:"refund_id"`                    // 微信退款单号
	RefundFee     int64        `gorm:"not null" json:"refund_fee"`                                 // 退款金额（分）
	TotalFee      int64        `gorm:"not null" json:"total_fee"`                                  // 订单总金额（分）
	RefundDesc    string       `gorm:"type:varchar(256)" json:"refund_desc"`                       // 退款原因
	RefundAccount string       `gorm:"type:varchar(32)" json:"refund_account"`                     // 退款资金来源
	RefundStatus  RefundStatus `gorm:"type:varchar(50);not null" json:"refund_status"`             // 退款状态

	// 微信返回信息
	TransactionID string     `gorm:"type:varchar(64)" json:"transaction_id"` // 微信支付订单号
	OutTradeNo    string     `gorm:"type:varchar(64)" json:"out_trade_no"`   // 商户订单号
	SuccessTime   *time.Time `gorm:"index" json:"success_time"`              // 退款成功时间

	// 关联信息
	UserID *uuid.UUID `gorm:"type:uuid;index" json:"user_id"` // 操作用户ID

	// 索引
	_ struct{} `gorm:"index:idx_wechat_refund_status"`
	_ struct{} `gorm:"index:idx_wechat_refund_created_at"`
}

// TableName returns the table name for RefundRecord
func (RefundRecord) TableName() string {
	return "wechat_refund_records"
}

// BeforeCreate sets the ID before creating
func (r *RefundRecord) BeforeCreate(tx *gorm.DB) error {
	if r.ID == uuid.Nil {
		r.ID = uuid.New()
	}
	return nil
}

// NotifyRecord 微信异步通知记录模型
type NotifyRecord struct {
	ID        uuid.UUID      `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	CreatedAt time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at,omitempty"`

	// 通知信息
	ReturnCode string `gorm:"type:varchar(16);not null" json:"return_code"`   // 返回状态码
	ReturnMsg  string `gorm:"type:varchar(128)" json:"return_msg"`            // 返回信息
	ResultCode string `gorm:"type:varchar(16)" json:"result_code"`            // 业务结果
	ErrCode    string `gorm:"type:varchar(32)" json:"err_code"`               // 错误代码
	ErrCodeDes string `gorm:"type:varchar(128)" json:"err_code_des"`          // 错误代码描述

	// 交易信息
	TransactionID string `gorm:"type:varchar(64);index" json:"transaction_id"` // 微信支付订单号
	OutTradeNo    string `gorm:"type:varchar(64);index" json:"out_trade_no"`   // 商户订单号
	TradeType     string `gorm:"type:varchar(16)" json:"trade_type"`           // 交易类型
	BankType      string `gorm:"type:varchar(32)" json:"bank_type"`            // 付款银行
	TotalFee      int64  `gorm:"default:0" json:"total_fee"`                   // 订单总金额
	CashFee       int64  `gorm:"default:0" json:"cash_fee"`                    // 现金支付金额
	TimeEnd       string `gorm:"type:varchar(32)" json:"time_end"`             // 支付完成时间

	// 用户信息
	OpenID string `gorm:"type:varchar(128)" json:"openid"` // 用户标识

	// 原始数据
	RawData     string `gorm:"type:text;not null" json:"raw_data"`             // 原始通知数据
	Signature   string `gorm:"type:text;not null" json:"signature"`            // 签名
	IsVerified  bool   `gorm:"not null;default:false" json:"is_verified"`      // 是否验证通过
	IsProcessed bool   `gorm:"not null;default:false" json:"is_processed"`     // 是否已处理

	// 处理结果
	ProcessResult string     `gorm:"type:text" json:"process_result"` // 处理结果
	ProcessedAt   *time.Time `gorm:"index" json:"processed_at"`       // 处理时间

	// 索引
	_ struct{} `gorm:"index:idx_wechat_notify_status"`
	_ struct{} `gorm:"index:idx_wechat_notify_created_at"`
}

// TableName returns the table name for NotifyRecord
func (NotifyRecord) TableName() string {
	return "wechat_notify_records"
}

// BeforeCreate sets the ID before creating
func (n *NotifyRecord) BeforeCreate(tx *gorm.DB) error {
	if n.ID == uuid.Nil {
		n.ID = uuid.New()
	}
	return nil
}

// PaymentStatistics 微信支付统计模型
type PaymentStatistics struct {
	Date          time.Time `gorm:"type:date;primaryKey" json:"date"`
	TotalCount    int       `gorm:"not null;default:0" json:"total_count"`     // 总支付笔数
	SuccessCount  int       `gorm:"not null;default:0" json:"success_count"`   // 成功支付笔数
	TotalAmount   int64     `gorm:"not null;default:0" json:"total_amount"`    // 总支付金额（分）
	SuccessAmount int64     `gorm:"not null;default:0" json:"success_amount"`  // 成功支付金额（分）
	RefundCount   int       `gorm:"not null;default:0" json:"refund_count"`    // 退款笔数
	RefundAmount  int64     `gorm:"not null;default:0" json:"refund_amount"`   // 退款金额（分）
	CreatedAt     time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}

// TableName returns the table name for PaymentStatistics
func (PaymentStatistics) TableName() string {
	return "wechat_payment_statistics"
}
