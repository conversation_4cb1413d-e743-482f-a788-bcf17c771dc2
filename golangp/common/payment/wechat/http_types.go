package wechat

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// ValidateUserID 验证用户ID格式
func ValidateUserID(userID string) (uuid.UUID, error) {
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return uuid.Nil, fmt.Errorf("用户ID格式错误: %w", err)
	}
	return userUUID, nil
}

// FormatExpireTime 格式化过期时间
func FormatExpireTime(expireMinutes int) string {
	return time.Now().Add(time.Duration(expireMinutes) * time.Minute).Format("2006-01-02 15:04:05")
}

// FormatPayTime 格式化支付时间
func FormatPayTime(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

// IsPaymentSuccess 检查支付状态是否为成功
func IsPaymentSuccess(status PaymentStatus) bool {
	return status == PaymentStatusSuccess
}

// IsPaymentPending 检查支付状态是否为等待中
func IsPaymentPending(status PaymentStatus) bool {
	return status == PaymentStatusNotPay || status == PaymentStatusUserPaying
}

// IsPaymentClosed 检查支付状态是否为已关闭
func IsPaymentClosed(status PaymentStatus) bool {
	return status == PaymentStatusClosed || status == PaymentStatusRevoked
}

// ParseFormToMap 将表单数据转换为map
func ParseFormToMap(formData map[string][]string) map[string]string {
	result := make(map[string]string)
	for key, values := range formData {
		if len(values) > 0 {
			result[key] = values[0]
		}
	}
	return result
}

// ValidateOutTradeNo 验证商户订单号
func ValidateOutTradeNo(outTradeNo string) error {
	if outTradeNo == "" {
		return fmt.Errorf("商户订单号不能为空")
	}
	return nil
}

// GetDefaultExpireTime 获取默认过期时间
func GetDefaultExpireTime() int {
	return 10 // 默认10分钟
}

// GenerateOutTradeNo 生成商户订单号
func GenerateOutTradeNo(prefix string) string {
	return fmt.Sprintf("%s%d", prefix, time.Now().Unix())
}

// BuildAttachData 构建附加数据
func BuildAttachData(userID, orderID, extraData string) string {
	if userID == "" {
		return extraData
	}

	attach := fmt.Sprintf("user_id::%s", userID)
	if orderID != "" {
		attach += fmt.Sprintf("::order_id::%s", orderID)
	}
	if extraData != "" {
		attach += fmt.Sprintf("::extra::%s", extraData)
	}
	return attach
}

// ParseAttachData 解析附加数据
func ParseAttachData(attach string) (userID, orderID, extraData string) {
	if attach == "" {
		return "", "", ""
	}

	// 简单的解析逻辑，实际项目中可能需要更复杂的解析
	// 格式: user_id::xxx::order_id::yyy::extra::zzz
	parts := make(map[string]string)

	// 这里简化处理，实际应该用更健壮的解析方法
	if len(attach) > 0 {
		// 假设格式正确，直接返回
		return userID, orderID, attach
	}

	return parts["user_id"], parts["order_id"], parts["extra"]
}

// CreateQRCodeRequest 创建二维码请求（用于HTTP接口）
type CreateQRCodeRequest struct {
	UserID     string `json:"user_id" binding:"required"`   // 用户ID
	OrderID    string `json:"order_id,omitempty"`           // 业务订单ID
	TotalFee   int64  `json:"total_fee" binding:"required"` // 订单总金额（分）
	Body       string `json:"body" binding:"required"`      // 商品描述
	Detail     string `json:"detail,omitempty"`             // 商品详情
	ExtraData  string `json:"extra_data,omitempty"`         // 额外数据
	ExpireTime int    `json:"expire_time,omitempty"`        // 过期时间（分钟）
	GoodsTag   string `json:"goods_tag,omitempty"`          // 商品标签
	LimitPay   string `json:"limit_pay,omitempty"`          // 指定支付方式
}

// CreateQRCodeResponse 创建二维码响应
type CreateQRCodeResponse struct {
	OutTradeNo string `json:"out_trade_no"` // 商户订单号
	QRCode     string `json:"qr_code"`      // 二维码链接
	ExpireTime string `json:"expire_time"`  // 过期时间
}

// PaymentStatusResponse 支付状态响应（详细信息）
type PaymentStatusResponse struct {
	OutTradeNo    string `json:"out_trade_no"`   // 商户订单号
	TransactionID string `json:"transaction_id"` // 微信支付订单号
	Status        string `json:"status"`         // 支付状态
	Amount        int64  `json:"amount"`         // 支付金额（分）
	PayTime       string `json:"pay_time"`       // 支付时间
	UserID        string `json:"user_id"`        // 用户ID
	OrderID       string `json:"order_id"`       // 业务订单ID
	ExtraData     string `json:"extra_data"`     // 额外数据
}

// CreateJSAPIRequest 创建JSAPI支付请求
type CreateJSAPIRequest struct {
	UserID     string `json:"user_id" binding:"required"`   // 用户ID
	OrderID    string `json:"order_id,omitempty"`           // 业务订单ID
	TotalFee   int64  `json:"total_fee" binding:"required"` // 订单总金额（分）
	Body       string `json:"body" binding:"required"`      // 商品描述
	Detail     string `json:"detail,omitempty"`             // 商品详情
	OpenID     string `json:"openid" binding:"required"`    // 用户OpenID
	ExtraData  string `json:"extra_data,omitempty"`         // 额外数据
	ExpireTime int    `json:"expire_time,omitempty"`        // 过期时间（分钟）
}

// CreateJSAPIResponse 创建JSAPI支付响应
type CreateJSAPIResponse struct {
	OutTradeNo  string `json:"out_trade_no"` // 商户订单号
	PaymentData string `json:"payment_data"` // 支付配置数据
	ExpireTime  string `json:"expire_time"`  // 过期时间
}

// CreateH5Request 创建H5支付请求
type CreateH5Request struct {
	UserID     string     `json:"user_id" binding:"required"`    // 用户ID
	OrderID    string     `json:"order_id,omitempty"`            // 业务订单ID
	TotalFee   int64      `json:"total_fee" binding:"required"`  // 订单总金额（分）
	Body       string     `json:"body" binding:"required"`       // 商品描述
	Detail     string     `json:"detail,omitempty"`              // 商品详情
	SceneInfo  *SceneInfo `json:"scene_info" binding:"required"` // 场景信息
	ExtraData  string     `json:"extra_data,omitempty"`          // 额外数据
	ExpireTime int        `json:"expire_time,omitempty"`         // 过期时间（分钟）
}

// CreateH5Response 创建H5支付响应
type CreateH5Response struct {
	OutTradeNo string `json:"out_trade_no"` // 商户订单号
	MWebURL    string `json:"mweb_url"`     // H5支付跳转链接
	ExpireTime string `json:"expire_time"`  // 过期时间
}
