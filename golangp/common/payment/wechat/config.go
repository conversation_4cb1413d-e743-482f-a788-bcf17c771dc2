package wechat

import (
	"fmt"
	"time"
)

// Config holds all configuration for WeChat payment service using PowerWeChat SDK
type Config struct {
	// 应用ID (微信公众号或小程序的AppID)
	AppID string `mapstructure:"WECHAT_APP_ID"`

	// 商户号
	MchID string `mapstructure:"WECHAT_MCH_ID"`

	// 商户API密钥 (API v2使用)
	APIKey string `mapstructure:"WECHAT_API_KEY"`

	// 商户API v3密钥
	APIv3Key string `mapstructure:"WECHAT_API_V3_KEY"`

	// 商户证书文件路径 (API v3使用)
	CertFile string `mapstructure:"WECHAT_CERT_FILE"`

	// 商户私钥文件路径 (API v3使用)
	KeyFile string `mapstructure:"WECHAT_KEY_FILE"`

	// 商户证书序列号 (API v3使用)
	SerialNo string `mapstructure:"WECHAT_SERIAL_NO"`

	// 微信支付平台证书文件路径 (API v3使用)
	PlatformCertFile string `mapstructure:"WECHAT_PLATFORM_CERT_FILE"`

	// 微信支付平台证书序列号 (API v3使用)
	PlatformSerialNo string `mapstructure:"WECHAT_PLATFORM_SERIAL_NO"`

	// API版本 (v2 或 v3，推荐v3)
	APIVersion string `mapstructure:"WECHAT_API_VERSION"`

	// 是否启用沙箱模式
	Sandbox bool `mapstructure:"WECHAT_SANDBOX"`

	// 异步通知地址
	NotifyURL string `mapstructure:"WECHAT_NOTIFY_URL"`

	// 超时时间 (分钟)
	TimeoutExpress int `mapstructure:"WECHAT_TIMEOUT_EXPRESS"`

	// 签名类型 (HMAC-SHA256 或 MD5 for v2, WECHATPAY2-SHA256-RSA2048 for v3)
	SignType string `mapstructure:"WECHAT_SIGN_TYPE"`

	// 交易类型 (JSAPI, NATIVE, APP, MWEB)
	TradeType string `mapstructure:"WECHAT_TRADE_TYPE"`

	// 场景信息 (用于H5支付)
	SceneInfo string `mapstructure:"WECHAT_SCENE_INFO"`

	// 子商户号 (服务商模式)
	SubMchID string `mapstructure:"PAYMENT_SUB_MCH_ID"`

	// 子商户AppID (服务商模式)
	SubAppID string `mapstructure:"PAYMENT_SUB_APP_ID"`

	// HTTP超时时间 (秒)
	HTTPTimeout int `mapstructure:"WECHAT_HTTP_TIMEOUT"`

	// 是否启用日志
	EnableLog bool `mapstructure:"WECHAT_ENABLE_LOG"`

	// 日志级别 (debug, info, warn, error)
	LogLevel string `mapstructure:"WECHAT_LOG_LEVEL"`
}

// Validate validates the WeChat payment configuration
func (c *Config) Validate() error {
	if c.AppID == "" {
		return fmt.Errorf("wechat app_id is required")
	}

	if c.MchID == "" {
		return fmt.Errorf("wechat mch_id is required")
	}

	// 验证API版本
	if c.APIVersion != "v2" && c.APIVersion != "v3" {
		return fmt.Errorf("wechat api_version must be v2 or v3")
	}

	// 如果是v3版本，需要证书文件和v3密钥
	if c.APIVersion == "v3" {
		if c.APIv3Key == "" {
			return fmt.Errorf("wechat api_v3_key is required for API v3")
		}
		if c.CertFile == "" {
			return fmt.Errorf("wechat cert_file is required for API v3")
		}
		if c.KeyFile == "" {
			return fmt.Errorf("wechat key_file is required for API v3")
		}
		if c.SerialNo == "" {
			return fmt.Errorf("wechat serial_no is required for API v3")
		}
	} else {
		// v2版本需要API密钥
		if c.APIKey == "" {
			return fmt.Errorf("wechat api_key is required for API v2")
		}
	}

	return nil
}

// SetDefaults sets default values for optional configuration fields
func (c *Config) SetDefaults() {
	// if c.APIVersion == "" {
	// 	c.APIVersion = "v3" // 默认使用v3版本
	// }

	// if c.SignType == "" {
	// 	if c.APIVersion == "v3" {
	// 		c.SignType = "WECHATPAY2-SHA256-RSA2048"
	// 	} else {
	// 		c.SignType = "HMAC-SHA256"
	// 	}
	// }

	// if c.TradeType == "" {
	// 	c.TradeType = "NATIVE" // 默认扫码支付
	// }

	if c.TimeoutExpress == 0 {
		c.TimeoutExpress = 30 // 默认30分钟
	}

	if c.HTTPTimeout == 0 {
		c.HTTPTimeout = 30 // 默认30秒HTTP超时
	}

	if c.LogLevel == "" {
		c.LogLevel = "info" // 默认info级别日志
	}
}

// GetTimeoutDuration returns timeout duration
func (c *Config) GetTimeoutDuration() time.Duration {
	return time.Duration(c.TimeoutExpress) * time.Minute
}

// IsV3 returns true if using API v3
func (c *Config) IsV3() bool {
	return c.APIVersion == "v3"
}

// GetBaseURL returns the base URL for WeChat Pay API
func (c *Config) GetBaseURL() string {
	if c.Sandbox {
		return "https://api.mch.weixin.qq.com/sandboxnew"
	}

	if c.IsV3() {
		return "https://api.mch.weixin.qq.com"
	}

	return "https://api.mch.weixin.qq.com"
}
