package wechat

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// CacheManager 微信支付缓存管理器
type CacheManager struct {
	rdb *redis.Client
}

// NewCacheManager 创建缓存管理器
func NewCacheManager(rdb *redis.Client) *CacheManager {
	return &CacheManager{
		rdb: rdb,
	}
}

// PaymentCacheData 支付缓存数据
type PaymentCacheData struct {
	OutTradeNo    string        `json:"out_trade_no"`
	Status        PaymentStatus `json:"status"`
	TransactionID string        `json:"transaction_id"`
	LastUpdated   time.Time     `json:"last_updated"`
	ExpiresAt     time.Time     `json:"expires_at"`
}

// SetPaymentCache 设置支付缓存
func (c *CacheManager) SetPaymentCache(ctx context.Context, outTradeNo string, status PaymentStatus, transactionID string, timeoutMinutes int) error {
	key := fmt.Sprintf("wechat_payment:%s", outTradeNo)
	
	data := PaymentCacheData{
		OutTradeNo:    outTradeNo,
		Status:        status,
		TransactionID: transactionID,
		LastUpdated:   time.Now(),
		ExpiresAt:     time.Now().Add(time.Duration(timeoutMinutes) * time.Minute),
	}

	dataBytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal cache data: %w", err)
	}

	// 设置缓存，过期时间比订单超时时间多10分钟
	expiration := time.Duration(timeoutMinutes+10) * time.Minute
	return c.rdb.Set(ctx, key, dataBytes, expiration).Err()
}

// GetPaymentCache 获取支付缓存
func (c *CacheManager) GetPaymentCache(ctx context.Context, outTradeNo string) (*PaymentCacheData, error) {
	key := fmt.Sprintf("wechat_payment:%s", outTradeNo)
	
	result, err := c.rdb.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 缓存不存在
		}
		return nil, fmt.Errorf("failed to get cache: %w", err)
	}

	var data PaymentCacheData
	if err := json.Unmarshal([]byte(result), &data); err != nil {
		return nil, fmt.Errorf("failed to unmarshal cache data: %w", err)
	}

	return &data, nil
}

// UpdatePaymentCache 更新支付缓存
func (c *CacheManager) UpdatePaymentCache(ctx context.Context, outTradeNo string, status PaymentStatus, transactionID string) error {
	// 先获取现有缓存
	existingData, err := c.GetPaymentCache(ctx, outTradeNo)
	if err != nil {
		return fmt.Errorf("failed to get existing cache: %w", err)
	}

	if existingData == nil {
		// 缓存不存在，创建新的（使用默认30分钟过期时间）
		return c.SetPaymentCache(ctx, outTradeNo, status, transactionID, 30)
	}

	// 更新现有缓存
	existingData.Status = status
	if transactionID != "" {
		existingData.TransactionID = transactionID
	}
	existingData.LastUpdated = time.Now()

	key := fmt.Sprintf("wechat_payment:%s", outTradeNo)
	dataBytes, err := json.Marshal(existingData)
	if err != nil {
		return fmt.Errorf("failed to marshal updated cache data: %w", err)
	}

	// 保持原有的过期时间
	ttl := c.rdb.TTL(ctx, key).Val()
	return c.rdb.Set(ctx, key, dataBytes, ttl).Err()
}

// DeletePaymentCache 删除支付缓存
func (c *CacheManager) DeletePaymentCache(ctx context.Context, outTradeNo string) error {
	key := fmt.Sprintf("wechat_payment:%s", outTradeNo)
	return c.rdb.Del(ctx, key).Err()
}

// QueryPaymentWithCache 带缓存的支付状态查询
func (c *CacheManager) QueryPaymentWithCache(ctx context.Context, outTradeNo string, 
	dbQueryFunc func() (*PaymentRecord, error),
	wechatQueryFunc func() error) (*PaymentPollingResponse, error) {

	// 1. 先从缓存获取
	cacheData, err := c.GetPaymentCache(ctx, outTradeNo)
	if err != nil {
		fmt.Printf("⚠️  获取缓存失败: %v\n", err)
	}

	// 2. 从数据库查询
	paymentRecord, err := dbQueryFunc()
	if err != nil {
		return nil, fmt.Errorf("failed to query from database: %w", err)
	}

	// 3. 检查是否需要调用微信查询API
	shouldQueryWechat := false
	
	if cacheData != nil {
		// 如果缓存中的状态是未支付，且距离上次更新超过30秒，则查询微信
		if cacheData.Status == PaymentStatusNotPay && 
		   time.Since(cacheData.LastUpdated) > 30*time.Second {
			shouldQueryWechat = true
		}
	} else {
		// 没有缓存且订单状态是未支付，则查询微信
		if paymentRecord.TradeState == PaymentStatusNotPay {
			shouldQueryWechat = true
		}
	}

	// 4. 如果需要，调用微信查询API
	if shouldQueryWechat {
		if err := wechatQueryFunc(); err != nil {
			fmt.Printf("⚠️  微信查询失败: %v\n", err)
		} else {
			// 重新查询数据库获取最新状态
			if updatedRecord, err := dbQueryFunc(); err == nil {
				paymentRecord = updatedRecord
			}
		}
	}

	// 5. 更新缓存
	if err := c.UpdatePaymentCache(ctx, paymentRecord.OutTradeNo, paymentRecord.TradeState, paymentRecord.TransactionID); err != nil {
		fmt.Printf("⚠️  更新缓存失败: %v\n", err)
	}

	// 6. 返回结果
	return &PaymentPollingResponse{
		OutTradeNo: paymentRecord.OutTradeNo,
		IsPaid:     IsPaymentSuccess(paymentRecord.TradeState),
		Status:     string(paymentRecord.TradeState),
		StatusDesc: paymentRecord.TradeStateDesc,
	}, nil
}


// CleanExpiredCache 清理过期缓存
func (c *CacheManager) CleanExpiredCache(ctx context.Context) error {
	// 使用 SCAN 命令遍历所有微信支付相关的缓存键
	iter := c.rdb.Scan(ctx, 0, "wechat_payment:*", 0).Iterator()
	
	var expiredKeys []string
	for iter.Next(ctx) {
		key := iter.Val()
		
		// 获取缓存数据
		result, err := c.rdb.Get(ctx, key).Result()
		if err != nil {
			if err == redis.Nil {
				continue // 键已经不存在
			}
			fmt.Printf("⚠️  获取缓存键 %s 失败: %v\n", key, err)
			continue
		}

		var data PaymentCacheData
		if err := json.Unmarshal([]byte(result), &data); err != nil {
			fmt.Printf("⚠️  解析缓存数据失败: %v\n", err)
			expiredKeys = append(expiredKeys, key) // 数据损坏，标记删除
			continue
		}

		// 检查是否过期
		if time.Now().After(data.ExpiresAt) {
			expiredKeys = append(expiredKeys, key)
		}
	}

	if err := iter.Err(); err != nil {
		return fmt.Errorf("failed to scan cache keys: %w", err)
	}

	// 批量删除过期键
	if len(expiredKeys) > 0 {
		if err := c.rdb.Del(ctx, expiredKeys...).Err(); err != nil {
			return fmt.Errorf("failed to delete expired keys: %w", err)
		}
		fmt.Printf("🧹 清理了 %d 个过期的微信支付缓存\n", len(expiredKeys))
	}

	return nil
}

// GetCacheStats 获取缓存统计信息
func (c *CacheManager) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 统计微信支付缓存键的数量
	iter := c.rdb.Scan(ctx, 0, "wechat_payment:*", 0).Iterator()
	
	totalKeys := 0
	statusCounts := make(map[string]int)
	
	for iter.Next(ctx) {
		totalKeys++
		
		key := iter.Val()
		result, err := c.rdb.Get(ctx, key).Result()
		if err != nil {
			continue
		}

		var data PaymentCacheData
		if err := json.Unmarshal([]byte(result), &data); err != nil {
			continue
		}

		statusCounts[string(data.Status)]++
	}

	if err := iter.Err(); err != nil {
		return nil, fmt.Errorf("failed to scan cache keys: %w", err)
	}

	stats["total_keys"] = totalKeys
	stats["status_counts"] = statusCounts
	stats["timestamp"] = time.Now()

	return stats, nil
}
