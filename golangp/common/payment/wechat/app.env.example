# 微信支付配置示例 - 使用 PowerWeChat SDK
# 复制此文件为 app.env 并填入真实的配置值

# 基本配置
WECHAT_APP_ID=wxbf2a7bf9525ac893
WECHAT_MCH_ID=1643093300
WECHAT_API_KEY=openailink123456789abcdefghijkV2

# API版本 (v2 或 v3，推荐使用v3)
WECHAT_API_VERSION=v3

# API v3 密钥 (API v3 必需)
WECHAT_API_V3_KEY=openailink123456789abcdefghijkV3

# 证书配置 (API v3 必需)
WECHAT_CERT_FILE=certs/apiclient_cert.pem
WECHAT_KEY_FILE=certs/apiclient_key.pem
WECHAT_SERIAL_NO=20736642C47368093A005C4A1532A07E16636578

# 回调地址
WECHAT_NOTIFY_URL=http://weixin.open2any.tools:8000/api/wechat/payment/notify

# 支付配置
WECHAT_TIMEOUT_EXPRESS=30
WECHAT_SIGN_TYPE=WECHATPAY2-SHA256-RSA2048

# 服务商模式配置 (可选)
PAYMENT_SUB_MCH_ID=
PAYMENT_SUB_APP_ID=

# HTTP配置
WECHAT_HTTP_TIMEOUT=30
WECHAT_ENABLE_LOG=true
WECHAT_LOG_LEVEL=info

# 配置说明：
# 1. WECHAT_APP_ID: 微信公众号或小程序的AppID
# 2. WECHAT_MCH_ID: 微信支付商户号
# 3. WECHAT_API_KEY: 商户API密钥，在微信支付商户平台设置
# 4. WECHAT_API_VERSION: API版本，v3版本更安全，推荐使用
# 5. 证书文件: API v3版本需要配置商户证书和微信支付平台证书
# 6. WECHAT_NOTIFY_URL: 异步通知地址，微信支付完成后会调用此地址
# 7. WECHAT_RETURN_URL: 同步回调地址，用户支付完成后跳转的页面
# 8. WECHAT_TIMEOUT_EXPRESS: 订单超时时间，单位分钟
# 9. WECHAT_SANDBOX: 是否启用沙箱模式，生产环境请设置为false

# 获取证书的方法：
# 1. 商户证书: 在微信支付商户平台下载
# 2. 微信支付平台证书: 通过API获取或在商户平台下载
# 3. 证书序列号: 可以通过openssl命令查看证书获取

# 支付方式说明：
# - NATIVE: 扫码支付，生成二维码供用户扫描
# - JSAPI: 公众号支付，在微信内H5页面调起支付
# - APP: APP支付，在移动应用中调起微信支付
# - MWEB: H5支付，在手机浏览器中调起微信支付

# 安全提醒：
# 1. 请妥善保管API密钥和证书文件
# 2. 生产环境请使用HTTPS
# 3. 定期更新API密钥和证书
# 4. 不要将敏感信息提交到版本控制系统
