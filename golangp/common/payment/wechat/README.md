# 微信支付 SDK

这是一个基于 PowerWeChat SDK 的微信支付 Go 封装库，支持扫码支付、公众号支付、APP 支付和 H5 支付等多种支付方式。

## 功能特性

- ✅ 扫码支付 (NATIVE) - 基于 PowerWeChat SDK 的 TransactionNative
- ✅ 公众号支付 (JSAPI) - 基于 PowerWeChat SDK 的 JSAPITransaction
- 🚧 APP 支付 (APP) - 待开发
- 🚧 H5 支付 (MWEB) - 待开发
- ✅ 支付查询
- ✅ 退款申请
- ✅ 异步通知处理
- ✅ 签名验证 - 基于 PowerWeChat SDK
- ✅ Redis 缓存支持
- ✅ 数据库记录管理
- ✅ 支付统计
- ✅ 回调 URL 验证 - 参考 CallbackVerify
- ✅ 支付通知处理 - 参考 CallbackWXPaymentNotify
- ✅ 交易状态检查 - 参考 CheckTradeStatus

## 依赖

本项目基于以下开源项目：

- [PowerWeChat](https://github.com/ArtisanCloud/PowerWeChat) - 强大的微信开发 SDK
- [GORM](https://gorm.io/) - Go ORM 库
- [Redis](https://github.com/redis/go-redis) - Redis Go 客户端

## 快速开始

### 1. 安装依赖

```bash
go mod tidy
```

### 2. 配置环境变量

复制 `app.env.example` 为 `app.env` 并填入真实的配置值：

```bash
cp app.env.example app.env
```

### 3. 基本使用

```go
package main

import (
    "log"
    "gorm.io/gorm"
    "pointer/golangp/common/payment/wechat"
)

func main() {
    // 配置微信支付
    config := &wechat.Config{
        AppID:          "wx1234567890abcdef",
        MchID:          "1234567890",
        APIKey:         "your_api_key_here",
        APIVersion:     "v3",
        NotifyURL:      "https://your-domain.com/wechat/notify",
        TimeoutExpress: 30,
        Sandbox:        true, // 开发环境
    }

    // 创建服务实例
    service, err := wechat.NewService(config, db)
    if err != nil {
        log.Fatal(err)
    }

    // 创建扫码支付
    req := &wechat.CreatePaymentRequest{
        OutTradeNo:    "ORDER_123456",
        TotalFee:      100, // 1元，单位为分
        Body:          "测试商品",
        PaymentMethod: wechat.PaymentMethodNative,
    }

    resp, err := service.CreatePayment(req)
    if err != nil {
        log.Fatal(err)
    }

    log.Printf("二维码链接: %s", resp.CodeURL)
}
```

## 支付方式

### 扫码支付 (NATIVE)

```go
req := &wechat.CreatePaymentRequest{
    OutTradeNo:    "NATIVE_123456",
    TotalFee:      100,
    Body:          "扫码支付商品",
    PaymentMethod: wechat.PaymentMethodNative,
}

resp, err := service.CreatePayment(req)
// 使用 resp.CodeURL 生成二维码
```

### 公众号支付 (JSAPI)

```go
req := &wechat.CreatePaymentRequest{
    OutTradeNo:    "JSAPI_123456",
    TotalFee:      200,
    Body:          "技术服务费-WECHAT pay",
    PaymentMethod: wechat.PaymentMethodJSAPI,
    OpenID:        "user_openid", // 必需
    Attach:        "user_id::200::30", // 附加数据
}

resp, err := service.CreatePayment(req)
// 使用 resp.PaymentData 中的支付配置调起微信支付
```

### 回调 URL 验证

```go
// 验证微信回调URL（参考 CallbackVerify）
response, err := service.VerifyURL(request)
if err != nil {
    log.Fatal(err)
}
```

### 支付通知处理

```go
// 处理微信支付异步通知（参考 CallbackWXPaymentNotify）
err := service.ProcessPaymentNotify(notifyData)
if err != nil {
    log.Fatal(err)
}
```

### 交易状态检查

```go
// 检查交易状态（参考 CheckTradeStatus）
status, err := service.CheckTradeStatus("ORDER_123456")
if err != nil {
    log.Fatal(err)
}

fmt.Printf("是否已支付: %t\n", status.IsPaid)
fmt.Printf("状态: %s\n", status.Status)
```

### H5 支付 (MWEB)

```go
req := &wechat.CreatePaymentRequest{
    OutTradeNo:    "H5_123456",
    TotalFee:      300,
    Body:          "H5支付商品",
    PaymentMethod: wechat.PaymentMethodH5,
    SceneInfo: &wechat.SceneInfo{
        Type:    "Wap",
        WapURL:  "https://your-domain.com",
        WapName: "您的网站",
    },
}

resp, err := service.CreatePayment(req)
// 跳转到 resp.MWebURL 进行支付
```

## 支付查询

```go
// 普通查询
queryReq := &wechat.QueryPaymentRequest{
    OutTradeNo: "ORDER_123456",
}
queryResp, err := service.QueryPayment(queryReq)

// 带缓存查询（推荐）
ctx := context.Background()
pollingResp, err := service.QueryPaymentWithCache(ctx, "ORDER_123456")
```

## 退款申请

```go
refundReq := &wechat.RefundRequest{
    OutTradeNo:  "ORDER_123456",
    OutRefundNo: "REFUND_123456",
    TotalFee:    100,
    RefundFee:   50, // 退款50分
    RefundDesc:  "用户申请退款",
}

refundResp, err := service.Refund(refundReq)
```

## 异步通知处理

```go
// 在你的HTTP处理器中
func handleWechatNotify(w http.ResponseWriter, r *http.Request) {
    // 解析通知数据
    notifyData := parseNotifyData(r) // 你需要实现这个函数

    // 验证签名
    isValid, err := service.VerifyNotify(notifyData)
    if err != nil || !isValid {
        w.WriteHeader(http.StatusBadRequest)
        return
    }

    // 处理通知
    err = service.ProcessNotify(notifyData)
    if err != nil {
        w.WriteHeader(http.StatusInternalServerError)
        return
    }

    // 返回成功响应
    w.Write([]byte("SUCCESS"))
}
```

## Redis 缓存支持

```go
import "github.com/redis/go-redis/v9"

// 创建Redis客户端
rdb := redis.NewClient(&redis.Options{
    Addr: "localhost:6379",
})

// 创建带缓存的服务
service, err := wechat.NewServiceWithRedis(config, db, rdb)
```

## 数据库模型

SDK 会自动创建以下数据表：

- `wechat_payment_records` - 支付记录
- `wechat_refund_records` - 退款记录
- `wechat_notify_records` - 异步通知记录
- `wechat_payment_statistics` - 支付统计

确保在使用前运行数据库迁移：

```go
db.AutoMigrate(
    &wechat.PaymentRecord{},
    &wechat.RefundRecord{},
    &wechat.NotifyRecord{},
    &wechat.PaymentStatistics{},
)
```

## 配置说明

| 配置项         | 说明                    | 必需    |
| -------------- | ----------------------- | ------- |
| AppID          | 微信公众号/小程序 AppID | ✅      |
| MchID          | 微信支付商户号          | ✅      |
| APIKey         | 商户 API 密钥           | ✅      |
| APIVersion     | API 版本(v2/v3)         | ✅      |
| CertFile       | 商户证书文件(v3)        | v3 必需 |
| KeyFile        | 商户私钥文件(v3)        | v3 必需 |
| SerialNo       | 证书序列号(v3)          | v3 必需 |
| NotifyURL      | 异步通知地址            | ✅      |
| ReturnURL      | 同步回调地址            | ❌      |
| TimeoutExpress | 订单超时时间(分钟)      | ❌      |
| Sandbox        | 沙箱模式                | ❌      |

## 错误处理

```go
resp, err := service.CreatePayment(req)
if err != nil {
    switch {
    case strings.Contains(err.Error(), "order number"):
        // 订单号重复
    case strings.Contains(err.Error(), "invalid"):
        // 参数无效
    default:
        // 其他错误
    }
}
```

## 最佳实践

1. **生产环境配置**

   - 使用 HTTPS
   - 关闭沙箱模式
   - 妥善保管证书和密钥

2. **缓存使用**

   - 推荐使用 Redis 缓存
   - 定期清理过期缓存

3. **异步通知**

   - 验证签名
   - 幂等性处理
   - 及时响应微信

4. **错误处理**
   - 记录详细日志
   - 实现重试机制
   - 监控支付状态

## 注意事项

- 金额单位为分，不是元
- 订单号需要保证唯一性
- 异步通知需要返回"SUCCESS"
- 证书文件需要定期更新
- 生产环境请关闭调试日志

## 许可证

MIT License
