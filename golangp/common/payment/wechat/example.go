package wechat

import (
	"context"
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
)

// Example 微信支付使用示例
func Example(db *gorm.DB) {
	// 1. 配置微信支付
	config := &Config{
		AppID:          "wx1234567890abcdef",
		MchID:          "1234567890",
		APIKey:         "your_api_key_here",
		APIVersion:     "v3",
		TradeType:      "NATIVE",
		NotifyURL:      "https://your-domain.com/wechat/notify",
		TimeoutExpress: 30,
		Sandbox:        true, // 沙箱模式
	}

	// 2. 创建微信支付服务
	service, err := NewService(config, db)
	if err != nil {
		log.Fatalf("Failed to create wechat service: %v", err)
	}

	// 3. 创建扫码支付
	fmt.Println("=== 创建扫码支付 ===")
	createReq := &CreatePaymentRequest{
		OutTradeNo:     fmt.Sprintf("WX%d", time.Now().Unix()),
		TotalFee:       100, // 1元，单位为分
		Body:           "测试商品",
		Detail:         "这是一个测试商品的详细描述",
		PaymentMethod:  PaymentMethodNative,
		TimeoutExpress: 30,
		Attach:         "test_data",
	}

	createResp, err := service.CreatePayment(createReq)
	if err != nil {
		log.Fatalf("Failed to create payment: %v", err)
	}

	fmt.Printf("支付创建成功:\n")
	fmt.Printf("  订单号: %s\n", createResp.OutTradeNo)
	fmt.Printf("  二维码链接: %s\n", createResp.CodeURL)
	fmt.Printf("  支付数据: %s\n", createResp.PaymentData)

	// 4. 查询支付状态
	fmt.Println("\n=== 查询支付状态 ===")
	queryReq := &QueryPaymentRequest{
		OutTradeNo: createResp.OutTradeNo,
	}

	queryResp, err := service.QueryPayment(queryReq)
	if err != nil {
		log.Printf("Failed to query payment: %v", err)
	} else {
		fmt.Printf("支付状态查询结果:\n")
		fmt.Printf("  订单号: %s\n", queryResp.OutTradeNo)
		fmt.Printf("  支付状态: %s\n", queryResp.TradeState)
		fmt.Printf("  状态描述: %s\n", queryResp.TradeStateDesc)
		fmt.Printf("  订单金额: %d分\n", queryResp.TotalFee)
	}

	// 5. 带缓存的支付状态查询
	fmt.Println("\n=== 带缓存的支付状态查询 ===")
	ctx := context.Background()
	pollingResp, err := service.QueryPaymentWithCache(ctx, createResp.OutTradeNo)
	if err != nil {
		log.Printf("Failed to query payment with cache: %v", err)
	} else {
		fmt.Printf("缓存查询结果:\n")
		fmt.Printf("  订单号: %s\n", pollingResp.OutTradeNo)
		fmt.Printf("  是否已支付: %t\n", pollingResp.IsPaid)
		fmt.Printf("  支付状态: %s\n", pollingResp.Status)
	}

	// 6. 模拟异步通知处理
	fmt.Println("\n=== 模拟异步通知处理 ===")
	notifyData := map[string]string{
		"return_code":    "SUCCESS",
		"return_msg":     "OK",
		"appid":          config.AppID,
		"mch_id":         config.MchID,
		"nonce_str":      "random_string",
		"result_code":    "SUCCESS",
		"openid":         "user_openid_123",
		"trade_type":     "NATIVE",
		"bank_type":      "CMC",
		"total_fee":      "100",
		"cash_fee":       "100",
		"transaction_id": "wx_transaction_id_123",
		"out_trade_no":   createResp.OutTradeNo,
		"time_end":       time.Now().Format("**************"),
	}

	// 生成签名
	service.generateSign(notifyData)
	notifyData["sign"] = service.generateSign(notifyData)

	// 验证通知
	isValid, err := service.VerifyNotify(notifyData)
	if err != nil {
		log.Printf("Failed to verify notify: %v", err)
	} else {
		fmt.Printf("通知验证结果: %t\n", isValid)
	}

	// 处理通知
	if isValid {
		err = service.ProcessNotify(notifyData)
		if err != nil {
			log.Printf("Failed to process notify: %v", err)
		} else {
			fmt.Printf("通知处理成功\n")
		}
	}

	// 7. 申请退款（需要先有成功的支付）
	fmt.Println("\n=== 申请退款 ===")
	refundReq := &RefundRequest{
		OutTradeNo:  createResp.OutTradeNo,
		OutRefundNo: fmt.Sprintf("RF%d", time.Now().Unix()),
		TotalFee:    100,
		RefundFee:   50, // 退款50分
		RefundDesc:  "用户申请退款",
	}

	refundResp, err := service.Refund(refundReq)
	if err != nil {
		log.Printf("Failed to refund: %v", err)
	} else {
		fmt.Printf("退款申请成功:\n")
		fmt.Printf("  退款单号: %s\n", refundResp.OutRefundNo)
		fmt.Printf("  退款金额: %d分\n", refundResp.RefundFee)
		fmt.Printf("  退款状态: %s\n", refundResp.RefundStatus)
	}
}

// ExampleNativePayment 扫码支付示例
func ExampleNativePayment(db *gorm.DB) {
	config := &Config{
		AppID:          "wx1234567890abcdef",
		MchID:          "1234567890",
		APIKey:         "your_api_key_here",
		APIVersion:     "v3",
		NotifyURL:      "https://your-domain.com/wechat/notify",
		TimeoutExpress: 30,
		Sandbox:        true,
	}

	service, err := NewService(config, db)
	if err != nil {
		log.Fatalf("Failed to create service: %v", err)
	}

	// 创建扫码支付
	req := &CreatePaymentRequest{
		OutTradeNo:    fmt.Sprintf("NATIVE_%d", time.Now().Unix()),
		TotalFee:      100, // 1元
		Body:          "扫码支付测试商品",
		PaymentMethod: PaymentMethodNative,
	}

	resp, err := service.CreatePayment(req)
	if err != nil {
		log.Fatalf("Failed to create native payment: %v", err)
	}

	fmt.Printf("扫码支付创建成功:\n")
	fmt.Printf("二维码链接: %s\n", resp.CodeURL)
	fmt.Printf("请使用微信扫描二维码进行支付\n")
}

// ExampleAdvancedNativePayment 高级扫码支付示例
func ExampleAdvancedNativePayment(db *gorm.DB) {
	config := &Config{
		AppID:          "wx1234567890abcdef",
		MchID:          "1234567890",
		APIKey:         "your_api_key_here",
		APIVersion:     "v3",
		NotifyURL:      "https://your-domain.com/wechat/notify",
		TimeoutExpress: 30,
		Sandbox:        true,
	}

	service, err := NewService(config, db)
	if err != nil {
		log.Fatalf("Failed to create service: %v", err)
	}

	// 创建扫码支付（带详细信息）
	req := &CreatePaymentRequest{
		OutTradeNo:     fmt.Sprintf("ADV_NATIVE_%d", time.Now().Unix()),
		TotalFee:       500, // 5元
		Body:           "高级扫码支付测试商品",
		Detail:         "这是一个包含详细信息的测试商品",
		PaymentMethod:  PaymentMethodNative,
		TimeoutExpress: 15, // 15分钟过期
		Attach:         "advanced_test_data",
		GoodsTag:       "premium",
	}

	resp, err := service.CreatePayment(req)
	if err != nil {
		log.Fatalf("Failed to create advanced native payment: %v", err)
	}

	fmt.Printf("高级扫码支付创建成功:\n")
	fmt.Printf("订单号: %s\n", resp.OutTradeNo)
	fmt.Printf("二维码链接: %s\n", resp.CodeURL)
	fmt.Printf("支付数据: %s\n", resp.PaymentData)
	fmt.Printf("请使用微信扫描二维码进行支付\n")
}

// ExampleJSAPIPayment JSAPI支付示例（参考真实的 APIMakeOrder 代码）
func ExampleJSAPIPayment(db *gorm.DB) {
	config := &Config{
		AppID:          "wx1234567890abcdef",
		MchID:          "1234567890",
		APIKey:         "your_api_key_here",
		APIVersion:     "v3",
		NotifyURL:      "https://your-domain.com/wechat/notify",
		TimeoutExpress: 30,
		Sandbox:        true,
	}

	service, err := NewService(config, db)
	if err != nil {
		log.Fatalf("Failed to create service: %v", err)
	}

	// 创建公众号支付（参考真实的 APIMakeOrder 代码）
	req := &CreatePaymentRequest{
		OutTradeNo:    fmt.Sprintf("JSAPI_%d", time.Now().Unix()),
		TotalFee:      200, // 2元
		Body:          "技术服务费-WECHAT pay",
		PaymentMethod: PaymentMethodJSAPI,
		OpenID:        "user_openid_from_wechat", // 用户的OpenID，必需
		Attach:        "user_id::200::30",        // 附加数据：用户ID::价格::额外天数
	}

	resp, err := service.CreatePayment(req)
	if err != nil {
		log.Fatalf("Failed to create JSAPI payment: %v", err)
	}

	fmt.Printf("公众号支付创建成功:\n")
	fmt.Printf("订单号: %s\n", resp.OutTradeNo)
	fmt.Printf("支付配置: %s\n", resp.PaymentData)
	fmt.Printf("请在微信公众号中调用支付接口\n")
}

// ExampleCallbackVerify 微信回调验证示例（参考 CallbackVerify）
func ExampleCallbackVerify(db *gorm.DB) {
	config := &Config{
		AppID:      "wx1234567890abcdef",
		MchID:      "1234567890",
		APIKey:     "your_api_key_here",
		APIVersion: "v3",
		NotifyURL:  "https://your-domain.com/wechat/notify",
		Sandbox:    true,
	}

	service, err := NewService(config, db)
	if err != nil {
		log.Fatalf("Failed to create service: %v", err)
	}

	// 验证微信回调URL
	response, err := service.VerifyURL(nil) // 这里应该传入实际的 HTTP 请求
	if err != nil {
		log.Fatalf("Failed to verify URL: %v", err)
	}

	fmt.Printf("回调验证成功: %v\n", response)
}

// ExamplePaymentNotify 支付通知处理示例（参考 CallbackWXPaymentNotify）
func ExamplePaymentNotify(db *gorm.DB) {
	config := &Config{
		AppID:      "wx1234567890abcdef",
		MchID:      "1234567890",
		APIKey:     "your_api_key_here",
		APIVersion: "v3",
		NotifyURL:  "https://your-domain.com/wechat/notify",
		Sandbox:    true,
	}

	service, err := NewService(config, db)
	if err != nil {
		log.Fatalf("Failed to create service: %v", err)
	}

	// 处理支付通知
	err = service.ProcessPaymentNotify(nil) // 这里应该传入实际的通知数据
	if err != nil {
		log.Fatalf("Failed to process payment notify: %v", err)
	}

	fmt.Printf("支付通知处理成功\n")
}

// ExampleCheckTradeStatus 检查交易状态示例（参考 CheckTradeStatus）
func ExampleCheckTradeStatus(db *gorm.DB) {
	config := &Config{
		AppID:      "wx1234567890abcdef",
		MchID:      "1234567890",
		APIKey:     "your_api_key_here",
		APIVersion: "v3",
		NotifyURL:  "https://your-domain.com/wechat/notify",
		Sandbox:    true,
	}

	service, err := NewService(config, db)
	if err != nil {
		log.Fatalf("Failed to create service: %v", err)
	}

	// 检查交易状态
	outTradeNo := "ORDER_123456"
	status, err := service.CheckTradeStatus(outTradeNo)
	if err != nil {
		log.Fatalf("Failed to check trade status: %v", err)
	}

	fmt.Printf("交易状态检查结果:\n")
	fmt.Printf("  订单号: %s\n", status.OutTradeNo)
	fmt.Printf("  是否已支付: %t\n", status.IsPaid)
	fmt.Printf("  状态: %s\n", status.Status)
	fmt.Printf("  状态描述: %s\n", status.StatusDesc)
}
