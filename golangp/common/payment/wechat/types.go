package wechat

import (
	"time"
)

// PaymentMethod 支付方式
type PaymentMethod string

const (
	// PaymentMethodNative 扫码支付
	PaymentMethodNative PaymentMethod = "native"
	// PaymentMethodJSAPI 公众号支付
	PaymentMethodJSAPI PaymentMethod = "jsapi"
	// PaymentMethodApp APP支付
	PaymentMethodApp PaymentMethod = "app"
	// PaymentMethodH5 H5支付
	PaymentMethodH5 PaymentMethod = "h5"
	// PaymentMethodMiniProgram 小程序支付
	PaymentMethodMiniProgram PaymentMethod = "miniprogram"
)

// PaymentStatus 支付状态
type PaymentStatus string

const (
	// PaymentStatusNotPay 未支付
	PaymentStatusNotPay PaymentStatus = "NOTPAY"
	// PaymentStatusSuccess 支付成功
	PaymentStatusSuccess PaymentStatus = "SUCCESS"
	// PaymentStatusRefund 转入退款
	PaymentStatusRefund PaymentStatus = "REFUND"
	// PaymentStatusClosed 已关闭
	PaymentStatusClosed PaymentStatus = "CLOSED"
	// PaymentStatusRevoked 已撤销（刷卡支付）
	PaymentStatusRevoked PaymentStatus = "REVOKED"
	// PaymentStatusUserPaying 用户支付中
	PaymentStatusUserPaying PaymentStatus = "USERPAYING"
	// PaymentStatusPayError 支付失败
	PaymentStatusPayError PaymentStatus = "PAYERROR"
)

// RefundStatus 退款状态
type RefundStatus string

const (
	// RefundStatusProcessing 退款处理中
	RefundStatusProcessing RefundStatus = "PROCESSING"
	// RefundStatusSuccess 退款成功
	RefundStatusSuccess RefundStatus = "SUCCESS"
	// RefundStatusClosed 退款关闭
	RefundStatusClosed RefundStatus = "CLOSED"
	// RefundStatusAbnormal 退款异常
	RefundStatusAbnormal RefundStatus = "ABNORMAL"
)

// TradeType 交易类型
type TradeType string

const (
	// TradeTypeJSAPI 公众号支付
	TradeTypeJSAPI TradeType = "JSAPI"
	// TradeTypeNative 扫码支付
	TradeTypeNative TradeType = "NATIVE"
	// TradeTypeApp APP支付
	TradeTypeApp TradeType = "APP"
	// TradeTypeMWeb H5支付
	TradeTypeMWeb TradeType = "MWEB"
)

// CreatePaymentRequest 创建支付请求
type CreatePaymentRequest struct {
	// 商户订单号
	OutTradeNo string `json:"out_trade_no" binding:"required"`

	// 订单总金额，单位为分
	TotalFee int64 `json:"total_fee" binding:"required"`

	// 商品描述
	Body string `json:"body" binding:"required"`

	// 商品详情
	Detail string `json:"detail,omitempty"`

	// 支付方式
	PaymentMethod PaymentMethod `json:"payment_method" binding:"required"`

	// 用户标识（JSAPI支付必填）
	OpenID string `json:"openid,omitempty"`

	// 超时时间（分钟）
	TimeoutExpress int `json:"timeout_express,omitempty"`

	// 商品标签
	GoodsTag string `json:"goods_tag,omitempty"`

	// 指定支付方式
	LimitPay string `json:"limit_pay,omitempty"`

	// 场景信息（H5支付必填）
	SceneInfo *SceneInfo `json:"scene_info,omitempty"`

	// 附加数据
	Attach string `json:"attach,omitempty"`
}

// SceneInfo 场景信息
type SceneInfo struct {
	// 场景类型
	Type string `json:"type"`
	// WAP网站URL地址
	WapURL string `json:"wap_url,omitempty"`
	// WAP网站名
	WapName string `json:"wap_name,omitempty"`
	// IOS移动应用
	IOSInfo *AppInfo `json:"ios_info,omitempty"`
	// 安卓移动应用
	AndroidInfo *AppInfo `json:"android_info,omitempty"`
}

// AppInfo 应用信息
type AppInfo struct {
	// 应用名称
	AppName string `json:"app_name"`
	// Bundle ID
	BundleID string `json:"bundle_id,omitempty"`
	// Package Name
	PackageName string `json:"package_name,omitempty"`
}

// CreatePaymentResponse 创建支付响应
type CreatePaymentResponse struct {
	// 微信支付订单号
	TransactionID string `json:"transaction_id,omitempty"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no"`

	// 支付链接或支付字符串（根据支付方式不同）
	PaymentData string `json:"payment_data"`

	// 二维码链接（扫码支付时）
	CodeURL string `json:"code_url,omitempty"`

	// 预支付交易会话标识
	PrepayID string `json:"prepay_id,omitempty"`

	// 支付跳转链接（H5支付时）
	MWebURL string `json:"mweb_url,omitempty"`
}

// QueryPaymentRequest 查询支付请求
type QueryPaymentRequest struct {
	// 微信支付订单号
	TransactionID string `json:"transaction_id,omitempty"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no,omitempty"`
}

// QueryPaymentResponse 查询支付响应
type QueryPaymentResponse struct {
	// 微信支付订单号
	TransactionID string `json:"transaction_id"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no"`

	// 交易状态
	TradeState PaymentStatus `json:"trade_state"`

	// 交易状态描述
	TradeStateDesc string `json:"trade_state_desc"`

	// 订单总金额
	TotalFee int64 `json:"total_fee"`

	// 现金支付金额
	CashFee int64 `json:"cash_fee"`

	// 交易创建时间
	TimeStart time.Time `json:"time_start"`

	// 交易结束时间
	TimeEnd *time.Time `json:"time_end,omitempty"`

	// 商品描述
	Body string `json:"body"`

	// 附加数据
	Attach string `json:"attach"`
}

// RefundRequest 退款请求
type RefundRequest struct {
	// 微信支付订单号
	TransactionID string `json:"transaction_id,omitempty"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no,omitempty"`

	// 商户退款单号
	OutRefundNo string `json:"out_refund_no" binding:"required"`

	// 订单总金额
	TotalFee int64 `json:"total_fee" binding:"required"`

	// 退款金额
	RefundFee int64 `json:"refund_fee" binding:"required"`

	// 退款原因
	RefundDesc string `json:"refund_desc,omitempty"`

	// 退款资金来源
	RefundAccount string `json:"refund_account,omitempty"`
}

// RefundResponse 退款响应
type RefundResponse struct {
	// 微信支付订单号
	TransactionID string `json:"transaction_id"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no"`

	// 微信退款单号
	RefundID string `json:"refund_id"`

	// 商户退款单号
	OutRefundNo string `json:"out_refund_no"`

	// 退款金额
	RefundFee int64 `json:"refund_fee"`

	// 退款状态
	RefundStatus RefundStatus `json:"refund_status"`

	// 退款成功时间
	SuccessTime *time.Time `json:"success_time,omitempty"`
}

// NotifyRequest 异步通知请求
type NotifyRequest struct {
	// 返回状态码
	ReturnCode string `json:"return_code"`

	// 返回信息
	ReturnMsg string `json:"return_msg"`

	// 应用ID
	AppID string `json:"appid"`

	// 商户号
	MchID string `json:"mch_id"`

	// 设备号
	DeviceInfo string `json:"device_info"`

	// 随机字符串
	NonceStr string `json:"nonce_str"`

	// 签名
	Sign string `json:"sign"`

	// 签名类型
	SignType string `json:"sign_type"`

	// 业务结果
	ResultCode string `json:"result_code"`

	// 错误代码
	ErrCode string `json:"err_code"`

	// 错误代码描述
	ErrCodeDes string `json:"err_code_des"`

	// 用户标识
	OpenID string `json:"openid"`

	// 交易类型
	TradeType string `json:"trade_type"`

	// 付款银行
	BankType string `json:"bank_type"`

	// 订单总金额
	TotalFee int64 `json:"total_fee"`

	// 现金支付金额
	CashFee int64 `json:"cash_fee"`

	// 微信支付订单号
	TransactionID string `json:"transaction_id"`

	// 商户订单号
	OutTradeNo string `json:"out_trade_no"`

	// 商家数据包
	Attach string `json:"attach"`

	// 支付完成时间
	TimeEnd string `json:"time_end"`
}

// PaymentPollingResponse 支付轮询响应
type PaymentPollingResponse struct {
	// 商户订单号
	OutTradeNo string `json:"out_trade_no"`

	// 是否已支付
	IsPaid bool `json:"is_paid"`

	// 支付状态
	Status string `json:"status"`

	// 状态描述
	StatusDesc string `json:"status_desc,omitempty"`
}
