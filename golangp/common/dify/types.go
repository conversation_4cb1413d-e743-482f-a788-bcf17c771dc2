package dify

import "fmt"

// APIError represents an error response from the Dify API
type APIError struct {
	StatusCode int    `json:"status_code"`
	Message    string `json:"message"`
	Code       string `json:"code"`
}

func (e *APIError) Error() string {
	if e.Code != "" {
		return fmt.Sprintf("Dify API error [%d] %s: %s", e.StatusCode, e.Code, e.Message)
	}
	return fmt.Sprintf("Dify API error [%d]: %s", e.StatusCode, e.Message)
}

// ErrorResponse represents the structure of error responses from Dify API
type ErrorResponse struct {
	Message string `json:"message"`
	Code    string `json:"code"`
}

// User represents a user in the conversation
type User struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// Message represents a message in the conversation
type Message struct {
	Role    string `json:"role"` // "user" or "assistant"
	Content string `json:"content"`
}

// ChatRequest represents a chat completion request
type ChatRequest struct {
	Inputs           map[string]interface{} `json:"inputs"`
	Query            string                 `json:"query"`
	ResponseMode     string                 `json:"response_mode"` // "streaming" or "blocking"
	ConversationID   string                 `json:"conversation_id,omitempty"`
	User             string                 `json:"user"`
	Files            []File                 `json:"files,omitempty"`
	AutoGenerateName bool                   `json:"auto_generate_name,omitempty"`
}

// ChatResponse represents a chat completion response
type ChatResponse struct {
	Event          string           `json:"event"`
	TaskID         string           `json:"task_id"`
	ID             string           `json:"id"`
	MessageID      string           `json:"message_id"`
	ConversationID string           `json:"conversation_id"`
	Mode           string           `json:"mode"`
	Answer         string           `json:"answer"`
	Metadata       ResponseMetadata `json:"metadata"`
	CreatedAt      int64            `json:"created_at"`
	Model          string           `json:"model"`
}

// ResponseMetadata represents metadata in chat response
type ResponseMetadata struct {
	Usage UsageInfo `json:"usage"`
}

// UsageInfo represents token usage information
type UsageInfo struct {
	PromptTokens        int    `json:"prompt_tokens"`
	PromptUnitPrice     string `json:"prompt_unit_price"`
	PromptPrice         string `json:"prompt_price"`
	CompletionTokens    int    `json:"completion_tokens"`
	CompletionUnitPrice string `json:"completion_unit_price"`
	CompletionPrice     string `json:"completion_price"`
	TotalTokens         int    `json:"total_tokens"`
	TotalPrice          string `json:"total_price"`
	Currency            string `json:"currency"`
}

// WorkflowRequest represents a workflow execution request
type WorkflowRequest struct {
	Inputs       map[string]interface{} `json:"inputs"`
	ResponseMode string                 `json:"response_mode"` // "streaming" or "blocking"
	User         string                 `json:"user"`          // Changed from User struct to string
	Files        []File                 `json:"files,omitempty"`
}

// WorkflowResponse represents a workflow execution response (blocking mode)
type WorkflowResponse struct {
	WorkflowRunID string       `json:"workflow_run_id"`
	TaskID        string       `json:"task_id"`
	Data          WorkflowData `json:"data"`
}

// WorkflowData represents the detailed workflow execution data
type WorkflowData struct {
	ID          string                 `json:"id"`
	WorkflowID  string                 `json:"workflow_id"`
	Status      string                 `json:"status"` // "running", "succeeded", "failed", "stopped"
	Outputs     map[string]interface{} `json:"outputs,omitempty"`
	Error       string                 `json:"error,omitempty"`
	ElapsedTime float64                `json:"elapsed_time,omitempty"`
	TotalTokens int                    `json:"total_tokens,omitempty"`
	TotalSteps  int                    `json:"total_steps"`
	CreatedAt   int64                  `json:"created_at"`
	FinishedAt  int64                  `json:"finished_at,omitempty"`
}

// WorkflowStreamResponse represents a streaming workflow response
type WorkflowStreamResponse struct {
	Event         string                 `json:"event"`
	TaskID        string                 `json:"task_id"`
	WorkflowRunID string                 `json:"workflow_run_id"`
	Data          map[string]interface{} `json:"data"`
}

// WorkflowStartedEvent represents the workflow_started event
type WorkflowStartedEvent struct {
	ID             string `json:"id"`
	WorkflowID     string `json:"workflow_id"`
	SequenceNumber int    `json:"sequence_number"`
	CreatedAt      int64  `json:"created_at"`
}

// NodeStartedEvent represents the node_started event
type NodeStartedEvent struct {
	ID                string                 `json:"id"`
	NodeID            string                 `json:"node_id"`
	NodeType          string                 `json:"node_type"`
	Title             string                 `json:"title"`
	Index             int                    `json:"index"`
	PredecessorNodeID string                 `json:"predecessor_node_id,omitempty"`
	Inputs            map[string]interface{} `json:"inputs"`
	CreatedAt         int64                  `json:"created_at"`
}

// TextChunkEvent represents the text_chunk event
type TextChunkEvent struct {
	Text                 string   `json:"text"`
	FromVariableSelector []string `json:"from_variable_selector"`
}

// NodeFinishedEvent represents the node_finished event
type NodeFinishedEvent struct {
	ID                string                 `json:"id"`
	NodeID            string                 `json:"node_id"`
	Index             int                    `json:"index"`
	PredecessorNodeID string                 `json:"predecessor_node_id,omitempty"`
	Inputs            map[string]interface{} `json:"inputs"`
	ProcessData       map[string]interface{} `json:"process_data,omitempty"`
	Outputs           map[string]interface{} `json:"outputs,omitempty"`
	Status            string                 `json:"status"`
	Error             string                 `json:"error,omitempty"`
	ElapsedTime       float64                `json:"elapsed_time,omitempty"`
	ExecutionMetadata ExecutionMetadata      `json:"execution_metadata"`
	CreatedAt         int64                  `json:"created_at"`
}

// WorkflowFinishedEvent represents the workflow_finished event
type WorkflowFinishedEvent struct {
	ID          string                 `json:"id"`
	WorkflowID  string                 `json:"workflow_id"`
	Status      string                 `json:"status"`
	Outputs     map[string]interface{} `json:"outputs,omitempty"`
	Error       string                 `json:"error,omitempty"`
	ElapsedTime float64                `json:"elapsed_time,omitempty"`
	TotalTokens int                    `json:"total_tokens,omitempty"`
	TotalSteps  int                    `json:"total_steps"`
	CreatedAt   int64                  `json:"created_at"`
	FinishedAt  int64                  `json:"finished_at,omitempty"`
}

// TTSMessageEvent represents the tts_message event
type TTSMessageEvent struct {
	MessageID string `json:"message_id"`
	Audio     string `json:"audio"`
	CreatedAt int64  `json:"created_at"`
}

// TTSMessageEndEvent represents the tts_message_end event
type TTSMessageEndEvent struct {
	MessageID string `json:"message_id"`
	Audio     string `json:"audio"` // Empty string for end event
	CreatedAt int64  `json:"created_at"`
}

// ExecutionMetadata represents execution metadata
type ExecutionMetadata struct {
	TotalTokens int     `json:"total_tokens,omitempty"`
	TotalPrice  float64 `json:"total_price,omitempty"`
	Currency    string  `json:"currency,omitempty"`
}

// File represents a file attachment
type File struct {
	Type           string `json:"type"`            // "image", "document", etc.
	TransferMethod string `json:"transfer_method"` // "remote_url" or "local_file"
	URL            string `json:"url,omitempty"`
	UploadFileID   string `json:"upload_file_id,omitempty"`
}

// ConversationResponse represents a conversation list response
type ConversationResponse struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Inputs       map[string]interface{} `json:"inputs"`
	Status       string                 `json:"status"`
	Introduction string                 `json:"introduction"`
	CreatedAt    int64                  `json:"created_at"`
	UpdatedAt    int64                  `json:"updated_at"`
}

// MessageResponse represents a message in conversation history
type MessageResponse struct {
	ID             string                 `json:"id"`
	ConversationID string                 `json:"conversation_id"`
	Inputs         map[string]interface{} `json:"inputs"`
	Query          string                 `json:"query"`
	Answer         string                 `json:"answer"`
	MessageFiles   []File                 `json:"message_files"`
	Feedback       map[string]interface{} `json:"feedback"`
	CreatedAt      int64                  `json:"created_at"`
}

// RetrieverResource represents retriever resource information
type RetrieverResource struct {
	Position          int                    `json:"position"`
	DatasetID         string                 `json:"dataset_id"`
	DatasetName       string                 `json:"dataset_name"`
	DocumentID        string                 `json:"document_id"`
	DocumentName      string                 `json:"document_name"`
	DataSourceType    string                 `json:"data_source_type"`
	SegmentID         string                 `json:"segment_id"`
	Score             float64                `json:"score"`
	Hit               map[string]interface{} `json:"hit"`
	Word              string                 `json:"word"`
	SegmentPosition   int                    `json:"segment_position"`
	IndexNodeHash     string                 `json:"index_node_hash"`
	RetrieverFromType string                 `json:"retriever_from"`
}

// MessageFileEvent represents the message_file event
type MessageFileEvent struct {
	ID             string `json:"id"`
	Type           string `json:"type"`       // "image", etc.
	BelongsTo      string `json:"belongs_to"` // "user" or "assistant"
	URL            string `json:"url"`
	ConversationID string `json:"conversation_id"`
}

// MessageEndEvent represents the message_end event
type MessageEndEvent struct {
	TaskID         string             `json:"task_id"`
	MessageID      string             `json:"message_id"`
	ConversationID string             `json:"conversation_id"`
	Metadata       MessageEndMetadata `json:"metadata"`
}

// MessageEndMetadata represents metadata in message_end event
type MessageEndMetadata struct {
	Usage              UsageInfo           `json:"usage"`
	RetrieverResources []RetrieverResource `json:"retriever_resources"`
}

// MessageReplaceEvent represents the message_replace event
type MessageReplaceEvent struct {
	TaskID         string `json:"task_id"`
	MessageID      string `json:"message_id"`
	ConversationID string `json:"conversation_id"`
	Answer         string `json:"answer"`
	CreatedAt      int64  `json:"created_at"`
}

// ErrorEvent represents the error event in streaming
type ErrorEvent struct {
	TaskID    string `json:"task_id"`
	MessageID string `json:"message_id"`
	Status    int    `json:"status"`
	Code      string `json:"code"`
	Message   string `json:"message"`
}

// PingEvent represents the ping event to keep connection alive
type PingEvent struct {
	Event string `json:"event"` // "ping"
}
