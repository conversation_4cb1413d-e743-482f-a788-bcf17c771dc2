package dify

import (
	"bufio"
	"context"
	"encoding/json"
	"strings"
)

// RunWorkflow executes a workflow
func (c *Client) RunWorkflow(ctx context.Context, req WorkflowRequest, apiKey string) (*WorkflowResponse, error) {
	if req.ResponseMode == "" {
		req.ResponseMode = "blocking"
	}

	resp, err := c.doRequest(ctx, "POST", "/workflows/run", req, apiKey)
	if err != nil {
		return nil, err
	}

	var result WorkflowResponse
	if err := c.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// RunWorkflowStream executes a workflow with streaming response
func (c *Client) RunWorkflowStream(ctx context.Context, req WorkflowRequest, apiKey string, callback func(WorkflowStreamResponse) error) error {
	req.ResponseMode = "streaming"

	resp, err := c.doRequest(ctx, "POST", "/workflows/run", req, apiKey)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return c.handleResponse(resp, nil)
	}

	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		line := scanner.Text()
		if !strings.HasPrefix(line, "data: ") {
			continue
		}

		data := strings.TrimPrefix(line, "data: ")
		if data == "[DONE]" {
			break
		}

		// Skip ping events
		if strings.Contains(data, `"event": "ping"`) {
			continue
		}

		var streamResp WorkflowStreamResponse
		if err := json.Unmarshal([]byte(data), &streamResp); err != nil {
			continue // Skip invalid JSON lines
		}

		if err := callback(streamResp); err != nil {
			return err
		}
	}

	return scanner.Err()
}

// StopWorkflow stops a running workflow
func (c *Client) StopWorkflow(ctx context.Context, taskID, userID string, apiKey string) error {
	req := map[string]string{
		"user": userID,
	}

	resp, err := c.doRequest(ctx, "POST", "/workflows/tasks/"+taskID+"/stop", req, apiKey)
	if err != nil {
		return err
	}

	return c.handleResponse(resp, nil)
}

// GetWorkflowResult gets the result of a workflow execution
func (c *Client) GetWorkflowResult(ctx context.Context, workflowRunID string, apiKey string) (*WorkflowResponse, error) {
	resp, err := c.doRequest(ctx, "GET", "/workflows/run/"+workflowRunID, nil, apiKey)
	if err != nil {
		return nil, err
	}

	var result WorkflowResponse
	if err := c.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// ParseWorkflowStartedEvent parses a workflow_started event from stream response
func ParseWorkflowStartedEvent(response WorkflowStreamResponse) (*WorkflowStartedEvent, bool) {
	if response.Event != "workflow_started" {
		return nil, false
	}

	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, false
	}

	var event WorkflowStartedEvent
	if err := json.Unmarshal(dataBytes, &event); err != nil {
		return nil, false
	}

	return &event, true
}

// ParseNodeStartedEvent parses a node_started event from stream response
func ParseNodeStartedEvent(response WorkflowStreamResponse) (*NodeStartedEvent, bool) {
	if response.Event != "node_started" {
		return nil, false
	}

	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, false
	}

	var event NodeStartedEvent
	if err := json.Unmarshal(dataBytes, &event); err != nil {
		return nil, false
	}

	return &event, true
}

// ParseTextChunkEvent parses a text_chunk event from stream response
func ParseTextChunkEvent(response WorkflowStreamResponse) (*TextChunkEvent, bool) {
	if response.Event != "text_chunk" {
		return nil, false
	}

	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, false
	}

	var event TextChunkEvent
	if err := json.Unmarshal(dataBytes, &event); err != nil {
		return nil, false
	}

	return &event, true
}

// ParseNodeFinishedEvent parses a node_finished event from stream response
func ParseNodeFinishedEvent(response WorkflowStreamResponse) (*NodeFinishedEvent, bool) {
	if response.Event != "node_finished" {
		return nil, false
	}

	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, false
	}

	var event NodeFinishedEvent
	if err := json.Unmarshal(dataBytes, &event); err != nil {
		return nil, false
	}

	return &event, true
}

// ParseWorkflowFinishedEvent parses a workflow_finished event from stream response
func ParseWorkflowFinishedEvent(response WorkflowStreamResponse) (*WorkflowFinishedEvent, bool) {
	if response.Event != "workflow_finished" {
		return nil, false
	}

	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, false
	}

	var event WorkflowFinishedEvent
	if err := json.Unmarshal(dataBytes, &event); err != nil {
		return nil, false
	}

	return &event, true
}

// ParseTTSMessageEvent parses a tts_message event from stream response
func ParseTTSMessageEvent(response WorkflowStreamResponse) (*TTSMessageEvent, bool) {
	if response.Event != "tts_message" {
		return nil, false
	}

	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, false
	}

	var event TTSMessageEvent
	if err := json.Unmarshal(dataBytes, &event); err != nil {
		return nil, false
	}

	return &event, true
}

// ParseTTSMessageEndEvent parses a tts_message_end event from stream response
func ParseTTSMessageEndEvent(response WorkflowStreamResponse) (*TTSMessageEndEvent, bool) {
	if response.Event != "tts_message_end" {
		return nil, false
	}

	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, false
	}

	var event TTSMessageEndEvent
	if err := json.Unmarshal(dataBytes, &event); err != nil {
		return nil, false
	}

	return &event, true
}
