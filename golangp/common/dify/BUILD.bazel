load("@rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "dify",
    srcs = [
        "chat.go",
        "client.go",
        "types.go",
        "utils.go",
        "workflow.go",
    ],
    importpath = "pointer/golangp/common/dify",
    visibility = ["//visibility:public"],
)

go_binary(
    name = "dify_test",
    srcs = ["_test/dify_test.go"],
    deps = [
        ":dify",
    ],
)
