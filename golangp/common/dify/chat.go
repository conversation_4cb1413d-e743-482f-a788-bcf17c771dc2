package dify

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"strings"
)

// ChatCompletion sends a chat completion request to Dify API
func (c *Client) ChatCompletion(ctx context.Context, req ChatRequest, apiKey string) (*ChatResponse, error) {
	if req.ResponseMode == "" {
		req.ResponseMode = "blocking"
	}

	resp, err := c.doRequest(ctx, "POST", "/chat-messages", req, apiKey)
	if err != nil {
		return nil, err
	}

	var result ChatResponse
	if err := c.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// ChatCompletionStream sends a streaming chat completion request to Dify API
func (c *Client) ChatCompletionStream(ctx context.Context, req ChatRequest, apiKey string, callback func(ChatResponse) error) error {
	req.ResponseMode = "streaming"

	resp, err := c.doRequest(ctx, "POST", "/chat-messages", req, apiKey)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		return c.handleResponse(resp, nil)
	}

	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		line := scanner.Text()
		if !strings.HasPrefix(line, "data: ") {
			continue
		}

		data := strings.TrimPrefix(line, "data: ")
		if data == "[DONE]" {
			break
		}

		var chatResp ChatResponse
		if err := json.Unmarshal([]byte(data), &chatResp); err != nil {
			continue // Skip invalid JSON lines
		}

		if err := callback(chatResp); err != nil {
			return err
		}
	}

	return scanner.Err()
}

// GetConversations retrieves the conversation list
func (c *Client) GetConversations(ctx context.Context, userID string, limit int, lastID string, apiKey string) ([]ConversationResponse, error) {
	endpoint := fmt.Sprintf("/conversations?user=%s", userID)
	if limit > 0 {
		endpoint += fmt.Sprintf("&limit=%d", limit)
	}
	if lastID != "" {
		endpoint += fmt.Sprintf("&last_id=%s", lastID)
	}

	resp, err := c.doRequest(ctx, "GET", endpoint, nil, apiKey)
	if err != nil {
		return nil, err
	}

	var result struct {
		Data []ConversationResponse `json:"data"`
	}
	if err := c.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return result.Data, nil
}

// GetMessages retrieves messages from a conversation
func (c *Client) GetMessages(ctx context.Context, conversationID, userID string, limit int, lastID string, apiKey string) ([]MessageResponse, error) {
	endpoint := fmt.Sprintf("/messages?conversation_id=%s&user=%s", conversationID, userID)
	if limit > 0 {
		endpoint += fmt.Sprintf("&limit=%d", limit)
	}
	if lastID != "" {
		endpoint += fmt.Sprintf("&last_id=%s", lastID)
	}

	resp, err := c.doRequest(ctx, "GET", endpoint, nil, apiKey)
	if err != nil {
		return nil, err
	}

	var result struct {
		Data []MessageResponse `json:"data"`
	}
	if err := c.handleResponse(resp, &result); err != nil {
		return nil, err
	}

	return result.Data, nil
}

// DeleteConversation deletes a conversation
func (c *Client) DeleteConversation(ctx context.Context, conversationID, userID string, apiKey string) error {
	endpoint := fmt.Sprintf("/conversations/%s", conversationID)

	req := map[string]string{
		"user": userID,
	}

	resp, err := c.doRequest(ctx, "DELETE", endpoint, req, apiKey)
	if err != nil {
		return err
	}

	return c.handleResponse(resp, nil)
}

// RenameConversation renames a conversation
func (c *Client) RenameConversation(ctx context.Context, conversationID, userID, name string, apiKey string) error {
	endpoint := fmt.Sprintf("/conversations/%s", conversationID)

	req := map[string]string{
		"name": name,
		"user": userID,
	}

	resp, err := c.doRequest(ctx, "POST", endpoint, req, apiKey)
	if err != nil {
		return err
	}

	return c.handleResponse(resp, nil)
}
