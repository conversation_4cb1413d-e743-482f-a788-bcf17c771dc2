package dify

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
)

// NewSimpleUser creates a simple User with just an ID
func NewSimpleUser(userID string) User {
	return User{
		ID:   userID,
		Name: userID,
	}
}

// NewUserWithName creates a User with ID and name
func NewUserWithName(userID, userName string) User {
	return User{
		ID:   userID,
		Name: userName,
	}
}

// NewChatRequest creates a simple chat request
func NewChatRequest(query string, user User) ChatRequest {
	return ChatRequest{
		Inputs:       make(map[string]interface{}),
		Query:        query,
		ResponseMode: "blocking",
		User:         user.ID,
	}
}

// NewStreamingChatRequest creates a streaming chat request
func NewStreamingChatRequest(query string, user User) ChatRequest {
	return ChatRequest{
		Inputs:       make(map[string]interface{}),
		Query:        query,
		ResponseMode: "streaming",
		User:         user.ID,
	}
}

// NewWorkflowRequest creates a simple workflow request
func NewWorkflowRequest(inputs map[string]interface{}, user User) WorkflowRequest {
	if inputs == nil {
		inputs = make(map[string]interface{})
	}
	return WorkflowRequest{
		Inputs:       inputs,
		ResponseMode: "blocking",
		User:         user.ID, // Use user ID string
	}
}

// NewStreamingWorkflowRequest creates a streaming workflow request
func NewStreamingWorkflowRequest(inputs map[string]interface{}, user User) WorkflowRequest {
	if inputs == nil {
		inputs = make(map[string]interface{})
	}
	return WorkflowRequest{
		Inputs:       inputs,
		ResponseMode: "streaming",
		User:         user.ID, // Use user ID string
	}
}

// WithFiles adds files to a workflow request
func (r WorkflowRequest) WithImage(fieldName string, uploadFileID string) WorkflowRequest {
	if r.Inputs == nil {
		r.Inputs = make(map[string]interface{})
	}

	// Create file object according to the JSON format
	fileObj := map[string]interface{}{
		"type":            "image",
		"transfer_method": "local_file",
		"upload_file_id":  uploadFileID,
	}

	// Add to existing files array or create new one
	if existingFiles, exists := r.Inputs[fieldName]; exists {
		if filesArray, ok := existingFiles.([]interface{}); ok {
			r.Inputs[fieldName] = append(filesArray, fileObj)
		} else {
			r.Inputs[fieldName] = []interface{}{existingFiles, fileObj}
		}
	} else {
		r.Inputs[fieldName] = []interface{}{fileObj}
	}

	return r
}

// WithImageURL adds an image by URL to a workflow request
func (r WorkflowRequest) WithImageURL(fieldName string, imageURL string) WorkflowRequest {
	if r.Inputs == nil {
		r.Inputs = make(map[string]interface{})
	}

	// Create file object for remote URL
	fileObj := map[string]interface{}{
		"type":            "image",
		"transfer_method": "remote_url",
		"url":             imageURL,
	}

	// Check if the field already exists and is an array
	if existing, exists := r.Inputs[fieldName]; exists {
		if existingArray, ok := existing.([]interface{}); ok {
			// Append to existing array
			r.Inputs[fieldName] = append(existingArray, fileObj)
		} else {
			// Replace with array containing the new file
			r.Inputs[fieldName] = []interface{}{fileObj}
		}
	} else {
		// Create new array with the file
		r.Inputs[fieldName] = []interface{}{fileObj}
	}

	return r
}

// WithStreaming sets workflow request to streaming mode
func (r WorkflowRequest) WithStreaming() WorkflowRequest {
	r.ResponseMode = "streaming"
	return r
}

// WithConversationID adds conversation ID to a chat request
func (r ChatRequest) WithConversationID(conversationID string) ChatRequest {
	r.ConversationID = conversationID
	return r
}

// WithInputs adds inputs to a chat request
func (r ChatRequest) WithInputs(inputs map[string]interface{}) ChatRequest {
	if r.Inputs == nil {
		r.Inputs = make(map[string]interface{})
	}
	for k, v := range inputs {
		r.Inputs[k] = v
	}
	return r
}

// WithFiles adds files to a chat request
func (r ChatRequest) WithFiles(files []File) ChatRequest {
	r.Files = files
	return r
}

// WithAutoGenerateName enables auto generation of conversation name
func (r ChatRequest) WithAutoGenerateName() ChatRequest {
	r.AutoGenerateName = true
	return r
}

// NewChatRequestWithUserID creates a chat request using user ID string directly
func NewChatRequestWithUserID(query string, userID string) ChatRequest {
	return ChatRequest{
		Inputs:       make(map[string]interface{}),
		Query:        query,
		ResponseMode: "blocking",
		User:         userID,
	}
}

// NewStreamingChatRequestWithUserID creates a streaming chat request using user ID string directly
func NewStreamingChatRequestWithUserID(query string, userID string) ChatRequest {
	return ChatRequest{
		Inputs:       make(map[string]interface{}),
		Query:        query,
		ResponseMode: "streaming",
		User:         userID,
	}
}

// UploadFileResponse represents the response from file upload API
type UploadFileResponse struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Size      int    `json:"size"`
	Extension string `json:"extension"`
	MimeType  string `json:"mime_type"`
	CreatedBy string `json:"created_by"`
	CreatedAt int64  `json:"created_at"`
}

// UploadBase64Image uploads a base64 encoded image to Dify and returns the file ID.
func (c *Client) UploadBase64Image(base64Data []byte, filename string, userID string, apiKey string) (string, error) {
	// 创建 buffer 来存储 multipart 表单数据
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 添加文件部分
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return "", fmt.Errorf("failed to create form file: %w", err)
	}
	_, err = part.Write(base64Data)
	if err != nil {
		return "", fmt.Errorf("failed to write image data: %w", err)
	}

	// 添加 user 字段
	err = writer.WriteField("user", userID)
	if err != nil {
		return "", fmt.Errorf("failed to write user field: %w", err)
	}

	// 关闭 writer
	err = writer.Close()
	if err != nil {
		return "", fmt.Errorf("failed to close multipart writer: %w", err)
	}

	// 创建请求
	url := c.baseURL + "/files/upload"
	req, err := http.NewRequest("POST", url, &buf)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查状态码
	if resp.StatusCode < 200 || resp.StatusCode > 299 {
		return "", fmt.Errorf("upload failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 解析 JSON 响应
	var uploadResp struct {
		ID string `json:"id"`
	}
	err = json.Unmarshal(body, &uploadResp)
	if err != nil {
		return "", fmt.Errorf("failed to parse response: %w", err)
	}

	return uploadResp.ID, nil
}
