package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"pointer/golangp/common/dify"
)

// TestConfig 测试配置
type TestConfig struct {
	APIKey  string
	BaseURL string
	UserID  string
}

// loadTestConfig 从环境变量加载测试配置
func loadTestConfig() TestConfig {
	return TestConfig{
		APIKey:  getEnvOrDefault("DIFY_API_KEY", ""),
		BaseURL: getEnvOrDefault("DIFY_BASE_URL", "http://dify.corp.sdj.cn/v1"),
		UserID:  getEnvOrDefault("DIFY_TEST_USER_ID", "test_user_123"),
	}
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// createTestClient 创建测试客户端
func createTestClient(testConfig TestConfig) *dify.Client {
	config := dify.Config{
		BaseURL:   testConfig.BaseURL,
		APIKey:    testConfig.APIKey,
		Timeout:   30 * time.Second,
		UserAgent: "DifyTestClient/1.0",
	}
	return dify.NewClient(config)
}

func testWorkflow(client *dify.Client, userID string) error {
	fmt.Println("\n🧪 测试工作流功能...")

	ctx := context.Background()
	user := dify.NewSimpleUser(userID)

	inputs := map[string]interface{}{
		"query": "这是一个测试文本",
	}

	req := dify.NewWorkflowRequest(inputs, user)

	response, err := client.RunWorkflow(ctx, req)
	if err != nil {
		// 工作流可能不可用，这是正常的
		fmt.Printf("⚠️  工作流测试跳过 (可能未配置): %v\n", err)
		return nil
	}

	fmt.Printf("✅ 工作流执行成功\n")
	fmt.Printf("   状态: %s\n", response.Data.Status)
	fmt.Printf("   工作流ID: %s\n", response.WorkflowRunID)

	// 获取outputs中的text字段
	if outputs, ok := response.Data.Outputs["text"]; ok {
		fmt.Printf("   响应内容: %v\n", outputs)
	} else {
		fmt.Printf("   所有输出: %v\n", response.Data.Outputs)
	}

	return nil
}

// testConfigCreation 测试配置创建
func testConfigCreation() error {
	fmt.Println("\n🧪 测试配置创建...")

	// 测试默认配置
	defaultConfig := dify.NewDefaultConfig()
	if defaultConfig.BaseURL != "https://api.dify.ai/v1" {
		return fmt.Errorf("默认BaseURL不正确: %s", defaultConfig.BaseURL)
	}
	if defaultConfig.Timeout != 30*time.Second {
		return fmt.Errorf("默认超时时间不正确: %v", defaultConfig.Timeout)
	}

	fmt.Printf("✅ 默认配置创建正确\n")
	fmt.Printf("   BaseURL: %s\n", defaultConfig.BaseURL)
	fmt.Printf("   Timeout: %v\n", defaultConfig.Timeout)
	fmt.Printf("   UserAgent: %s\n", defaultConfig.UserAgent)

	// 测试自定义配置
	customConfig := dify.Config{
		BaseURL:   "https://custom.example.com/v1",
		APIKey:    "custom-key",
		Timeout:   60 * time.Second,
		UserAgent: "CustomApp/2.0",
	}

	client := dify.NewClient(customConfig)
	if client == nil {
		return fmt.Errorf("客户端创建失败")
	}

	fmt.Printf("✅ 自定义配置创建正确\n")

	return nil
}

// runTests 运行所有测试
func runTests() error {
	fmt.Println("🚀 开始运行 Dify 模块测试...")

	// 加载测试配置
	testConfig := loadTestConfig()

	// 检查API密钥
	if testConfig.APIKey == "" {
		fmt.Println("⚠️  警告: 未设置 DIFY_API_KEY 环境变量，将跳过需要API调用的测试")
		fmt.Println("   设置方法: export DIFY_API_KEY=your-api-key")

		// 只运行不需要API调用的测试
		if err := testConfigCreation(); err != nil {
			return err
		}
		fmt.Println("\n✅ 基本测试完成")
		return nil
	}

	// 创建客户端
	client := createTestClient(testConfig)

	// 运行所有测试
	tests := []struct {
		name string
		fn   func() error
	}{
		{"配置创建", testConfigCreation},
		{"工作流", func() error { return testWorkflow(client, testConfig.UserID) }},
	}

	for _, test := range tests {
		if err := test.fn(); err != nil {
			return fmt.Errorf("测试 '%s' 失败: %v", test.name, err)
		}
	}

	fmt.Println("\n🎉 所有测试完成!")
	return nil
}

func main() {
	// 运行测试
	if err := runTests(); err != nil {
		log.Fatalf("❌ 测试失败: %v", err)
	}
}
