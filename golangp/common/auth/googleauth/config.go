/*
 * @Description: Google ID Token verification for authentication
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package googleauth

// Config holds Google ID Token verification configuration
type Config struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
}

// NewConfig creates a new Google ID Token verification configuration
func NewConfig(clientID string, clientSecret string) *Config {
	return &Config{
		ClientID:     clientID,
		ClientSecret: clientSecret,
	}
}
