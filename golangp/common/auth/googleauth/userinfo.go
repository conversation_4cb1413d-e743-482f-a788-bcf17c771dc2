/*
 * @Description: Google ID Token verification and user information extraction
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package googleauth

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"google.golang.org/api/idtoken"
)

// UserInfo represents Google user information from ID Token
type UserInfo struct {
	ID            string `json:"sub"`            // Subject (user ID)
	Email         string `json:"email"`          // Email address
	VerifiedEmail bool   `json:"email_verified"` // Email verification status
	Name          string `json:"name"`           // Full name
	GivenName     string `json:"given_name"`     // First name
	FamilyName    string `json:"family_name"`    // Last name
	Picture       string `json:"picture"`        // Profile picture URL
	Locale        string `json:"locale"`         // User locale
	HostedDomain  string `json:"hd"`             // Hosted domain (for G Suite users)
}

// VerifyIDToken verifies Google ID Token and extracts user information
func VerifyIDToken(ctx context.Context, idToken string, clientID string) (*UserInfo, error) {
	// Verify the ID token
	payload, err := idtoken.Validate(ctx, idToken, clientID)
	if err != nil {
		return nil, fmt.Errorf("failed to verify ID token: %w", err)
	}

	// Extract user information from payload
	userInfo := &UserInfo{}

	if sub, ok := payload.Claims["sub"].(string); ok {
		userInfo.ID = sub
	}

	if email, ok := payload.Claims["email"].(string); ok {
		userInfo.Email = email
	}

	if emailVerified, ok := payload.Claims["email_verified"].(bool); ok {
		userInfo.VerifiedEmail = emailVerified
	}

	if name, ok := payload.Claims["name"].(string); ok {
		userInfo.Name = name
	}

	if givenName, ok := payload.Claims["given_name"].(string); ok {
		userInfo.GivenName = givenName
	}

	if familyName, ok := payload.Claims["family_name"].(string); ok {
		userInfo.FamilyName = familyName
	}

	if picture, ok := payload.Claims["picture"].(string); ok {
		userInfo.Picture = picture
	}

	if locale, ok := payload.Claims["locale"].(string); ok {
		userInfo.Locale = locale
	}

	if hd, ok := payload.Claims["hd"].(string); ok {
		userInfo.HostedDomain = hd
	}

	return userInfo, nil
}

// ExchangeAuthCode exchanges an authorization code for tokens
func ExchangeAuthCode(ctx context.Context, code string, clientID string, clientSecret string, redirectURI string) (string, error) {
	// Set up the token exchange request
	data := url.Values{}
	data.Set("code", code)
	data.Set("client_id", clientID)
	data.Set("client_secret", clientSecret)
	data.Set("redirect_uri", redirectURI)
	data.Set("grant_type", "authorization_code")

	// Make the request to Google's token endpoint
	req, err := http.NewRequestWithContext(ctx, "POST", "https://oauth2.googleapis.com/token", strings.NewReader(data.Encode()))
	if err != nil {
		return "", fmt.Errorf("failed to create token request: %w", err)
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to exchange code: %w", err)
	}
	defer resp.Body.Close()

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response: %w", err)
	}

	// Check for error response
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("token exchange failed: %s", string(body))
	}

	// Parse the response
	var tokenResponse struct {
		IDToken      string `json:"id_token"`
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
		ExpiresIn    int    `json:"expires_in"`
	}
	if err := json.Unmarshal(body, &tokenResponse); err != nil {
		return "", fmt.Errorf("failed to parse token response: %w", err)
	}

	// Return the ID token
	return tokenResponse.IDToken, nil
}
