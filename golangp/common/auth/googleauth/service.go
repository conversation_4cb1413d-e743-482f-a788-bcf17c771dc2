/*
 * @Description: Google ID Token verification service
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package googleauth

import (
	"context"
	"fmt"
	"time"
)

// Service provides Google ID Token verification functionality
type Service struct {
	config *Config
}

// NewService creates a new Google ID Token verification service
func NewService(config *Config) *Service {
	return &Service{
		config: config,
	}
}

// VerifyIDToken verifies Google ID Token and returns user information
func (s *Service) VerifyIDToken(ctx context.Context, idToken string) (*UserInfo, error) {
	userInfo, err := VerifyIDToken(ctx, idToken, s.config.ClientID)
	if err != nil {
		return nil, fmt.Errorf("failed to verify ID token: %w", err)
	}

	return userInfo, nil
}

// ExchangeAuthCode exchanges an authorization code for an ID token
func (s *Service) ExchangeAuthCode(ctx context.Context, code string, redirectURI string) (string, error) {
	idToken, err := ExchangeAuthCode(ctx, code, s.config.ClientID, s.config.ClientSecret, redirectURI)
	if err != nil {
		return "", fmt.Errorf("failed to exchange auth code: %w", err)
	}

	return idToken, nil
}

// AuthResult represents the result of authentication
type AuthResult struct {
	UserInfo  *UserInfo `json:"user_info"`
	IsNewUser bool      `json:"is_new_user"`
	LoginTime time.Time `json:"login_time"`
}

// CreateAuthResult creates a new authentication result
func CreateAuthResult(userInfo *UserInfo, isNewUser bool) *AuthResult {
	return &AuthResult{
		UserInfo:  userInfo,
		IsNewUser: isNewUser,
		LoginTime: time.Now(),
	}
}
