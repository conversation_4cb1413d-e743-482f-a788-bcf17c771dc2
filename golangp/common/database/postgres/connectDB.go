/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-15 11:17:55
 */
package postgres

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var DB *gorm.DB

func ConnectDB(config *Config) {
	var err error
	// Enhanced DSN with explicit UTF-8 encoding settings
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s TimeZone=Asia/Shanghai client_encoding=UTF8 application_name=geok_center search_path=public",
		config.DBHost, config.DBUserName, config.DBPassword, config.DBName, config.DBPort, config.DBSslMode)

	// Configure GORM with proper settings for UTF-8
	DB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: true,
	})
	if err != nil {
		log.Fatal("Failed to connect to the Database")
	}

	// Set connection charset to UTF8 to ensure proper encoding
	sqlDB, err := DB.DB()
	if err != nil {
		log.Fatal("Failed to get underlying sql.DB")
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)

	// Execute SET commands to ensure UTF8 encoding and proper locale
	commands := []string{
		"SET client_encoding TO 'UTF8'",
		"SET standard_conforming_strings TO on",
		"SET check_function_bodies TO false",
		"SET client_min_messages TO warning",
		"SET escape_string_warning TO off",
		"SET row_security TO off",
	}

	for _, cmd := range commands {
		if _, err := sqlDB.Exec(cmd); err != nil {
			log.Printf("Warning: Failed to execute %s: %v", cmd, err)
		}
	}

	fmt.Println("🚀 Connected Successfully to the Database")
}
