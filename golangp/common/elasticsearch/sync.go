package elasticsearch

import (
	"context"
	"time"

	"github.com/olivere/elastic/v7"
)

// SyncOptions represents options for index synchronization
type SyncOptions struct {
	BatchSize     int
	ScrollTimeout time.Duration
	MaxRetries    int
}

// DefaultSyncOptions returns default synchronization options
func DefaultSyncOptions() *SyncOptions {
	return &SyncOptions{
		BatchSize:     1000,
		ScrollTimeout: 5 * time.Minute,
		MaxRetries:    3,
	}
}

// SyncIndex synchronizes data from source index to target index
func (c *Client) SyncIndex(ctx context.Context, sourceIndex, targetIndex string, options *SyncOptions) error {
	if options == nil {
		options = DefaultSyncOptions()
	}

	// Create a scroll service
	scroll := c.client.Scroll(sourceIndex).
		Size(options.BatchSize).
		Scroll(options.ScrollTimeout.String())

	// Start scrolling
	scrollResult, err := scroll.Do(ctx)
	if err != nil {
		return err
	}

	// Process each batch
	for {
		// Get the hits
		hits := scrollResult.Hits.Hits
		if len(hits) == 0 {
			break
		}

		// Prepare bulk request
		bulk := c.client.Bulk().Index(targetIndex)
		for _, hit := range hits {
			// Add document to bulk request
			bulk.Add(elastic.NewBulkIndexRequest().
				Id(hit.Id).
				Doc(hit.Source))
		}

		// Execute bulk request
		bulkResponse, err := bulk.Do(ctx)
		if err != nil {
			return err
		}

		// Check for errors
		if bulkResponse.Errors {
			return ErrBulkOperationFailed
		}

		// Get next batch
		scrollResult, err = c.client.Scroll().
			ScrollId(scrollResult.ScrollId).
			Scroll(options.ScrollTimeout.String()).
			Do(ctx)
		if err != nil {
			return err
		}
	}

	return nil
}

// Reindex performs reindex operation from source to target index
func (c *Client) Reindex(ctx context.Context, sourceIndex, targetIndex string, options *SyncOptions) error {
	if options == nil {
		options = DefaultSyncOptions()
	}

	// Create reindex service
	reindex := c.client.Reindex().
		SourceIndex(sourceIndex).
		DestinationIndex(targetIndex).
		WaitForCompletion(true).
		Refresh("true")

	// Execute reindex
	_, err := reindex.Do(ctx)
	return err
}

// UpdateByQuery updates documents matching a query
func (c *Client) UpdateByQuery(ctx context.Context, index string, query elastic.Query, script *elastic.Script) error {
	updateByQuery := c.client.UpdateByQuery(index).
		Query(query).
		Script(script).
		Refresh("true")

	_, err := updateByQuery.Do(ctx)
	return err
}

// DeleteByQuery deletes documents matching a query
func (c *Client) DeleteByQuery(ctx context.Context, index string, query elastic.Query) error {
	deleteByQuery := c.client.DeleteByQuery(index).
		Query(query).
		Refresh("true")

	_, err := deleteByQuery.Do(ctx)
	return err
}
