package elasticsearch

import (
	"context"

	"github.com/olivere/elastic/v7"
)

// Config represents Elasticsearch configuration
type Config struct {
	URLs        []string
	Username    string
	Password    string
	Sniff       bool
	HealthCheck bool
}

// Client represents Elasticsearch client
type Client struct {
	client *elastic.Client
	config *Config
}

// NewClient creates a new Elasticsearch client
func NewClient(config *Config) (*Client, error) {
	options := []elastic.ClientOptionFunc{
		elastic.SetURL(config.URLs...),
		elastic.SetSniff(config.Sniff),
		elastic.SetHealthcheck(config.HealthCheck),
	}

	if config.Username != "" && config.Password != "" {
		options = append(options, elastic.SetBasicAuth(config.Username, config.Password))
	}

	client, err := elastic.NewClient(options...)
	if err != nil {
		return nil, err
	}

	return &Client{
		client: client,
		config: config,
	}, nil
}

// GetClient returns the underlying elastic.Client
func (c *Client) GetClient() *elastic.Client {
	return c.client
}

// Ping checks if the Elasticsearch server is available
func (c *Client) Ping(ctx context.Context) error {
	_, _, err := c.client.Ping(c.config.URLs[0]).Do(ctx)
	return err
}

// Close closes the Elasticsearch client
func (c *Client) Close() error {
	if c.client != nil {
		c.client.Stop()
	}
	return nil
}
