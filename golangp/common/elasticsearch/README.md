# Elasticsearch Client Module

这个模块提供了一个简单的 Elasticsearch 客户端封装，支持基本的 CRUD 操作、索引管理和数据同步功能。

## 功能特性

- 客户端连接管理
- 索引操作（创建、删除、检查存在性）
- 索引设置和映射管理
- CRUD 操作（创建、读取、更新、删除文档）
- 数据同步和批量操作
- 错误处理和自定义错误类型

## 安装

确保你的项目中已经添加了以下依赖：

```go
import "github.com/olivere/elastic/v7"
```

## 使用方法

### 1. 创建客户端

```go
config := &elasticsearch.Config{
    URLs:       []string{"http://localhost:9200"},
    Username:   "admin",
    Password:   "admin123",
    Sniff:      false,
    HealthCheck: true,
}

client, err := elasticsearch.NewClient(config)
if err != nil {
    log.Fatalf("Failed to create client: %v", err)
}
defer client.Close()
```

### 2. 索引操作

```go
// 创建索引
settings := &elasticsearch.IndexSettings{
    NumberOfShards:   1,
    NumberOfReplicas: 1,
}

mappings := map[string]interface{}{
    "properties": map[string]interface{}{
        "title": map[string]interface{}{
            "type": "text",
        },
    },
}

err := client.CreateIndex(ctx, "my_index", settings, mappings)

// 检查索引是否存在
exists, err := client.IndexExists(ctx, "my_index")

// 删除索引
err := client.DeleteIndex(ctx, "my_index")
```

### 3. CRUD 操作

```go
// 创建文档
doc := map[string]interface{}{
    "title": "Example Document",
    "content": "This is a test document",
}

err := client.Create(ctx, "my_index", "doc_id", doc)

// 获取文档
doc, err := client.Get(ctx, "my_index", "doc_id")

// 更新文档
update := map[string]interface{}{
    "title": "Updated Title",
}
err := client.Update(ctx, "my_index", "doc_id", update)

// 删除文档
err := client.Delete(ctx, "my_index", "doc_id")
```

### 4. 数据同步

```go
options := &elasticsearch.SyncOptions{
    BatchSize:     1000,
    ScrollTimeout: 5 * time.Minute,
    MaxRetries:    3,
}

// 同步索引数据
err := client.SyncIndex(ctx, "source_index", "target_index", options)

// 重新索引
err := client.Reindex(ctx, "source_index", "target_index", options)
```

## 错误处理

模块定义了以下自定义错误类型：

- `ErrIndexNotAcknowledged`: 索引操作未被确认
- `ErrDocumentNotFound`: 文档不存在
- `ErrBulkOperationFailed`: 批量操作失败

## 配置说明

### Config 结构体

```go
type Config struct {
    URLs       []string // Elasticsearch 服务器地址
    Username   string   // 用户名
    Password   string   // 密码
    Sniff      bool     // 是否启用节点嗅探
    HealthCheck bool    // 是否启用健康检查
}
```

### SyncOptions 结构体

```go
type SyncOptions struct {
    BatchSize     int           // 批量处理大小
    ScrollTimeout time.Duration // 滚动超时时间
    MaxRetries    int          // 最大重试次数
}
```

## 注意事项

1. 确保 Elasticsearch 服务器已启动并可访问
2. 正确配置认证信息（用户名和密码）
3. 在生产环境中适当调整批量操作的大小和超时设置
4. 注意处理错误情况，特别是网络错误和认证失败

## 示例

完整的示例代码可以参考 `golangp/test/common/elasticsearch/elasticsearch_test.go` 文件。 