package elasticsearch

import (
	"context"
	"encoding/json"

	"github.com/olivere/elastic/v7"
)

// Document represents a generic document interface
type Document interface {
	GetID() string
}

// IndexDocument indexes a document
func (c *Client) IndexDocument(ctx context.Context, index string, doc Document) error {
	_, err := c.client.Index().
		Index(index).
		Id(doc.GetID()).
		BodyJson(doc).
		Do(ctx)
	return err
}

// GetDocument retrieves a document by ID
func (c *Client) GetDocument(ctx context.Context, index, id string, result interface{}) error {
	res, err := c.client.Get().
		Index(index).
		Id(id).
		Do(ctx)
	if err != nil {
		return err
	}

	return json.Unmarshal(res.Source, result)
}

// UpdateDocument updates a document
func (c *Client) UpdateDocument(ctx context.Context, index, id string, doc interface{}) error {
	_, err := c.client.Update().
		Index(index).
		Id(id).
		Doc(doc).
		Do(ctx)
	return err
}

// DeleteDocument deletes a document
func (c *Client) DeleteDocument(ctx context.Context, index, id string) error {
	_, err := c.client.Delete().
		Index(index).
		Id(id).
		Do(ctx)
	return err
}

// BulkIndex performs bulk indexing of documents
func (c *Client) BulkIndex(ctx context.Context, index string, docs []Document) error {
	bulk := c.client.Bulk()
	for _, doc := range docs {
		bulk.Add(elastic.NewBulkIndexRequest().
			Index(index).
			Id(doc.GetID()).
			Doc(doc))
	}

	res, err := bulk.Do(ctx)
	if err != nil {
		return err
	}

	if res.Errors {
		return ErrBulkOperationFailed
	}

	return nil
}

// Search performs a search query
func (c *Client) Search(ctx context.Context, index string, query elastic.Query, result interface{}) error {
	res, err := c.client.Search().
		Index(index).
		Query(query).
		Do(ctx)
	if err != nil {
		return err
	}

	return json.Unmarshal(res.Hits.Hits[0].Source, result)
}

// SearchWithPagination performs a search query with pagination
func (c *Client) SearchWithPagination(ctx context.Context, index string, query elastic.Query, from, size int, result interface{}) error {
	res, err := c.client.Search().
		Index(index).
		Query(query).
		From(from).
		Size(size).
		Do(ctx)
	if err != nil {
		return err
	}

	return json.Unmarshal(res.Hits.Hits[0].Source, result)
}

// Count returns the number of documents matching a query
func (c *Client) Count(ctx context.Context, index string, query elastic.Query) (int64, error) {
	return c.client.Count().
		Index(index).
		Query(query).
		Do(ctx)
}
