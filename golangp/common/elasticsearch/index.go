package elasticsearch

import (
	"context"
	"encoding/json"
)

// IndexSettings represents Elasticsearch index settings
type IndexSettings struct {
	NumberOfShards   int                    `json:"number_of_shards,omitempty"`
	NumberOfReplicas int                    `json:"number_of_replicas,omitempty"`
	Analysis         map[string]interface{} `json:"analysis,omitempty"`
}

// CreateIndex creates a new index with the specified settings and mappings
func (c *Client) CreateIndex(ctx context.Context, indexName string, settings *IndexSettings, mappings map[string]interface{}) error {
	createIndex, err := c.client.CreateIndex(indexName).BodyJson(map[string]interface{}{
		"settings": settings,
		"mappings": mappings,
	}).Do(ctx)

	if err != nil {
		return err
	}

	if !createIndex.Acknowledged {
		return ErrIndexNotAcknowledged
	}

	return nil
}

// DeleteIndex deletes an existing index
func (c *Client) DeleteIndex(ctx context.Context, indexName string) error {
	deleteIndex, err := c.client.DeleteIndex(indexName).Do(ctx)
	if err != nil {
		return err
	}

	if !deleteIndex.Acknowledged {
		return ErrIndexNotAcknowledged
	}

	return nil
}

// IndexExists checks if an index exists
func (c *Client) IndexExists(ctx context.Context, indexName string) (bool, error) {
	return c.client.IndexExists(indexName).Do(ctx)
}

// GetIndexSettings retrieves the settings of an index
func (c *Client) GetIndexSettings(ctx context.Context, indexName string) (map[string]interface{}, error) {
	settings, err := c.client.IndexGetSettings(indexName).Do(ctx)
	if err != nil {
		return nil, err
	}

	return settings[indexName].Settings, nil
}

// UpdateIndexSettings updates the settings of an existing index
func (c *Client) UpdateIndexSettings(ctx context.Context, indexName string, settings *IndexSettings) error {
	body, err := json.Marshal(settings)
	if err != nil {
		return err
	}

	_, err = c.client.IndexPutSettings(indexName).BodyString(string(body)).Do(ctx)
	return err
}

// GetIndexMappings retrieves the mappings of an index
func (c *Client) GetIndexMappings(ctx context.Context, indexName string) (map[string]interface{}, error) {
	mappings, err := c.client.GetMapping().Index(indexName).Do(ctx)
	if err != nil {
		return nil, err
	}

	if indexMappings, ok := mappings[indexName]; ok {
		return indexMappings.(map[string]interface{}), nil
	}
	return nil, nil
}

// UpdateIndexMappings updates the mappings of an existing index
func (c *Client) UpdateIndexMappings(ctx context.Context, indexName string, mappings map[string]interface{}) error {
	body, err := json.Marshal(mappings)
	if err != nil {
		return err
	}

	_, err = c.client.PutMapping().Index(indexName).BodyString(string(body)).Do(ctx)
	return err
}
