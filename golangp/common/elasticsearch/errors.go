package elasticsearch

import "errors"

var (
	// ErrIndexNotAcknowledged is returned when an index operation is not acknowledged
	ErrIndexNotAcknowledged = errors.New("index operation not acknowledged")

	// ErrBulkOperationFailed is returned when a bulk operation fails
	ErrBulkOperationFailed = errors.New("bulk operation failed")

	// ErrDocumentNotFound is returned when a document is not found
	ErrDocumentNotFound = errors.New("document not found")

	// ErrInvalidQuery is returned when a query is invalid
	ErrInvalidQuery = errors.New("invalid query")

	// ErrInvalidIndex is returned when an index name is invalid
	ErrInvalidIndex = errors.New("invalid index name")

	// ErrInvalidDocument is returned when a document is invalid
	ErrInvalidDocument = errors.New("invalid document")
)
