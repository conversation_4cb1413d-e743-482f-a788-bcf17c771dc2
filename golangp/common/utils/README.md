# Utils Module

这个模块提供了基于Sonyflake的雪花算法实现，用于生成分布式系统中的唯一ID。

## 功能特性

- 基于Sonyflake的雪花算法实现
- 分布式唯一ID生成
- 支持自定义机器ID
- ID解析功能
- 单例模式支持
- 线程安全
- 高性能ID生成

## 安装

确保你的项目中已经添加了以下依赖：

```go
import "github.com/sony/sonyflake"
```

## 使用方法

### 1. 基本使用（实例模式）

```go
import "your-project/golangp/common/utils"

// 创建雪花算法实例
snowflake := utils.NewSnowflake(1) // 机器ID为1

// 生成ID
id, err := snowflake.NextID()
if err != nil {
    log.Fatalf("Failed to generate ID: %v", err)
}

fmt.Printf("Generated ID: %d\n", id)

// 解析ID
parsed := snowflake.ParseID(id)
fmt.Printf("Timestamp: %v\n", parsed["timestamp"])
fmt.Printf("Sequence: %v\n", parsed["sequence"])
fmt.Printf("Machine ID: %v\n", parsed["machineID"])
```

### 2. 单例模式使用

```go
import "your-project/golangp/common/utils"

// 初始化全局雪花算法实例
utils.InitSnowflake(1) // 机器ID为1

// 生成ID
id, err := utils.GenerateID()
if err != nil {
    log.Fatalf("Failed to generate ID: %v", err)
}

fmt.Printf("Generated ID: %d\n", id)

// 解析ID
parsed := utils.ParseSnowflakeID(id)
if parsed != nil {
    fmt.Printf("Timestamp: %v\n", parsed["timestamp"])
    fmt.Printf("Sequence: %v\n", parsed["sequence"])
    fmt.Printf("Machine ID: %v\n", parsed["machineID"])
}

// 获取雪花算法实例
snowflake := utils.GetSnowflake()
if snowflake != nil {
    anotherID, err := snowflake.NextID()
    if err == nil {
        fmt.Printf("Another ID: %d\n", anotherID)
    }
}
```

### 3. 批量ID生成

```go
import (
    "sync"
    "your-project/golangp/common/utils"
)

// 初始化
utils.InitSnowflake(1)

// 并发生成多个ID
var wg sync.WaitGroup
ids := make([]uint64, 0, 1000)
var mutex sync.Mutex

for i := 0; i < 1000; i++ {
    wg.Add(1)
    go func() {
        defer wg.Done()
        
        id, err := utils.GenerateID()
        if err != nil {
            log.Printf("Failed to generate ID: %v", err)
            return
        }
        
        mutex.Lock()
        ids = append(ids, id)
        mutex.Unlock()
    }()
}

wg.Wait()
fmt.Printf("Generated %d unique IDs\n", len(ids))
```

### 4. 多机器部署示例

```go
import (
    "os"
    "strconv"
    "your-project/golangp/common/utils"
)

func main() {
    // 从环境变量获取机器ID
    machineIDStr := os.Getenv("MACHINE_ID")
    if machineIDStr == "" {
        machineIDStr = "1" // 默认机器ID
    }
    
    machineID, err := strconv.ParseUint(machineIDStr, 10, 16)
    if err != nil {
        log.Fatalf("Invalid machine ID: %v", err)
    }
    
    // 初始化雪花算法
    utils.InitSnowflake(uint16(machineID))
    
    // 生成ID
    for i := 0; i < 10; i++ {
        id, err := utils.GenerateID()
        if err != nil {
            log.Printf("Failed to generate ID: %v", err)
            continue
        }
        
        fmt.Printf("ID %d: %d\n", i+1, id)
        
        // 解析ID信息
        parsed := utils.ParseSnowflakeID(id)
        if parsed != nil {
            fmt.Printf("  Machine ID: %v\n", parsed["machineID"])
            fmt.Printf("  Timestamp: %v\n", parsed["timestamp"])
            fmt.Printf("  Sequence: %v\n", parsed["sequence"])
        }
        fmt.Println()
    }
}
```

## API 参考

### Snowflake 结构体

```go
type Snowflake struct {
    sf *sonyflake.Sonyflake
}
```

#### 方法

- `NewSnowflake(machineID uint16) *Snowflake`: 创建新的雪花算法实例
- `NextID() (uint64, error)`: 生成下一个唯一ID
- `ParseID(id uint64) map[string]interface{}`: 解析ID的各个组成部分

### 全局函数

- `InitSnowflake(machineID uint16)`: 初始化全局雪花算法实例
- `GetSnowflake() *Snowflake`: 获取全局雪花算法实例
- `GenerateID() (uint64, error)`: 使用全局实例生成ID
- `ParseSnowflakeID(id uint64) map[string]interface{}`: 使用全局实例解析ID

## Sonyflake ID 结构

Sonyflake生成的ID由以下部分组成：
- **39位时间戳**: 以10毫秒为单位的时间戳
- **8位序列号**: 同一时间戳内的序列号
- **16位机器ID**: 机器标识符

总共63位（最高位为0，确保为正数）。

## 配置说明

### 开始时间

模块使用2025年1月1日作为开始时间戳：
```go
startTime = time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
```

### 机器ID范围

机器ID的有效范围是0-65535（16位无符号整数）。

## 注意事项

1. **机器ID唯一性**: 在分布式系统中，确保每个节点使用不同的机器ID
2. **时钟同步**: 确保各节点的系统时钟同步，避免时钟回拨问题
3. **单例初始化**: 使用单例模式时，确保在应用启动时调用`InitSnowflake`
4. **错误处理**: 生成ID可能失败（如时钟回拨），需要适当处理错误
5. **性能考虑**: Sonyflake在高并发下性能优秀，但仍需注意合理使用

## 性能特点

- **高并发**: 支持高并发ID生成
- **低延迟**: 生成ID的延迟极低
- **有序性**: 在同一机器上生成的ID基本有序
- **唯一性**: 在正确配置的情况下保证全局唯一

## 示例

完整的示例代码：

```go
package main

import (
    "fmt"
    "log"
    "sync"
    "time"
    "your-project/golangp/common/utils"
)

func main() {
    // 初始化雪花算法
    utils.InitSnowflake(1)
    
    fmt.Println("=== 基本ID生成 ===")
    for i := 0; i < 5; i++ {
        id, err := utils.GenerateID()
        if err != nil {
            log.Printf("Error: %v", err)
            continue
        }
        
        fmt.Printf("ID: %d\n", id)
        
        // 解析ID
        parsed := utils.ParseSnowflakeID(id)
        if parsed != nil {
            fmt.Printf("  时间戳: %v\n", parsed["timestamp"])
            fmt.Printf("  序列号: %v\n", parsed["sequence"])
            fmt.Printf("  机器ID: %v\n", parsed["machineID"])
        }
        fmt.Println()
    }
    
    fmt.Println("=== 并发ID生成测试 ===")
    var wg sync.WaitGroup
    idChan := make(chan uint64, 100)
    
    // 启动多个goroutine并发生成ID
    for i := 0; i < 10; i++ {
        wg.Add(1)
        go func(workerID int) {
            defer wg.Done()
            
            for j := 0; j < 10; j++ {
                id, err := utils.GenerateID()
                if err != nil {
                    log.Printf("Worker %d error: %v", workerID, err)
                    continue
                }
                idChan <- id
            }
        }(i)
    }
    
    // 等待所有goroutine完成
    go func() {
        wg.Wait()
        close(idChan)
    }()
    
    // 收集生成的ID
    var ids []uint64
    for id := range idChan {
        ids = append(ids, id)
    }
    
    fmt.Printf("并发生成了 %d 个ID\n", len(ids))
    
    // 检查唯一性
    idMap := make(map[uint64]bool)
    duplicates := 0
    for _, id := range ids {
        if idMap[id] {
            duplicates++
        }
        idMap[id] = true
    }
    
    fmt.Printf("重复ID数量: %d\n", duplicates)
    fmt.Printf("唯一ID数量: %d\n", len(idMap))
}
```
