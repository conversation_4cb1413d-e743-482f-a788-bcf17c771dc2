package utils

import (
	"time"

	"github.com/sony/sonyflake"
)

var (
	// 开始时间戳 (2025-01-01 00:00:00 UTC)
	startTime = time.Date(2025, 1, 1, 0, 0, 0, 0, time.UTC)
)

// Snowflake 雪花算法结构体
type Snowflake struct {
	sf *sonyflake.Sonyflake
}

// NewSnowflake 创建一个新的雪花算法实例
func NewSnowflake(machineID uint16) *Snowflake {
	st := sonyflake.Settings{
		StartTime: startTime,
		MachineID: func() (uint16, error) {
			return machineID, nil
		},
	}

	return &Snowflake{
		sf: sonyflake.NewSonyflake(st),
	}
}

// NextID 生成下一个ID
func (s *Snowflake) NextID() (uint64, error) {
	return s.sf.NextID()
}

// ParseID 解析ID的各个部分
func (s *Snowflake) ParseID(id uint64) map[string]interface{} {
	// Sonyflake 的 ID 结构：
	// 39 bits for time in units of 10 msec
	// 8 bits for a sequence number
	// 16 bits for a machine id
	timeBits := (id >> 24) & 0x7FFFFFFFFF
	sequence := (id >> 16) & 0xFF
	machineID := id & 0xFFFF

	return map[string]interface{}{
		"timestamp": startTime.Add(time.Duration(timeBits) * 10 * time.Millisecond),
		"sequence":  sequence,
		"machineID": machineID,
	}
}
