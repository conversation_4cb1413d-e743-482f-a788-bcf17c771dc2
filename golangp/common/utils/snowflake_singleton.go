package utils

import (
	"sync"
)

var (
	snowflakeInstance *Snowflake
	snowflakeOnce     sync.Once
)

// InitSnowflake 初始化雪花算法实例
func InitSnowflake(machineID uint16) {
	snowflakeOnce.Do(func() {
		snowflakeInstance = NewSnowflake(machineID)
	})
}

// GetSnowflake 获取雪花算法实例
func GetSnowflake() *Snowflake {
	return snowflakeInstance
}

// GenerateID 生成一个新的ID
func GenerateID() (uint64, error) {
	if snowflakeInstance == nil {
		return 0, nil
	}
	return snowflakeInstance.NextID()
}

// ParseSnowflakeID 解析雪花算法生成的ID
func ParseSnowflakeID(id uint64) map[string]interface{} {
	if snowflakeInstance == nil {
		return nil
	}
	return snowflakeInstance.ParseID(id)
}
