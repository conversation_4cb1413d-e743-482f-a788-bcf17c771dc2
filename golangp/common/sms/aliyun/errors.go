/*
 * @Description: SMS error definitions
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
package sms

import "errors"

// SMS service errors
var (
	ErrMissingAccessKeyID     = errors.New("SMS access key ID is required")
	ErrMissingAccessKeySecret = errors.New("SMS access key secret is required")
	ErrMissingSignName        = errors.New("SMS sign name is required")
	ErrMissingTemplateCode    = errors.New("SMS template code is required")
	ErrInvalidPhoneNumber     = errors.New("invalid phone number format")
	ErrEmptyTemplateParams    = errors.New("template parameters cannot be empty")
	ErrSMSClientNotInitialized = errors.New("SMS client is not initialized")
	ErrSendSMSFailed          = errors.New("failed to send SMS")
)
