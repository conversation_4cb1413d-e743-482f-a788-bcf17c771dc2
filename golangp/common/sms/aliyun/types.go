/*
 * @Description: SMS types and structures
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
package sms

import "time"

// SMSRequest represents a SMS sending request
type SMSRequest struct {
	PhoneNumber    string            `json:"phone_number" validate:"required"`    // 手机号码
	TemplateCode   string            `json:"template_code,omitempty"`             // 短信模板代码，可选，使用配置中的默认值
	TemplateParams map[string]string `json:"template_params" validate:"required"` // 模板参数
	SignName       string            `json:"sign_name,omitempty"`                 // 短信签名，可选，使用配置中的默认值
}

// SMSResponse represents a SMS sending response
type SMSResponse struct {
	RequestID string    `json:"request_id"` // 请求ID
	BizID     string    `json:"biz_id"`     // 发送回执ID
	Code      string    `json:"code"`       // 请求状态码
	Message   string    `json:"message"`    // 状态码的描述
	Success   bool      `json:"success"`    // 是否发送成功
	Timestamp time.Time `json:"timestamp"`  // 发送时间
}

// VerificationCodeRequest represents a verification code SMS request
type VerificationCodeRequest struct {
	PhoneNumber string `json:"phone_number" validate:"required"` // 手机号码
	Code        string `json:"code" validate:"required"`         // 验证码
	Purpose     string `json:"purpose,omitempty"`                // 验证码用途（注册、登录、重置密码等）
}

// SMSTemplate represents a SMS template configuration
type SMSTemplate struct {
	Code        string            `json:"code"`        // 模板代码
	Name        string            `json:"name"`        // 模板名称
	Content     string            `json:"content"`     // 模板内容
	ParamNames  []string          `json:"param_names"` // 参数名称列表
	DefaultVars map[string]string `json:"default_vars,omitempty"` // 默认变量值
}

// SMSStats represents SMS sending statistics
type SMSStats struct {
	TotalSent     int64     `json:"total_sent"`     // 总发送数
	SuccessCount  int64     `json:"success_count"`  // 成功发送数
	FailureCount  int64     `json:"failure_count"`  // 失败发送数
	LastSentTime  time.Time `json:"last_sent_time"` // 最后发送时间
	SuccessRate   float64   `json:"success_rate"`   // 成功率
}
