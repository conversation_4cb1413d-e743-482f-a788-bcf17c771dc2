/*
 * @Description: SMS module tests
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
package sms_test

import (
	"testing"

	"pointer/golangp/common/sms/aliyun"
)

// TestConfig tests SMS configuration
func TestConfig(t *testing.T) {
	config := &sms.Config{
		AccessKeyID:     "test_key_id",
		AccessKeySecret: "test_key_secret",
		RegionID:        "cn-hangzhou",
		SignName:        "TestSign",
		TemplateCode:    "SMS_123456789",
		Endpoint:        "dysmsapi.aliyuncs.com",
	}

	// Test SetDefaults
	config.SetDefaults()
	if config.RegionID != "cn-hangzhou" {
		t.Errorf("Expected RegionID to be 'cn-hangzhou', got '%s'", config.RegionID)
	}
	if config.Endpoint != "dysmsapi.aliyuncs.com" {
		t.Errorf("Expected Endpoint to be 'dysmsapi.aliyuncs.com', got '%s'", config.Endpoint)
	}

	// Test Validate
	if err := config.Validate(); err != nil {
		t.<PERSON><PERSON>rf("Expected config to be valid, got error: %v", err)
	}

	// Test validation with missing fields
	invalidConfig := &sms.Config{}
	if err := invalidConfig.Validate(); err == nil {
		t.Error("Expected validation to fail for empty config")
	}
}

// TestGenerateVerificationCode tests verification code generation
func TestGenerateVerificationCode(t *testing.T) {
	// Test default length
	code, err := sms.GenerateVerificationCode(6)
	if err != nil {
		t.Errorf("Failed to generate verification code: %v", err)
	}
	if len(code) != 6 {
		t.Errorf("Expected code length to be 6, got %d", len(code))
	}

	// Test custom length
	code, err = sms.GenerateVerificationCode(4)
	if err != nil {
		t.Errorf("Failed to generate verification code: %v", err)
	}
	if len(code) != 4 {
		t.Errorf("Expected code length to be 4, got %d", len(code))
	}

	// Test zero length (should default to 6)
	code, err = sms.GenerateVerificationCode(0)
	if err != nil {
		t.Errorf("Failed to generate verification code: %v", err)
	}
	if len(code) != 6 {
		t.Errorf("Expected code length to be 6 (default), got %d", len(code))
	}
}

// TestValidatePhoneNumber tests phone number validation
func TestValidatePhoneNumber(t *testing.T) {
	testCases := []struct {
		phoneNumber string
		expected    bool
	}{
		{"13800138000", true},    // Valid Chinese mobile
		{"15912345678", true},    // Valid Chinese mobile
		{"12345678901", false},   // Invalid Chinese mobile
		{"1380013800", false},    // Too short
		{"+8613800138000", true}, // Valid international format
		{"+1234567890123", true}, // Valid international format
		{"", false},              // Empty
		{"abc", false},           // Non-numeric
		{"138 0013 8000", true},  // With spaces (should be cleaned)
		{"138-0013-8000", true},  // With dashes (should be cleaned)
	}

	for _, tc := range testCases {
		result := sms.ValidatePhoneNumber(tc.phoneNumber)
		if result != tc.expected {
			t.Errorf("ValidatePhoneNumber(%s) = %v, expected %v", tc.phoneNumber, result, tc.expected)
		}
	}
}

// TestFormatPhoneNumber tests phone number formatting
func TestFormatPhoneNumber(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"13800138000", "+8613800138000"},
		{"8613800138000", "+8613800138000"},
		{"+8613800138000", "+8613800138000"},
		{"138 0013 8000", "+8613800138000"},
		{"138-0013-8000", "+8613800138000"},
		{"+1234567890", "+1234567890"},
	}

	for _, tc := range testCases {
		result := sms.FormatPhoneNumber(tc.input)
		if result != tc.expected {
			t.Errorf("FormatPhoneNumber(%s) = %s, expected %s", tc.input, result, tc.expected)
		}
	}
}

// TestMaskPhoneNumber tests phone number masking
func TestMaskPhoneNumber(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"13800138000", "138****8000"},
		{"+8613800138000", "+86****00"},
		{"1234567890", "123****90"},
		{"123", "123"},
		{"12", "12****"},
	}

	for _, tc := range testCases {
		result := sms.MaskPhoneNumber(tc.input)
		if result != tc.expected {
			t.Errorf("MaskPhoneNumber(%s) = %s, expected %s", tc.input, result, tc.expected)
		}
	}
}

// TestIsValidTemplateParams tests template parameter validation
func TestIsValidTemplateParams(t *testing.T) {
	testCases := []struct {
		params   map[string]string
		expected bool
	}{
		{map[string]string{"code": "123456"}, true},
		{map[string]string{"code": "123456", "name": "test"}, true},
		{map[string]string{}, false},
		{map[string]string{"": "value"}, false},
		{map[string]string{"key": ""}, false},
		{nil, false},
	}

	for _, tc := range testCases {
		result := sms.IsValidTemplateParams(tc.params)
		if result != tc.expected {
			t.Errorf("IsValidTemplateParams(%v) = %v, expected %v", tc.params, result, tc.expected)
		}
	}
}

// TestBuildTemplateParams tests template parameter building
func TestBuildTemplateParams(t *testing.T) {
	// Test verification scenario
	params := sms.BuildTemplateParams("verification", map[string]string{
		"code": "123456",
	})
	if params["code"] != "123456" {
		t.Errorf("Expected code to be '123456', got '%s'", params["code"])
	}
	if params["expiry"] != "5" {
		t.Errorf("Expected expiry to be '5', got '%s'", params["expiry"])
	}

	// Test welcome scenario
	params = sms.BuildTemplateParams("welcome", map[string]string{
		"name": "John",
	})
	if params["name"] != "John" {
		t.Errorf("Expected name to be 'John', got '%s'", params["name"])
	}

	// Test custom scenario
	params = sms.BuildTemplateParams("custom", map[string]string{
		"key1": "value1",
		"key2": "value2",
	})
	if params["key1"] != "value1" || params["key2"] != "value2" {
		t.Errorf("Custom scenario parameters not copied correctly: %v", params)
	}
}

// TestSanitizeTemplateParam tests template parameter sanitization
func TestSanitizeTemplateParam(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"normal text", "normal text"},
		{"<script>alert('xss')</script>", "scriptalert(xss)/script"},
		{"text with \"quotes\"", "text with quotes"},
		{"  spaced text  ", "spaced text"},
		{"text&amp;entity", "textamp;entity"},
	}

	for _, tc := range testCases {
		result := sms.SanitizeTemplateParam(tc.input)
		if result != tc.expected {
			t.Errorf("SanitizeTemplateParam(%s) = %s, expected %s", tc.input, result, tc.expected)
		}
	}
}

// TestValidateSignName tests sign name validation
func TestValidateSignName(t *testing.T) {
	testCases := []struct {
		signName string
		expected bool
	}{
		{"测试签名", true},
		{"TestSign", true},
		{"Test123", true},
		{"", false},
		{"A", false},                // Too short
		{"VeryLongSignName", false}, // Too long
		{"Test@Sign", false},        // Special characters
	}

	for _, tc := range testCases {
		result := sms.ValidateSignName(tc.signName)
		if result != tc.expected {
			t.Errorf("ValidateSignName(%s) = %v, expected %v", tc.signName, result, tc.expected)
		}
	}
}

// TestValidateTemplateCode tests template code validation
func TestValidateTemplateCode(t *testing.T) {
	testCases := []struct {
		templateCode string
		expected     bool
	}{
		{"SMS_123456789", true},
		{"SMS_000001", true},
		{"", false},
		{"123456789", false},
		{"SMS_", false},
		{"SMS_abc", false},
	}

	for _, tc := range testCases {
		result := sms.ValidateTemplateCode(tc.templateCode)
		if result != tc.expected {
			t.Errorf("ValidateTemplateCode(%s) = %v, expected %v", tc.templateCode, result, tc.expected)
		}
	}
}
