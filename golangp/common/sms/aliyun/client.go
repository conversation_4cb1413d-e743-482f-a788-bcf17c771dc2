/*
 * @Description: Alibaba Cloud SMS client implementation
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
package sms

import (
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"sync"
	"time"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/dysmsapi"
)

// Client represents the SMS client
type Client struct {
	config    *Config
	client    *dysmsapi.Client
	stats     *SMSStats
	statsMux  sync.RWMutex
	templates map[string]*SMSTemplate
}

// NewClient creates a new SMS client
func NewClient(config *Config) (*Client, error) {
	if config == nil {
		return nil, fmt.Errorf("SMS config cannot be nil")
	}

	// Set defaults and validate config
	config.SetDefaults()
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid SMS config: %w", err)
	}

	// Create Alibaba Cloud SMS client
	client, err := dysmsapi.NewClientWithAccessKey(
		config.RegionID,
		config.AccessKeyID,
		config.AccessKeySecret,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create SMS client: %w", err)
	}

	// Set endpoint if specified
	if config.Endpoint != "" {
		client.Domain = config.Endpoint
	}

	return &Client{
		config:    config,
		client:    client,
		stats:     &SMSStats{},
		templates: make(map[string]*SMSTemplate),
	}, nil
}

// NewClientForTesting creates a new SMS client for testing (allows empty sign name)
func NewClientForTesting(config *Config) (*Client, error) {
	if config == nil {
		return nil, fmt.Errorf("SMS config cannot be nil")
	}

	// Set defaults and validate config for testing
	config.SetDefaults()
	if err := config.ValidateForTesting(); err != nil {
		return nil, fmt.Errorf("invalid SMS config: %w", err)
	}

	// Create Alibaba Cloud SMS client
	client, err := dysmsapi.NewClientWithAccessKey(
		config.RegionID,
		config.AccessKeyID,
		config.AccessKeySecret,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create SMS client: %w", err)
	}

	// Set endpoint if specified
	if config.Endpoint != "" {
		client.Domain = config.Endpoint
	}

	return &Client{
		config:    config,
		client:    client,
		stats:     &SMSStats{},
		templates: make(map[string]*SMSTemplate),
	}, nil
}

// SendSMS sends a SMS message
func (c *Client) SendSMS(request *SMSRequest) (*SMSResponse, error) {
	if c.client == nil {
		return nil, ErrSMSClientNotInitialized
	}

	if err := c.validateSMSRequest(request); err != nil {
		return nil, err
	}

	// Use default values from config if not specified in request
	templateCode := request.TemplateCode
	if templateCode == "" {
		templateCode = c.config.TemplateCode
	}

	signName := request.SignName
	if signName == "" {
		signName = c.config.SignName
	}

	// 如果签名仍然为空，给出警告
	if signName == "" {
		log.Println("警告：未设置短信签名，大部分模板都需要签名才能发送成功")
		log.Println("请在阿里云控制台申请短信签名：https://dysms.console.aliyun.com/dysms.htm#/domestic/text/sign")
	}

	// Convert template parameters to JSON string
	templateParamsJSON, err := json.Marshal(request.TemplateParams)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal template parameters: %w", err)
	}

	// Create SMS request
	smsRequest := dysmsapi.CreateSendSmsRequest()
	smsRequest.Scheme = "https"
	smsRequest.PhoneNumbers = request.PhoneNumber
	smsRequest.TemplateCode = templateCode
	smsRequest.TemplateParam = string(templateParamsJSON)

	// 只有当签名不为空时才设置签名
	if signName != "" {
		smsRequest.SignName = signName
	}

	// Send SMS
	response, err := c.client.SendSms(smsRequest)
	if err != nil {
		c.updateStats(false)
		return nil, fmt.Errorf("%w: %v", ErrSendSMSFailed, err)
	}

	// Create response
	smsResponse := &SMSResponse{
		RequestID: response.RequestId,
		BizID:     response.BizId,
		Code:      response.Code,
		Message:   response.Message,
		Success:   response.Code == "OK",
		Timestamp: time.Now(),
	}

	// Update statistics
	c.updateStats(smsResponse.Success)

	if !smsResponse.Success {
		log.Printf("SMS sending failed: Code=%s, Message=%s, RequestID=%s",
			response.Code, response.Message, response.RequestId)
	}

	return smsResponse, nil
}

// SendVerificationCode sends a verification code SMS
func (c *Client) SendVerificationCode(request *VerificationCodeRequest) (*SMSResponse, error) {
	templateParams := map[string]string{
		"code": request.Code,
	}

	// Add purpose if specified
	if request.Purpose != "" {
		templateParams["purpose"] = request.Purpose
	}

	smsRequest := &SMSRequest{
		PhoneNumber:    request.PhoneNumber,
		TemplateParams: templateParams,
	}

	return c.SendSMS(smsRequest)
}

// validateSMSRequest validates the SMS request
func (c *Client) validateSMSRequest(request *SMSRequest) error {
	if request == nil {
		return fmt.Errorf("SMS request cannot be nil")
	}

	if request.PhoneNumber == "" {
		return ErrInvalidPhoneNumber
	}

	// Validate phone number format (basic validation for Chinese mobile numbers)
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if !phoneRegex.MatchString(request.PhoneNumber) {
		return ErrInvalidPhoneNumber
	}

	if len(request.TemplateParams) == 0 {
		return ErrEmptyTemplateParams
	}

	return nil
}

// updateStats updates SMS sending statistics
func (c *Client) updateStats(success bool) {
	c.statsMux.Lock()
	defer c.statsMux.Unlock()

	c.stats.TotalSent++
	c.stats.LastSentTime = time.Now()

	if success {
		c.stats.SuccessCount++
	} else {
		c.stats.FailureCount++
	}

	// Calculate success rate
	if c.stats.TotalSent > 0 {
		c.stats.SuccessRate = float64(c.stats.SuccessCount) / float64(c.stats.TotalSent) * 100
	}
}

// GetStats returns SMS sending statistics
func (c *Client) GetStats() *SMSStats {
	c.statsMux.RLock()
	defer c.statsMux.RUnlock()

	// Return a copy to avoid race conditions
	return &SMSStats{
		TotalSent:    c.stats.TotalSent,
		SuccessCount: c.stats.SuccessCount,
		FailureCount: c.stats.FailureCount,
		LastSentTime: c.stats.LastSentTime,
		SuccessRate:  c.stats.SuccessRate,
	}
}

// AddTemplate adds a SMS template configuration
func (c *Client) AddTemplate(template *SMSTemplate) {
	if template != nil && template.Code != "" {
		c.templates[template.Code] = template
	}
}

// GetTemplate gets a SMS template by code
func (c *Client) GetTemplate(code string) (*SMSTemplate, bool) {
	template, exists := c.templates[code]
	return template, exists
}

// GetConfig returns the SMS configuration
func (c *Client) GetConfig() *Config {
	return c.config
}

// Close closes the SMS client and cleans up resources
func (c *Client) Close() error {
	// Currently, the Alibaba Cloud SDK doesn't require explicit cleanup
	// This method is provided for future compatibility
	return nil
}
