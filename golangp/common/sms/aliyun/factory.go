/*
 * @Description: SMS service factory for creating SMS clients from application config
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
package sms

import (
	"fmt"
	"log"
)

// AppConfig represents the application configuration interface
// This allows the SMS module to work with different config structures
type AppConfig interface {
	GetSMSAccessKeyID() string
	GetSMSAccessKeySecret() string
	GetSMSRegionID() string
	GetSMSSignName() string
	GetSMSTemplateCode() string
	GetSMSEndpoint() string
	IsSMSEnabled() bool
}

// NewClientFromAppConfig creates a new SMS client from application configuration
func NewClientFromAppConfig(appConfig AppConfig) (*Client, error) {
	if appConfig == nil {
		return nil, fmt.Errorf("application config cannot be nil")
	}

	// Check if SMS is enabled
	if !appConfig.IsSMSEnabled() {
		log.Println("SMS service is disabled in configuration")
		return nil, fmt.Errorf("SMS service is disabled")
	}

	// Create SMS configuration from app config
	config := &Config{
		AccessKeyID:     appConfig.GetSMSAccessKeyID(),
		AccessKeySecret: appConfig.GetSMSAccessKeySecret(),
		RegionID:        appConfig.GetSMSRegionID(),
		SignName:        appConfig.GetSMSSignName(),
		TemplateCode:    appConfig.GetSMSTemplateCode(),
		Endpoint:        appConfig.GetSMSEndpoint(),
	}

	// Create and return SMS client
	return NewClient(config)
}

// ConfigAdapter adapts a generic config struct to AppConfig interface
// This is useful when you have a config struct that doesn't implement AppConfig
type ConfigAdapter struct {
	AccessKeyID     string
	AccessKeySecret string
	RegionID        string
	SignName        string
	TemplateCode    string
	Endpoint        string
	Enabled         bool
}

func (c *ConfigAdapter) GetSMSAccessKeyID() string     { return c.AccessKeyID }
func (c *ConfigAdapter) GetSMSAccessKeySecret() string { return c.AccessKeySecret }
func (c *ConfigAdapter) GetSMSRegionID() string        { return c.RegionID }
func (c *ConfigAdapter) GetSMSSignName() string        { return c.SignName }
func (c *ConfigAdapter) GetSMSTemplateCode() string    { return c.TemplateCode }
func (c *ConfigAdapter) GetSMSEndpoint() string        { return c.Endpoint }
func (c *ConfigAdapter) IsSMSEnabled() bool            { return c.Enabled }

// NewConfigAdapter creates a new config adapter
func NewConfigAdapter(accessKeyID, accessKeySecret, regionID, signName, templateCode, endpoint string, enabled bool) *ConfigAdapter {
	return &ConfigAdapter{
		AccessKeyID:     accessKeyID,
		AccessKeySecret: accessKeySecret,
		RegionID:        regionID,
		SignName:        signName,
		TemplateCode:    templateCode,
		Endpoint:        endpoint,
		Enabled:         enabled,
	}
}

// SMSService represents a high-level SMS service
type SMSService struct {
	client    *Client
	enabled   bool
	templates map[string]*SMSTemplate
}

// NewSMSService creates a new SMS service
func NewSMSService(appConfig AppConfig) (*SMSService, error) {
	service := &SMSService{
		enabled:   appConfig.IsSMSEnabled(),
		templates: make(map[string]*SMSTemplate),
	}

	if service.enabled {
		client, err := NewClientFromAppConfig(appConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to create SMS client: %w", err)
		}
		service.client = client
	}

	return service, nil
}

// IsEnabled returns whether SMS service is enabled
func (s *SMSService) IsEnabled() bool {
	return s.enabled
}

// SendSMS sends a SMS message (only if enabled)
func (s *SMSService) SendSMS(request *SMSRequest) (*SMSResponse, error) {
	if !s.enabled {
		log.Println("SMS service is disabled, skipping SMS sending")
		return &SMSResponse{
			Success: false,
			Message: "SMS service is disabled",
		}, nil
	}

	if s.client == nil {
		return nil, ErrSMSClientNotInitialized
	}

	return s.client.SendSMS(request)
}

// SendVerificationCode sends a verification code SMS (only if enabled)
func (s *SMSService) SendVerificationCode(request *VerificationCodeRequest) (*SMSResponse, error) {
	if !s.enabled {
		log.Println("SMS service is disabled, skipping verification code sending")
		return &SMSResponse{
			Success: false,
			Message: "SMS service is disabled",
		}, nil
	}

	if s.client == nil {
		return nil, ErrSMSClientNotInitialized
	}

	return s.client.SendVerificationCode(request)
}

// GetStats returns SMS statistics (only if enabled)
func (s *SMSService) GetStats() *SMSStats {
	if !s.enabled || s.client == nil {
		return &SMSStats{}
	}

	return s.client.GetStats()
}

// AddTemplate adds a SMS template
func (s *SMSService) AddTemplate(template *SMSTemplate) {
	if template != nil && template.Code != "" {
		s.templates[template.Code] = template
		if s.client != nil {
			s.client.AddTemplate(template)
		}
	}
}

// GetTemplate gets a SMS template by code
func (s *SMSService) GetTemplate(code string) (*SMSTemplate, bool) {
	template, exists := s.templates[code]
	return template, exists
}

// Close closes the SMS service
func (s *SMSService) Close() error {
	if s.client != nil {
		return s.client.Close()
	}
	return nil
}
