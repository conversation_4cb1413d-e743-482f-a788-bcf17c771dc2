/*
 * @Description: SMS verification code sending example
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
package main

import (
	"fmt"
	"log"

	sms "pointer/golangp/common/sms/aliyun"
)

func main() {
	fmt.Println("=== SMS Verification Code Example ===")

	// 1. 创建SMS配置
	config := &sms.Config{
		AccessKeyID:     "LTAI5tC1q2jNSxfawJgLQWaj",       // 替换为您的阿里云AccessKey ID
		AccessKeySecret: "******************************", // 替换为您的阿里云AccessKey Secret
		RegionID:        "cn-hangzhou",                    // 地域ID
		SignName:        "",                               // 替换为您的短信签名
		TemplateCode:    "SMS_323760152",                  // 替换为您的短信模板代码
		Endpoint:        "dysmsapi.aliyuncs.com",          // 阿里云短信服务端点
	}

	// 2. 创建SMS客户端
	client, err := sms.NewClient(config)
	if err != nil {
		log.Fatalf("创建SMS客户端失败: %v", err)
	}
	defer client.Close()

	// 注意：如果使用的是赠送模板，可能不需要签名
	if config.SignName == "" {
		fmt.Println("ℹ️  信息：未设置签名，使用赠送模板或无需签名的模板")
	}

	// 3. 目标手机号码
	phoneNumber := "18819597673" // 替换为实际的手机号码

	// 4. 生成6位验证码
	verificationCode, err := sms.GenerateVerificationCode(6)
	if err != nil {
		log.Fatalf("生成验证码失败: %v", err)
	}
	fmt.Printf("生成的验证码: %s\n", verificationCode)

	// 5. 发送验证码短信
	request := &sms.VerificationCodeRequest{
		PhoneNumber: phoneNumber,
		Code:        verificationCode,
		Purpose:     "login", // 验证码用途：登录
	}

	response, err := client.SendVerificationCode(request)
	if err != nil {
		log.Fatalf("发送验证码失败: %v", err)
	}

	// 6. 输出发送结果
	if response.Success {
		fmt.Printf("✅ 验证码发送成功!\n")
		fmt.Printf("   手机号码: %s\n", sms.MaskPhoneNumber(phoneNumber))
		fmt.Printf("   请求ID: %s\n", response.RequestID)
		fmt.Printf("   业务ID: %s\n", response.BizID)
		fmt.Printf("   发送时间: %s\n", response.Timestamp.Format("2006-01-02 15:04:05"))
	} else {
		fmt.Printf("❌ 验证码发送失败!\n")
		fmt.Printf("   错误代码: %s\n", response.Code)
		fmt.Printf("   错误信息: %s\n", response.Message)
		fmt.Printf("   请求ID: %s\n", response.RequestID)
	}

	// 7. 显示发送统计
	stats := client.GetStats()
	fmt.Printf("\n📊 发送统计:\n")
	fmt.Printf("   总发送数: %d\n", stats.TotalSent)
	fmt.Printf("   成功数: %d\n", stats.SuccessCount)
	fmt.Printf("   失败数: %d\n", stats.FailureCount)
	fmt.Printf("   成功率: %.2f%%\n", stats.SuccessRate)
}
