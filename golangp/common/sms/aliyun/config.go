/*
 * @Description: SMS configuration for Alibaba Cloud SMS service
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
package sms

// Config holds the configuration for Alibaba Cloud SMS service
type Config struct {
	AccessKeyID     string `mapstructure:"SMS_ACCESS_KEY_ID"`     // 阿里云AccessKey ID
	AccessKeySecret string `mapstructure:"SMS_ACCESS_KEY_SECRET"` // 阿里云AccessKey Secret
	RegionID        string `mapstructure:"SMS_REGION_ID"`         // 地域ID，默认cn-hangzhou
	SignName        string `mapstructure:"SMS_SIGN_NAME"`         // 短信签名
	TemplateCode    string `mapstructure:"SMS_TEMPLATE_CODE"`     // 短信模板代码
	Endpoint        string `mapstructure:"SMS_ENDPOINT"`          // 服务端点，默认dysmsapi.aliyuncs.com
}

// SetDefaults sets default values for SMS configuration
func (c *Config) SetDefaults() {
	if c.RegionID == "" {
		c.RegionID = "cn-hangzhou"
	}
	if c.Endpoint == "" {
		c.Endpoint = "dysmsapi.aliyuncs.com"
	}
}

// Validate validates the SMS configuration
func (c *Config) Validate() error {
	if c.AccessKeyID == "" {
		return ErrMissingAccessKeyID
	}
	if c.AccessKeySecret == "" {
		return ErrMissingAccessKeySecret
	}
	if c.TemplateCode == "" {
		return ErrMissingTemplateCode
	}
	// 签名现在是可选的，某些模板（如赠送模板）可能不需要签名
	return nil
}

// ValidateForTesting validates the SMS configuration for testing (allows empty sign name)
func (c *Config) ValidateForTesting() error {
	if c.AccessKeyID == "" {
		return ErrMissingAccessKeyID
	}
	if c.AccessKeySecret == "" {
		return ErrMissingAccessKeySecret
	}
	if c.TemplateCode == "" {
		return ErrMissingTemplateCode
	}
	return nil
}
