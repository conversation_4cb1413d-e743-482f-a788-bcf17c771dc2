/*
 * @Description: SMS utility functions
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
package sms

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"regexp"
	"strings"
	"time"
)

// GenerateVerificationCode generates a random verification code
func GenerateVerificationCode(length int) (string, error) {
	if length <= 0 {
		length = 6 // Default length
	}

	const digits = "0123456789"
	code := make([]byte, length)

	for i := range code {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(digits))))
		if err != nil {
			return "", fmt.Errorf("failed to generate verification code: %w", err)
		}
		code[i] = digits[num.Int64()]
	}

	return string(code), nil
}

// ValidatePhoneNumber validates phone number format
func ValidatePhoneNumber(phoneNumber string) bool {
	if phoneNumber == "" {
		return false
	}

	// Remove spaces and dashes
	phoneNumber = strings.ReplaceAll(phoneNumber, " ", "")
	phoneNumber = strings.ReplaceAll(phoneNumber, "-", "")

	// Chinese mobile number validation
	chineseRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	if chineseRegex.MatchString(phoneNumber) {
		return true
	}

	// International number validation (basic)
	internationalRegex := regexp.MustCompile(`^\+\d{10,15}$`)
	if internationalRegex.MatchString(phoneNumber) {
		return true
	}

	return false
}

// FormatPhoneNumber formats phone number to standard format
func FormatPhoneNumber(phoneNumber string) string {
	// Remove all non-digit characters except +
	phoneNumber = regexp.MustCompile(`[^\d+]`).ReplaceAllString(phoneNumber, "")

	// If it's a Chinese number without country code, add +86
	if regexp.MustCompile(`^1[3-9]\d{9}$`).MatchString(phoneNumber) {
		return "+86" + phoneNumber
	}

	// If it starts with 86 but no +, add +
	if regexp.MustCompile(`^86\d{11}$`).MatchString(phoneNumber) {
		return "+" + phoneNumber
	}

	return phoneNumber
}

// MaskPhoneNumber masks phone number for privacy
func MaskPhoneNumber(phoneNumber string) string {
	if len(phoneNumber) < 4 {
		if len(phoneNumber) <= 2 {
			return phoneNumber + "****"
		}
		return phoneNumber
	}

	// For Chinese mobile numbers
	if regexp.MustCompile(`^1[3-9]\d{9}$`).MatchString(phoneNumber) {
		return phoneNumber[:3] + "****" + phoneNumber[7:]
	}

	// For international numbers
	if len(phoneNumber) > 7 {
		return phoneNumber[:3] + "****" + phoneNumber[len(phoneNumber)-2:]
	}

	return phoneNumber[:2] + "****"
}

// IsValidTemplateParams validates template parameters
func IsValidTemplateParams(params map[string]string) bool {
	if len(params) == 0 {
		return false
	}

	for key, value := range params {
		if key == "" || value == "" {
			return false
		}
	}

	return true
}

// BuildTemplateParams builds template parameters for common scenarios
func BuildTemplateParams(scenario string, values map[string]string) map[string]string {
	params := make(map[string]string)

	switch scenario {
	case "verification":
		if code, exists := values["code"]; exists {
			params["code"] = code
		}
		if expiry, exists := values["expiry"]; exists {
			params["expiry"] = expiry
		} else {
			params["expiry"] = "5" // Default 5 minutes
		}

	case "welcome":
		if name, exists := values["name"]; exists {
			params["name"] = name
		}
		if company, exists := values["company"]; exists {
			params["company"] = company
		}

	case "notification":
		if title, exists := values["title"]; exists {
			params["title"] = title
		}
		if content, exists := values["content"]; exists {
			params["content"] = content
		}
		if timeValue, exists := values["time"]; exists {
			params["time"] = timeValue
		} else {
			params["time"] = time.Now().Format("2006-01-02 15:04:05")
		}

	default:
		// Copy all values for custom scenarios
		for k, v := range values {
			params[k] = v
		}
	}

	return params
}

// CalculateRateLimit calculates if rate limit is exceeded
func CalculateRateLimit(lastSentTime time.Time, interval time.Duration) bool {
	return time.Since(lastSentTime) < interval
}

// SanitizeTemplateParam sanitizes template parameter value
func SanitizeTemplateParam(value string) string {
	// Remove potentially dangerous characters
	value = strings.ReplaceAll(value, "<", "")
	value = strings.ReplaceAll(value, ">", "")
	value = strings.ReplaceAll(value, "&", "")
	value = strings.ReplaceAll(value, "\"", "")
	value = strings.ReplaceAll(value, "'", "")

	// Trim whitespace
	value = strings.TrimSpace(value)

	return value
}

// ValidateSignName validates SMS sign name
func ValidateSignName(signName string) bool {
	if signName == "" {
		return false
	}

	// Sign name should be 2-12 characters (counting runes for Unicode support)
	runes := []rune(signName)
	if len(runes) < 2 || len(runes) > 12 {
		return false
	}

	// Should not contain special characters (Chinese characters, letters, numbers only)
	matched, _ := regexp.MatchString(`^[一-龯a-zA-Z0-9]+$`, signName)
	return matched
}

// ValidateTemplateCode validates SMS template code
func ValidateTemplateCode(templateCode string) bool {
	if templateCode == "" {
		return false
	}

	// Template code should match pattern SMS_xxxxxxx
	matched, _ := regexp.MatchString(`^SMS_\d+$`, templateCode)
	return matched
}
