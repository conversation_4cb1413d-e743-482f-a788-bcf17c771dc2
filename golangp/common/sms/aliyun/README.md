# SMS Module

阿里云短信服务模块，提供短信发送功能。

## 功能特性

- 支持阿里云短信服务 API
- 验证码短信发送
- 通知类短信发送
- 手机号码格式验证
- 短信发送统计
- 模板参数验证
- 发送频率限制检查

## 配置

在应用配置中添加以下SMS相关配置：

```env
# 阿里云短信服务配置
SMS_ACCESS_KEY_ID=your_access_key_id
SMS_ACCESS_KEY_SECRET=your_access_key_secret
SMS_REGION_ID=cn-hangzhou
SMS_SIGN_NAME=your_sign_name
SMS_TEMPLATE_CODE=SMS_123456789
SMS_ENDPOINT=dysmsapi.aliyuncs.com
```

## 使用示例

### 基本使用

```go
package main

import (
    "log"
    "pointer/golangp/common/sms"
)

func main() {
    // 创建SMS配置
    config := &sms.Config{
        AccessKeyID:     "your_access_key_id",
        AccessKeySecret: "your_access_key_secret",
        RegionID:        "cn-hangzhou",
        SignName:        "your_sign_name",
        TemplateCode:    "SMS_123456789",
    }

    // 创建SMS客户端
    client, err := sms.NewClient(config)
    if err != nil {
        log.Fatal("Failed to create SMS client:", err)
    }
    defer client.Close()

    // 发送短信
    request := &sms.SMSRequest{
        PhoneNumber: "13800138000",
        TemplateParams: map[string]string{
            "code": "123456",
        },
    }

    response, err := client.SendSMS(request)
    if err != nil {
        log.Fatal("Failed to send SMS:", err)
    }

    log.Printf("SMS sent successfully: %+v", response)
}
```

### 发送验证码

```go
// 生成验证码
code, err := sms.GenerateVerificationCode(6)
if err != nil {
    log.Fatal("Failed to generate verification code:", err)
}

// 发送验证码短信
request := &sms.VerificationCodeRequest{
    PhoneNumber: "13800138000",
    Code:        code,
    Purpose:     "login",
}

response, err := client.SendVerificationCode(request)
if err != nil {
    log.Fatal("Failed to send verification code:", err)
}

log.Printf("Verification code sent: %+v", response)
```

### 手机号码验证

```go
// 验证手机号码格式
if !sms.ValidatePhoneNumber("13800138000") {
    log.Println("Invalid phone number format")
}

// 格式化手机号码
formatted := sms.FormatPhoneNumber("138 0013 8000")
log.Println("Formatted phone number:", formatted)

// 掩码手机号码
masked := sms.MaskPhoneNumber("13800138000")
log.Println("Masked phone number:", masked)
```

### 获取发送统计

```go
stats := client.GetStats()
log.Printf("SMS Statistics: %+v", stats)
```

## API 参考

### 类型定义

#### SMSRequest
```go
type SMSRequest struct {
    PhoneNumber    string            `json:"phone_number"`
    TemplateCode   string            `json:"template_code,omitempty"`
    TemplateParams map[string]string `json:"template_params"`
    SignName       string            `json:"sign_name,omitempty"`
}
```

#### SMSResponse
```go
type SMSResponse struct {
    RequestID string    `json:"request_id"`
    BizID     string    `json:"biz_id"`
    Code      string    `json:"code"`
    Message   string    `json:"message"`
    Success   bool      `json:"success"`
    Timestamp time.Time `json:"timestamp"`
}
```

#### VerificationCodeRequest
```go
type VerificationCodeRequest struct {
    PhoneNumber string `json:"phone_number"`
    Code        string `json:"code"`
    Purpose     string `json:"purpose,omitempty"`
}
```

### 主要方法

- `NewClient(config *Config) (*Client, error)` - 创建SMS客户端
- `SendSMS(request *SMSRequest) (*SMSResponse, error)` - 发送短信
- `SendVerificationCode(request *VerificationCodeRequest) (*SMSResponse, error)` - 发送验证码
- `GetStats() *SMSStats` - 获取发送统计
- `GenerateVerificationCode(length int) (string, error)` - 生成验证码
- `ValidatePhoneNumber(phoneNumber string) bool` - 验证手机号码

## 错误处理

模块定义了以下错误类型：

- `ErrMissingAccessKeyID` - 缺少AccessKey ID
- `ErrMissingAccessKeySecret` - 缺少AccessKey Secret
- `ErrMissingSignName` - 缺少短信签名
- `ErrMissingTemplateCode` - 缺少模板代码
- `ErrInvalidPhoneNumber` - 无效的手机号码格式
- `ErrEmptyTemplateParams` - 模板参数为空
- `ErrSMSClientNotInitialized` - SMS客户端未初始化
- `ErrSendSMSFailed` - 发送短信失败

## 注意事项

1. 确保阿里云账户有足够的短信余额
2. 短信签名和模板需要在阿里云控制台预先申请和审核
3. 注意短信发送频率限制，避免被限流
4. 生产环境中请妥善保管AccessKey信息
5. 建议实现短信发送日志记录和监控
