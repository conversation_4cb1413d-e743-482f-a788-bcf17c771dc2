package main

import (
	"fmt"
	"io"
	"log"
	"strings"
	"time"

	"pointer/golangp/common/storage"
)

func main() {
	// MinIO服务器配置
	provider := "oss"
	endpoint := "oss-cn-hangzhou.aliyuncs.com"
	accessKeyID := ""
	secretAccessKey := ""
	region := "" // MinIO不需要region
	useSSL := true

	fmt.Println("=== MinIO Storage Service Test ===")
	fmt.Printf("Connecting to MinIO server: %s\n", endpoint)

	//使用工厂函数
	fmt.Println("\n--- 方法2: 使用工厂函数 ---")
	config := storage.Config{
		Provider:        provider, // minio、s3、oss
		Endpoint:        endpoint,
		AccessKeyID:     accessKeyID,
		SecretAccessKey: secretAccessKey,
		UseSSL:          useSSL,
		Region:          region,
	}

	service, err := storage.NewStorageService(config)
	if err != nil {
		log.Fatalf("Failed to create service via factory: %v", err)
	}
	fmt.Println("✓ Factory service created successfully")

	// 测试配置
	testBucket := "test-bucket-test-123"
	testPrefix := "" // 确保对象键符合MinIO中的匿名访问策略前缀
	testObjectKey := "folder/test-object.txt"
	testContent := "Hello, MinIO! This is a test file created at " + time.Now().Format("2006-01-02 15:04:05")

	fmt.Printf("\n--- 测试配置 ---\n")
	fmt.Printf("Bucket: %s\n", testBucket)
	fmt.Printf("Prefix for anonymous access: '%s'\n", testPrefix)
	fmt.Printf("Object Key: %s\n", testObjectKey)
	fmt.Printf("Content: %s\n", testContent)

	// 使用新函数配置bucket为公开读取
	// fmt.Println("\n--- 配置Bucket策略 ---")
	// accessLevel := "readonly"
	// err = service.SetAnonymousAccessPolicy(testBucket, testPrefix, accessLevel)
	// if err != nil {
	// 	log.Fatalf("Failed to set bucket policy: %v", err)
	// }
	// fmt.Printf("✓ Bucket policy set to '%s'\n", accessLevel)

	// 测试获取bucket策略
	fmt.Println("\n--- 测试获取Bucket策略 ---")
	policy, err := service.GetBucketPolicy(testBucket)
	if err != nil {
		if provider == "oss" {
			fmt.Printf("✓ OSS provider: %v (expected for OSS)\n", err)
		} else {
			log.Fatalf("Failed to get bucket policy: %v", err)
		}
	} else {
		if policy == "" {
			fmt.Println("✓ No bucket policy set (empty string returned)")
		} else {
			fmt.Printf("✓ Current bucket policy:\n%s\n", policy)
		}
	}

	// 测试上传
	fmt.Println("\n--- 测试上传 ---")
	reader := strings.NewReader(testContent)
	objectKey, objectPath, err := service.Upload(testBucket, testObjectKey, reader, int64(len(testContent)), "text/plain")
	if err != nil {
		log.Fatalf("Upload failed: %v", err)
	}
	fmt.Printf("✓ Upload successful. Object key: %s\n", objectKey)

	fmt.Printf("✓ Object access path: %s\n", objectPath)

	// 测试下载
	fmt.Println("\n--- 测试下载 ---")
	downloadReader, err := service.Download(testBucket, testObjectKey)
	if err != nil {
		log.Fatalf("Download failed: %v", err)
	}
	defer downloadReader.Close()

	downloadedContent, err := io.ReadAll(downloadReader)
	if err != nil {
		log.Fatalf("Failed to read downloaded content: %v", err)
	}

	if string(downloadedContent) == testContent {
		fmt.Println("✓ Download successful. Content verified.")
	} else {
		fmt.Printf("✗ Content mismatch!\nExpected: %s\nGot: %s\n", testContent, string(downloadedContent))
	}

	// 测试预览URL
	fmt.Println("\n--- 测试预览URL ---")
	previewURL, err := service.GetPreviewURL(testBucket, testObjectKey, "")
	if err != nil {
		log.Fatalf("GetPreviewURL failed: %v", err)
	}
	fmt.Printf("✓ Preview URL: %s\n", previewURL)

	fmt.Println("\n=== 测试完成 ===")
	fmt.Printf("测试bucket: %s\n", testBucket)
	fmt.Println("测试文件已上传到MinIO服务器")
	fmt.Println("你可以通过MinIO控制台查看上传的文件")
}
