# Storage Module

这个模块提供了一个统一的存储服务接口，支持多种S3兼容的存储后端，包括MinIO、AWS S3和阿里云OSS。

## 功能特性

- 统一的存储服务接口
- 支持多种S3兼容存储后端（MinIO、AWS S3、阿里云OSS）
- 文件上传和下载
- 预览URL生成
- 匿名访问策略设置
- 存储桶策略管理
- 灵活的配置选项

## 安装

确保你的项目中已经添加了以下依赖：

```go
import (
    "github.com/minio/minio-go/v7"
    "github.com/aliyun/aliyun-oss-go-sdk/oss"
)
```

## 使用方法

### 1. 创建存储服务

```go
import "your-project/golangp/common/storage"

// MinIO 配置
minioConfig := storage.Config{
    Provider:        "minio",
    Endpoint:        "localhost:9000",
    AccessKeyID:     "minioadmin",
    SecretAccessKey: "minioadmin",
    UseSSL:          false,
    Region:          "",
}

// AWS S3 配置
s3Config := storage.Config{
    Provider:        "s3",
    Endpoint:        "s3.amazonaws.com",
    AccessKeyID:     "your-access-key",
    SecretAccessKey: "your-secret-key",
    UseSSL:          true,
    Region:          "us-east-1",
}

// 阿里云OSS 配置
ossConfig := storage.Config{
    Provider:        "oss",
    Endpoint:        "oss-cn-hangzhou.aliyuncs.com",
    AccessKeyID:     "your-access-key",
    SecretAccessKey: "your-secret-key",
    UseSSL:          true,
    Region:          "cn-hangzhou",
}

// 创建存储服务
service, err := storage.NewStorageService(minioConfig)
if err != nil {
    log.Fatalf("Failed to create storage service: %v", err)
}
```

### 2. 文件上传

```go
import (
    "bytes"
    "context"
    "os"
)

// 从文件上传
file, err := os.Open("example.jpg")
if err != nil {
    log.Fatal(err)
}
defer file.Close()

fileInfo, err := file.Stat()
if err != nil {
    log.Fatal(err)
}

objectKey, url, err := service.Upload(
    "my-bucket",           // 存储桶名称
    "images/example.jpg",  // 对象键
    file,                  // 文件内容
    fileInfo.Size(),       // 文件大小
    "image/jpeg",          // 内容类型
)
if err != nil {
    log.Fatalf("Upload failed: %v", err)
}

fmt.Printf("Uploaded to: %s, URL: %s\n", objectKey, url)

// 从字节数组上传
data := []byte("Hello, World!")
reader := bytes.NewReader(data)

objectKey, url, err = service.Upload(
    "my-bucket",
    "text/hello.txt",
    reader,
    int64(len(data)),
    "text/plain",
)
```

### 3. 文件下载

```go
// 下载文件
reader, err := service.Download("my-bucket", "images/example.jpg")
if err != nil {
    log.Fatalf("Download failed: %v", err)
}
defer reader.Close()

// 保存到本地文件
outFile, err := os.Create("downloaded_example.jpg")
if err != nil {
    log.Fatal(err)
}
defer outFile.Close()

_, err = io.Copy(outFile, reader)
if err != nil {
    log.Fatalf("Failed to save file: %v", err)
}

fmt.Println("File downloaded successfully")
```

### 4. 生成预览URL

```go
// 生成预览URL
baseURL := "https://cdn.example.com"  // 你的CDN或存储服务的公共URL
previewURL, err := service.GetPreviewURL(
    "my-bucket",
    "images/example.jpg",
    baseURL,
)
if err != nil {
    log.Fatalf("Failed to generate preview URL: %v", err)
}

fmt.Printf("Preview URL: %s\n", previewURL)
```

### 5. 设置匿名访问策略

```go
// 设置存储桶为只读访问
err := service.SetAnonymousAccessPolicy(
    "my-bucket",
    "images/",      // 前缀
    "readonly",     // 访问类型: readonly, writeonly, readwrite, none
)
if err != nil {
    log.Fatalf("Failed to set policy: %v", err)
}

// 获取存储桶策略
policy, err := service.GetBucketPolicy("my-bucket")
if err != nil {
    log.Fatalf("Failed to get policy: %v", err)
}
fmt.Printf("Bucket policy: %s\n", policy)
```

## 配置说明

### Config 结构体

```go
type Config struct {
    Provider        string // 存储提供商: "minio", "s3", "oss"
    Endpoint        string // 存储服务端点
    AccessKeyID     string // 访问密钥ID
    SecretAccessKey string // 访问密钥
    UseSSL          bool   // 是否使用HTTPS
    Region          string // 区域（AWS S3必需）
}
```

### 存储服务接口

```go
type StorageService interface {
    // 上传文件
    Upload(bucketName string, objectKey string, reader io.Reader, objectSize int64, contentType string) (string, string, error)
    
    // 下载文件
    Download(bucketName string, objectKey string) (io.ReadCloser, error)
    
    // 生成预览URL
    GetPreviewURL(bucketName string, objectKey string, baseURL string) (string, error)
    
    // 设置匿名访问策略
    SetAnonymousAccessPolicy(bucketName string, prefix string, access string) error
    
    // 获取存储桶策略
    GetBucketPolicy(bucketName string) (string, error)
    
    // 获取配置
    Config() Config
}
```

## 访问策略类型

- **readonly**: 只读访问
- **writeonly**: 只写访问
- **readwrite**: 读写访问
- **none**: 无匿名访问

## 注意事项

1. 确保存储服务已启动并可访问
2. 正确配置认证信息（访问密钥）
3. AWS S3 需要正确设置区域
4. 阿里云OSS通过S3兼容API访问时可能有功能限制
5. 上传前确保存储桶已存在
6. 注意处理错误情况，特别是网络错误和认证失败

## 示例

完整的示例代码：

```go
package main

import (
    "bytes"
    "fmt"
    "io"
    "log"
    "os"
    "your-project/golangp/common/storage"
)

func main() {
    // 创建存储服务
    config := storage.Config{
        Provider:        "minio",
        Endpoint:        "localhost:9000",
        AccessKeyID:     "minioadmin",
        SecretAccessKey: "minioadmin",
        UseSSL:          false,
        Region:          "",
    }
    
    service, err := storage.NewStorageService(config)
    if err != nil {
        log.Fatalf("Failed to create storage service: %v", err)
    }
    
    // 上传文件
    data := []byte("Hello, Storage World!")
    reader := bytes.NewReader(data)
    
    objectKey, url, err := service.Upload(
        "test-bucket",
        "test/hello.txt",
        reader,
        int64(len(data)),
        "text/plain",
    )
    if err != nil {
        log.Fatalf("Upload failed: %v", err)
    }
    
    fmt.Printf("Uploaded: %s\n", objectKey)
    
    // 下载文件
    downloadReader, err := service.Download("test-bucket", "test/hello.txt")
    if err != nil {
        log.Fatalf("Download failed: %v", err)
    }
    defer downloadReader.Close()
    
    content, err := io.ReadAll(downloadReader)
    if err != nil {
        log.Fatalf("Failed to read content: %v", err)
    }
    
    fmt.Printf("Downloaded content: %s\n", string(content))
    
    // 生成预览URL
    previewURL, err := service.GetPreviewURL(
        "test-bucket",
        "test/hello.txt",
        "http://localhost:9000",
    )
    if err != nil {
        log.Fatalf("Failed to generate preview URL: %v", err)
    }
    
    fmt.Printf("Preview URL: %s\n", previewURL)
}
```
