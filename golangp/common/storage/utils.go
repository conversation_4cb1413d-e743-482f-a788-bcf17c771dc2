package storage

import (
	"fmt"
	"strings"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
)

const (
	ProviderMinIO string = "minio"
	ProviderOSS   string = "oss"
	ProviderS3    string = "s3"
)

// GeneratePreviewBaseURL constructs a preview base URL from storage configuration.
// This utility helps in standardizing how preview URLs are generated across different S3-compatible services.
// For OSS and S3, it generates virtual-hosted style URLs with bucket name prepended to the hostname.
func GeneratePreviewBaseURL(provider string, endpoint, region, bucketName string, useSSL bool) string {
	scheme := "http"
	if useSSL {
		scheme = "https"
	}

	var host string

	switch provider {
	case ProviderS3:
		// For AWS S3, construct regional endpoint with bucket name
		if region != "" && !strings.Contains(endpoint, region) {
			host = fmt.Sprintf("%s.s3.%s.amazonaws.com", bucketName, region)
		} else {
			// If endpoint already contains region, just prepend bucket name
			host = fmt.Sprintf("%s.%s", bucketName, endpoint)
		}
	case ProviderOSS:
		// For Aliyun OSS, prepend bucket name to endpoint
		// e.g., "my-bucket.oss-cn-hangzhou.aliyuncs.com"
		host = fmt.Sprintf("%s.%s", bucketName, endpoint)
	case ProviderMinIO:
		// For MinIO, use path-style: endpoint/bucket-name
		host = endpoint
	default:
		// For unknown providers, use path-style
		host = endpoint
	}

	return fmt.Sprintf("%s://%s", scheme, host)
}

// GenerateBucketPolicy generates a bucket policy JSON string based on the access type.
// bucketName: The name of the bucket.
// provider: The storage provider (minio, oss, s3).
// access: The access type - "readonly", "writeonly", "readwrite", or "none".
// prefix: Optional prefix to restrict the policy to specific objects (e.g., "public/"). Only applicable to MinIO and S3.
func GenerateBucketPolicy(bucketName string, provider string, access string, prefix string) (string, error) {
	if bucketName == "" {
		return "", fmt.Errorf("bucket name cannot be empty")
	}

	switch access {
	case "none":
		// Return empty policy to remove all access
		return "", nil
	case "readonly":
		return GeneratePublicReadPolicy(bucketName, provider, prefix)
	case "writeonly":
		return GenerateWriteOnlyPolicy(bucketName, provider, prefix)
	case "readwrite":
		return GenerateReadWritePolicy(bucketName, provider, prefix)
	default:
		return "", fmt.Errorf("invalid access type '%s'; expected 'readonly', 'writeonly', 'readwrite', or 'none'", access)
	}
}

func GenerateOSSBucketPolicy(client *oss.Client, bucketName string, access string) error {
	var aclType oss.ACLType
	switch access {
	case "readonly":
		aclType = oss.ACLPublicRead
	case "readwrite":
		aclType = oss.ACLPublicReadWrite
	case "none":
		aclType = oss.ACLPrivate
	default:
		return fmt.Errorf("unsupported access type for OSS: %s", access)
	}

	err := client.SetBucketACL(bucketName, aclType)
	if err != nil {
		return fmt.Errorf("failed to set bucket ACL: %w", err)
	}
	return nil
}

// GeneratePublicReadPolicy generates a bucket policy JSON string that grants public read access.
func GeneratePublicReadPolicy(bucketName string, provider string, prefix string) (string, error) {
	if bucketName == "" {
		return "", fmt.Errorf("bucket name cannot be empty")
	}

	var policy string

	switch provider {
	case ProviderMinIO, ProviderS3:
		// MinIO and S3 use standard S3 policy format
		if prefix != "" {
			// When prefix is specified, include both ListBucket and GetObject permissions
			// with proper conditions and resource restrictions
			policy = fmt.Sprintf(`{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {"AWS": ["*"]},
            "Action": ["s3:ListBucket"],
            "Resource": ["arn:aws:s3:::%s"],
            "Condition": {
                "StringEquals": {
                    "s3:prefix": ["%s"]
                }
            }
        },
        {
            "Effect": "Allow",
            "Principal": {"AWS": ["*"]},
            "Action": ["s3:GetObject"],
            "Resource": ["arn:aws:s3:::%s/%s*"]
        }
    ]
}`, bucketName, strings.Trim(prefix, "/"), bucketName, strings.Trim(prefix, "/"))
		} else {
			// When no prefix is specified, use simple GetObject permission
			policy = fmt.Sprintf(`{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": "*",
            "Action": [
                "s3:GetObject"
            ],
            "Resource": ["arn:aws:s3:::%s/*"]
        }
    ]
}`, bucketName)
		}
	case ProviderOSS:
		// OSS uses OSS-specific policy format
		policy = fmt.Sprintf(`{
    "Version": "1",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": "*",
            "Action": ["oss:GetObject"],
            "Resource": ["acs:oss:::*:%s/*"]
        }
    ]
}`, bucketName)
	default:
		return "", fmt.Errorf("unsupported provider for policy generation: %s", provider)
	}

	return strings.TrimSpace(policy), nil
}

// GenerateWriteOnlyPolicy generates a bucket policy JSON string that grants public write access.
func GenerateWriteOnlyPolicy(bucketName string, provider string, prefix string) (string, error) {
	if bucketName == "" {
		return "", fmt.Errorf("bucket name cannot be empty")
	}

	var policy string

	switch provider {
	case ProviderMinIO, ProviderS3:
		// MinIO and S3 use standard S3 policy format with prefix support
		if prefix != "" {
			// When prefix is specified, include both ListBucket and PutObject permissions
			policy = fmt.Sprintf(`{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {"AWS": ["*"]},
            "Action": ["s3:ListBucket"],
            "Resource": ["arn:aws:s3:::%s"],
            "Condition": {
                "StringEquals": {
                    "s3:prefix": ["%s"]
                }
            }
        },
        {
            "Effect": "Allow",
            "Principal": {"AWS": ["*"]},
            "Action": ["s3:PutObject"],
            "Resource": ["arn:aws:s3:::%s/%s*"]
        }
    ]
}`, bucketName, strings.Trim(prefix, "/"), bucketName, strings.Trim(prefix, "/"))
		} else {
			// When no prefix is specified, use simple PutObject permission
			policy = fmt.Sprintf(`{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {"AWS": "*"},
            "Action": ["s3:PutObject"],
            "Resource": ["arn:aws:s3:::%s/*"]
        }
    ]
}`, bucketName)
		}
	case ProviderOSS:
		// OSS uses OSS-specific policy format (prefix not supported in OSS policies)
		policy = fmt.Sprintf(`{
		"Version": "1",
		"Statement": [
			{
				"Effect": "Allow",
				"Principal": "*",
				"Action": ["oss:PutObject"],
				"Resource": ["acs:oss:::*:%s/*"]
			}
		]
	}`, bucketName)
	default:
		return "", fmt.Errorf("unsupported provider for policy generation: %s", provider)
	}

	return strings.TrimSpace(policy), nil
}

// GenerateReadWritePolicy generates a bucket policy JSON string that grants public read and write access.
func GenerateReadWritePolicy(bucketName string, provider string, prefix string) (string, error) {
	if bucketName == "" {
		return "", fmt.Errorf("bucket name cannot be empty")
	}

	var policy string

	switch provider {
	case ProviderMinIO, ProviderS3:
		// MinIO and S3 use standard S3 policy format with prefix support
		if prefix != "" {
			// When prefix is specified, include both ListBucket and GetObject/PutObject permissions
			policy = fmt.Sprintf(`{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {"AWS": ["*"]},
            "Action": ["s3:ListBucket"],
            "Resource": ["arn:aws:s3:::%s"],
            "Condition": {
                "StringEquals": {
                    "s3:prefix": ["%s"]
                }
            }
        },
        {
            "Effect": "Allow",
            "Principal": {"AWS": ["*"]},
            "Action": ["s3:GetObject", "s3:PutObject"],
            "Resource": ["arn:aws:s3:::%s/%s*"]
        }
    ]
}`, bucketName, strings.Trim(prefix, "/"), bucketName, strings.Trim(prefix, "/"))
		} else {
			// When no prefix is specified, use simple GetObject/PutObject permissions
			policy = fmt.Sprintf(`{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {"AWS": "*"},
            "Action": ["s3:GetObject", "s3:PutObject"],
            "Resource": ["arn:aws:s3:::%s/*"]
        }
    ]
}`, bucketName)
		}
	case ProviderOSS:
		// OSS uses OSS-specific policy format (prefix not supported in OSS policies)
		policy = fmt.Sprintf(`{
		"Version": "1",
		"Statement": [
			{
				"Effect": "Allow",
				"Principal": "*",
				"Action": ["oss:GetObject", "oss:PutObject"],
				"Resource": ["acs:oss:::*:%s/*"]
			}
		]
	}`, bucketName)
	default:
		return "", fmt.Errorf("unsupported provider for policy generation: %s", provider)
	}

	return strings.TrimSpace(policy), nil
}

// GenerateObjectPath generates the correct object path format based on the provider.
// This function returns the relative path that should be used for accessing the object after upload.
// bucketName: The name of the bucket.
// objectKey: The object key (e.g., "folder/file.txt").
// provider: The storage provider (minio, oss, s3).
func GenerateObjectPath(bucketName, objectKey, provider string) string {
	switch provider {
	case "s3":
		// S3: bucket-name/object-key
		return fmt.Sprintf("%s/%s", bucketName, objectKey)
	case "oss":
		// OSS: object-key (bucket name is in the domain)
		return objectKey
	case "minio":
		// MinIO: bucket-name/object-key
		return fmt.Sprintf("%s/%s", bucketName, objectKey)
	default:
		// Default format for unknown providers
		return fmt.Sprintf("%s/%s", bucketName, objectKey)
	}
}

// GenerateObjectURL generates a complete URL for accessing an object.
// This is similar to GenerateObjectPath but provides more flexibility.
// bucketName: The name of the bucket.
// objectKey: The object key.
// provider: The storage provider.
// endpoint: The storage endpoint.
// region: The region (for S3).
// useSSL: Whether to use HTTPS.
// customDomain: Optional custom domain (e.g., CDN domain).
func GenerateObjectURL(bucketName, objectKey, provider, endpoint, region string, useSSL bool, customDomain string) string {
	if customDomain != "" {
		// If custom domain is provided, use it with the object key
		scheme := "http"
		if useSSL {
			scheme = "https"
		}
		return fmt.Sprintf("%s://%s/%s", scheme, customDomain, objectKey)
	}

	// Otherwise, use the provider-specific format
	scheme := "http"
	if useSSL {
		scheme = "https"
	}

	switch provider {
	case "s3":
		// S3: https://bucket-name.s3.region.amazonaws.com/object-key
		if region != "" && !strings.Contains(endpoint, region) {
			return fmt.Sprintf("%s://%s.s3.%s.amazonaws.com/%s", scheme, bucketName, region, objectKey)
		} else {
			return fmt.Sprintf("%s://%s.%s/%s", scheme, bucketName, endpoint, objectKey)
		}
	case "oss":
		// OSS: https://bucket-name.oss-cn-hangzhou.aliyuncs.com/object-key
		return fmt.Sprintf("%s://%s.%s/%s", scheme, bucketName, endpoint, objectKey)
	case "minio":
		// MinIO: http://endpoint/bucket-name/object-key
		return fmt.Sprintf("%s://%s/%s/%s", scheme, endpoint, bucketName, objectKey)
	default:
		// Default to MinIO format for unknown providers
		return fmt.Sprintf("%s://%s/%s/%s", scheme, endpoint, bucketName, objectKey)
	}
}
