/*
 * @Description:
 * @Author: Devin
 * @Date: 2025-07-15 10:04:49
 */
package storage

import (
	"fmt"
)

// Config holds the necessary configuration parameters to initialize a StorageService.
// It's designed to be flexible enough for MinIO, AWS S3, and Aliyun OSS (via S3 compatibility).
type Config struct {
	Provider string `json:"provider"`

	// Type specifies the backend type, though with a unified MinioS3Service, this might be less critical
	// unless other types are added later. For now, it's implicitly "s3compatible".
	// Type string `json:"type"` // Example: "minio", "s3", "oss"
	Endpoint        string `json:"endpoint"`        // e.g., "s3.amazonaws.com", "oss-cn-hangzhou.aliyuncs.com", "localhost:9000"
	AccessKeyID     string `json:"accessKeyID"`     // Access Key ID
	SecretAccessKey string `json:"secretAccessKey"` // Secret Access Key
	UseSSL          bool   `json:"useSSL"`          // True to use HTTPS, false for HTTP
	Region          string `json:"region"`          // AWS region, e.g., "us-east-1". Important for S3. Optional for MinIO, oss.
}

// NewStorageService is a factory function that creates and returns a StorageService
// based on the provided configuration. Currently, it only supports the MinioS3Service.
func NewStorageService(cfg Config) (StorageService, error) {
	if cfg.Endpoint == "" {
		return nil, fmt.Errorf("storage endpoint must be configured")
	}
	if cfg.AccessKeyID == "" {
		return nil, fmt.Errorf("storage accessKeyID must be configured")
	}
	if cfg.SecretAccessKey == "" {
		return nil, fmt.Errorf("storage secretAccessKey must be configured")
	}

	// For now, we only have one type of service, MinioS3Service, which handles all S3-compatible backends.
	service, err := NewMinioS3Service(
		cfg.Provider, // Pass provider as string directly
		cfg.Endpoint,
		cfg.AccessKeyID,
		cfg.SecretAccessKey,
		cfg.Region, // Pass region
		cfg.UseSSL,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create MinIO S3 compatible service: %w", err)
	}

	return service, nil
}
