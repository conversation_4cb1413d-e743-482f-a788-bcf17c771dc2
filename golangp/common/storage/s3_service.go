package storage

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"path"
	"strings"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// MinioS3Service provides an implementation of StorageService using the MinIO Go SDK.
// It can be used to connect to MinIO servers as well as other S3-compatible services
// like AWS S3 and Aliyun OSS (when S3 compatibility is enabled).
type MinioS3Service struct {
	client   *minio.Client
	region   string // Optional, but often needed for AWS S3
	provider string // The provider as string (e.g., "minio", "oss", "s3").
	config   Config
}

// NewMinioS3Service creates a new MinioS3Service instance.
// providerStr: The provider as string (e.g., "minio", "oss", "s3").
// endpoint: The S3-compatible endpoint (e.g., "s3.amazonaws.com", "oss-cn-hangzhou.aliyuncs.com", "localhost:9000").
// accessKeyID: The access key.
// secretAccessKey: The secret key.
// useSSL: Boolean indicating whether to use HTTPS.
// region: The AWS region (e.g., "us-east-1"). Can be an empty string if not applicable (e.g., for local MinIO).
func NewMinioS3Service(provider string, endpoint, accessKeyID, secretAccessKey, region string, useSSL bool) (*MinioS3Service, error) {
	minioClient, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(accessKeyID, secretAccessKey, ""),
		Secure: useSSL,
		Region: region, // Set region for S3 services
	})
	if err != nil {
		return nil, fmt.Errorf("failed to initialize MinIO client: %w", err)
	}

	// Optional: Ping the server to ensure connectivity, though MinIO client doesn't have a direct ping.
	// A lightweight operation like ListBuckets (even if it fails due to permissions) can verify endpoint and credentials.
	// For now, we assume the client initializes correctly if no error is returned by minio.New.

	config := Config{
		Provider:        provider,
		Endpoint:        endpoint,
		AccessKeyID:     accessKeyID,
		SecretAccessKey: secretAccessKey,
		Region:          region,
		UseSSL:          useSSL,
	}

	return &MinioS3Service{
		client:   minioClient,
		region:   region,
		provider: provider,
		config:   config,
	}, nil
}

// Upload uploads a file to the specified bucket with the given object key.
func (s *MinioS3Service) Upload(bucketName string, objectKey string, reader io.Reader, objectSize int64, contentType string) (string, string, error) {
	ctx := context.Background()

	// Ensure bucket exists, create if not.
	// For production, you might want more sophisticated bucket management or assume buckets are pre-created.
	exists, err := s.client.BucketExists(ctx, bucketName)
	if err != nil {
		return "", "", fmt.Errorf("failed to check if bucket '%s' exists: %w", bucketName, err)
	}
	if !exists {
		// MakeBucketOptions can take a region, defaults to client's region if empty.
		err = s.client.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{Region: s.region})
		if err != nil {
			return "", "", fmt.Errorf("failed to create bucket '%s': %w", bucketName, err)
		}
		// fmt.Printf("Successfully created bucket %s\n", bucketName) // For debugging
	}

	uploadInfo, err := s.client.PutObject(ctx, bucketName, objectKey, reader, objectSize, minio.PutObjectOptions{
		ContentType: contentType,
		// Other options like UserMetadata, SSE, etc., can be added here.
	})
	if err != nil {
		return "", "", fmt.Errorf("failed to upload object '%s' to bucket '%s': %w", objectKey, bucketName, err)
	}

	// Generate complete URL for accessing the object
	objectURL := GenerateObjectURL(bucketName, objectKey, s.provider, s.config.Endpoint, s.region, s.config.UseSSL, "")

	// uploadInfo.Key is the object key, which we already have.
	// Return the object key and the complete URL
	return uploadInfo.Key, objectURL, nil
}

// Download retrieves a file from the specified bucket with the given object key.
func (s *MinioS3Service) Download(bucketName string, objectKey string) (io.ReadCloser, error) {
	ctx := context.Background()
	object, err := s.client.GetObject(ctx, bucketName, objectKey, minio.GetObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to download object '%s' from bucket '%s': %w", objectKey, bucketName, err)
	}
	return object, nil
}

// SetAnonymousAccessPolicy sets a bucket's policy to allow anonymous access.
// access can be "readonly", "writeonly", "readwrite", or "none".
func (s *MinioS3Service) SetAnonymousAccessPolicy(bucketName string, prefix string, access string) error {
	ctx := context.Background()

	// Ensure bucket exists, create if not.
	exists, err := s.client.BucketExists(ctx, bucketName)
	if err != nil {
		return fmt.Errorf("failed to check if bucket '%s' exists: %w", bucketName, err)
	}
	if !exists {
		err = s.client.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{Region: s.region})
		if err != nil {
			return fmt.Errorf("failed to create bucket '%s' for policy setting: %w", bucketName, err)
		}
	}

	// For OSS, use OSS-specific ACL instead of bucket policy
	if s.provider == "oss" {
		client, err := oss.New(s.client.EndpointURL().Host, s.config.AccessKeyID, s.config.SecretAccessKey)
		if err != nil {
			return fmt.Errorf("failed to create OSS client: %w", err)
		}
		err = GenerateOSSBucketPolicy(client, bucketName, access)
		if err != nil {
			return fmt.Errorf("failed to set OSS bucket ACL: %w", err)
		}

		return nil
	}

	// Generate the policy using the utility function
	policy, err := GenerateBucketPolicy(bucketName, s.provider, access, prefix)
	if err != nil {
		return fmt.Errorf("failed to generate bucket policy for bucket '%s' with access '%s': %w", bucketName, access, err)
	}

	// Set the bucket policy (empty policy for "none" access removes the policy)
	err = s.client.SetBucketPolicy(ctx, bucketName, policy)
	if err != nil {
		return fmt.Errorf("failed to set bucket policy for bucket '%s' with access '%s': %w", bucketName, access, err)
	}

	return nil
}

// GetBucketPolicy retrieves the current bucket policy.
// Returns the policy as a JSON string, or an empty string if no policy is set.
// For OSS, this will return an error as bucket policies are not supported via S3 API.
func (s *MinioS3Service) GetBucketPolicy(bucketName string) (string, error) {
	ctx := context.Background()

	// Check if bucket exists
	exists, err := s.client.BucketExists(ctx, bucketName)
	if err != nil {
		return "", fmt.Errorf("failed to check if bucket '%s' exists: %w", bucketName, err)
	}
	if !exists {
		return "", fmt.Errorf("bucket '%s' does not exist", bucketName)
	}

	// For OSS, bucket policies are not supported via S3 API
	if s.provider == "oss" {
		client, err := oss.New(s.client.EndpointURL().Host, s.config.AccessKeyID, s.config.SecretAccessKey)
		if err != nil {
			return "", fmt.Errorf("failed to create OSS client: %w", err)
		}
		acl, err := client.GetBucketACL(bucketName)
		if err != nil {
			return "", fmt.Errorf("failed to get bucket ACL: %w", err)
		}
		return acl.ACL, nil
	}

	// Get the bucket policy
	policy, err := s.client.GetBucketPolicy(ctx, bucketName)
	if err != nil {
		// Check if the error is due to no policy being set
		if strings.Contains(err.Error(), "NoSuchBucketPolicy") ||
			strings.Contains(err.Error(), "The bucket policy does not exist") ||
			strings.Contains(err.Error(), "NoSuchBucketPolicyException") {
			return "", nil // Return empty string for no policy
		}
		return "", fmt.Errorf("failed to get bucket policy for bucket '%s': %w", bucketName, err)
	}

	return policy, nil
}

// GetPreviewURL generates a URL suitable for previewing an object.
// This implementation constructs a URL using the configured previewBaseURL, bucketName, and objectKey.
// It assumes that the objects are publicly readable or that the previewBaseURL points to a CDN/proxy
// that handles access. For private objects, pre-signed URLs would be needed.
func (s *MinioS3Service) GetPreviewURL(bucketName string, objectKey string, configuredBaseURL string) (string, error) {
	// Generate the base URL dynamically if not provided
	baseURL := configuredBaseURL
	if baseURL == "" {
		baseURL = GeneratePreviewBaseURL(s.provider, s.client.EndpointURL().Host, s.region, bucketName, s.client.EndpointURL().Scheme == "https")
	}

	base, err := url.Parse(baseURL)
	if err != nil {
		return "", fmt.Errorf("invalid previewBaseURL '%s': %w", baseURL, err)
	}

	// Handle different URL styles based on provider
	switch s.provider {
	case ProviderOSS, ProviderS3:
		// For OSS and S3, the baseURL already includes the bucket name in virtual-hosted style
		// Just append the object key to the path
		base.Path = path.Join(base.Path, objectKey)

	case ProviderMinIO:
		// For MinIO, use path-style: endpoint/bucket-name/object-key
		base.Path = path.Join(base.Path, bucketName, objectKey)

	default:
		// Default to path-style for unknown providers
		base.Path = path.Join(base.Path, bucketName, objectKey)
	}

	return base.String(), nil
}

func (s *MinioS3Service) Config() Config {
	return s.config
}
