# Pointer Center Authentication API Documentation

## Overview

This document describes all authentication and user management endpoints available in the Pointer Center API.

## Base URL

```
http://localhost:8080/api/v1
```

## Authentication

Most endpoints require authentication via J<PERSON><PERSON> token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Public Endpoints (No Authentication Required)

### 1. User Registration

**POST** `/auth/register`

Register a new user account.

**Request Body:**
```json
{
  "username": "johndoe",
  "email": "<EMAIL>",
  "password": "securepassword123",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Registration successful",
  "data": {
    "user": {
      "id": "uuid",
      "username": "johndoe",
      "email": "<EMAIL>",
      "first_name": "<PERSON>",
      "last_name": "<PERSON><PERSON>",
      "role": "user",
      "status": "active",
      "origin": "local",
      "created_at": "2025-07-17T10:00:00Z"
    },
    "access_token": "jwt-access-token",
    "refresh_token": "jwt-refresh-token",
    "expires_at": "2025-07-17T10:15:00Z",
    "is_new_user": true,
    "login_time": "2025-07-17T10:00:00Z",
    "message": "Registration successful"
  }
}
```

### 2. User Login (Unified)

**POST** `/auth/login`

Supports multiple login methods:
- Normal email/password login
- Google ID Token login (frontend flow)
- Google Authorization Code login (server flow)

**Request Body (Normal Login):**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Request Body (Google ID Token):**
```json
{
  "id_token": "google-id-token"
}
```

**Request Body (Google Auth Code):**
```json
{
  "auth_code": "google-auth-code",
  "redirect_uri": "http://localhost:3000/auth/callback"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "username": "johndoe",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "role": "user",
      "status": "active",
      "origin": "local",
      "last_login": "2025-07-17T10:00:00Z"
    },
    "access_token": "jwt-access-token",
    "refresh_token": "jwt-refresh-token",
    "expires_at": "2025-07-17T10:15:00Z",
    "is_new_user": false,
    "login_time": "2025-07-17T10:00:00Z",
    "message": "Login successful"
  }
}
```

## Protected Endpoints (Authentication Required)

### 3. User Logout

**POST** `/auth/logout`

Logout the current user and invalidate all sessions.

**Headers:**
```
Authorization: Bearer <access-token>
```

**Response:**
```json
{
  "success": true,
  "message": "Logout successful",
  "data": null
}
```

### 4. Refresh Token

**POST** `/auth/refresh`

Refresh the access token using a refresh token.

**Headers:**
```
Authorization: Bearer <access-token>
```

**Request Body:**
```json
{
  "refresh_token": "jwt-refresh-token"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": {
    "access_token": "new-jwt-access-token",
    "refresh_token": "new-jwt-refresh-token",
    "expires_at": "2025-07-17T10:30:00Z"
  }
}
```

### 5. Get Current User

**GET** `/auth/me`

Get current user information.

**Headers:**
```
Authorization: Bearer <access-token>
```

**Response:**
```json
{
  "success": true,
  "message": "User information retrieved successfully",
  "data": {
    "id": "uuid",
    "username": "johndoe",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "bio": "Software developer",
    "phone": "+1234567890",
    "company": "Tech Corp",
    "country": "USA",
    "role": "user",
    "status": "active",
    "origin": "local",
    "email_verified": true,
    "last_login": "2025-07-17T10:00:00Z",
    "created_at": "2025-07-17T09:00:00Z",
    "updated_at": "2025-07-17T10:00:00Z"
  }
}
```

### 6. Update Profile

**PUT** `/auth/profile`

Update user profile information.

**Headers:**
```
Authorization: Bearer <access-token>
```

**Request Body:**
```json
{
  "username": "johnsmith",
  "first_name": "John",
  "last_name": "Smith",
  "bio": "Senior Software Developer",
  "phone": "+1234567890",
  "company": "New Tech Corp",
  "country": "Canada"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "id": "uuid",
    "username": "johnsmith",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Smith",
    "bio": "Senior Software Developer",
    "phone": "+1234567890",
    "company": "New Tech Corp",
    "country": "Canada",
    "role": "user",
    "status": "active",
    "updated_at": "2025-07-17T10:05:00Z"
  }
}
```

### 7. Change Password

**PUT** `/auth/password`

Change user password.

**Headers:**
```
Authorization: Bearer <access-token>
```

**Request Body:**
```json
{
  "current_password": "oldpassword123",
  "new_password": "newsecurepassword456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password changed successfully",
  "data": null
}
```

### 8. Upload Avatar

**PUT** `/auth/avatar`

Upload user avatar image.

**Headers:**
```
Authorization: Bearer <access-token>
Content-Type: multipart/form-data
```

**Request Body (Form Data):**
```
avatar: <image-file> (max 5MB, JPEG/PNG/WebP)
```

**Response:**
```json
{
  "success": true,
  "message": "Avatar uploaded successfully",
  "data": {
    "avatar_url": "/uploads/avatars/uuid_1642428000.jpg"
  }
}
```

### 9. Request Email Change

**PUT** `/auth/email`

Request to change email address.

**Headers:**
```
Authorization: Bearer <access-token>
```

**Request Body:**
```json
{
  "new_email": "<EMAIL>",
  "password": "currentpassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Email change verification sent",
  "data": {
    "message": "Please check your new email address for verification instructions"
  }
}
```

### 10. Verify Email Change

**GET** `/auth/email/verify?token=<verification-token>`

Verify email change using verification token.

**Query Parameters:**
- `token`: Email verification token

**Response:**
```json
{
  "success": true,
  "message": "Email address updated successfully",
  "data": {
    "new_email": "<EMAIL>"
  }
}
```

## Error Responses

All endpoints may return error responses in the following format:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information"
}
```

Common HTTP status codes:
- `400` - Bad Request (invalid input)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource not found)
- `409` - Conflict (resource already exists)
- `500` - Internal Server Error

## Authentication Flow

1. **Registration/Login**: Use `/auth/register` or `/auth/login` to get access and refresh tokens
2. **API Calls**: Include access token in Authorization header for protected endpoints
3. **Token Refresh**: Use `/auth/refresh` when access token expires
4. **Logout**: Use `/auth/logout` to invalidate all tokens

## Google OAuth Integration

The system supports Google OAuth through two flows:

1. **Frontend Flow**: Client gets ID token from Google and sends it to `/auth/login`
2. **Server Flow**: Client gets authorization code and sends it with redirect URI to `/auth/login`

Both flows will create a new user if the Google account doesn't exist in the system.
