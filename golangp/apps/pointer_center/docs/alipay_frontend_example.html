<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝支付示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #1890ff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #40a9ff;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .qr-container {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            margin: 20px 0;
        }
        .qr-code {
            max-width: 200px;
            margin: 20px auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .status.info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .hidden {
            display: none;
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>支付宝支付示例</h1>
    
    <!-- 支付表单 -->
    <div class="container">
        <h2>创建支付订单</h2>
        <form id="paymentForm">
            <div class="form-group">
                <label for="amount">支付金额（元）:</label>
                <input type="number" id="amount" step="0.01" min="0.01" value="0.01" required>
            </div>
            
            <div class="form-group">
                <label for="subject">商品标题:</label>
                <input type="text" id="subject" value="测试商品" required>
            </div>
            
            <div class="form-group">
                <label for="body">商品描述:</label>
                <textarea id="body" rows="3">这是一个测试商品的描述</textarea>
            </div>
            
            <div class="form-group">
                <label for="userId">用户ID:</label>
                <input type="text" id="userId" value="test-user-uuid" required>
            </div>
            
            <div class="form-group">
                <label for="orderId">订单ID:</label>
                <input type="text" id="orderId" value="order-123">
            </div>
            
            <div class="form-group">
                <label for="expireTime">过期时间（分钟）:</label>
                <input type="number" id="expireTime" min="1" max="120" value="10">
            </div>
            
            <button type="submit" id="createPaymentBtn">创建支付</button>
        </form>
    </div>
    
    <!-- 支付二维码 -->
    <div id="qrContainer" class="qr-container hidden">
        <h2>请使用支付宝扫码支付</h2>
        <div id="qrCode" class="qr-code"></div>
        <p>订单号: <span id="outTradeNo"></span></p>
        <p>过期时间: <span id="expireTime"></span></p>
        <div id="countdown"></div>
        <button id="cancelPaymentBtn">取消支付</button>
    </div>
    
    <!-- 状态显示 -->
    <div id="statusContainer"></div>
    
    <!-- 加载中 -->
    <div id="loading" class="loading hidden">
        <div class="spinner"></div>
        <p>处理中...</p>
    </div>

    <script>
        // 配置
        const API_BASE = '/api/v1';
        const POLL_INTERVAL = 2000; // 2秒
        const MAX_POLL_TIME = 600000; // 10分钟
        
        let pollTimer = null;
        let countdownTimer = null;
        let currentOutTradeNo = null;
        
        // DOM元素
        const paymentForm = document.getElementById('paymentForm');
        const qrContainer = document.getElementById('qrContainer');
        const statusContainer = document.getElementById('statusContainer');
        const loading = document.getElementById('loading');
        const createPaymentBtn = document.getElementById('createPaymentBtn');
        const cancelPaymentBtn = document.getElementById('cancelPaymentBtn');
        
        // 显示状态消息
        function showStatus(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusContainer.appendChild(statusDiv);
            
            // 5秒后自动移除
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.parentNode.removeChild(statusDiv);
                }
            }, 5000);
        }
        
        // 显示/隐藏加载状态
        function setLoading(show) {
            loading.classList.toggle('hidden', !show);
            createPaymentBtn.disabled = show;
        }
        
        // 生成二维码
        function generateQRCode(text) {
            // 这里使用简单的文本显示，实际项目中可以使用QRCode.js等库
            return `<div style="border: 1px solid #ccc; padding: 20px; background: white;">
                <p>二维码内容:</p>
                <p style="word-break: break-all; font-size: 12px;">${text}</p>
                <p style="color: #666;">请使用支付宝APP扫描此二维码</p>
            </div>`;
        }
        
        // 创建支付
        async function createPayment(formData) {
            try {
                setLoading(true);
                
                const response = await fetch(`${API_BASE}/alipay/qrcode`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // 'Authorization': 'Bearer ' + getToken() // 实际项目中需要添加认证
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    currentOutTradeNo = result.data.out_trade_no;
                    showQRCode(result.data);
                    startPolling(result.data.out_trade_no);
                    showStatus('支付二维码创建成功', 'success');
                } else {
                    showStatus(`创建支付失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`网络错误: ${error.message}`, 'error');
            } finally {
                setLoading(false);
            }
        }
        
        // 显示二维码
        function showQRCode(data) {
            document.getElementById('qrCode').innerHTML = generateQRCode(data.qr_code);
            document.getElementById('outTradeNo').textContent = data.out_trade_no;
            document.getElementById('expireTime').textContent = data.expire_time;
            qrContainer.classList.remove('hidden');
            
            // 开始倒计时
            startCountdown(new Date(data.expire_time));
        }
        
        // 开始倒计时
        function startCountdown(expireTime) {
            const countdownElement = document.getElementById('countdown');
            
            countdownTimer = setInterval(() => {
                const now = new Date();
                const timeLeft = expireTime - now;
                
                if (timeLeft <= 0) {
                    countdownElement.innerHTML = '<span style="color: red;">订单已过期</span>';
                    stopPolling();
                    clearInterval(countdownTimer);
                    return;
                }
                
                const minutes = Math.floor(timeLeft / 60000);
                const seconds = Math.floor((timeLeft % 60000) / 1000);
                countdownElement.innerHTML = `剩余时间: ${minutes}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }
        
        // 查询支付状态
        async function queryPaymentStatus(outTradeNo) {
            try {
                const response = await fetch(`${API_BASE}/alipay/status?out_trade_no=${outTradeNo}`, {
                    headers: {
                        // 'Authorization': 'Bearer ' + getToken() // 实际项目中需要添加认证
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const status = result.data.status;
                    
                    if (status === 'TRADE_SUCCESS') {
                        handlePaymentSuccess(result.data);
                    } else if (status === 'TRADE_CLOSED') {
                        handlePaymentFailed();
                    }
                    // WAIT_BUYER_PAY 继续轮询
                } else {
                    showStatus(`查询支付状态失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`查询支付状态网络错误: ${error.message}`, 'error');
            }
        }
        
        // 开始轮询
        function startPolling(outTradeNo) {
            pollTimer = setInterval(() => {
                queryPaymentStatus(outTradeNo);
            }, POLL_INTERVAL);
            
            // 最大轮询时间
            setTimeout(() => {
                stopPolling();
                showStatus('支付超时，请重新创建订单', 'error');
            }, MAX_POLL_TIME);
        }
        
        // 停止轮询
        function stopPolling() {
            if (pollTimer) {
                clearInterval(pollTimer);
                pollTimer = null;
            }
            if (countdownTimer) {
                clearInterval(countdownTimer);
                countdownTimer = null;
            }
        }
        
        // 处理支付成功
        function handlePaymentSuccess(data) {
            stopPolling();
            qrContainer.classList.add('hidden');
            showStatus(`支付成功！交易号: ${data.trade_no}`, 'success');
            
            // 这里可以跳转到成功页面或执行其他业务逻辑
            console.log('支付成功数据:', data);
        }
        
        // 处理支付失败
        function handlePaymentFailed() {
            stopPolling();
            qrContainer.classList.add('hidden');
            showStatus('支付失败或已取消', 'error');
        }
        
        // 取消支付
        function cancelPayment() {
            stopPolling();
            qrContainer.classList.add('hidden');
            showStatus('支付已取消', 'info');
        }
        
        // 事件监听
        paymentForm.addEventListener('submit', (e) => {
            e.preventDefault();
            
            const formData = {
                amount: document.getElementById('amount').value,
                subject: document.getElementById('subject').value,
                body: document.getElementById('body').value,
                user_id: document.getElementById('userId').value,
                order_id: document.getElementById('orderId').value,
                expire_time: parseInt(document.getElementById('expireTime').value) || 10
            };
            
            createPayment(formData);
        });
        
        cancelPaymentBtn.addEventListener('click', cancelPayment);
        
        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', () => {
            stopPolling();
        });
    </script>
</body>
</html>
