# Handler层重构指南

## 重构目标

将gin.Context的使用限制在HTTP相关操作上，将业务逻辑参数作为单独参数传递，实现关注点分离。

## 重构模式

### 1. 原始模式（需要重构）

```go
func (h *Handler) CreateResource(c *gin.Context) {
    var req CreateRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.BadRequest(c, "Invalid request", err)
        return
    }
    
    // 直接在handler中处理业务逻辑
    ctx := c.Request.Context()
    resource := &models.Resource{
        Name: req.Name,
        // ...
    }
    
    if err := h.repo.Create(ctx, resource); err != nil {
        response.InternalServerError(c, "Failed to create", err)
        return
    }
    
    response.Created(c, "Created successfully", resource)
}
```

### 2. 重构后模式（推荐）

```go
func (h *Handler) CreateResource(c *gin.Context) {
    startTime := time.Now()
    clientIP := c.ClientIP()
    
    var req CreateRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        response.BadRequest(c, "Invalid request", err)
        return
    }
    
    // 提取context用于数据库操作
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    // 调用业务逻辑层
    resource, err := h.createResourceLogic(ctx, req, clientIP)
    if err != nil {
        h.handleCreateResourceError(c, err, req.Name, clientIP)
        return
    }
    
    h.logger.Info("✅ Resource created - ID: %s, Duration: %v", 
        resource.ID, time.Since(startTime))
    
    response.Created(c, "Created successfully", resource)
}

// 业务逻辑层 - 纯函数，易于测试
func (h *Handler) createResourceLogic(ctx context.Context, req CreateRequest, clientIP string) (*models.Resource, error) {
    // 验证业务规则
    if err := h.validateCreateRequest(req); err != nil {
        return nil, err
    }
    
    // 创建资源
    resource := &models.Resource{
        Name: req.Name,
        // 设置默认值等
    }
    
    // 保存到数据库
    if err := h.repo.Create(ctx, resource); err != nil {
        return nil, err
    }
    
    return resource, nil
}

// 错误处理层
func (h *Handler) handleCreateResourceError(c *gin.Context, err error, name, clientIP string) {
    switch err.Error() {
    case "resource_already_exists":
        response.Conflict(c, "Resource already exists", nil)
    case "invalid_name":
        response.BadRequest(c, "Invalid name format", nil)
    default:
        response.InternalServerError(c, "Failed to create resource", err)
    }
}
```

### 3. 复杂查询的重构模式

```go
// 参数结构体
type ListResourcesParams struct {
    Page     int
    PageSize int
    Status   string
    Category string
}

func (h *Handler) ListResources(c *gin.Context) {
    startTime := time.Now()
    clientIP := c.ClientIP()
    
    // 解析参数
    params := h.parseListResourcesParams(c)
    
    // 提取context
    ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
    defer cancel()
    
    // 调用业务逻辑
    resources, total, err := h.listResourcesLogic(ctx, params)
    if err != nil {
        response.InternalServerError(c, "Failed to list resources", err)
        return
    }
    
    response.Paginated(c, "Resources retrieved", resources, total, params.Page, params.PageSize)
}

// 参数解析层
func (h *Handler) parseListResourcesParams(c *gin.Context) ListResourcesParams {
    page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
    pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
    
    // 验证和设置默认值
    if page < 1 {
        page = 1
    }
    if pageSize < 1 || pageSize > 100 {
        pageSize = 20
    }
    
    return ListResourcesParams{
        Page:     page,
        PageSize: pageSize,
        Status:   c.Query("status"),
        Category: c.Query("category"),
    }
}

// 业务逻辑层
func (h *Handler) listResourcesLogic(ctx context.Context, params ListResourcesParams) ([]*models.Resource, int64, error) {
    offset := (params.Page - 1) * params.PageSize
    
    // 根据过滤条件查询
    var resources []*models.Resource
    var err error
    
    if params.Status != "" {
        resources, err = h.repo.ListByStatus(ctx, params.Status, params.PageSize, offset)
    } else if params.Category != "" {
        resources, err = h.repo.ListByCategory(ctx, params.Category, params.PageSize, offset)
    } else {
        resources, err = h.repo.List(ctx, params.PageSize, offset)
    }
    
    if err != nil {
        return nil, 0, err
    }
    
    // 获取总数
    total, err := h.repo.Count(ctx)
    if err != nil {
        return nil, 0, err
    }
    
    return resources, total, nil
}
```

## 重构收益

1. **关注点分离**: gin.Context只处理HTTP相关操作，业务逻辑独立
2. **易于测试**: 业务逻辑函数是纯函数，可以独立测试
3. **代码复用**: 业务逻辑可以在不同的handler中复用
4. **错误处理**: 统一的错误处理模式
5. **可维护性**: 代码结构清晰，易于理解和维护

## 需要重构的文件

- [x] learning_lesson_handler.go (已部分重构)
- [ ] learning_path_handler.go
- [ ] learning_node_handler.go  
- [ ] tech_competency_handler.go
- [ ] static_profile_handler.go
- [ ] auth_handler.go (已修改，但可以进一步优化)

## 重构步骤

1. 添加必要的import (context, errors)
2. 创建参数结构体（对于复杂查询）
3. 重构handler方法，提取业务逻辑
4. 创建业务逻辑方法
5. 创建参数解析方法（如需要）
6. 创建错误处理方法
7. 更新日志记录
8. 编写单元测试
