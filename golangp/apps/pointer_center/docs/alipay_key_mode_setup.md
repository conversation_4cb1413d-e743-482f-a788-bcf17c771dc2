# 支付宝支付密钥模式配置指南

## 概述

本指南介绍如何配置支付宝支付的密钥模式。密钥模式相比证书模式配置更简单，适合快速集成和测试。

## 配置步骤

### 1. 生成应用密钥对

#### 方法一：使用支付宝开发助手（推荐）
1. 下载支付宝开放平台开发助手：https://opendocs.alipay.com/common/02kipl
2. 打开工具，选择"RSA密钥生成"
3. 密钥长度选择"2048"
4. 点击"生成密钥"
5. 保存应用私钥和应用公钥

#### 方法二：使用OpenSSL命令
```bash
# 生成RSA私钥
openssl genrsa -out app_private_key.pem 2048

# 从私钥生成公钥
openssl rsa -in app_private_key.pem -pubout -out app_public_key.pem
```

### 2. 在支付宝开放平台配置

1. 登录支付宝开放平台：https://open.alipay.com
2. 进入你的应用管理页面
3. 点击"开发设置" → "接口加签方式"
4. 选择"公钥"模式
5. 将你生成的**应用公钥**内容粘贴到"应用公钥"框中
6. 点击保存
7. 复制页面显示的**支付宝公钥**

### 3. 配置环境变量

在 `app.env` 文件中配置以下参数：

```env
# 支付宝应用ID（从开放平台获取）
ALIPAY_APP_ID=2021005178619320

# 你生成的应用私钥（去掉头尾标识，只要中间内容）
ALIPAY_PRIVATE_KEY=MIIEpQIBAAKCAQEAwDwXRMD2xAuqDgzYRYcj...

# 从支付宝平台复制的支付宝公钥
ALIPAY_PUBLIC_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB...

# 网关地址（沙箱环境）
ALIPAY_GATEWAY_URL=https://openapi.alipaydev.com/gateway.do

# 基础配置
ALIPAY_SIGN_TYPE=RSA2
ALIPAY_CHARSET=utf-8
ALIPAY_FORMAT=JSON
ALIPAY_VERSION=1.0

# 回调地址
ALIPAY_RETURN_URL=http://localhost:8012/alipay/return
ALIPAY_NOTIFY_URL=http://localhost:8012/api/alipay/notify

# 业务配置
ALIPAY_TIMEOUT_EXPRESS=30
ALIPAY_SANDBOX=true
```

## 重要说明

### 密钥格式
- **应用私钥**：去掉 `-----BEGIN PRIVATE KEY-----` 和 `-----END PRIVATE KEY-----` 标识
- **支付宝公钥**：去掉 `-----BEGIN PUBLIC KEY-----` 和 `-----END PUBLIC KEY-----` 标识
- 只保留中间的Base64编码内容，可以包含换行符

### 安全注意事项
1. **应用私钥**绝对不能泄露，只能你自己保存
2. **应用私钥**不要提交到代码仓库
3. 生产环境建议使用环境变量或密钥管理服务
4. 定期更换密钥对

### 沙箱vs正式环境

#### 沙箱环境（测试）
```env
ALIPAY_GATEWAY_URL=https://openapi.alipaydev.com/gateway.do
ALIPAY_SANDBOX=true
```

#### 正式环境（生产）
```env
ALIPAY_GATEWAY_URL=https://openapi.alipay.com/gateway.do
ALIPAY_SANDBOX=false
```

## 验证配置

### 1. 启动应用
```bash
bazel run //golangp/apps/pointer_center:pointer_center
```

### 2. 测试API
```bash
# 创建支付二维码
curl -X POST http://localhost:8012/api/v1/alipay/qrcode \
  -H "Content-Type: application/json" \
  -d '{
    "amount": "0.01",
    "subject": "测试商品",
    "user_id": "test-user-uuid"
  }'
```

### 3. 检查日志
如果配置正确，应该能看到：
- 支付宝服务初始化成功
- 二维码创建成功
- 没有密钥相关的错误

## 常见问题

### Q: 提示"invalid alipay config: alipay public_key is required"
A: 检查 `ALIPAY_PUBLIC_KEY` 是否正确配置，确保是从支付宝平台复制的支付宝公钥

### Q: 提示"failed to load alipay public key"
A: 检查支付宝公钥格式是否正确，确保去掉了头尾标识

### Q: 支付时提示签名错误
A: 检查应用私钥是否正确，确保与上传到支付宝平台的应用公钥是配对的

### Q: 异步通知验证失败
A: 检查支付宝公钥是否是最新的，支付宝可能会更新公钥

## 密钥模式 vs 证书模式

| 特性 | 密钥模式 | 证书模式 |
|------|----------|----------|
| 配置复杂度 | 简单 | 复杂 |
| 安全性 | 高 | 更高 |
| 密钥管理 | 手动更新 | 自动更新 |
| 推荐场景 | 开发测试、小型应用 | 大型生产应用 |

当前实现使用密钥模式，满足大部分业务需求。如需证书模式，可以后续扩展。

## 下一步

配置完成后，可以：
1. 使用提供的前端示例页面测试支付流程
2. 集成到你的业务系统中
3. 配置生产环境参数
4. 添加业务逻辑和错误处理

更多API使用说明请参考 `alipay_api.md` 文档。
