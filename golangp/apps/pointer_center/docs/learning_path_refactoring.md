# Learning Path Model Refactoring

## Overview
This document describes the refactoring of the learning path models to improve modularity and implement proper relationship management using junction tables.

## Changes Made

### 1. Difficulty Level Refactoring
- **Changed from**: String-based difficulty levels (e.g., "beginner", "intermediate")
- **Changed to**: Numeric difficulty levels (1-10 scale)
- **Benefits**: 
  - More granular difficulty control
  - Easier to implement adaptive learning algorithms
  - Better for AI-driven content generation

#### Difficulty Level Mapping
```go
type DifficultyLevel int

const (
    DifficultyBeginner1    DifficultyLevel = 1  // Very Easy - Basic concepts
    DifficultyBeginner2    DifficultyLevel = 2  // Easy - Simple applications
    DifficultyBeginner3    DifficultyLevel = 3  // Easy-Medium - Guided practice
    DifficultyIntermediate4 DifficultyLevel = 4  // Medium-Easy - Independent practice
    DifficultyIntermediate5 DifficultyLevel = 5  // Medium - Standard complexity
    DifficultyIntermediate6 DifficultyLevel = 6  // Medium-Hard - Complex applications
    DifficultyAdvanced7    DifficultyLevel = 7  // Hard - Advanced concepts
    DifficultyAdvanced8    DifficultyLevel = 8  // Very Hard - Expert level
    DifficultyExpert9      DifficultyLevel = 9  // Extremely Hard - Cutting edge
    DifficultyExpert10     DifficultyLevel = 10 // Master Level - Research/Innovation
)
```

### 2. Model Separation

#### Before Refactoring
- All models were in `learning_path.go`
- Direct embedding of nodes and lessons in path structures
- Array-based relationships (e.g., `[]LearningNode`)

#### After Refactoring
- **`learning_path.go`**: Core learning path models and metadata
- **`learning_node.go`**: Learning node models and node-specific logic
- **`learning_lesson.go`**: Learning lesson models and lesson-specific logic

### 3. Junction Table Implementation

#### Learning Path ↔ Learning Node Relationship
```go
// LearningPathNode represents the association between learning paths and nodes (PostgreSQL)
type LearningPathNode struct {
    ID             uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
    LearningPathID uuid.UUID `gorm:"type:uuid;not null;index:idx_path_node_path"`
    NodeID         string    `gorm:"size:100;not null;index:idx_path_node_node"`
    Order          int       `gorm:"not null"`
    IsRequired     bool      `gorm:"default:true"`
    UnlockCriteria string    `gorm:"type:text"`
    // ... timestamps
}
```

#### Learning Node ↔ Learning Lesson Relationship
```go
// NodeLesson represents the association between nodes and lessons (PostgreSQL)
type NodeLesson struct {
    ID       uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
    NodeID   string    `gorm:"size:100;not null;index:idx_node_lesson_node"`
    LessonID string    `gorm:"size:100;not null;index:idx_node_lesson_lesson"`
    Order    int       `gorm:"not null"`
    IsRequired bool    `gorm:"default:true"`
    // ... timestamps
}
```

### 4. Storage Strategy

#### PostgreSQL (Relational Data)
- `learning_paths`: Core path metadata
- `learning_nodes`: Node metadata and structure (NEW: moved from MongoDB)
- `learning_path_nodes`: Path-Node associations
- `node_lessons`: Node-Lesson associations (LessonID references MongoDB)
- `user_path_progress`: User progress tracking

#### MongoDB (Document Data)
- `learning_lessons`: Detailed lesson content and media (large content documents)
- `learning_path_details`: Complete path structure for fast retrieval
- `user_path_details`: User-specific personalization data

#### Hybrid Approach Benefits
- **PostgreSQL for structure**: Learning paths and nodes benefit from relational integrity
- **MongoDB for content**: Lessons contain large, flexible content documents
- **Junction tables**: Clean many-to-many relationships with proper foreign keys
- **Cross-database references**: NodeLesson.LessonID references MongoDB lesson_id

### 5. Benefits of New Architecture

#### Flexibility
- Easy to reorder nodes and lessons
- Support for optional/required content
- Conditional unlocking of content
- Multiple paths can share the same nodes

#### Scalability
- Efficient queries for large datasets
- Proper indexing on relationship tables
- Separation of metadata and content

#### Maintainability
- Clear separation of concerns
- Modular code structure
- Easier testing and debugging

#### AI Integration
- Granular difficulty progression
- Better data for adaptive algorithms
- Flexible content generation

### 6. Migration Considerations

#### Database Migration
1. Create new junction tables
2. Migrate existing embedded data to separate collections/tables
3. Update foreign key relationships
4. Create proper indexes

#### Code Migration
1. Update repository layer to handle new relationships
2. Modify service layer to work with junction tables
3. Update API responses to maintain backward compatibility
4. Add new endpoints for relationship management

### 7. API Impact

#### New Endpoints
- `POST /api/v1/learning-path/{id}/nodes` - Add node to path
- `DELETE /api/v1/learning-path/{id}/nodes/{nodeId}` - Remove node from path
- `PUT /api/v1/learning-path/{id}/nodes/order` - Reorder nodes
- `POST /api/v1/learning-node/{id}/lessons` - Add lesson to node
- `DELETE /api/v1/learning-node/{id}/lessons/{lessonId}` - Remove lesson from node

#### Modified Endpoints
- Path creation now requires separate node association calls
- Path retrieval includes relationship data
- Progress tracking works with new relationship structure

### 8. Performance Optimizations

#### Indexing Strategy
```sql
-- PostgreSQL indexes
CREATE INDEX idx_path_node_path ON learning_path_nodes(learning_path_id);
CREATE INDEX idx_path_node_node ON learning_path_nodes(node_id);
CREATE INDEX idx_path_node_order ON learning_path_nodes(learning_path_id, "order");
CREATE INDEX idx_node_lesson_node ON node_lessons(node_id);
CREATE INDEX idx_node_lesson_lesson ON node_lessons(lesson_id);
CREATE INDEX idx_node_lesson_order ON node_lessons(node_id, "order");
```

#### MongoDB Indexes
```javascript
// MongoDB indexes
db.learning_nodes.createIndex({ "node_id": 1 }, { unique: true });
db.learning_nodes.createIndex({ "difficulty": 1 });
db.learning_nodes.createIndex({ "skills": 1 });
db.learning_lessons.createIndex({ "lesson_id": 1 }, { unique: true });
db.learning_lessons.createIndex({ "type": 1 });
db.learning_lessons.createIndex({ "difficulty": 1 });
```

### 9. Future Enhancements

#### Planned Features
- Dynamic path generation based on user progress
- A/B testing for different node sequences
- Machine learning-driven difficulty adjustment
- Collaborative filtering for content recommendations

#### Technical Improvements
- Caching layer for frequently accessed paths
- Real-time progress synchronization
- Offline content support
- Multi-language content management

### 10. Final Architecture Summary

#### Data Storage Distribution
```
PostgreSQL (Structured Data):
├── learning_paths (path metadata)
├── learning_nodes (node structure)
├── learning_path_nodes (path ↔ node associations)
├── node_lessons (node ↔ lesson associations)
└── user_path_progress (progress tracking)

MongoDB (Content Data):
├── learning_lessons (rich content documents)
├── learning_path_details (cached complete structures)
└── user_path_details (personalization data)
```

#### Key Design Decisions
1. **Learning Nodes in PostgreSQL**: Structural data benefits from relational integrity
2. **Learning Lessons in MongoDB**: Large content documents need flexible schema
3. **Junction Tables**: Clean many-to-many relationships with proper indexing
4. **Cross-Database References**: NodeLesson.LessonID → MongoDB lesson_id
5. **Numeric Difficulty**: 1-10 scale for granular AI-driven progression

#### Benefits of This Approach
- **Performance**: Fast queries for structure, efficient storage for content
- **Scalability**: Proper indexing and relationship management
- **Flexibility**: MongoDB for evolving content schemas
- **Integrity**: PostgreSQL foreign keys for critical relationships
- **AI-Ready**: Structured data for machine learning algorithms

## Conclusion

This hybrid architecture provides the best of both worlds: relational integrity for structured learning path data and document flexibility for rich lesson content. The separation of concerns and proper relationship modeling through junction tables enables scalable, flexible learning path management while maintaining the ability to support advanced AI-driven features.
