# 支付宝支付API文档

## 概述

本文档描述了pointer_center项目中支付宝支付相关的API接口，包括二维码生成、支付状态查询、回调处理等功能。

## API接口列表

### 1. 创建支付二维码

**接口地址：** `POST /api/v1/alipay/qrcode`

**功能描述：** 生成支付宝扫码支付的二维码链接，携带用户信息

**请求参数：**
```json
{
  "amount": "0.01",           // 支付金额（必填）
  "subject": "测试商品",       // 订单标题（必填）
  "body": "这是一个测试订单",   // 订单描述（可选）
  "user_id": "uuid",          // 用户ID（必填）
  "order_id": "order123",     // 业务订单ID（可选）
  "expire_time": 10,          // 过期时间（分钟，可选，默认10分钟）
  "extra_data": "custom"      // 额外数据（可选）
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "二维码创建成功",
  "data": {
    "out_trade_no": "QR_1704067200_abc12345",
    "qr_code": "https://qr.alipay.com/bax08431...",
    "expire_time": "2024-01-01 12:10:00"
  }
}
```

### 2. 查询支付状态（轮询接口）

**接口地址：** `GET /api/v1/alipay/status`

**功能描述：** 轮询查询支付状态，用于前端定时检查支付结果

**请求参数：**
- `out_trade_no`: 商户订单号（必填）

**请求示例：**
```
GET /api/v1/alipay/status?out_trade_no=QR_1704067200_abc12345
```

**响应示例：**
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "out_trade_no": "QR_1704067200_abc12345",
    "trade_no": "2024010122001400000000000001",
    "status": "TRADE_SUCCESS",
    "amount": "0.01",
    "paid_amount": "0.01",
    "pay_time": "2024-01-01 12:01:00",
    "user_id": "uuid",
    "order_id": "order123",
    "extra_data": "custom"
  }
}
```

### 3. 支付同步回调

**接口地址：** `GET /alipay/return`

**功能描述：** 处理支付宝支付成功后的同步回调，用户支付完成后跳转到此页面

**请求参数：**
- `out_trade_no`: 商户订单号
- `trade_no`: 支付宝交易号
- `total_amount`: 订单金额

**响应：** HTML页面显示支付结果

### 4. 支付异步通知

**接口地址：** `POST /api/v1/alipay/notify`

**功能描述：** 接收支付宝的异步通知，更新订单状态

**请求方式：** POST（application/x-www-form-urlencoded）

**响应：** 
- 成功：`success`
- 失败：`fail`

## 支付状态说明

| 状态值 | 说明 |
|--------|------|
| WAIT_BUYER_PAY | 交易创建，等待买家付款 |
| TRADE_CLOSED | 未付款交易超时关闭，或支付完成后全额退款 |
| TRADE_SUCCESS | 交易支付成功 |
| TRADE_FINISHED | 交易结束，不可退款 |

## 使用流程

### 1. 前端发起支付

```javascript
// 1. 创建支付二维码
const createPayment = async () => {
  const response = await fetch('/api/v1/alipay/qrcode', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
      amount: '0.01',
      subject: '测试商品',
      body: '这是一个测试订单',
      user_id: 'user-uuid',
      order_id: 'order-123'
    })
  });
  
  const result = await response.json();
  if (result.success) {
    // 显示二维码
    showQRCode(result.data.qr_code);
    // 开始轮询支付状态
    startPolling(result.data.out_trade_no);
  }
};

// 2. 轮询支付状态
const pollPaymentStatus = async (outTradeNo) => {
  const response = await fetch(`/api/v1/alipay/status?out_trade_no=${outTradeNo}`, {
    headers: {
      'Authorization': 'Bearer ' + token
    }
  });
  
  const result = await response.json();
  if (result.success) {
    const status = result.data.status;
    if (status === 'TRADE_SUCCESS') {
      // 支付成功
      handlePaymentSuccess(result.data);
    } else if (status === 'TRADE_CLOSED') {
      // 支付失败或超时
      handlePaymentFailed();
    }
    // WAIT_BUYER_PAY 继续轮询
  }
};

// 3. 开始轮询
const startPolling = (outTradeNo) => {
  const interval = setInterval(async () => {
    await pollPaymentStatus(outTradeNo);
  }, 2000); // 每2秒查询一次
  
  // 10分钟后停止轮询
  setTimeout(() => {
    clearInterval(interval);
  }, 600000);
};
```

### 2. 后端处理流程

```go
// 1. 用户请求创建支付
// POST /api/v1/alipay/qrcode
// -> 生成商户订单号
// -> 调用支付宝API创建支付
// -> 保存支付记录到数据库
// -> 返回二维码链接

// 2. 前端轮询支付状态
// GET /api/v1/alipay/status
// -> 查询数据库中的支付记录
// -> 如果状态为等待支付，调用支付宝API查询最新状态
// -> 更新数据库记录
// -> 返回最新状态

// 3. 支付宝异步通知
// POST /api/v1/alipay/notify
// -> 验证通知签名
// -> 解析通知数据
// -> 更新数据库中的支付记录
// -> 返回success确认收到通知
```

## 错误处理

### 常见错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 参数错误 | 检查请求参数格式和必填字段 |
| 401 | 未授权 | 检查用户登录状态和token |
| 404 | 订单不存在 | 检查订单号是否正确 |
| 500 | 服务器错误 | 检查支付宝配置和网络连接 |

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 安全注意事项

1. **用户认证**：所有API接口都需要用户登录认证
2. **参数验证**：严格验证所有输入参数
3. **签名验证**：异步通知必须验证支付宝签名
4. **HTTPS**：生产环境必须使用HTTPS协议
5. **回调地址**：确保回调地址可以被支付宝服务器访问

## 测试说明

### 沙箱环境

- 网关地址：`https://openapi.alipaydev.com/gateway.do`
- 测试账号：`<EMAIL>`
- 测试密码：`111111`
- 支付密码：`111111`

### 测试流程

1. 配置沙箱环境参数
2. 创建测试订单
3. 使用支付宝沙箱APP扫码支付
4. 验证支付状态更新
5. 检查异步通知处理

## 配置说明

详细的配置说明请参考 `app.env.example` 文件中的支付宝配置部分。
