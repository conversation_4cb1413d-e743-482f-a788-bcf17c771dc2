package repositories

import (
	"go.mongodb.org/mongo-driver/mongo"
	"gorm.io/gorm"
)

// Repositories holds all repository instances
type Repositories struct {
	// PostgreSQL repositories
	StaticProfile      StaticProfileRepository
	LearningPath       LearningPathRepository
	UserMasterPath     UserMasterPathRepository
	UserPathProgress   UserPathProgressRepository
	LearningNode       LearningNodeRepository
	LearningPathNode   LearningPathNodeRepository
	NodeLesson         NodeLessonRepository
	Tag                TagRepository
	LearningPathTag    LearningPathTagRepository
	
	// MongoDB repositories
	TechCompetency     TechCompetencyRepository
	LearningLesson     LearningLessonRepository
}

// NewRepositories creates and initializes all repositories
func NewRepositories(postgresDB *gorm.DB, mongoDB *mongo.Database) *Repositories {
	return &Repositories{
		// PostgreSQL repositories
		StaticProfile:    NewStaticProfileRepository(postgresDB),
		LearningPath:     NewLearningPathRepository(postgresDB),
		UserMasterPath:   NewUserMasterPathRepository(postgresDB),
		UserPathProgress: NewUserPathProgressRepository(postgresDB),
		LearningNode:     NewLearningNodeRepository(postgresDB),
		LearningPathNode: NewLearningPathNodeRepository(postgresDB),
		NodeLesson:       NewNodeLessonRepository(postgresDB),
		Tag:              NewTagRepository(postgresDB),
		LearningPathTag:  NewLearningPathTagRepository(postgresDB),
		
		// MongoDB repositories
		TechCompetency: NewTechCompetencyRepository(mongoDB),
		LearningLesson: NewLearningLessonRepository(mongoDB),
	}
}

// Close closes all repository connections (if needed)
func (r *Repositories) Close() error {
	// Add any cleanup logic here if repositories need explicit cleanup
	// For GORM and MongoDB driver, connections are typically managed at the database level
	return nil
}
