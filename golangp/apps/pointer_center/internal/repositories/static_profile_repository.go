package repositories

import (
	"context"
	"fmt"
	"time"

	"pointer/golangp/apps/pointer_center/internal/models/student_profile"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// StaticProfileRepository interface defines methods for static profile operations
type StaticProfileRepository interface {
	Create(ctx context.Context, profile *student_profile.StaticProfile) error
	GetByID(ctx context.Context, id uuid.UUID) (*student_profile.StaticProfile, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) (*student_profile.StaticProfile, error)
	Update(ctx context.Context, profile *student_profile.StaticProfile) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*student_profile.StaticProfile, error)
	Count(ctx context.Context) (int64, error)
	GetProfilesByAge(ctx context.Context, minAge, maxAge int) ([]*student_profile.StaticProfile, error)
	GetProfilesByIndustry(ctx context.Context, industry string) ([]*student_profile.StaticProfile, error)
	GetProfilesByLearningStyle(ctx context.Context, learningStyle string) ([]*student_profile.StaticProfile, error)
	UpdateLastAccessed(ctx context.Context, userID uuid.UUID) error
	ExistsByUserID(ctx context.Context, userID uuid.UUID) (bool, error)
	GetProfileSummary(ctx context.Context, userID uuid.UUID) (*student_profile.ProfileSummary, error)
}

// staticProfileRepository implements StaticProfileRepository interface
type staticProfileRepository struct {
	db *gorm.DB
}

// NewStaticProfileRepository creates a new static profile repository
func NewStaticProfileRepository(db *gorm.DB) StaticProfileRepository {
	return &staticProfileRepository{
		db: db,
	}
}

// Create creates a new static profile
func (r *staticProfileRepository) Create(ctx context.Context, profile *student_profile.StaticProfile) error {
	if err := r.db.WithContext(ctx).Create(profile).Error; err != nil {
		return fmt.Errorf("failed to create static profile: %w", err)
	}
	return nil
}

// GetByID retrieves a static profile by ID
func (r *staticProfileRepository) GetByID(ctx context.Context, id uuid.UUID) (*student_profile.StaticProfile, error) {
	var profile student_profile.StaticProfile
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&profile).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("static profile not found with id %s", id.String())
		}
		return nil, fmt.Errorf("failed to get static profile: %w", err)
	}
	return &profile, nil
}

// GetByUserID retrieves a static profile by user ID
func (r *staticProfileRepository) GetByUserID(ctx context.Context, userID uuid.UUID) (*student_profile.StaticProfile, error) {
	var profile student_profile.StaticProfile
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&profile).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("static profile not found for user %s", userID.String())
		}
		return nil, fmt.Errorf("failed to get static profile for user: %w", err)
	}
	return &profile, nil
}

// Update updates an existing static profile
func (r *staticProfileRepository) Update(ctx context.Context, profile *student_profile.StaticProfile) error {
	if err := r.db.WithContext(ctx).Save(profile).Error; err != nil {
		return fmt.Errorf("failed to update static profile: %w", err)
	}
	return nil
}

// Delete deletes a static profile by ID (soft delete)
func (r *staticProfileRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&student_profile.StaticProfile{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete static profile: %w", err)
	}
	return nil
}

// List retrieves a list of static profiles with pagination
func (r *staticProfileRepository) List(ctx context.Context, limit, offset int) ([]*student_profile.StaticProfile, error) {
	var profiles []*student_profile.StaticProfile
	if err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Find(&profiles).Error; err != nil {
		return nil, fmt.Errorf("failed to list static profiles: %w", err)
	}
	return profiles, nil
}

// Count returns the total number of static profiles
func (r *staticProfileRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&student_profile.StaticProfile{}).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count static profiles: %w", err)
	}
	return count, nil
}

// GetProfilesByAge retrieves profiles by age range
func (r *staticProfileRepository) GetProfilesByAge(ctx context.Context, minAge, maxAge int) ([]*student_profile.StaticProfile, error) {
	var profiles []*student_profile.StaticProfile
	if err := r.db.WithContext(ctx).Where("age BETWEEN ? AND ?", minAge, maxAge).Find(&profiles).Error; err != nil {
		return nil, fmt.Errorf("failed to get profiles by age: %w", err)
	}
	return profiles, nil
}

// GetProfilesByIndustry retrieves profiles by industry
func (r *staticProfileRepository) GetProfilesByIndustry(ctx context.Context, industry string) ([]*student_profile.StaticProfile, error) {
	var profiles []*student_profile.StaticProfile
	if err := r.db.WithContext(ctx).Where("industry = ?", industry).Find(&profiles).Error; err != nil {
		return nil, fmt.Errorf("failed to get profiles by industry: %w", err)
	}
	return profiles, nil
}

// GetProfilesByLearningStyle retrieves profiles by learning style
func (r *staticProfileRepository) GetProfilesByLearningStyle(ctx context.Context, learningStyle string) ([]*student_profile.StaticProfile, error) {
	var profiles []*student_profile.StaticProfile
	if err := r.db.WithContext(ctx).Where("learning_style = ?", learningStyle).Find(&profiles).Error; err != nil {
		return nil, fmt.Errorf("failed to get profiles by learning style: %w", err)
	}
	return profiles, nil
}

// UpdateLastAccessed updates the last accessed time for a profile
func (r *staticProfileRepository) UpdateLastAccessed(ctx context.Context, userID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Model(&student_profile.StaticProfile{}).
		Where("user_id = ?", userID).
		Update("updated_at", time.Now()).Error; err != nil {
		return fmt.Errorf("failed to update last accessed: %w", err)
	}
	return nil
}

// ExistsByUserID checks if a static profile exists for a user
func (r *staticProfileRepository) ExistsByUserID(ctx context.Context, userID uuid.UUID) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&student_profile.StaticProfile{}).
		Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if profile exists: %w", err)
	}
	return count > 0, nil
}

// GetProfileSummary returns a profile summary for a user
func (r *staticProfileRepository) GetProfileSummary(ctx context.Context, userID uuid.UUID) (*student_profile.ProfileSummary, error) {
	profile, err := r.GetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	return profile.GetSummary(), nil
}
