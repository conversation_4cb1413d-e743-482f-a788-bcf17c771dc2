package repositories

import (
	"context"
	"fmt"
	"time"

	"pointer/golangp/apps/pointer_center/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// LearningPathRepository interface defines methods for learning path operations
type LearningPathRepository interface {
	Create(ctx context.Context, path *models.LearningPath) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.LearningPath, error)
	Update(ctx context.Context, path *models.LearningPath) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*models.LearningPath, error)
	ListByType(ctx context.Context, pathType models.PathType, limit, offset int) ([]*models.LearningPath, error)
	ListByDifficulty(ctx context.Context, difficulty models.DifficultyLevel, limit, offset int) ([]*models.LearningPath, error)
	ListByCreator(ctx context.Context, creatorID uuid.UUID, limit, offset int) ([]*models.LearningPath, error)
	Count(ctx context.Context) (int64, error)
	CountByType(ctx context.Context, pathType models.PathType) (int64, error)
	Search(ctx context.Context, query string, limit, offset int) ([]*models.LearningPath, error)
	GetPublicPaths(ctx context.Context, limit, offset int) ([]*models.LearningPath, error)
	GetPopularPaths(ctx context.Context, limit, offset int) ([]*models.LearningPath, error)
	GetPathsByEstimatedTime(ctx context.Context, minHours, maxHours int) ([]*models.LearningPath, error)
	IncrementUsageCount(ctx context.Context, pathID uuid.UUID) error
	UpdateRating(ctx context.Context, pathID uuid.UUID, rating float64) error
	ExistsByID(ctx context.Context, id uuid.UUID) (bool, error)
	GetPathStatistics(ctx context.Context) (map[string]interface{}, error)
}

// UserMasterPathRepository interface defines methods for user master path operations
type UserMasterPathRepository interface {
	Create(ctx context.Context, masterPath *models.UserMasterPath) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.UserMasterPath, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) (*models.UserMasterPath, error)
	Update(ctx context.Context, masterPath *models.UserMasterPath) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*models.UserMasterPath, error)
	Count(ctx context.Context) (int64, error)
	UpdateCurrentPath(ctx context.Context, userID, pathID uuid.UUID) error
	UpdateProgress(ctx context.Context, userID uuid.UUID, totalPaths, completedPaths int, overallProgress float64) error
	UpdateLastAccessed(ctx context.Context, userID uuid.UUID) error
	IncrementAdaptationCount(ctx context.Context, userID uuid.UUID) error
	ExistsByUserID(ctx context.Context, userID uuid.UUID) (bool, error)
}

// UserPathProgressRepository interface defines methods for user path progress operations
type UserPathProgressRepository interface {
	Create(ctx context.Context, progress *models.UserPathProgress) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.UserPathProgress, error)
	GetByUserAndPath(ctx context.Context, userID, pathID uuid.UUID) (*models.UserPathProgress, error)
	Update(ctx context.Context, progress *models.UserPathProgress) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*models.UserPathProgress, error)
	ListByUser(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*models.UserPathProgress, error)
	ListByPath(ctx context.Context, pathID uuid.UUID, limit, offset int) ([]*models.UserPathProgress, error)
	ListByStatus(ctx context.Context, status models.PathStatus, limit, offset int) ([]*models.UserPathProgress, error)
	Count(ctx context.Context) (int64, error)
	CountByUser(ctx context.Context, userID uuid.UUID) (int64, error)
	CountByStatus(ctx context.Context, status models.PathStatus) (int64, error)
	UpdateProgress(ctx context.Context, userID, pathID uuid.UUID, completedNodes, completedLessons int, progressPercent float64) error
	UpdateCurrentPosition(ctx context.Context, userID, pathID, nodeID uuid.UUID, lessonID string) error
	UpdateStatus(ctx context.Context, userID, pathID uuid.UUID, status models.PathStatus) error
	CompleteProgress(ctx context.Context, userID, pathID uuid.UUID) error
	AddTimeSpent(ctx context.Context, userID, pathID uuid.UUID, minutes int) error
	UpdateScore(ctx context.Context, userID, pathID uuid.UUID, score float64) error
	GetUserProgressStatistics(ctx context.Context, userID uuid.UUID) (map[string]interface{}, error)
	ExistsByUserAndPath(ctx context.Context, userID, pathID uuid.UUID) (bool, error)
}

// learningPathRepository implements LearningPathRepository interface
type learningPathRepository struct {
	db *gorm.DB
}

// userMasterPathRepository implements UserMasterPathRepository interface
type userMasterPathRepository struct {
	db *gorm.DB
}

// userPathProgressRepository implements UserPathProgressRepository interface
type userPathProgressRepository struct {
	db *gorm.DB
}

// NewLearningPathRepository creates a new learning path repository
func NewLearningPathRepository(db *gorm.DB) LearningPathRepository {
	return &learningPathRepository{db: db}
}

// NewUserMasterPathRepository creates a new user master path repository
func NewUserMasterPathRepository(db *gorm.DB) UserMasterPathRepository {
	return &userMasterPathRepository{db: db}
}

// NewUserPathProgressRepository creates a new user path progress repository
func NewUserPathProgressRepository(db *gorm.DB) UserPathProgressRepository {
	return &userPathProgressRepository{db: db}
}

// ============================================================================
// LearningPathRepository Implementation
// ============================================================================

// Create creates a new learning path
func (r *learningPathRepository) Create(ctx context.Context, path *models.LearningPath) error {
	if err := r.db.WithContext(ctx).Create(path).Error; err != nil {
		return fmt.Errorf("failed to create learning path: %w", err)
	}
	return nil
}

// GetByID retrieves a learning path by ID
func (r *learningPathRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.LearningPath, error) {
	var path models.LearningPath
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&path).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("learning path not found with id %s", id.String())
		}
		return nil, fmt.Errorf("failed to get learning path: %w", err)
	}
	return &path, nil
}

// Update updates an existing learning path
func (r *learningPathRepository) Update(ctx context.Context, path *models.LearningPath) error {
	if err := r.db.WithContext(ctx).Save(path).Error; err != nil {
		return fmt.Errorf("failed to update learning path: %w", err)
	}
	return nil
}

// Delete deletes a learning path by ID (soft delete)
func (r *learningPathRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.LearningPath{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete learning path: %w", err)
	}
	return nil
}

// List retrieves a list of learning paths with pagination
func (r *learningPathRepository) List(ctx context.Context, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Order("created_at DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to list learning paths: %w", err)
	}
	return paths, nil
}

// ListByType retrieves learning paths by type
func (r *learningPathRepository) ListByType(ctx context.Context, pathType models.PathType, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("path_type = ?", pathType).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to list paths by type: %w", err)
	}
	return paths, nil
}

// ListByDifficulty retrieves learning paths by difficulty level
func (r *learningPathRepository) ListByDifficulty(ctx context.Context, difficulty models.DifficultyLevel, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("difficulty = ?", difficulty).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to list paths by difficulty: %w", err)
	}
	return paths, nil
}

// ListByCreator retrieves learning paths by creator
func (r *learningPathRepository) ListByCreator(ctx context.Context, creatorID uuid.UUID, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("creator_id = ?", creatorID).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to list paths by creator: %w", err)
	}
	return paths, nil
}

// Count returns the total number of learning paths
func (r *learningPathRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count learning paths: %w", err)
	}
	return count, nil
}

// CountByType returns the number of paths by type
func (r *learningPathRepository) CountByType(ctx context.Context, pathType models.PathType) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).Where("path_type = ?", pathType).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count paths by type: %w", err)
	}
	return count, nil
}

// Search searches for paths by title, description, or goal
func (r *learningPathRepository) Search(ctx context.Context, query string, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	searchQuery := "%" + query + "%"
	if err := r.db.WithContext(ctx).Where("title ILIKE ? OR description ILIKE ? OR goal ILIKE ?", 
		searchQuery, searchQuery, searchQuery).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to search learning paths: %w", err)
	}
	return paths, nil
}

// GetPublicPaths retrieves public learning paths
func (r *learningPathRepository) GetPublicPaths(ctx context.Context, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("is_public = ?", true).
		Limit(limit).Offset(offset).Order("usage_count DESC, rating DESC").Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to get public paths: %w", err)
	}
	return paths, nil
}

// GetPopularPaths retrieves popular learning paths by usage count and rating
func (r *learningPathRepository) GetPopularPaths(ctx context.Context, limit, offset int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("is_public = ?", true).
		Order("usage_count DESC, rating DESC").
		Limit(limit).Offset(offset).Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to get popular paths: %w", err)
	}
	return paths, nil
}

// GetPathsByEstimatedTime retrieves paths by estimated time range
func (r *learningPathRepository) GetPathsByEstimatedTime(ctx context.Context, minHours, maxHours int) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Where("estimated_hours BETWEEN ? AND ?", minHours, maxHours).Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to get paths by estimated time: %w", err)
	}
	return paths, nil
}

// IncrementUsageCount increments the usage count of a path
func (r *learningPathRepository) IncrementUsageCount(ctx context.Context, pathID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).
		Where("id = ?", pathID).Update("usage_count", gorm.Expr("usage_count + 1")).Error; err != nil {
		return fmt.Errorf("failed to increment usage count: %w", err)
	}
	return nil
}

// UpdateRating updates the rating of a path
func (r *learningPathRepository) UpdateRating(ctx context.Context, pathID uuid.UUID, rating float64) error {
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).
		Where("id = ?", pathID).Update("rating", rating).Error; err != nil {
		return fmt.Errorf("failed to update rating: %w", err)
	}
	return nil
}

// ExistsByID checks if a path exists by ID
func (r *learningPathRepository) ExistsByID(ctx context.Context, id uuid.UUID) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).Where("id = ?", id).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if path exists: %w", err)
	}
	return count > 0, nil
}

// GetPathStatistics returns various statistics about paths
func (r *learningPathRepository) GetPathStatistics(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// Total count
	totalCount, err := r.Count(ctx)
	if err != nil {
		return nil, err
	}
	stats["total"] = totalCount
	
	// Public count
	var publicCount int64
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).Where("is_public = ?", true).Count(&publicCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count public paths: %w", err)
	}
	stats["public"] = publicCount
	
	// Average rating
	var avgRating float64
	if err := r.db.WithContext(ctx).Model(&models.LearningPath{}).Select("AVG(rating)").Scan(&avgRating).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average rating: %w", err)
	}
	stats["average_rating"] = avgRating
	
	return stats, nil
}

// ============================================================================
// UserMasterPathRepository Implementation
// ============================================================================

// Create creates a new user master path
func (r *userMasterPathRepository) Create(ctx context.Context, masterPath *models.UserMasterPath) error {
	if err := r.db.WithContext(ctx).Create(masterPath).Error; err != nil {
		return fmt.Errorf("failed to create user master path: %w", err)
	}
	return nil
}

// GetByID retrieves a user master path by ID
func (r *userMasterPathRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.UserMasterPath, error) {
	var masterPath models.UserMasterPath
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&masterPath).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user master path not found with id %s", id.String())
		}
		return nil, fmt.Errorf("failed to get user master path: %w", err)
	}
	return &masterPath, nil
}

// GetByUserID retrieves a user master path by user ID
func (r *userMasterPathRepository) GetByUserID(ctx context.Context, userID uuid.UUID) (*models.UserMasterPath, error) {
	var masterPath models.UserMasterPath
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&masterPath).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user master path not found for user %s", userID.String())
		}
		return nil, fmt.Errorf("failed to get user master path: %w", err)
	}
	return &masterPath, nil
}

// Update updates an existing user master path
func (r *userMasterPathRepository) Update(ctx context.Context, masterPath *models.UserMasterPath) error {
	if err := r.db.WithContext(ctx).Save(masterPath).Error; err != nil {
		return fmt.Errorf("failed to update user master path: %w", err)
	}
	return nil
}

// Delete deletes a user master path by ID (soft delete)
func (r *userMasterPathRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.UserMasterPath{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete user master path: %w", err)
	}
	return nil
}

// List retrieves a list of user master paths with pagination
func (r *userMasterPathRepository) List(ctx context.Context, limit, offset int) ([]*models.UserMasterPath, error) {
	var masterPaths []*models.UserMasterPath
	if err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Order("created_at DESC").Find(&masterPaths).Error; err != nil {
		return nil, fmt.Errorf("failed to list user master paths: %w", err)
	}
	return masterPaths, nil
}

// Count returns the total number of user master paths
func (r *userMasterPathRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.UserMasterPath{}).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count user master paths: %w", err)
	}
	return count, nil
}

// UpdateCurrentPath updates the current active path for a user
func (r *userMasterPathRepository) UpdateCurrentPath(ctx context.Context, userID, pathID uuid.UUID) error {
	now := time.Now()
	if err := r.db.WithContext(ctx).Model(&models.UserMasterPath{}).
		Where("user_id = ?", userID).
		Updates(map[string]interface{}{
			"current_path_id":   pathID,
			"last_accessed_at":  &now,
		}).Error; err != nil {
		return fmt.Errorf("failed to update current path: %w", err)
	}
	return nil
}

// UpdateProgress updates the progress statistics for a user
func (r *userMasterPathRepository) UpdateProgress(ctx context.Context, userID uuid.UUID, totalPaths, completedPaths int, overallProgress float64) error {
	if err := r.db.WithContext(ctx).Model(&models.UserMasterPath{}).
		Where("user_id = ?", userID).
		Updates(map[string]interface{}{
			"total_paths":       totalPaths,
			"completed_paths":   completedPaths,
			"overall_progress":  overallProgress,
		}).Error; err != nil {
		return fmt.Errorf("failed to update progress: %w", err)
	}
	return nil
}

// UpdateLastAccessed updates the last accessed time for a user
func (r *userMasterPathRepository) UpdateLastAccessed(ctx context.Context, userID uuid.UUID) error {
	now := time.Now()
	if err := r.db.WithContext(ctx).Model(&models.UserMasterPath{}).
		Where("user_id = ?", userID).
		Update("last_accessed_at", &now).Error; err != nil {
		return fmt.Errorf("failed to update last accessed: %w", err)
	}
	return nil
}

// IncrementAdaptationCount increments the adaptation count for a user
func (r *userMasterPathRepository) IncrementAdaptationCount(ctx context.Context, userID uuid.UUID) error {
	now := time.Now()
	if err := r.db.WithContext(ctx).Model(&models.UserMasterPath{}).
		Where("user_id = ?", userID).
		Updates(map[string]interface{}{
			"adaptation_count": gorm.Expr("adaptation_count + 1"),
			"last_adapted_at":  &now,
		}).Error; err != nil {
		return fmt.Errorf("failed to increment adaptation count: %w", err)
	}
	return nil
}

// ExistsByUserID checks if a master path exists for a user
func (r *userMasterPathRepository) ExistsByUserID(ctx context.Context, userID uuid.UUID) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.UserMasterPath{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if master path exists: %w", err)
	}
	return count > 0, nil
}

// ============================================================================
// UserPathProgressRepository Implementation
// ============================================================================

// Create creates a new user path progress
func (r *userPathProgressRepository) Create(ctx context.Context, progress *models.UserPathProgress) error {
	if err := r.db.WithContext(ctx).Create(progress).Error; err != nil {
		return fmt.Errorf("failed to create user path progress: %w", err)
	}
	return nil
}

// GetByID retrieves a user path progress by ID
func (r *userPathProgressRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.UserPathProgress, error) {
	var progress models.UserPathProgress
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&progress).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user path progress not found with id %s", id.String())
		}
		return nil, fmt.Errorf("failed to get user path progress: %w", err)
	}
	return &progress, nil
}

// GetByUserAndPath retrieves a user path progress by user and path ID
func (r *userPathProgressRepository) GetByUserAndPath(ctx context.Context, userID, pathID uuid.UUID) (*models.UserPathProgress, error) {
	var progress models.UserPathProgress
	if err := r.db.WithContext(ctx).Where("user_id = ? AND learning_path_id = ?", userID, pathID).First(&progress).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user path progress not found for user %s and path %s", userID.String(), pathID.String())
		}
		return nil, fmt.Errorf("failed to get user path progress: %w", err)
	}
	return &progress, nil
}

// Update updates an existing user path progress
func (r *userPathProgressRepository) Update(ctx context.Context, progress *models.UserPathProgress) error {
	if err := r.db.WithContext(ctx).Save(progress).Error; err != nil {
		return fmt.Errorf("failed to update user path progress: %w", err)
	}
	return nil
}

// Delete deletes a user path progress by ID (soft delete)
func (r *userPathProgressRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.UserPathProgress{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete user path progress: %w", err)
	}
	return nil
}

// List retrieves a list of user path progress with pagination
func (r *userPathProgressRepository) List(ctx context.Context, limit, offset int) ([]*models.UserPathProgress, error) {
	var progressList []*models.UserPathProgress
	if err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Order("created_at DESC").Find(&progressList).Error; err != nil {
		return nil, fmt.Errorf("failed to list user path progress: %w", err)
	}
	return progressList, nil
}

// ListByUser retrieves all progress for a specific user
func (r *userPathProgressRepository) ListByUser(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*models.UserPathProgress, error) {
	var progressList []*models.UserPathProgress
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&progressList).Error; err != nil {
		return nil, fmt.Errorf("failed to list progress by user: %w", err)
	}
	return progressList, nil
}

// ListByPath retrieves all progress for a specific path
func (r *userPathProgressRepository) ListByPath(ctx context.Context, pathID uuid.UUID, limit, offset int) ([]*models.UserPathProgress, error) {
	var progressList []*models.UserPathProgress
	if err := r.db.WithContext(ctx).Where("learning_path_id = ?", pathID).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&progressList).Error; err != nil {
		return nil, fmt.Errorf("failed to list progress by path: %w", err)
	}
	return progressList, nil
}

// ListByStatus retrieves progress by status
func (r *userPathProgressRepository) ListByStatus(ctx context.Context, status models.PathStatus, limit, offset int) ([]*models.UserPathProgress, error) {
	var progressList []*models.UserPathProgress
	if err := r.db.WithContext(ctx).Where("status = ?", status).
		Limit(limit).Offset(offset).Order("created_at DESC").Find(&progressList).Error; err != nil {
		return nil, fmt.Errorf("failed to list progress by status: %w", err)
	}
	return progressList, nil
}

// Count returns the total number of user path progress records
func (r *userPathProgressRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count user path progress: %w", err)
	}
	return count, nil
}

// CountByUser returns the number of progress records for a user
func (r *userPathProgressRepository) CountByUser(ctx context.Context, userID uuid.UUID) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count progress by user: %w", err)
	}
	return count, nil
}

// CountByStatus returns the number of progress records by status
func (r *userPathProgressRepository) CountByStatus(ctx context.Context, status models.PathStatus) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).Where("status = ?", status).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count progress by status: %w", err)
	}
	return count, nil
}

// UpdateProgress updates the progress metrics
func (r *userPathProgressRepository) UpdateProgress(ctx context.Context, userID, pathID uuid.UUID, completedNodes, completedLessons int, progressPercent float64) error {
	now := time.Now()
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).
		Where("user_id = ? AND learning_path_id = ?", userID, pathID).
		Updates(map[string]interface{}{
			"completed_nodes":   completedNodes,
			"completed_lessons": completedLessons,
			"progress_percent":  progressPercent,
			"last_accessed_at":  &now,
		}).Error; err != nil {
		return fmt.Errorf("failed to update progress: %w", err)
	}
	return nil
}

// UpdateCurrentPosition updates the current position in the path
func (r *userPathProgressRepository) UpdateCurrentPosition(ctx context.Context, userID, pathID, nodeID uuid.UUID, lessonID string) error {
	now := time.Now()
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).
		Where("user_id = ? AND learning_path_id = ?", userID, pathID).
		Updates(map[string]interface{}{
			"current_node_id":   nodeID,
			"current_lesson_id": lessonID,
			"last_accessed_at":  &now,
		}).Error; err != nil {
		return fmt.Errorf("failed to update current position: %w", err)
	}
	return nil
}

// UpdateStatus updates the status of the progress
func (r *userPathProgressRepository) UpdateStatus(ctx context.Context, userID, pathID uuid.UUID, status models.PathStatus) error {
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).
		Where("user_id = ? AND learning_path_id = ?", userID, pathID).
		Update("status", status).Error; err != nil {
		return fmt.Errorf("failed to update status: %w", err)
	}
	return nil
}

// CompleteProgress marks the progress as completed
func (r *userPathProgressRepository) CompleteProgress(ctx context.Context, userID, pathID uuid.UUID) error {
	now := time.Now()
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).
		Where("user_id = ? AND learning_path_id = ?", userID, pathID).
		Updates(map[string]interface{}{
			"status":         models.PathStatusCompleted,
			"completed_at":   &now,
			"progress_percent": 100.0,
		}).Error; err != nil {
		return fmt.Errorf("failed to complete progress: %w", err)
	}
	return nil
}

// AddTimeSpent adds time spent to the progress
func (r *userPathProgressRepository) AddTimeSpent(ctx context.Context, userID, pathID uuid.UUID, minutes int) error {
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).
		Where("user_id = ? AND learning_path_id = ?", userID, pathID).
		Update("total_time_spent", gorm.Expr("total_time_spent + ?", minutes)).Error; err != nil {
		return fmt.Errorf("failed to add time spent: %w", err)
	}
	return nil
}

// UpdateScore updates the average score
func (r *userPathProgressRepository) UpdateScore(ctx context.Context, userID, pathID uuid.UUID, score float64) error {
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).
		Where("user_id = ? AND learning_path_id = ?", userID, pathID).
		Update("average_score", score).Error; err != nil {
		return fmt.Errorf("failed to update score: %w", err)
	}
	return nil
}

// GetUserProgressStatistics returns progress statistics for a user
func (r *userPathProgressRepository) GetUserProgressStatistics(ctx context.Context, userID uuid.UUID) (map[string]interface{}, error) {
	stats := make(map[string]interface{})
	
	// Total paths
	totalCount, err := r.CountByUser(ctx, userID)
	if err != nil {
		return nil, err
	}
	stats["total_paths"] = totalCount
	
	// Completed paths
	var completedCount int64
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).
		Where("user_id = ? AND status = ?", userID, models.PathStatusCompleted).Count(&completedCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count completed paths: %w", err)
	}
	stats["completed_paths"] = completedCount
	
	// Average progress
	var avgProgress float64
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).
		Where("user_id = ?", userID).Select("AVG(progress_percent)").Scan(&avgProgress).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate average progress: %w", err)
	}
	stats["average_progress"] = avgProgress
	
	// Total time spent
	var totalTime int
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).
		Where("user_id = ?", userID).Select("SUM(total_time_spent)").Scan(&totalTime).Error; err != nil {
		return nil, fmt.Errorf("failed to calculate total time: %w", err)
	}
	stats["total_time_spent"] = totalTime
	
	return stats, nil
}

// ExistsByUserAndPath checks if a progress record exists for user and path
func (r *userPathProgressRepository) ExistsByUserAndPath(ctx context.Context, userID, pathID uuid.UUID) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.UserPathProgress{}).
		Where("user_id = ? AND learning_path_id = ?", userID, pathID).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if progress exists: %w", err)
	}
	return count > 0, nil
}
