load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "repositories",
    srcs = glob(["**/*.go"]),
    importpath = "pointer/golangp/apps/pointer_center/internal/repositories",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/pointer_center/internal/models",
        "//golangp/apps/pointer_center/internal/models/student_profile",
        "@com_github_google_uuid//:uuid",
        "@io_gorm_gorm//:gorm",
        "@org_mongodb_go_mongo_driver//bson",
        "@org_mongodb_go_mongo_driver//bson/primitive",
        "@org_mongodb_go_mongo_driver//mongo",
        "@org_mongodb_go_mongo_driver//mongo/options",
    ],
)
