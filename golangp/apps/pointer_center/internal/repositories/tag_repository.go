package repositories

import (
	"context"
	"fmt"

	"pointer/golangp/apps/pointer_center/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TagRepository interface defines methods for tag operations
type TagRepository interface {
	Create(ctx context.Context, tag *models.Tag) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Tag, error)
	GetByName(ctx context.Context, name string) (*models.Tag, error)
	Update(ctx context.Context, tag *models.Tag) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*models.Tag, error)
	ListByType(ctx context.Context, tagType models.TagType, limit, offset int) ([]*models.Tag, error)
	ListActive(ctx context.Context, limit, offset int) ([]*models.Tag, error)
	ListByParent(ctx context.Context, parentID *uuid.UUID, limit, offset int) ([]*models.Tag, error)
	Count(ctx context.Context) (int64, error)
	CountByType(ctx context.Context, tagType models.TagType) (int64, error)
	Search(ctx context.Context, query string, limit, offset int) ([]*models.Tag, error)
	GetSystemTags(ctx context.Context) ([]*models.Tag, error)
	GetTopLevelTags(ctx context.Context) ([]*models.Tag, error)
	GetChildTags(ctx context.Context, parentID uuid.UUID) ([]*models.Tag, error)
	IncrementUsage(ctx context.Context, tagID uuid.UUID) error
	DecrementUsage(ctx context.Context, tagID uuid.UUID) error
	UpdateStatus(ctx context.Context, tagID uuid.UUID, isActive bool) error
	ExistsByName(ctx context.Context, name string) (bool, error)
	ExistsByID(ctx context.Context, id uuid.UUID) (bool, error)
	GetMostUsedTags(ctx context.Context, limit int) ([]*models.Tag, error)
	GetTagHierarchy(ctx context.Context, rootID *uuid.UUID) ([]*models.Tag, error)
}

// LearningPathTagRepository interface defines methods for learning path tag associations
type LearningPathTagRepository interface {
	Create(ctx context.Context, pathTag *models.LearningPathTag) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.LearningPathTag, error)
	Delete(ctx context.Context, id uuid.UUID) error
	GetByPathID(ctx context.Context, pathID uuid.UUID) ([]*models.LearningPathTag, error)
	GetByTagID(ctx context.Context, tagID uuid.UUID) ([]*models.LearningPathTag, error)
	DeleteByPathID(ctx context.Context, pathID uuid.UUID) error
	DeleteByTagID(ctx context.Context, tagID uuid.UUID) error
	ExistsByPathAndTag(ctx context.Context, pathID, tagID uuid.UUID) (bool, error)
	GetTagsForPath(ctx context.Context, pathID uuid.UUID) ([]*models.Tag, error)
	GetPathsForTag(ctx context.Context, tagID uuid.UUID) ([]*models.LearningPath, error)
	GetRecommendedTagsForPath(ctx context.Context, pathID uuid.UUID) ([]*models.Tag, error)
	UpdateRelevance(ctx context.Context, pathID, tagID uuid.UUID, relevance float64) error
	SetRecommended(ctx context.Context, pathID, tagID uuid.UUID, isRecommended bool) error
	GetTagUsageStatistics(ctx context.Context) (map[uuid.UUID]int64, error)
	AddTagsToPath(ctx context.Context, pathID uuid.UUID, tagIDs []uuid.UUID, addedBy uuid.UUID) error
	RemoveTagsFromPath(ctx context.Context, pathID uuid.UUID, tagIDs []uuid.UUID) error
}

// tagRepository implements TagRepository interface
type tagRepository struct {
	db *gorm.DB
}

// learningPathTagRepository implements LearningPathTagRepository interface
type learningPathTagRepository struct {
	db *gorm.DB
}

// NewTagRepository creates a new tag repository
func NewTagRepository(db *gorm.DB) TagRepository {
	return &tagRepository{db: db}
}

// NewLearningPathTagRepository creates a new learning path tag repository
func NewLearningPathTagRepository(db *gorm.DB) LearningPathTagRepository {
	return &learningPathTagRepository{db: db}
}

// ============================================================================
// TagRepository Implementation
// ============================================================================

// Create creates a new tag
func (r *tagRepository) Create(ctx context.Context, tag *models.Tag) error {
	if err := r.db.WithContext(ctx).Create(tag).Error; err != nil {
		return fmt.Errorf("failed to create tag: %w", err)
	}
	return nil
}

// GetByID retrieves a tag by ID
func (r *tagRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Tag, error) {
	var tag models.Tag
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&tag).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("tag not found with id %s", id.String())
		}
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}
	return &tag, nil
}

// GetByName retrieves a tag by name
func (r *tagRepository) GetByName(ctx context.Context, name string) (*models.Tag, error) {
	var tag models.Tag
	if err := r.db.WithContext(ctx).Where("name = ?", name).First(&tag).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("tag not found with name %s", name)
		}
		return nil, fmt.Errorf("failed to get tag by name: %w", err)
	}
	return &tag, nil
}

// Update updates an existing tag
func (r *tagRepository) Update(ctx context.Context, tag *models.Tag) error {
	if err := r.db.WithContext(ctx).Save(tag).Error; err != nil {
		return fmt.Errorf("failed to update tag: %w", err)
	}
	return nil
}

// Delete deletes a tag by ID (soft delete)
func (r *tagRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.Tag{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete tag: %w", err)
	}
	return nil
}

// List retrieves a list of tags with pagination
func (r *tagRepository) List(ctx context.Context, limit, offset int) ([]*models.Tag, error) {
	var tags []*models.Tag
	if err := r.db.WithContext(ctx).Limit(limit).Offset(offset).Order("sort_order ASC, name ASC").Find(&tags).Error; err != nil {
		return nil, fmt.Errorf("failed to list tags: %w", err)
	}
	return tags, nil
}

// ListByType retrieves tags by type
func (r *tagRepository) ListByType(ctx context.Context, tagType models.TagType, limit, offset int) ([]*models.Tag, error) {
	var tags []*models.Tag
	if err := r.db.WithContext(ctx).Where("tag_type = ?", tagType).
		Limit(limit).Offset(offset).Order("sort_order ASC, name ASC").Find(&tags).Error; err != nil {
		return nil, fmt.Errorf("failed to list tags by type: %w", err)
	}
	return tags, nil
}

// ListActive retrieves only active tags
func (r *tagRepository) ListActive(ctx context.Context, limit, offset int) ([]*models.Tag, error) {
	var tags []*models.Tag
	if err := r.db.WithContext(ctx).Where("is_active = ?", true).
		Limit(limit).Offset(offset).Order("sort_order ASC, name ASC").Find(&tags).Error; err != nil {
		return nil, fmt.Errorf("failed to list active tags: %w", err)
	}
	return tags, nil
}

// ListByParent retrieves tags by parent ID
func (r *tagRepository) ListByParent(ctx context.Context, parentID *uuid.UUID, limit, offset int) ([]*models.Tag, error) {
	var tags []*models.Tag
	query := r.db.WithContext(ctx)
	
	if parentID == nil {
		query = query.Where("parent_id IS NULL")
	} else {
		query = query.Where("parent_id = ?", *parentID)
	}
	
	if err := query.Limit(limit).Offset(offset).Order("sort_order ASC, name ASC").Find(&tags).Error; err != nil {
		return nil, fmt.Errorf("failed to list tags by parent: %w", err)
	}
	return tags, nil
}

// Count returns the total number of tags
func (r *tagRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Tag{}).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count tags: %w", err)
	}
	return count, nil
}

// CountByType returns the number of tags by type
func (r *tagRepository) CountByType(ctx context.Context, tagType models.TagType) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Tag{}).Where("tag_type = ?", tagType).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count tags by type: %w", err)
	}
	return count, nil
}

// Search searches for tags by name or display name
func (r *tagRepository) Search(ctx context.Context, query string, limit, offset int) ([]*models.Tag, error) {
	var tags []*models.Tag
	searchQuery := "%" + query + "%"
	if err := r.db.WithContext(ctx).Where("name ILIKE ? OR display_name ILIKE ?", searchQuery, searchQuery).
		Limit(limit).Offset(offset).Order("usage_count DESC, name ASC").Find(&tags).Error; err != nil {
		return nil, fmt.Errorf("failed to search tags: %w", err)
	}
	return tags, nil
}

// GetSystemTags retrieves system tags
func (r *tagRepository) GetSystemTags(ctx context.Context) ([]*models.Tag, error) {
	var tags []*models.Tag
	if err := r.db.WithContext(ctx).Where("is_system = ?", true).Order("sort_order ASC, name ASC").Find(&tags).Error; err != nil {
		return nil, fmt.Errorf("failed to get system tags: %w", err)
	}
	return tags, nil
}

// GetTopLevelTags retrieves top-level tags (no parent)
func (r *tagRepository) GetTopLevelTags(ctx context.Context) ([]*models.Tag, error) {
	return r.ListByParent(ctx, nil, 0, 0)
}

// GetChildTags retrieves child tags of a parent
func (r *tagRepository) GetChildTags(ctx context.Context, parentID uuid.UUID) ([]*models.Tag, error) {
	return r.ListByParent(ctx, &parentID, 0, 0)
}

// IncrementUsage increments the usage count of a tag
func (r *tagRepository) IncrementUsage(ctx context.Context, tagID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Model(&models.Tag{}).
		Where("id = ?", tagID).Update("usage_count", gorm.Expr("usage_count + 1")).Error; err != nil {
		return fmt.Errorf("failed to increment tag usage: %w", err)
	}
	return nil
}

// DecrementUsage decrements the usage count of a tag
func (r *tagRepository) DecrementUsage(ctx context.Context, tagID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Model(&models.Tag{}).
		Where("id = ? AND usage_count > 0", tagID).Update("usage_count", gorm.Expr("usage_count - 1")).Error; err != nil {
		return fmt.Errorf("failed to decrement tag usage: %w", err)
	}
	return nil
}

// UpdateStatus updates the active status of a tag
func (r *tagRepository) UpdateStatus(ctx context.Context, tagID uuid.UUID, isActive bool) error {
	if err := r.db.WithContext(ctx).Model(&models.Tag{}).
		Where("id = ?", tagID).Update("is_active", isActive).Error; err != nil {
		return fmt.Errorf("failed to update tag status: %w", err)
	}
	return nil
}

// ExistsByName checks if a tag exists by name
func (r *tagRepository) ExistsByName(ctx context.Context, name string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Tag{}).Where("name = ?", name).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if tag exists by name: %w", err)
	}
	return count > 0, nil
}

// ExistsByID checks if a tag exists by ID
func (r *tagRepository) ExistsByID(ctx context.Context, id uuid.UUID) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Tag{}).Where("id = ?", id).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check if tag exists by ID: %w", err)
	}
	return count > 0, nil
}

// GetMostUsedTags retrieves the most used tags
func (r *tagRepository) GetMostUsedTags(ctx context.Context, limit int) ([]*models.Tag, error) {
	var tags []*models.Tag
	if err := r.db.WithContext(ctx).Where("is_active = ?", true).
		Order("usage_count DESC").Limit(limit).Find(&tags).Error; err != nil {
		return nil, fmt.Errorf("failed to get most used tags: %w", err)
	}
	return tags, nil
}

// GetTagHierarchy retrieves tags in a hierarchical structure
func (r *tagRepository) GetTagHierarchy(ctx context.Context, rootID *uuid.UUID) ([]*models.Tag, error) {
	// This is a simplified version. For a complete hierarchy, you might want to use
	// recursive queries or build the hierarchy in application code
	return r.ListByParent(ctx, rootID, 0, 0)
}

// ============================================================================
// LearningPathTagRepository Implementation
// ============================================================================

// Create creates a new learning path tag association
func (r *learningPathTagRepository) Create(ctx context.Context, pathTag *models.LearningPathTag) error {
	if err := r.db.WithContext(ctx).Create(pathTag).Error; err != nil {
		return fmt.Errorf("failed to create learning path tag: %w", err)
	}
	return nil
}

// GetByID retrieves a learning path tag by ID
func (r *learningPathTagRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.LearningPathTag, error) {
	var pathTag models.LearningPathTag
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&pathTag).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("learning path tag not found with id %s", id.String())
		}
		return nil, fmt.Errorf("failed to get learning path tag: %w", err)
	}
	return &pathTag, nil
}

// Delete deletes a learning path tag by ID
func (r *learningPathTagRepository) Delete(ctx context.Context, id uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.LearningPathTag{}, "id = ?", id).Error; err != nil {
		return fmt.Errorf("failed to delete learning path tag: %w", err)
	}
	return nil
}

// GetByPathID retrieves all tags for a specific path
func (r *learningPathTagRepository) GetByPathID(ctx context.Context, pathID uuid.UUID) ([]*models.LearningPathTag, error) {
	var pathTags []*models.LearningPathTag
	if err := r.db.WithContext(ctx).Where("learning_path_id = ?", pathID).Find(&pathTags).Error; err != nil {
		return nil, fmt.Errorf("failed to get tags for path: %w", err)
	}
	return pathTags, nil
}

// GetByTagID retrieves all paths for a specific tag
func (r *learningPathTagRepository) GetByTagID(ctx context.Context, tagID uuid.UUID) ([]*models.LearningPathTag, error) {
	var pathTags []*models.LearningPathTag
	if err := r.db.WithContext(ctx).Where("tag_id = ?", tagID).Find(&pathTags).Error; err != nil {
		return nil, fmt.Errorf("failed to get paths for tag: %w", err)
	}
	return pathTags, nil
}

// DeleteByPathID deletes all tags for a specific path
func (r *learningPathTagRepository) DeleteByPathID(ctx context.Context, pathID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.LearningPathTag{}, "learning_path_id = ?", pathID).Error; err != nil {
		return fmt.Errorf("failed to delete tags for path: %w", err)
	}
	return nil
}

// DeleteByTagID deletes all paths for a specific tag
func (r *learningPathTagRepository) DeleteByTagID(ctx context.Context, tagID uuid.UUID) error {
	if err := r.db.WithContext(ctx).Delete(&models.LearningPathTag{}, "tag_id = ?", tagID).Error; err != nil {
		return fmt.Errorf("failed to delete paths for tag: %w", err)
	}
	return nil
}

// ExistsByPathAndTag checks if an association exists between path and tag
func (r *learningPathTagRepository) ExistsByPathAndTag(ctx context.Context, pathID, tagID uuid.UUID) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.LearningPathTag{}).
		Where("learning_path_id = ? AND tag_id = ?", pathID, tagID).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check path-tag association: %w", err)
	}
	return count > 0, nil
}

// GetTagsForPath retrieves all tags for a specific path with details
func (r *learningPathTagRepository) GetTagsForPath(ctx context.Context, pathID uuid.UUID) ([]*models.Tag, error) {
	var tags []*models.Tag
	if err := r.db.WithContext(ctx).Table("tags").
		Joins("JOIN learning_path_tags ON tags.id = learning_path_tags.tag_id").
		Where("learning_path_tags.learning_path_id = ?", pathID).
		Order("learning_path_tags.relevance DESC, tags.name ASC").
		Find(&tags).Error; err != nil {
		return nil, fmt.Errorf("failed to get tags for path: %w", err)
	}
	return tags, nil
}

// GetPathsForTag retrieves all paths for a specific tag with details
func (r *learningPathTagRepository) GetPathsForTag(ctx context.Context, tagID uuid.UUID) ([]*models.LearningPath, error) {
	var paths []*models.LearningPath
	if err := r.db.WithContext(ctx).Table("learning_paths").
		Joins("JOIN learning_path_tags ON learning_paths.id = learning_path_tags.learning_path_id").
		Where("learning_path_tags.tag_id = ?", tagID).
		Order("learning_path_tags.relevance DESC, learning_paths.usage_count DESC").
		Find(&paths).Error; err != nil {
		return nil, fmt.Errorf("failed to get paths for tag: %w", err)
	}
	return paths, nil
}

// GetRecommendedTagsForPath retrieves recommended tags for a path
func (r *learningPathTagRepository) GetRecommendedTagsForPath(ctx context.Context, pathID uuid.UUID) ([]*models.Tag, error) {
	var tags []*models.Tag
	if err := r.db.WithContext(ctx).Table("tags").
		Joins("JOIN learning_path_tags ON tags.id = learning_path_tags.tag_id").
		Where("learning_path_tags.learning_path_id = ? AND learning_path_tags.is_recommended = ?", pathID, true).
		Order("learning_path_tags.relevance DESC").
		Find(&tags).Error; err != nil {
		return nil, fmt.Errorf("failed to get recommended tags for path: %w", err)
	}
	return tags, nil
}

// UpdateRelevance updates the relevance score of a path-tag association
func (r *learningPathTagRepository) UpdateRelevance(ctx context.Context, pathID, tagID uuid.UUID, relevance float64) error {
	if err := r.db.WithContext(ctx).Model(&models.LearningPathTag{}).
		Where("learning_path_id = ? AND tag_id = ?", pathID, tagID).
		Update("relevance", relevance).Error; err != nil {
		return fmt.Errorf("failed to update relevance: %w", err)
	}
	return nil
}

// SetRecommended sets the recommended status of a path-tag association
func (r *learningPathTagRepository) SetRecommended(ctx context.Context, pathID, tagID uuid.UUID, isRecommended bool) error {
	if err := r.db.WithContext(ctx).Model(&models.LearningPathTag{}).
		Where("learning_path_id = ? AND tag_id = ?", pathID, tagID).
		Update("is_recommended", isRecommended).Error; err != nil {
		return fmt.Errorf("failed to set recommended status: %w", err)
	}
	return nil
}

// GetTagUsageStatistics returns usage statistics for tags
func (r *learningPathTagRepository) GetTagUsageStatistics(ctx context.Context) (map[uuid.UUID]int64, error) {
	type result struct {
		TagID uuid.UUID `gorm:"column:tag_id"`
		Count int64     `gorm:"column:count"`
	}
	
	var results []result
	if err := r.db.WithContext(ctx).Table("learning_path_tags").
		Select("tag_id, COUNT(*) as count").
		Group("tag_id").
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get tag usage statistics: %w", err)
	}
	
	stats := make(map[uuid.UUID]int64)
	for _, r := range results {
		stats[r.TagID] = r.Count
	}
	
	return stats, nil
}

// AddTagsToPath adds multiple tags to a path
func (r *learningPathTagRepository) AddTagsToPath(ctx context.Context, pathID uuid.UUID, tagIDs []uuid.UUID, addedBy uuid.UUID) error {
	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, tagID := range tagIDs {
		// Check if association already exists
		exists, err := r.ExistsByPathAndTag(ctx, pathID, tagID)
		if err != nil {
			tx.Rollback()
			return err
		}
		
		if !exists {
			pathTag := &models.LearningPathTag{
				LearningPathID: pathID,
				TagID:          tagID,
				AddedBy:        addedBy,
				Relevance:      1.0,
			}
			
			if err := tx.Create(pathTag).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to add tag %s to path: %w", tagID.String(), err)
			}
		}
	}

	return tx.Commit().Error
}

// RemoveTagsFromPath removes multiple tags from a path
func (r *learningPathTagRepository) RemoveTagsFromPath(ctx context.Context, pathID uuid.UUID, tagIDs []uuid.UUID) error {
	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, tagID := range tagIDs {
		if err := tx.Delete(&models.LearningPathTag{}, 
			"learning_path_id = ? AND tag_id = ?", pathID, tagID).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to remove tag %s from path: %w", tagID.String(), err)
		}
	}

	return tx.Commit().Error
}
