/*
 * @Description: Tech competency routes for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-08-07
 */
package routes

import (
	"pointer/golangp/apps/pointer_center/internal/handlers"
	"pointer/golangp/apps/pointer_center/internal/middleware"
	"pointer/golangp/common/database/mongodb"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
)

// SetupCompetencyRoutes configures tech competency-related routes
func SetupCompetencyRoutes(api *gin.RouterGroup, logger *logging.Logger) {
	// Initialize handler
	competencyHandler := handlers.NewTechCompetencyHandler(mongodb.GetDB())

	// Tech competency routes (MongoDB)
	competency := api.Group("/competency")
	competency.Use(middleware.AuthMiddleware())
	{
		// CRUD operations
		competency.POST("", competencyHandler.CreateCompetencyGraph)
		competency.GET("/:user_id", competencyHandler.GetCompetencyGraph)
		competency.PUT("/:user_id", competencyHandler.UpdateCompetencyGraph)
		competency.DELETE("/:user_id", competencyHandler.DeleteCompetencyGraph)

		// Version management
		competency.GET("/:user_id/version/:version", competencyHandler.GetCompetencyGraphByVersion)
		competency.GET("/:user_id/versions", competencyHandler.GetVersionHistory)

		// Statistics and analytics
		competency.GET("/:user_id/stats", competencyHandler.GetCompetencyStats)

		// List operations
		competency.GET("", competencyHandler.ListCompetencyGraphs)

		// Utility endpoints for user creation
		competency.POST("/:user_id/empty", competencyHandler.CreateEmptyCompetencyGraph)
	}

	logger.Info("✅ Tech competency routes configured successfully")
}
