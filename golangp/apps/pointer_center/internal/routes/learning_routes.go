/*
 * @Description: Learning-related routes for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-08-07
 */
package routes

import (
	"pointer/golangp/apps/pointer_center/internal/handlers"
	"pointer/golangp/apps/pointer_center/internal/middleware"
	"pointer/golangp/common/database/mongodb"
	"pointer/golangp/common/database/postgres"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
)

// SetupLearningRoutes configures learning-related routes
func SetupLearningRoutes(api *gin.RouterGroup, logger *logging.Logger) {
	// Initialize handlers
	lessonHandler := handlers.NewLearningLessonHandler(mongodb.GetDB())
	pathHandler := handlers.NewLearningPathHandler(postgres.DB)
	nodeHandler := handlers.NewLearningNodeHandler(postgres.DB)

	// Learning lessons routes (MongoDB)
	lessons := api.Group("/lessons")
	lessons.Use(middleware.AuthMiddleware())
	{
		// CRUD operations
		lessons.POST("", lessonHandler.CreateLesson)
		lessons.GET("/:id", lessonHandler.GetLesson)
		lessons.GET("/lesson/:lesson_id", lessonHandler.GetLessonByLessonID)
		lessons.PUT("/:id", lessonHandler.UpdateLesson)
		lessons.DELETE("/:id", lessonHandler.DeleteLesson)

		// List and search
		lessons.GET("", lessonHandler.ListLessons)
		lessons.GET("/search", lessonHandler.SearchLessons)

		// AI-powered features
		lessons.POST("/generate", lessonHandler.GenerateLessonWithAI)
		lessons.POST("/:id/enhance", lessonHandler.EnhanceLessonWithAI)
	}

	// Learning paths routes (PostgreSQL)
	paths := api.Group("/paths")
	paths.Use(middleware.AuthMiddleware())
	{
		// CRUD operations
		paths.POST("", pathHandler.CreatePath)
		paths.GET("/:id", pathHandler.GetPath)
		paths.PUT("/:id", pathHandler.UpdatePath)
		paths.DELETE("/:id", pathHandler.DeletePath)

		// List and search
		paths.GET("", pathHandler.ListPaths)
		paths.GET("/search", pathHandler.SearchPaths)

		// Additional endpoints
		paths.GET("/creator/:creator_id", pathHandler.GetPathsByCreator)
	}

	// Learning nodes routes (PostgreSQL)
	nodes := api.Group("/nodes")
	nodes.Use(middleware.AuthMiddleware())
	{
		// CRUD operations
		nodes.POST("", nodeHandler.CreateNode)
		nodes.GET("/:id", nodeHandler.GetNode)
		nodes.PUT("/:id", nodeHandler.UpdateNode)
		nodes.DELETE("/:id", nodeHandler.DeleteNode)

		// List and search
		nodes.GET("", nodeHandler.ListNodes)
		nodes.GET("/search", nodeHandler.SearchNodes)

		// Additional endpoints
		nodes.GET("/creator/:creator_id", nodeHandler.GetNodesByCreator)
	}

	logger.Info("✅ Learning routes configured successfully")
}
