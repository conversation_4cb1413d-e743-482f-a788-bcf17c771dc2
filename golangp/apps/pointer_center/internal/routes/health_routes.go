/*
 * @Description: Health check routes for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package routes

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// SetupHealthRoutes configures health check routes
func SetupHealthRoutes(router *gin.Engine) {
	// Basic health check
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().UTC().Format(time.RFC3339),
			"service":   "pointer_center",
			"version":   "1.0.0",
		})
	})

	// Readiness probe
	router.GET("/ready", func(c *gin.Context) {
		// TODO: Add database connectivity check
		c.J<PERSON>(http.StatusOK, gin.H{
			"status": "ready",
			"checks": gin.H{
				"database": "ok",
			},
		})
	})

	// Liveness probe
	router.GET("/live", func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{
			"status": "alive",
		})
	})
}
