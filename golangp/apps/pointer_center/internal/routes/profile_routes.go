/*
 * @Description: Static profile routes for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-08-07
 */
package routes

import (
	"pointer/golangp/apps/pointer_center/internal/handlers"
	"pointer/golangp/apps/pointer_center/internal/middleware"
	"pointer/golangp/common/database/postgres"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
)

// SetupProfileRoutes configures static profile-related routes
func SetupProfileRoutes(api *gin.RouterGroup, logger *logging.Logger) {
	// Initialize handler
	profileHandler := handlers.NewStaticProfileHandler(postgres.DB)

	// Static profile routes (PostgreSQL)
	profiles := api.Group("/profiles")
	profiles.Use(middleware.AuthMiddleware())
	{
		// CRUD operations
		profiles.POST("", profileHandler.CreateProfile)
		profiles.GET("/:user_id", profileHandler.GetProfile)
		profiles.PUT("/:user_id", profileHandler.UpdateProfile)
		profiles.DELETE("/:user_id", profileHandler.DeleteProfile)

		// List operations
		profiles.GET("", profileHandler.ListProfiles)

		// Utility endpoints for user creation
		profiles.POST("/:user_id/empty", profileHandler.CreateEmptyProfile)
	}

	logger.Info("✅ Static profile routes configured successfully")
}
