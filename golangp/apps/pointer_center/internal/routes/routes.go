/*
 * @Description: Main routes configuration for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package routes

import (
	"pointer/golangp/apps/pointer_center/internal/config"
	"pointer/golangp/apps/pointer_center/internal/middleware"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes configures all routes for the application
func SetupRoutes(router *gin.Engine, cfg *config.Config, db *gorm.DB, googleClientID string, googleClientSecret string) {
	// Initialize logger
	loggerConfig := &logging.LoggerConfig{
		Level:    logging.INFO,
		LogPath:  "logs",
		FileName: "pointer_center",
		Env:      "dev",
	}
	logging.InitLogger(loggerConfig)
	logger := logging.GetLogger("pointer_center")

	// Add global middleware
	router.Use(middleware.LoggerMiddleware(logger))
	router.Use(middleware.CORSMiddleware())
	router.Use(middleware.RequestIDMiddleware())
	router.Use(gin.Recovery())

	// Setup health check routes
	SetupHealthRoutes(router)

	// API routes group
	api := router.Group("/api/v1")
	{
		// Setup authentication routes (includes user management)
		SetupAuthRoutes(api, googleClientID, googleClientSecret, logger)

		// Setup learning-related routes
		SetupLearningRoutes(api, logger)

		// Setup tech competency routes
		SetupCompetencyRoutes(api, logger)

		// Setup static profile routes
		SetupProfileRoutes(api, logger)

		// Setup Alipay payment routes
		err := SetupAlipayRoutes(router, api, cfg, db, logger)
		if err != nil {
			logger.Error("Failed to setup Alipay routes: %v", err)
		}

		// Setup WeChat payment routes
		err = SetupWeChatRoutes(router, api, cfg, db, logger)
		if err != nil {
			logger.Error("Failed to setup WeChat routes: %v", err)
		}
	}
}
