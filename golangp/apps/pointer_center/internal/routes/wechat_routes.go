/*
 * @Description: WeChat payment routes configuration
 * @Author: AI Assistant
 * @Date: 2025-07-28
 */
package routes

import (
	"pointer/golangp/apps/pointer_center/internal/config"
	"pointer/golangp/apps/pointer_center/internal/handlers"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupWeChatRoutes configures all WeChat payment related routes
func SetupWeChatRoutes(router *gin.Engine, api *gin.RouterGroup, cfg *config.Config, db *gorm.DB, logger *logging.Logger) error {
	// Create WeChat handler
	wechatHandler, err := handlers.NewWeChatHandler(cfg, db)
	if err != nil {
		logger.Error("Failed to create wechat handler: %v", err)
		return err
	}

	// API routes group for WeChat Pay (需要认证)
	wechatAPI := api.Group("/wechat")
	{
		// 创建扫码支付二维码 (需要用户认证)
		wechatAPI.POST("/qrcode", wechatHandler.CreateQRCode)

		// 创建JSAPI支付 (需要用户认证)
		wechatAPI.POST("/jsapi", wechatHandler.CreateJSAPI)

		// 查询支付状态 (轮询接口，需要用户认证)
		wechatAPI.GET("/status", wechatHandler.QueryPaymentStatus)

		// 异步通知接口 (微信服务器调用，不需要认证)
		wechatAPI.GET("/notify", wechatHandler.PaymentNotify)
	}

	// 同步回调路由 (直接挂在根路由下，不需要认证)
	// 微信支付同步回调支持 GET 和 POST 方法
	router.GET("/wechat/callback", wechatHandler.PaymentCallback)
	router.POST("/wechat/callback", wechatHandler.PaymentCallback)

	logger.Info("WeChat payment routes configured successfully")
	return nil
}
