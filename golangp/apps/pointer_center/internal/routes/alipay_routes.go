/*
 * @Description: Alipay payment routes configuration
 * @Author: AI Assistant
 * @Date: 2025-07-24
 */
package routes

import (
	"pointer/golangp/apps/pointer_center/internal/config"
	"pointer/golangp/apps/pointer_center/internal/handlers"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupAlipayRoutes configures all Alipay payment related routes
func SetupAlipayRoutes(router *gin.Engine, api *gin.RouterGroup, cfg *config.Config, db *gorm.DB, logger *logging.Logger) error {
	// Create Alipay handler
	alipayHandler, err := handlers.NewAlipayHandler(cfg, db)
	if err != nil {
		logger.Error("Failed to create alipay handler: %v", err)
		return err
	}

	// API routes group for Alipay (需要认证)
	alipayAPI := api.Group("/alipay")
	{
		// 创建支付二维码 (需要用户认证)
		alipayAPI.POST("/qrcode", alipayHandler.CreateQRCode)

		// 创建网页支付 (需要用户认证)
		alipayAPI.POST("/web", alipayHandler.CreateWebPayment)

		// 查询支付状态 (轮询接口，需要用户认证)
		alipayAPI.GET("/status", alipayHandler.QueryPaymentStatus)

		// 异步通知接口 (支付宝服务器调用，不需要认证)
		alipayAPI.POST("/notify", alipayHandler.PaymentNotify)
	}


	// 同步回调路由 (直接挂在根路由下，不需要认证)
	// 支付宝同步回调支持 GET 和 POST 方法
	router.GET("/alipay/callback", alipayHandler.PaymentCallback)
	router.POST("/alipay/callback", alipayHandler.PaymentCallback)

	logger.Info("Alipay routes configured successfully")
	return nil
}
