/*
 * @Description: Authentication routes for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package routes

import (
	"pointer/golangp/apps/pointer_center/internal/handlers"
	"pointer/golangp/apps/pointer_center/internal/middleware"
	"pointer/golangp/common/database/mongodb"
	"pointer/golangp/common/database/postgres"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
)

// SetupAuthRoutes configures authentication-related routes
func SetupAuthRoutes(api *gin.RouterGroup, googleClientID string, googleClientSecret string, logger *logging.Logger) {
	// Initialize auth handler
	authHandler := handlers.NewAuthHandler(postgres.DB, mongodb.GetDB(), googleClientID, googleClientSecret)

	auth := api.Group("/auth")
	{
		// Public routes (no authentication required)
		auth.POST("/register", authHandler.HandleRegister)
		auth.POST("/login", authHandler.HandleLogin)

		// Protected routes (authentication required)
		protected := auth.Group("")
		protected.Use(middleware.AuthMiddleware())
		{
			// Authentication management
			protected.POST("/logout", authHandler.HandleLogout)
			protected.POST("/refresh", authHandler.RefreshToken)

			// User profile management
			protected.GET("/me", authHandler.GetCurrentUser)
			protected.PUT("/profile", authHandler.UpdateProfile)

			// Password management
			protected.PUT("/password", authHandler.ChangePassword)

			// Avatar management
			protected.PUT("/avatar", authHandler.UploadAvatar)

			// Email management
			protected.PUT("/email", authHandler.RequestEmailChange)
			protected.GET("/email/verify", authHandler.VerifyEmailChange)
		}
	}
}
