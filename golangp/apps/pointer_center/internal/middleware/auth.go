/*
 * @Description: Authentication middleware for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package middleware

import (
	"strings"

	"pointer/golangp/apps/pointer_center/pkg/response"
	"pointer/golangp/common/utils"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware validates JWT tokens and sets user context
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			response.Unauthorized(c, "Authorization header required", nil)
			c.Abort()
			return
		}

		// Extract token from "Bearer <token>"
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			response.Unauthorized(c, "Invalid authorization header format", nil)
			c.Abort()
			return
		}

		// Validate JWT token
		claims, err := utils.ValidateJWT(tokenString)
		if err != nil {
			response.Unauthorized(c, "Invalid or expired token", err)
			c.Abort()
			return
		}

		// Set user context
		c.Set("user_id", claims.UserID)
		c.Set("email", claims.Email)
		c.Set("is_admin", claims.IsAdmin)

		c.Next()
	}
}

// OptionalAuthMiddleware validates JWT tokens if present but doesn't require them
func OptionalAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// Extract token from "Bearer <token>"
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.Next()
			return
		}

		// Validate JWT token
		claims, err := utils.ValidateJWT(tokenString)
		if err != nil {
			c.Next()
			return
		}

		// Set user context
		c.Set("user_id", claims.UserID)
		c.Set("email", claims.Email)
		c.Set("is_admin", claims.IsAdmin)

		c.Next()
	}
}

// AdminMiddleware ensures the user has admin privileges
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		isAdmin, exists := c.Get("is_admin")
		if !exists || !isAdmin.(bool) {
			response.Forbidden(c, "Admin access required", nil)
			c.Abort()
			return
		}

		c.Next()
	}
}

// AdminAuthMiddleware combines authentication and admin authorization
func AdminAuthMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// First validate authentication
		authMiddleware := AuthMiddleware()
		authMiddleware(c)

		// If authentication failed, abort
		if c.IsAborted() {
			return
		}

		// Then check admin privileges
		adminMiddleware := AdminMiddleware()
		adminMiddleware(c)
	})
}
