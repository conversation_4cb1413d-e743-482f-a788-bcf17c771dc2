load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "middleware",
    srcs = [
        "auth.go",
        "cors.go",
        "logger.go",
    ],
    importpath = "pointer/golangp/apps/pointer_center/internal/middleware",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/pointer_center/pkg/response",
        "//golangp/common/logging:logger",
        "//golangp/common/utils",
        "@com_github_gin_gonic_gin//:gin",
    ],
)
