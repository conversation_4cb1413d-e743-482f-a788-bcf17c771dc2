/*
 * @Description: CORS middleware for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// CORSMiddleware handles Cross-Origin Resource Sharing
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization, X-Requested-With")
		c.<PERSON>("Access-Control-Expose-Headers", "Content-Length")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
