load("@rules_go//go:def.bzl", "go_library")

# This is a meta package that aggregates all internal packages
go_library(
    name = "internal",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/pointer_center/internal/config",
        "//golangp/apps/pointer_center/internal/handlers",
        "//golangp/apps/pointer_center/internal/middleware",
        "//golangp/apps/pointer_center/internal/models",
        "//golangp/apps/pointer_center/internal/routes",
        "//golangp/apps/pointer_center/internal/services",
    ],
)
