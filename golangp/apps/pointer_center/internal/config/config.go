/*
 * @Description: Configuration management for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package config

import (
	"log"
	"os"
	"path/filepath"
	"pointer/golangp/common/payment/alipay"
	"pointer/golangp/common/payment/wechat"

	"github.com/spf13/viper"
)

// Config holds all configuration for the application
type Config struct {
	Environment string `mapstructure:"ENVIRONMENT"`
	LogPath     string `mapstructure:"LOG_PATH"`
	LogLevel    string `mapstructure:"LOG_LEVEL"`
	LogFormat   string `mapstructure:"LOG_FORMAT"`

	// Server Configuration
	ServerPort         string `mapstructure:"SERVER_PORT"`
	ServerReadTimeout  int    `mapstructure:"SERVER_READ_TIMEOUT"`
	ServerWriteTimeout int    `mapstructure:"SERVER_WRITE_TIMEOUT"`

	// Database Configuration
	DBHost     string `mapstructure:"DB_HOST"`
	DBPort     string `mapstructure:"DB_PORT"`
	DBUsername string `mapstructure:"DB_USERNAME"`
	DBPassword string `mapstructure:"DB_PASSWORD"`
	DBName     string `mapstructure:"DB_NAME"`
	DBSSLMode  string `mapstructure:"DB_SSL_MODE"`

	// MongoDB Configuration
	MongoHost     string `mapstructure:"MONGO_HOST"`
	MongoPort     string `mapstructure:"MONGO_PORT"`
	MongoUsername string `mapstructure:"MONGO_USERNAME"`
	MongoPassword string `mapstructure:"MONGO_PASSWORD"`
	MongoDatabase string `mapstructure:"MONGO_DATABASE"`
	MongoAuthDB   string `mapstructure:"MONGO_AUTH_DB"`

	// JWT Configuration
	JWTSecret      string `mapstructure:"JWT_SECRET"`
	JWTExpiryHours int    `mapstructure:"JWT_EXPIRY_HOURS"`

	// Google OAuth Configuration
	GoogleClientID     string `mapstructure:"GOOGLE_CLIENT_ID"`
	GoogleClientSecret string `mapstructure:"GOOGLE_CLIENT_SECRET"`
	GoogleRedirectURI  string `mapstructure:"GOOGLE_REDIRECT_URI"`

	// Cookie Configuration
	Domain             string `mapstructure:"DOMAIN"`
	AccessTokenMaxAge  int    `mapstructure:"ACCESS_TOKEN_MAX_AGE"`
	RefreshTokenMaxAge int    `mapstructure:"REFRESH_TOKEN_MAX_AGE"`

	// Alipay Configuration
	AlipayAppID          string `mapstructure:"ALIPAY_APP_ID"`
	AlipayPrivateKey     string `mapstructure:"ALIPAY_PRIVATE_KEY"`
	AlipayPublicKey      string `mapstructure:"ALIPAY_PUBLIC_KEY"`
	AlipayRootCert       string `mapstructure:"ALIPAY_ROOT_CERT"`
	AlipayAppCert        string `mapstructure:"ALIPAY_APP_CERT"`
	AlipayCert           string `mapstructure:"ALIPAY_CERT"`
	AlipayGatewayURL     string `mapstructure:"ALIPAY_GATEWAY_URL"`
	AlipaySignType       string `mapstructure:"ALIPAY_SIGN_TYPE"`
	AlipayCharset        string `mapstructure:"ALIPAY_CHARSET"`
	AlipayFormat         string `mapstructure:"ALIPAY_FORMAT"`
	AlipayVersion        string `mapstructure:"ALIPAY_VERSION"`
	AlipayReturnURL      string `mapstructure:"ALIPAY_RETURN_URL"`
	AlipayNotifyURL      string `mapstructure:"ALIPAY_NOTIFY_URL"`
	AlipayTimeoutExpress int    `mapstructure:"ALIPAY_TIMEOUT_EXPRESS"`
	AlipaySandbox        bool   `mapstructure:"ALIPAY_SANDBOX"`
	AlipayEncryptKey     string `mapstructure:"ALIPAY_ENCRYPT_KEY"`

	// WeChat Pay Configuration
	WeChatAppID            string `mapstructure:"WECHAT_APP_ID"`
	WeChatMchID            string `mapstructure:"WECHAT_MCH_ID"`
	WeChatAPIKey           string `mapstructure:"WECHAT_API_KEY"`
	WeChatAPIv3Key         string `mapstructure:"WECHAT_API_V3_KEY"`
	WeChatCertFile         string `mapstructure:"WECHAT_CERT_FILE"`
	WeChatKeyFile          string `mapstructure:"WECHAT_KEY_FILE"`
	WeChatSerialNo         string `mapstructure:"WECHAT_SERIAL_NO"`
	WeChatPlatformCertFile string `mapstructure:"WECHAT_PLATFORM_CERT_FILE"`
	WeChatPlatformSerialNo string `mapstructure:"WECHAT_PLATFORM_SERIAL_NO"`
	WeChatAPIVersion       string `mapstructure:"WECHAT_API_VERSION"`
	WeChatSandbox          bool   `mapstructure:"WECHAT_SANDBOX"`
	WeChatNotifyURL        string `mapstructure:"WECHAT_NOTIFY_URL"`
	WeChatTimeoutExpress   int    `mapstructure:"WECHAT_TIMEOUT_EXPRESS"`
	WeChatSignType         string `mapstructure:"WECHAT_SIGN_TYPE"`
	WeChatTradeType        string `mapstructure:"WECHAT_TRADE_TYPE"`
	WeChatSceneInfo        string `mapstructure:"WECHAT_SCENE_INFO"`
	WeChatSubMchID         string `mapstructure:"PAYMENT_SUB_MCH_ID"`
	WeChatSubAppID         string `mapstructure:"PAYMENT_SUB_APP_ID"`
	WeChatHTTPTimeout      int    `mapstructure:"WECHAT_HTTP_TIMEOUT"`
	WeChatEnableLog        bool   `mapstructure:"WECHAT_ENABLE_LOG"`
	WeChatLogLevel         string `mapstructure:"WECHAT_LOG_LEVEL"`

	// JWT Token Configuration
	AccessTokenPrivateKey  string `mapstructure:"ACCESS_TOKEN_PRIVATE_KEY"`
	AccessTokenPublicKey   string `mapstructure:"ACCESS_TOKEN_PUBLIC_KEY"`
	RefreshTokenPrivateKey string `mapstructure:"REFRESH_TOKEN_PRIVATE_KEY"`
	RefreshTokenPublicKey  string `mapstructure:"REFRESH_TOKEN_PUBLIC_KEY"`

	// Redis Configuration
	RedisHost     string `mapstructure:"REDIS_HOST"`
	RedisPort     string `mapstructure:"REDIS_PORT"`
	RedisPassword string `mapstructure:"REDIS_PASSWORD"`
	RedisDB       int    `mapstructure:"REDIS_DB"`

	// Export Configuration
	ExportMaxRecords int    `mapstructure:"EXPORT_MAX_RECORDS"`
	ExportTempDir    string `mapstructure:"EXPORT_TEMP_DIR"`

	// External API Configuration
	AIServiceURL string `mapstructure:"AI_SERVICE_URL"`
	APITimeout   int    `mapstructure:"API_TIMEOUT"`

	// Email Configuration
	SMTPHost     string `mapstructure:"SMTP_HOST"`
	SMTPPort     int    `mapstructure:"SMTP_PORT"`
	SMTPUsername string `mapstructure:"SMTP_USERNAME"`
	SMTPPassword string `mapstructure:"SMTP_PASSWORD"`
	SMTPFrom     string `mapstructure:"SMTP_FROM"`

	// CORS Configuration
	CORSAllowedOrigins string `mapstructure:"CORS_ALLOWED_ORIGINS"`
	CORSAllowedMethods string `mapstructure:"CORS_ALLOWED_METHODS"`
	CORSAllowedHeaders string `mapstructure:"CORS_ALLOWED_HEADERS"`

	// Rate Limiting
	RateLimitRequestsPerMin int `mapstructure:"RATE_LIMIT_REQUESTS_PER_MINUTE"`
	RateLimitBurst          int `mapstructure:"RATE_LIMIT_BURST"`

	// File Upload Configuration
	MaxUploadSize    int64  `mapstructure:"MAX_UPLOAD_SIZE"`
	AllowedFileTypes string `mapstructure:"ALLOWED_FILE_TYPES"`

	// Storage Configuration (S3-compatible)
	StorageProvider    string `mapstructure:"STORAGE_PROVIDER"`      // Storage provider name
	StorageEndpoint    string `mapstructure:"STORAGE_ENDPOINT"`      // S3-compatible endpoint
	StorageAccessKeyID string `mapstructure:"STORAGE_ACCESS_KEY_ID"` // Access Key ID
	StorageSecretKey   string `mapstructure:"STORAGE_SECRET_KEY"`    // Secret Access Key
	StorageUseSSL      bool   `mapstructure:"STORAGE_USE_SSL"`       // Use HTTPS
	StorageRegion      string `mapstructure:"STORAGE_REGION"`        // Storage region
	StorageBucket      string `mapstructure:"STORAGE_BUCKET"`        // Storage bucket name
	StorageBaseURL     string `mapstructure:"STORAGE_BASE_URL"`      // Base URL for accessing files
	AllowedImageExts   string `mapstructure:"ALLOWED_IMAGE_EXTS"`    // Allowed image extensions

	// AI Agents Configuration
	DifyBaseURL                string `mapstructure:"DIFY_BASE_URL"`
	DifyTimeout               int    `mapstructure:"DIFY_TIMEOUT"`
	AIAgentMaxRetries         int    `mapstructure:"AI_AGENT_MAX_RETRIES"`
	AIAgentTimeout            int    `mapstructure:"AI_AGENT_TIMEOUT"`
	
	// AI Agent API Keys
	AgentGoalPlannerAPIKey        string `mapstructure:"AGENT_GOAL_PLANNER_API_KEY"`
	AgentPathGeneratorAPIKey      string `mapstructure:"AGENT_PATH_GENERATOR_API_KEY"`
	AgentCourseGeneratorAPIKey    string `mapstructure:"AGENT_COURSE_GENERATOR_API_KEY"`
	AgentLessonGeneratorAPIKey    string `mapstructure:"AGENT_LESSON_GENERATOR_API_KEY"`
	AgentQualityAssuranceAPIKey   string `mapstructure:"AGENT_QUALITY_ASSURANCE_API_KEY"`
	AgentProfileAnalyzerAPIKey    string `mapstructure:"AGENT_PROFILE_ANALYZER_API_KEY"`

	// Security Configuration
	EnableHTTPS    bool   `mapstructure:"ENABLE_HTTPS"`
	SSLCertPath    string `mapstructure:"SSL_CERT_PATH"`
	SSLKeyPath     string `mapstructure:"SSL_KEY_PATH"`
	TrustedProxies string `mapstructure:"TRUSTED_PROXIES"`

	// Monitoring Configuration
	EnableMetrics       bool `mapstructure:"ENABLE_METRICS"`
	MetricsPort         int  `mapstructure:"METRICS_PORT"`
	HealthCheckInterval int  `mapstructure:"HEALTH_CHECK_INTERVAL"`

	// Feature Flags
	EnableExport         bool `mapstructure:"ENABLE_EXPORT"`
	EnableAnalytics      bool `mapstructure:"ENABLE_ANALYTICS"`
	EnableRealTimeUpdate bool `mapstructure:"ENABLE_REAL_TIME_UPDATES"`
	EnableSwaggerUI      bool `mapstructure:"ENABLE_SWAGGER_UI"`

	// Third-party Integrations

	// Development/Debug Settings
	DebugMode       bool `mapstructure:"DEBUG_MODE"`
	EnableProfiling bool `mapstructure:"ENABLE_PROFILING"`
	ProfilingPort   int  `mapstructure:"PROFILING_PORT"`
}

func init() {
	var envPath string
	if sr := os.Getenv("TEST_SRCDIR"); sr != "" {
		ws := os.Getenv("TEST_WORKSPACE")
		if ws == "" {
			ws = filepath.Base(sr)
		}
		envPath = filepath.Join(sr, ws, "golangp", "apps", "pointer_center", "app.env")
	} else {
		envPath = filepath.Join("golangp", "apps", "pointer_center", "app.env")
	}

	// 如果指定路径不存在，尝试使用同级目录下的 app.env
	if _, err := os.Stat(envPath); os.IsNotExist(err) {
		envPath = "app.env"
	}

	viper.SetConfigFile(envPath)
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		log.Printf("Warning: Could not read config file: %v", err)
	}
}

// Load reads configuration from environment variables and config file
func Load() *Config {
	var cfg Config

	if err := viper.Unmarshal(&cfg); err != nil {
		log.Fatalf("Unable to decode config: %v", err)
	}

	// Set default values
	setDefaults(&cfg)

	return &cfg
}

func setDefaults(cfg *Config) {
	if cfg.Environment == "" {
		cfg.Environment = "development"
	}
	if cfg.ServerPort == "" {
		cfg.ServerPort = "8080"
	}
	if cfg.ServerReadTimeout == 0 {
		cfg.ServerReadTimeout = 30
	}
	if cfg.ServerWriteTimeout == 0 {
		cfg.ServerWriteTimeout = 30
	}
	if cfg.DBHost == "" {
		cfg.DBHost = "localhost"
	}
	if cfg.DBPort == "" {
		cfg.DBPort = "5432"
	}
	if cfg.DBSSLMode == "" {
		cfg.DBSSLMode = "disable"
	}
	
	// Set default MongoDB configuration
	if cfg.MongoHost == "" {
		cfg.MongoHost = "localhost"
	}
	if cfg.MongoPort == "" {
		cfg.MongoPort = "27017"
	}
	if cfg.MongoDatabase == "" {
		cfg.MongoDatabase = "pointer_center"
	}
	if cfg.MongoAuthDB == "" {
		cfg.MongoAuthDB = "admin"
	}
	
	if cfg.JWTSecret == "" {
		cfg.JWTSecret = "your-secret-key"
	}
	if cfg.JWTExpiryHours == 0 {
		cfg.JWTExpiryHours = 24
	}
	if cfg.RedisHost == "" {
		cfg.RedisHost = "localhost"
	}
	if cfg.RedisPort == "" {
		cfg.RedisPort = "6379"
	}
	if cfg.ExportMaxRecords == 0 {
		cfg.ExportMaxRecords = 10000
	}
	if cfg.ExportTempDir == "" {
		cfg.ExportTempDir = "/tmp"
	}
	if cfg.APITimeout == 0 {
		cfg.APITimeout = 30
	}
	if cfg.CORSAllowedOrigins == "" {
		cfg.CORSAllowedOrigins = "*"
	}
	if cfg.RateLimitRequestsPerMin == 0 {
		cfg.RateLimitRequestsPerMin = 100
	}
	if cfg.MaxUploadSize == 0 {
		cfg.MaxUploadSize = 10485760 // 10MB
	}
	if cfg.LogLevel == "" {
		cfg.LogLevel = "info"
	}
	if cfg.LogFormat == "" {
		cfg.LogFormat = "json"
	}
	if cfg.AccessTokenMaxAge == 0 {
		cfg.AccessTokenMaxAge = 15 // 15 minutes
	}
	if cfg.RefreshTokenMaxAge == 0 {
		cfg.RefreshTokenMaxAge = 10080 // 7 days
	}
	if cfg.Domain == "" {
		cfg.Domain = "localhost"
	}

	// Set default storage configuration
	if cfg.StorageProvider == "" {
		cfg.StorageProvider = "minio"
	}
	if cfg.StorageEndpoint == "" {
		cfg.StorageEndpoint = "localhost:9000"
	}
	if cfg.StorageRegion == "" {
		cfg.StorageRegion = "us-east-1"
	}
	if cfg.StorageBucket == "" {
		cfg.StorageBucket = "pointer-center"
	}
	if cfg.StorageBaseURL == "" {
		cfg.StorageBaseURL = "http://localhost:9000"
	}
	if cfg.AllowedImageExts == "" {
		cfg.AllowedImageExts = "jpg,jpeg,png,webp"
	}

	// Set default email configuration
	if cfg.SMTPPort == 0 {
		cfg.SMTPPort = 587
	}
	if cfg.SMTPFrom == "" {
		cfg.SMTPFrom = "<EMAIL>"
	}

	// Set default Alipay configuration
	if cfg.AlipaySignType == "" {
		cfg.AlipaySignType = "RSA2"
	}
	if cfg.AlipayCharset == "" {
		cfg.AlipayCharset = "utf-8"
	}
	if cfg.AlipayFormat == "" {
		cfg.AlipayFormat = "JSON"
	}
	if cfg.AlipayVersion == "" {
		cfg.AlipayVersion = "1.0"
	}
	if cfg.AlipayTimeoutExpress == 0 {
		cfg.AlipayTimeoutExpress = 30 // 默认30分钟
	}

	// Set default WeChat Pay configuration
	// if cfg.WeChatAPIVersion == "" {
	// 	cfg.WeChatAPIVersion = "v3"
	// }
	// if cfg.WeChatSignType == "" {
	// 	cfg.WeChatSignType = "WECHATPAY2-SHA256-RSA2048"
	// }
	// if cfg.WeChatTradeType == "" {
	// 	cfg.WeChatTradeType = "NATIVE"
	// }
	if cfg.WeChatTimeoutExpress == 0 {
		cfg.WeChatTimeoutExpress = 30 // 默认30分钟
	}
	if cfg.WeChatHTTPTimeout == 0 {
		cfg.WeChatHTTPTimeout = 30 // 默认30秒
	}
	if cfg.WeChatLogLevel == "" {
		cfg.WeChatLogLevel = "info"
	}

	// Set default AI Agents configuration
	if cfg.DifyBaseURL == "" {
		cfg.DifyBaseURL = "http://localhost:8001"
	}
	if cfg.DifyTimeout == 0 {
		cfg.DifyTimeout = 60 // 默认60秒
	}
	if cfg.AIAgentMaxRetries == 0 {
		cfg.AIAgentMaxRetries = 3
	}
	if cfg.AIAgentTimeout == 0 {
		cfg.AIAgentTimeout = 30 // 默认30秒
	}

	// 根据沙箱模式设置默认网关地址
	// if cfg.AlipayGatewayURL == "" {
	// 	if cfg.AlipaySandbox {
	// 		cfg.AlipayGatewayURL = "https://openapi-sandbox.dl.alipaydev.com/gateway.do"
	// 	} else {
	// 		cfg.AlipayGatewayURL = "https://openapi.alipay.com/gateway.do"
	// 	}
	// }
}

// GetAgentAPIKeys returns a map of agent types to their API keys
func (cfg *Config) GetAgentAPIKeys() map[string]string {
	return map[string]string{
		"goal_planner":      cfg.AgentGoalPlannerAPIKey,
		"path_generator":    cfg.AgentPathGeneratorAPIKey,
		"course_generator":  cfg.AgentCourseGeneratorAPIKey,
		"lesson_generator":  cfg.AgentLessonGeneratorAPIKey,
		"quality_assurance": cfg.AgentQualityAssuranceAPIKey,
		"profile_analyzer":  cfg.AgentProfileAnalyzerAPIKey,
	}
}

// GetAlipayConfig creates an Alipay configuration from the main config
func (cfg *Config) GetAlipayConfig() *alipay.Config {
	return &alipay.Config{
		AppID:          cfg.AlipayAppID,
		PrivateKey:     cfg.AlipayPrivateKey,
		PublicKey:      cfg.AlipayPublicKey,
		RootCert:       cfg.AlipayRootCert,
		AppCert:        cfg.AlipayAppCert,
		AlipayCert:     cfg.AlipayCert,
		GatewayURL:     cfg.AlipayGatewayURL,
		SignType:       cfg.AlipaySignType,
		Charset:        cfg.AlipayCharset,
		Format:         cfg.AlipayFormat,
		Version:        cfg.AlipayVersion,
		ReturnURL:      cfg.AlipayReturnURL,
		NotifyURL:      cfg.AlipayNotifyURL,
		TimeoutExpress: cfg.AlipayTimeoutExpress,
		Sandbox:        cfg.AlipaySandbox,
		EncryptKey:     cfg.AlipayEncryptKey,
	}
}

// GetWeChatConfig creates a WeChat Pay configuration from the main config
func (cfg *Config) GetWeChatConfig() *wechat.Config {
	return &wechat.Config{
		AppID:            cfg.WeChatAppID,
		MchID:            cfg.WeChatMchID,
		APIKey:           cfg.WeChatAPIKey,
		APIv3Key:         cfg.WeChatAPIv3Key,
		CertFile:         cfg.WeChatCertFile,
		KeyFile:          cfg.WeChatKeyFile,
		SerialNo:         cfg.WeChatSerialNo,
		PlatformCertFile: cfg.WeChatPlatformCertFile,
		PlatformSerialNo: cfg.WeChatPlatformSerialNo,
		APIVersion:       cfg.WeChatAPIVersion,
		Sandbox:          cfg.WeChatSandbox,
		NotifyURL:        cfg.WeChatNotifyURL,
		TimeoutExpress:   cfg.WeChatTimeoutExpress,
		SignType:         cfg.WeChatSignType,
		TradeType:        cfg.WeChatTradeType,
		SceneInfo:        cfg.WeChatSceneInfo,
		SubMchID:         cfg.WeChatSubMchID,
		SubAppID:         cfg.WeChatSubAppID,
		HTTPTimeout:      cfg.WeChatHTTPTimeout,
		EnableLog:        cfg.WeChatEnableLog,
		LogLevel:         cfg.WeChatLogLevel,
	}
}
