package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// TagType represents the type of tag
type TagType string

const (
	TagTypeGeneral     TagType = "general"     // General purpose tag
	TagTypeTechnology  TagType = "technology"  // Technology/skill tag
	TagTypeCategory    TagType = "category"    // Category tag
	TagTypeDifficulty  TagType = "difficulty"  // Difficulty level tag
	TagTypeIndustry    TagType = "industry"    // Industry specific tag
	TagTypeLanguage    TagType = "language"    // Programming language tag
	TagTypeFramework   TagType = "framework"   // Framework tag
	TagTypeTool        TagType = "tool"        // Tool tag
	TagTypeConcept     TagType = "concept"     // Concept tag
)

// Tag represents a tag that can be associated with learning paths
type Tag struct {
	ID          uuid.UUID  `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:标签唯一标识符" json:"id"`
	Name        string     `gorm:"size:100;not null;unique;comment:标签名称" json:"name"`
	DisplayName string     `gorm:"size:100;not null;comment:标签显示名称" json:"display_name"`
	Description string     `gorm:"type:text;comment:标签描述" json:"description"`
	TagType     TagType    `gorm:"size:50;not null;comment:标签类型" json:"tag_type"`
	Color       string     `gorm:"size:20;comment:标签颜色(HEX)" json:"color"`
	Icon        string     `gorm:"size:100;comment:标签图标" json:"icon"`
	
	// Usage Statistics
	UsageCount  int        `gorm:"default:0;comment:使用次数" json:"usage_count"`
	IsSystem    bool       `gorm:"default:false;comment:是否为系统标签" json:"is_system"`
	IsActive    bool       `gorm:"default:true;comment:是否激活" json:"is_active"`
	
	// Hierarchy support
	ParentID    *uuid.UUID `gorm:"type:uuid;index:idx_tag_parent;comment:父标签ID" json:"parent_id"`
	Level       int        `gorm:"default:0;comment:标签层级" json:"level"`
	SortOrder   int        `gorm:"default:0;comment:排序" json:"sort_order"`
	
	// Metadata
	CreatedBy   uuid.UUID  `gorm:"type:uuid;not null;comment:创建者ID" json:"created_by"`
	
	// Timestamps
	CreatedAt   time.Time     `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt   time.Time     `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index:idx_tag_deleted_at;comment:软删除时间戳" json:"-"`
}

// LearningPathTag represents the association between learning paths and tags
type LearningPathTag struct {
	ID             uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:关联唯一标识符" json:"id"`
	LearningPathID uuid.UUID `gorm:"type:uuid;not null;index:idx_learning_path_tag_path;comment:学习路径ID" json:"learning_path_id"`
	TagID          uuid.UUID `gorm:"type:uuid;not null;index:idx_learning_path_tag_tag;comment:标签ID" json:"tag_id"`
	
	// Association metadata
	AddedBy        uuid.UUID  `gorm:"type:uuid;not null;comment:添加者ID" json:"added_by"`
	IsRecommended  bool       `gorm:"default:false;comment:是否为推荐标签" json:"is_recommended"`
	Relevance      float64    `gorm:"default:1.0;comment:相关性分数(0-1)" json:"relevance"`
	
	// Timestamps
	CreatedAt      time.Time  `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	
	// Foreign key relationships
	LearningPath   LearningPath `gorm:"foreignKey:LearningPathID;constraint:OnDelete:CASCADE" json:"learning_path,omitempty"`
	Tag            Tag          `gorm:"foreignKey:TagID;constraint:OnDelete:CASCADE" json:"tag,omitempty"`
}

// BeforeCreate sets the ID if not provided for Tag
func (t *Tag) BeforeCreate(tx *gorm.DB) error {
	if t.ID == uuid.Nil {
		t.ID = uuid.New()
	}
	return nil
}

// BeforeCreate sets the ID if not provided for LearningPathTag
func (lpt *LearningPathTag) BeforeCreate(tx *gorm.DB) error {
	if lpt.ID == uuid.Nil {
		lpt.ID = uuid.New()
	}
	return nil
}

// IncrementUsage increments the tag usage count
func (t *Tag) IncrementUsage() {
	t.UsageCount++
}

// DecrementUsage decrements the tag usage count
func (t *Tag) DecrementUsage() {
	if t.UsageCount > 0 {
		t.UsageCount--
	}
}

// IsSystemTag checks if the tag is a system tag
func (t *Tag) IsSystemTag() bool {
	return t.IsSystem
}

// IsActiveTag checks if the tag is active
func (t *Tag) IsActiveTag() bool {
	return t.IsActive
}

// HasParent checks if the tag has a parent
func (t *Tag) HasParent() bool {
	return t.ParentID != nil
}

// TableName returns the table name for the Tag model
func (Tag) TableName() string {
	return "tags"
}

// TableName returns the table name for the LearningPathTag model
func (LearningPathTag) TableName() string {
	return "learning_path_tags"
}


// TODO: Tag的辅助方法，后续使用redis位图替代查询等
// TagWithStats represents a tag with usage statistics
type TagWithStats struct {
	Tag
	LearningPathCount int `json:"learning_path_count"`
	RecentUsage       int `json:"recent_usage"`
}

// PopularTag represents a popular tag for recommendations
type PopularTag struct {
	TagID       uuid.UUID `json:"tag_id"`
	Name        string    `json:"name"`
	DisplayName string    `json:"display_name"`
	UsageCount  int       `json:"usage_count"`
	TagType     TagType   `json:"tag_type"`
}

// LearningPathWithTags represents a learning path with its associated tags
type LearningPathWithTags struct {
	LearningPath
	Tags         []Tag `json:"tags"`
	TagNames     []string `json:"tag_names"`     // For quick access to tag names
	TagTypes     []TagType `json:"tag_types"`    // For filtering by tag types
}

// TagAssignmentRequest represents a request to assign tags to a learning path
type TagAssignmentRequest struct {
	LearningPathID uuid.UUID   `json:"learning_path_id"`
	TagIDs         []uuid.UUID `json:"tag_ids"`
	AddedBy        uuid.UUID   `json:"added_by"`
	IsRecommended  bool        `json:"is_recommended"`
}

// TagSearchCriteria represents criteria for searching tags
type TagSearchCriteria struct {
	Name        string    `json:"name"`
	TagType     TagType   `json:"tag_type"`
	ParentID    *uuid.UUID `json:"parent_id"`
	IsActive    *bool     `json:"is_active"`
	IsSystem    *bool     `json:"is_system"`
	MinUsage    int       `json:"min_usage"`
	Limit       int       `json:"limit"`
	Offset      int       `json:"offset"`
}

// LearningPathTagFilter represents filters for learning path tags
type LearningPathTagFilter struct {
	TagIDs       []uuid.UUID `json:"tag_ids"`
	TagTypes     []TagType   `json:"tag_types"`
	TagNames     []string    `json:"tag_names"`
	Operator     string      `json:"operator"` // "AND" or "OR"
	MinRelevance float64     `json:"min_relevance"`
}

// TagStatistics represents statistics about tag usage
type TagStatistics struct {
	TotalTags              int                    `json:"total_tags"`
	ActiveTags             int                    `json:"active_tags"`
	SystemTags             int                    `json:"system_tags"`
	UserTags               int                    `json:"user_tags"`
	TagsByType             map[TagType]int        `json:"tags_by_type"`
	MostUsedTags           []PopularTag           `json:"most_used_tags"`
	RecentlyCreatedTags    []Tag                  `json:"recently_created_tags"`
	UnusedTags             []Tag                  `json:"unused_tags"`
}