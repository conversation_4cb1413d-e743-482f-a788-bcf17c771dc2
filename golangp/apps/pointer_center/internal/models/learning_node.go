package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NodeStatus represents the status of a learning node
type NodeStatus string

const (
	NodeStatusDraft     NodeStatus = "draft"     // Node is being created
	NodeStatusActive    NodeStatus = "active"    // Node is active and available
	NodeStatusArchived  NodeStatus = "archived"  // Node is archived
	NodeStatusDeprecated NodeStatus = "deprecated" // Node is deprecated
)

// LearningNode represents a node within a learning path (stored in PostgreSQL)
type LearningNode struct {
	ID                 uuid.UUID      `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:学习节点唯一标识符" json:"id"`
	Title              string         `gorm:"size:255;not null;comment:节点标题" json:"title"`
	Description        string         `gorm:"type:text;comment:节点描述" json:"description"`
	EstimatedHours     int            `gorm:"default:1;comment:预计学习时长(小时)" json:"estimated_hours"`
	Difficulty         DifficultyLevel `gorm:"type:int;default:5;comment:难度级别(1-10)" json:"difficulty"`
	
	// Status and metadata
	Prerequisites      string         `gorm:"type:jsonb;comment:前置条件(JSON数组)" json:"prerequisites"`
	Status             NodeStatus     `gorm:"size:50;default:'draft';comment:节点状态" json:"status"`
	CreatedBy          uuid.UUID      `gorm:"type:uuid;comment:创建者ID" json:"created_by"`
	UpdatedBy          uuid.UUID      `gorm:"type:uuid;comment:更新者ID" json:"updated_by"`
	
	// Timestamps
	CreatedAt          time.Time      `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt          time.Time      `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt          gorm.DeletedAt `gorm:"index:idx_learning_nodes_deleted_at;comment:软删除时间戳" json:"-"`
}

// LearningPathNode represents the association between learning paths and nodes (PostgreSQL)
type LearningPathNode struct {
	ID             uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:关联唯一标识符" json:"id"`
	LearningPathID uuid.UUID `gorm:"type:uuid;not null;index:idx_path_node_path;comment:学习路径ID" json:"learning_path_id"`
	NodeID         uuid.UUID `gorm:"type:uuid;not null;index:idx_path_node_node;comment:学习节点ID" json:"node_id"`
	
	// Timestamps
	CreatedAt      time.Time      `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt      time.Time      `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt      gorm.DeletedAt `gorm:"index:idx_path_node_deleted_at;comment:软删除时间戳" json:"-"`
	
	// Associations
	LearningPath   LearningPath   `gorm:"foreignKey:LearningPathID;references:ID" json:"learning_path,omitempty"`
	Node           LearningNode   `gorm:"foreignKey:NodeID;references:ID" json:"node,omitempty"`
}

// NodeLesson represents the association between nodes and lessons (PostgreSQL)
type NodeLesson struct {
	ID       uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:关联唯一标识符" json:"id"`
	NodeID   uuid.UUID `gorm:"type:uuid;not null;index:idx_node_lesson_node;comment:学习节点ID" json:"node_id"`
	LessonID string    `gorm:"size:100;not null;index:idx_node_lesson_lesson;comment:课程ID(MongoDB)" json:"lesson_id"`
	Order    int       `gorm:"not null;comment:课程在节点中的顺序" json:"order"`

	// Timestamps
	CreatedAt time.Time      `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_node_lesson_deleted_at;comment:软删除时间戳" json:"-"`

	// Associations
	Node     LearningNode   `gorm:"foreignKey:NodeID;references:ID" json:"node,omitempty"`
	// Note: Lesson is stored in MongoDB, so no direct GORM association
}

// BeforeCreate sets the ID if not provided for LearningNode
func (ln *LearningNode) BeforeCreate(tx *gorm.DB) error {
	if ln.ID == uuid.Nil {
		ln.ID = uuid.New()
	}
	return nil
}

// BeforeCreate sets the ID if not provided for LearningPathNode
func (lpn *LearningPathNode) BeforeCreate(tx *gorm.DB) error {
	if lpn.ID == uuid.Nil {
		lpn.ID = uuid.New()
	}
	return nil
}

// BeforeCreate sets the ID if not provided for NodeLesson
func (nl *NodeLesson) BeforeCreate(tx *gorm.DB) error {
	if nl.ID == uuid.Nil {
		nl.ID = uuid.New()
	}
	return nil
}

// TableName returns the table name for the LearningNode model
func (LearningNode) TableName() string {
	return "learning_nodes"
}

// TableName returns the table name for the LearningPathNode model
func (LearningPathNode) TableName() string {
	return "learning_path_nodes"
}

// TableName returns the table name for the NodeLesson model
func (NodeLesson) TableName() string {
	return "node_lessons"
}

// NewLearningNode creates a new learning node
func NewLearningNode(nodeID, title string) *LearningNode {
	return &LearningNode{
		Title:              title,
		EstimatedHours:     1,
		Difficulty:         DifficultyIntermediate5,
		Prerequisites:      "[]",
		Status:             NodeStatusDraft,
	}
}

// GetPrerequisites returns prerequisites as string slice
func (ln *LearningNode) GetPrerequisites() ([]string, error) {
	var prerequisites []string
	if ln.Prerequisites == "" || ln.Prerequisites == "null" {
		return prerequisites, nil
	}
	err := json.Unmarshal([]byte(ln.Prerequisites), &prerequisites)
	return prerequisites, err
}

// SetPrerequisites sets prerequisites from string slice
func (ln *LearningNode) SetPrerequisites(prerequisites []string) error {
	data, err := json.Marshal(prerequisites)
	if err != nil {
		return err
	}
	ln.Prerequisites = string(data)
	return nil
}

// AddPrerequisite adds a prerequisite node ID
func (ln *LearningNode) AddPrerequisite(nodeID string) error {
	prerequisites, err := ln.GetPrerequisites()
	if err != nil {
		return err
	}
	
	// Check if already exists
	for _, prereq := range prerequisites {
		if prereq == nodeID {
			return nil // Already exists
		}
	}
	
	prerequisites = append(prerequisites, nodeID)
	return ln.SetPrerequisites(prerequisites)
}

// RemovePrerequisite removes a prerequisite node ID
func (ln *LearningNode) RemovePrerequisite(nodeID string) error {
	prerequisites, err := ln.GetPrerequisites()
	if err != nil {
		return err
	}
	
	for i, prereq := range prerequisites {
		if prereq == nodeID {
			prerequisites = append(prerequisites[:i], prerequisites[i+1:]...)
			break
		}
	}
	
	return ln.SetPrerequisites(prerequisites)
}

// IsValidDifficulty checks if the difficulty level is valid
func (ln *LearningNode) IsValidDifficulty() bool {
	return ln.Difficulty.IsValid()
}

// Activate sets the node status to active
func (ln *LearningNode) Activate() {
	ln.Status = NodeStatusActive
}

// Archive sets the node status to archived
func (ln *LearningNode) Archive() {
	ln.Status = NodeStatusArchived
}

// Deprecate sets the node status to deprecated
func (ln *LearningNode) Deprecate() {
	ln.Status = NodeStatusDeprecated
}

// IsActive checks if the node is active
func (ln *LearningNode) IsActive() bool {
	return ln.Status == NodeStatusActive
}

// String returns the string representation of node status
func (ns NodeStatus) String() string {
	return string(ns)
}

// IsValid checks if the node status is valid
func (ns NodeStatus) IsValid() bool {
	switch ns {
	case NodeStatusDraft, NodeStatusActive, NodeStatusArchived, NodeStatusDeprecated:
		return true
	default:
		return false
	}
}
