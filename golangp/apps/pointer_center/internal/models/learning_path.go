package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PathType represents the type of learning path
type PathType string

const (
	PathTypePersonalized PathType = "personalized" // AI-generated personalized path
	PathTypeStandard     PathType = "standard"     // Pre-defined standard path
	PathTypeCustom       PathType = "custom"       // User-customized path
	PathTypeAdaptive     PathType = "adaptive"     // Dynamically adaptive path
)

// PathStatus represents the status of a learning path
type PathStatus string

const (
	PathStatusDraft      PathStatus = "draft"       // Path is being created
	PathStatusActive     PathStatus = "active"      // Path is active and can be followed
	PathStatusCompleted  PathStatus = "completed"   // Path has been completed
	PathStatusArchived   PathStatus = "archived"    // Path is archived
	PathStatusCancelled  PathStatus = "cancelled"   // Path was cancelled
)

// DifficultyLevel represents difficulty level using numeric scale 1-10
type DifficultyLevel int

const (
	DifficultyBeginner1    DifficultyLevel = 1  // Very Easy - Basic concepts
	DifficultyBeginner2    DifficultyLevel = 2  // Easy - Simple applications
	DifficultyBeginner3    DifficultyLevel = 3  // Easy-Medium - Guided practice
	DifficultyIntermediate4 DifficultyLevel = 4  // Medium-Easy - Independent practice
	DifficultyIntermediate5 DifficultyLevel = 5  // Medium - Standard complexity
	DifficultyIntermediate6 DifficultyLevel = 6  // Medium-Hard - Complex applications
	DifficultyAdvanced7    DifficultyLevel = 7  // Hard - Advanced concepts
	DifficultyAdvanced8    DifficultyLevel = 8  // Very Hard - Expert level
	DifficultyExpert9      DifficultyLevel = 9  // Extremely Hard - Cutting edge
	DifficultyExpert10     DifficultyLevel = 10 // Master Level - Research/Innovation
)

// String returns the string representation of difficulty level
func (d DifficultyLevel) String() string {
	switch d {
	case DifficultyBeginner1:
		return "Beginner (1)"
	case DifficultyBeginner2:
		return "Beginner (2)"
	case DifficultyBeginner3:
		return "Beginner (3)"
	case DifficultyIntermediate4:
		return "Intermediate (4)"
	case DifficultyIntermediate5:
		return "Intermediate (5)"
	case DifficultyIntermediate6:
		return "Intermediate (6)"
	case DifficultyAdvanced7:
		return "Advanced (7)"
	case DifficultyAdvanced8:
		return "Advanced (8)"
	case DifficultyExpert9:
		return "Expert (9)"
	case DifficultyExpert10:
		return "Expert (10)"
	default:
		return "Unknown"
	}
}

// GetCategory returns the general category of the difficulty level
func (d DifficultyLevel) GetCategory() string {
	switch {
	case d >= 1 && d <= 3:
		return "Beginner"
	case d >= 4 && d <= 6:
		return "Intermediate"
	case d >= 7 && d <= 8:
		return "Advanced"
	case d >= 9 && d <= 10:
		return "Expert"
	default:
		return "Unknown"
	}
}

// IsValid checks if the difficulty level is within valid range
func (d DifficultyLevel) IsValid() bool {
	return d >= 1 && d <= 10
}

// SystemUserID represents the AI system user ID
const SystemUserID = "00000000-0000-0000-0000-000000000001"

// LearningPath represents a complete learning path that can be reused
type LearningPath struct {
	ID               uuid.UUID       `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:学习路径唯一标识符" json:"id"`
	CreatorID        uuid.UUID       `gorm:"type:uuid;not null;index:idx_learning_path_creator;comment:创建者ID" json:"creator_id"`
	
	// Goal Information (moved from separate goal table)
	Goal       		 string          `gorm:"size:200;not null;comment:学习目标" json:"goal"`
	GoalCategory     string          `gorm:"size:100;comment:目标类别" json:"goal_category"`

	// Path Information  
	Title            string          `gorm:"size:200;not null;comment:学习路径标题" json:"title"`
	Description      string          `gorm:"type:text;comment:学习路径描述" json:"description"`
	PathType         PathType        `gorm:"size:50;not null;comment:路径类型" json:"path_type"`
	Difficulty       DifficultyLevel `gorm:"size:50;comment:难度级别" json:"difficulty"`
	
	// Path Characteristics
	EstimatedHours   int             `gorm:"comment:预估学习时长(小时)" json:"estimated_hours"`
	TotalNodes       int             `gorm:"comment:总节点数" json:"total_nodes"`
	SuitableFor		 string          `gorm:"type:text;comment:适用人群(JSON数组)" json:"suitable_for"`
	LearningOutcomes string          `gorm:"type:text;comment:学习成果(JSON数组)" json:"learning_outcomes"`
	
	// Reusability and Sharing
	IsPublic         bool            `gorm:"default:false;comment:是否公开可复用" json:"is_public"`
	UsageCount       int             `gorm:"default:0;comment:被使用次数" json:"usage_count"`
	Rating           float64         `gorm:"default:0;comment:用户评分" json:"rating"`
	
	// Timestamps
	CreatedAt        time.Time       `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time       `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt        gorm.DeletedAt  `gorm:"index:idx_learning_path_deleted_at;comment:软删除时间戳" json:"-"`
}

// UserMasterPath represents a user's master learning path composed of multiple learning paths
type UserMasterPath struct {
	ID               uuid.UUID       `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:用户总路径唯一标识符" json:"id"`
	UserID           uuid.UUID       `gorm:"type:uuid;not null;unique;index:idx_user_master_path_user;comment:用户ID" json:"user_id"`
	
	// Current Active Goal and Path
	CurrentGoalTitle string          `gorm:"size:200;comment:当前活跃目标标题" json:"current_goal_title"`
	CurrentPathID    *uuid.UUID      `gorm:"type:uuid;index:idx_user_master_path_current;comment:当前活跃学习路径ID" json:"current_path_id"`
	
	// Progress Tracking (now calculated from composition table)
	TotalPaths       int             `gorm:"default:0;comment:总路径数" json:"total_paths"`
	CompletedPaths   int             `gorm:"default:0;comment:已完成路径数" json:"completed_paths"`
	OverallProgress  float64         `gorm:"default:0;comment:总体完成百分比" json:"overall_progress"`
	
	// Timing Information
	StartedAt        *time.Time      `gorm:"comment:开始学习时间" json:"started_at"`
	LastAccessedAt   *time.Time      `gorm:"comment:最后访问时间" json:"last_accessed_at"`
	
	// Adaptation Info
	AdaptationCount  int             `gorm:"default:0;comment:总路径调整次数" json:"adaptation_count"`
	LastAdaptedAt    *time.Time      `gorm:"comment:最后调整时间" json:"last_adapted_at"`
	
	// Timestamps
	CreatedAt        time.Time       `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time       `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt        gorm.DeletedAt  `gorm:"index:idx_user_master_path_deleted_at;comment:软删除时间戳" json:"-"`
}

// UserPathProgress represents a user's progress on a specific learning path
type UserPathProgress struct {
	ID               uuid.UUID       `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:学习进度唯一标识符" json:"id"`
	UserID           uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_path_progress_user;comment:用户ID" json:"user_id"`
	LearningPathID   uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_path_progress_path;comment:学习路径ID" json:"learning_path_id"`
	
	// Progress Information
	Status           PathStatus      `gorm:"size:50;default:draft;comment:路径状态" json:"status"`
	CurrentNodeID    uuid.UUID       `gorm:"type:uuid;comment:当前节点ID" json:"current_node_id"`
	CurrentLessonID  string          `gorm:"size:100;comment:当前课程ID" json:"current_lesson_id"`
	CompletedNodes   int             `gorm:"default:0;comment:已完成节点数" json:"completed_nodes"`
	CompletedLessons int             `gorm:"default:0;comment:已完成课程数" json:"completed_lessons"`
	ProgressPercent  float64         `gorm:"default:0;comment:完成百分比" json:"progress_percent"`
	
	// Timing Information
	StartedAt        *time.Time      `gorm:"comment:开始学习时间" json:"started_at"`
	LastAccessedAt   *time.Time      `gorm:"comment:最后访问时间" json:"last_accessed_at"`
	CompletedAt      *time.Time      `gorm:"comment:完成时间" json:"completed_at"`
	
	// Performance Metrics
	TotalTimeSpent   int             `gorm:"default:0;comment:总学习时间(分钟)" json:"total_time_spent"`
	AverageScore     float64         `gorm:"default:0;comment:平均分数" json:"average_score"`
	
	// Timestamps
	CreatedAt        time.Time       `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time       `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt        gorm.DeletedAt  `gorm:"index:idx_user_path_progress_deleted_at;comment:软删除时间戳" json:"-"`
}

// BeforeCreate sets the ID if not provided for LearningPath
func (lp *LearningPath) BeforeCreate(tx *gorm.DB) error {
	if lp.ID == uuid.Nil {
		lp.ID = uuid.New()
	}
	return nil
}

// BeforeCreate sets the ID if not provided for UserMasterPath
func (ump *UserMasterPath) BeforeCreate(tx *gorm.DB) error {
	if ump.ID == uuid.Nil {
		ump.ID = uuid.New()
	}
	return nil
}

// BeforeCreate sets the ID if not provided for UserPathProgress
func (upp *UserPathProgress) BeforeCreate(tx *gorm.DB) error {
	if upp.ID == uuid.Nil {
		upp.ID = uuid.New()
	}
	return nil
}

// IsPublicPath checks if the learning path is public
func (lp *LearningPath) IsPublicPath() bool {
	return lp.IsPublic
}

// IncrementUsage increments the usage count
func (lp *LearningPath) IncrementUsage() {
	lp.UsageCount++
}

// IsActive checks if the user's path progress is active
func (upp *UserPathProgress) IsActive() bool {
	return upp.Status == PathStatusActive
}

// IsCompleted checks if the user's path progress is completed
func (upp *UserPathProgress) IsCompleted() bool {
	return upp.Status == PathStatusCompleted
}

// UpdateProgress updates the progress information
func (upp *UserPathProgress) UpdateProgress(completedNodes, completedLessons, totalLessons int) {
	upp.CompletedNodes = completedNodes
	upp.CompletedLessons = completedLessons
	
	if totalLessons > 0 {
		upp.ProgressPercent = float64(completedLessons) / float64(totalLessons) * 100
	}
	
	upp.LastAccessedAt = &[]time.Time{time.Now()}[0]
	
	if upp.ProgressPercent >= 100 {
		upp.Status = PathStatusCompleted
		upp.CompletedAt = &[]time.Time{time.Now()}[0]
	}
}

// UpdateMasterPathProgress updates the overall progress in master path
func (ump *UserMasterPath) UpdateMasterPathProgress() {
	if ump.TotalPaths > 0 {
		ump.OverallProgress = float64(ump.CompletedPaths) / float64(ump.TotalPaths) * 100
	}
	ump.LastAccessedAt = &[]time.Time{time.Now()}[0]
}

// GetAllNodesInMasterPath returns all nodes from all learning paths in the master path
// This method should be implemented in the service layer to avoid circular dependencies
func (ump *UserMasterPath) GetAllNodesInMasterPath() ([]uuid.UUID, error) {
	// This is a placeholder - actual implementation would be in service layer
	// The service would:
	// 1. Get all learning paths in the master path via UserMasterPathComposition
	// 2. For each learning path, get all nodes via PathNodeRelation
	// 3. Return the combined list of node IDs
	return nil, nil
}

// GetCurrentPathNodes returns nodes for the current active learning path
// This method should be implemented in the service layer
func (ump *UserMasterPath) GetCurrentPathNodes() ([]uuid.UUID, error) {
	// This is a placeholder - actual implementation would be in service layer
	// The service would:
	// 1. Get the current learning path via CurrentPathID
	// 2. Get all nodes in that path via PathNodeRelation
	// 3. Return the ordered list of node IDs
	return nil, nil
}

// GetNextAvailableNode returns the next available node for the user
// This method should be implemented in the service layer
func (ump *UserMasterPath) GetNextAvailableNode() (*uuid.UUID, error) {
	// This is a placeholder - actual implementation would be in service layer
	// The service would:
	// 1. Check user's current progress
	// 2. Find the next unlocked node based on dependencies and prerequisites
	// 3. Return the node ID
	return nil, nil
}

// TableName returns the table name for the LearningPath model
func (LearningPath) TableName() string {
	return "learning_paths"
}

// TableName returns the table name for the UserMasterPath model
func (UserMasterPath) TableName() string {
	return "user_master_paths"
}

// TableName returns the table name for the UserPathProgress model
func (UserPathProgress) TableName() string {
	return "user_path_progress"
}

// ============================================================================
// PostgreSQL Models for Structured Metadata and Relationships
// ============================================================================

// UserNodeProgress represents user progress on learning nodes (PostgreSQL)
type UserNodeProgress struct {
	ID               uuid.UUID       `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:用户节点进度唯一标识符" json:"id"`
	UserID           uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_node_user;comment:用户ID" json:"user_id"`
	NodeID           uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_node_node;comment:学习节点ID" json:"node_id"`
	LearningPathID   uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_node_path;comment:学习路径ID" json:"learning_path_id"`
	
	// Progress Information
	IsCompleted      bool            `gorm:"default:false;comment:是否完成" json:"is_completed"`
	CompletedAt      *time.Time      `gorm:"comment:完成时间" json:"completed_at"`
	Score            float64         `gorm:"default:0;comment:节点分数(0-100)" json:"score"`
	TimeSpent        int             `gorm:"default:0;comment:学习时间(分钟)" json:"time_spent"`
	Attempts         int             `gorm:"default:0;comment:尝试次数" json:"attempts"`
	LastAccessedAt   *time.Time      `gorm:"comment:最后访问时间" json:"last_accessed_at"`
	
	// Timestamps
	CreatedAt        time.Time       `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time       `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt        gorm.DeletedAt  `gorm:"index:idx_user_node_deleted_at;comment:软删除时间戳" json:"-"`
}

// UserLessonProgress represents user progress on lessons (PostgreSQL)
type UserLessonProgress struct {
	ID               uuid.UUID       `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:用户课程进度唯一标识符" json:"id"`
	UserID           uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_lesson_user;comment:用户ID" json:"user_id"`
	LessonID         string          `gorm:"size:100;not null;index:idx_user_lesson_lesson;comment:课程ID(MongoDB)" json:"lesson_id"`
	NodeID           uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_lesson_node;comment:学习节点ID" json:"node_id"`
	LearningPathID   uuid.UUID       `gorm:"type:uuid;not null;index:idx_user_lesson_path;comment:学习路径ID" json:"learning_path_id"`
	
	// Progress Information
	IsCompleted      bool            `gorm:"default:false;comment:是否完成" json:"is_completed"`
	CompletedAt      *time.Time      `gorm:"comment:完成时间" json:"completed_at"`
	Score            float64         `gorm:"default:0;comment:课程分数(0-100)" json:"score"`
	TimeSpent        int             `gorm:"default:0;comment:学习时间(分钟)" json:"time_spent"`
	Attempts         int             `gorm:"default:0;comment:尝试次数" json:"attempts"`
	LastAccessedAt   *time.Time      `gorm:"comment:最后访问时间" json:"last_accessed_at"`
	
	// Timestamps
	CreatedAt        time.Time       `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time       `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt        gorm.DeletedAt  `gorm:"index:idx_user_lesson_deleted_at;comment:软删除时间戳" json:"-"`
}

// TODO: 这里要考虑如何收集用户学习数据喂给AI或是算法分析
// LearningAnalytics represents learning analytics data (PostgreSQL)
type LearningAnalytics struct {
	ID               uuid.UUID       `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:学习分析唯一标识符" json:"id"`
	UserID           uuid.UUID       `gorm:"type:uuid;not null;index:idx_analytics_user;comment:用户ID" json:"user_id"`
	LearningPathID   uuid.UUID       `gorm:"type:uuid;not null;index:idx_analytics_path;comment:学习路径ID" json:"learning_path_id"`
	
	// Analytics Data
	TotalTimeSpent   int             `gorm:"default:0;comment:总学习时间(分钟)" json:"total_time_spent"`
	AverageSessionTime int           `gorm:"default:0;comment:平均会话时间(分钟)" json:"average_session_time"`
	CompletionRate   float64         `gorm:"default:0;comment:完成率" json:"completion_rate"`
	EngagementScore  float64         `gorm:"default:0;comment:参与度评分" json:"engagement_score"`
	DifficultyAreas  string          `gorm:"type:jsonb;comment:困难领域(JSON数组)" json:"difficulty_areas"`
	StrengthAreas    string          `gorm:"type:jsonb;comment:优势领域(JSON数组)" json:"strength_areas"`
	LearningVelocity float64         `gorm:"default:0;comment:学习速度" json:"learning_velocity"`
	RetentionRate    float64         `gorm:"default:0;comment:知识保持率" json:"retention_rate"`
	PreferredContentTypes string     `gorm:"type:jsonb;comment:偏好内容类型(JSON数组)" json:"preferred_content_types"`
	OptimalStudyTimes string         `gorm:"type:jsonb;comment:最佳学习时间(JSON数组)" json:"optimal_study_times"`
	LearningPatterns string          `gorm:"type:jsonb;comment:学习模式(JSON对象)" json:"learning_patterns"`
	
	// Timestamps
	CreatedAt        time.Time       `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt        time.Time       `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt        gorm.DeletedAt  `gorm:"index:idx_analytics_deleted_at;comment:软删除时间戳" json:"-"`
	
	// Associations
	LearningPath     LearningPath    `gorm:"foreignKey:LearningPathID;references:ID" json:"learning_path,omitempty"`
}

// ============================================================================
// BeforeCreate Hooks for New Models
// ============================================================================

// BeforeCreate sets the ID if not provided for UserNodeProgress
func (unp *UserNodeProgress) BeforeCreate(tx *gorm.DB) error {
	if unp.ID == uuid.Nil {
		unp.ID = uuid.New()
	}
	return nil
}

// BeforeCreate sets the ID if not provided for UserLessonProgress
func (ulp *UserLessonProgress) BeforeCreate(tx *gorm.DB) error {
	if ulp.ID == uuid.Nil {
		ulp.ID = uuid.New()
	}
	return nil
}

// BeforeCreate sets the ID if not provided for LearningAnalytics
func (la *LearningAnalytics) BeforeCreate(tx *gorm.DB) error {
	if la.ID == uuid.Nil {
		la.ID = uuid.New()
	}
	return nil
}

// ============================================================================
// Table Names for New Models
// ============================================================================

// TableName returns the table name for the UserNodeProgress model
func (UserNodeProgress) TableName() string {
	return "user_node_progress"
}

// TableName returns the table name for the UserLessonProgress model
func (UserLessonProgress) TableName() string {
	return "user_lesson_progress"
}

// TableName returns the table name for the LearningAnalytics model
func (LearningAnalytics) TableName() string {
	return "learning_analytics"
}

// ============================================================================
// Helper Methods for Progress Management
// ============================================================================

// UpdateNodeProgress updates the progress information for a node
func (unp *UserNodeProgress) UpdateProgress(score float64, timeSpent int, isCompleted bool) {
	unp.Score = score
	unp.TimeSpent += timeSpent
	unp.Attempts++
	unp.LastAccessedAt = &[]time.Time{time.Now()}[0]
	
	if isCompleted && !unp.IsCompleted {
		unp.IsCompleted = true
		unp.CompletedAt = &[]time.Time{time.Now()}[0]
	}
}

// UpdateLessonProgress updates the progress information for a lesson
func (ulp *UserLessonProgress) UpdateProgress(score float64, timeSpent int, isCompleted bool) {
	ulp.Score = score
	ulp.TimeSpent += timeSpent
	ulp.Attempts++
	ulp.LastAccessedAt = &[]time.Time{time.Now()}[0]
	
	if isCompleted && !ulp.IsCompleted {
		ulp.IsCompleted = true
		ulp.CompletedAt = &[]time.Time{time.Now()}[0]
	}
}