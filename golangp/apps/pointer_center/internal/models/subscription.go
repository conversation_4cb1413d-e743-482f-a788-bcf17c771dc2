package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Subscription represents a user subscription
type Subscription struct {
	ID          uuid.UUID           `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID      uuid.UUID           `gorm:"type:uuid;not null;index" json:"user_id"`
	PlanType    SubscriptionPlan    `gorm:"size:50;not null;index" json:"plan_type"`
	Status      SubscriptionStatus  `gorm:"size:50;default:'active';index" json:"status"`
	StartDate   time.Time           `gorm:"not null" json:"start_date"`
	EndDate     *time.Time          `json:"end_date"`
	AutoRenew   bool                `gorm:"default:false" json:"auto_renew"`
	Price       float64             `gorm:"type:decimal(10,2)" json:"price"`
	Currency    string              `gorm:"size:10;default:'USD'" json:"currency"`
	PaymentID   string              `gorm:"size:255" json:"payment_id"`
	
	// 订阅特性配置
	Features    SubscriptionFeatures `gorm:"type:jsonb" json:"features"`
	
	// 使用统计
	UsageCount  int                 `gorm:"default:0" json:"usage_count"`
	UsageLimit  int                 `gorm:"default:-1" json:"usage_limit"`
	
	// 试用相关
	IsTrialPeriod bool              `gorm:"default:false" json:"is_trial_period"`
	TrialEndDate  *time.Time        `json:"trial_end_date"`
	
	// 取消相关
	CancelledAt   *time.Time        `json:"cancelled_at"`
	CancelReason  string            `gorm:"size:500" json:"cancel_reason"`
	
	CreatedAt     time.Time         `json:"created_at"`
	UpdatedAt     time.Time         `json:"updated_at"`
	DeletedAt     gorm.DeletedAt    `gorm:"index" json:"-"`

	// Relationships
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// SubscriptionPlan represents subscription plan types
type SubscriptionPlan string

const (
	SubscriptionPlanFree       SubscriptionPlan = "free"
	SubscriptionPlanBasic      SubscriptionPlan = "basic"
	SubscriptionPlanPro        SubscriptionPlan = "pro"
	SubscriptionPlanEnterprise SubscriptionPlan = "enterprise"
)

// SubscriptionStatus represents subscription status
type SubscriptionStatus string

const (
	SubscriptionStatusActive    SubscriptionStatus = "active"
	SubscriptionStatusInactive  SubscriptionStatus = "inactive"
	SubscriptionStatusExpired   SubscriptionStatus = "expired"
	SubscriptionStatusCancelled SubscriptionStatus = "cancelled"
	SubscriptionStatusSuspended SubscriptionStatus = "suspended"
	SubscriptionStatusTrial     SubscriptionStatus = "trial"
)

// SubscriptionFeatures represents the features included in a subscription
type SubscriptionFeatures struct {
	APICallsPerMonth    int  `json:"api_calls_per_month"`
	DataExportEnabled   bool `json:"data_export_enabled"`
	AdvancedAnalytics   bool `json:"advanced_analytics"`
	PrioritySupport     bool `json:"priority_support"`
	CustomIntegrations  bool `json:"custom_integrations"`
	MultiUserAccess     bool `json:"multi_user_access"`
	WhiteLabelEnabled   bool `json:"white_label_enabled"`
	MaxProjects         int  `json:"max_projects"`
	MaxBrands           int  `json:"max_brands"`
	StorageGB           int  `json:"storage_gb"`
}

// BeforeCreate sets the ID if not provided
func (s *Subscription) BeforeCreate(tx *gorm.DB) error {
	if s.ID == uuid.Nil {
		s.ID = uuid.New()
	}
	return nil
}

// IsActive checks if the subscription is currently active
func (s *Subscription) IsActive() bool {
	now := time.Now()
	
	// 检查状态
	if s.Status != SubscriptionStatusActive && s.Status != SubscriptionStatusTrial {
		return false
	}
	
	// 检查是否在有效期内
	if s.EndDate != nil && now.After(*s.EndDate) {
		return false
	}
	
	// 检查试用期
	if s.IsTrialPeriod && s.TrialEndDate != nil && now.After(*s.TrialEndDate) {
		return false
	}
	
	return true
}

// IsExpired checks if the subscription has expired
func (s *Subscription) IsExpired() bool {
	now := time.Now()
	
	if s.EndDate != nil && now.After(*s.EndDate) {
		return true
	}
	
	if s.IsTrialPeriod && s.TrialEndDate != nil && now.After(*s.TrialEndDate) {
		return true
	}
	
	return false
}

// CanUseFeature checks if a specific feature is available in the subscription
func (s *Subscription) CanUseFeature(feature string) bool {
	if !s.IsActive() {
		return false
	}
	
	switch feature {
	case "data_export":
		return s.Features.DataExportEnabled
	case "advanced_analytics":
		return s.Features.AdvancedAnalytics
	case "priority_support":
		return s.Features.PrioritySupport
	case "custom_integrations":
		return s.Features.CustomIntegrations
	case "multi_user_access":
		return s.Features.MultiUserAccess
	case "white_label":
		return s.Features.WhiteLabelEnabled
	default:
		return false
	}
}

// HasUsageLimit checks if the subscription has reached its usage limit
func (s *Subscription) HasUsageLimit() bool {
	if s.UsageLimit == -1 {
		return false // 无限制
	}
	return s.UsageCount >= s.UsageLimit
}

// GetRemainingUsage returns the remaining usage count
func (s *Subscription) GetRemainingUsage() int {
	if s.UsageLimit == -1 {
		return -1 // 无限制
	}
	remaining := s.UsageLimit - s.UsageCount
	if remaining < 0 {
		return 0
	}
	return remaining
}

// TableName returns the table name for the Subscription model
func (Subscription) TableName() string {
	return "subscriptions"
}
