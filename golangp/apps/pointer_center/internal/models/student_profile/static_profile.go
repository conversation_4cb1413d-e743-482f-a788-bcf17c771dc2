package student_profile

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// StaticProfile represents the static student profile stored in PostgreSQL
type StaticProfile struct {
	ID     uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:静态画像唯一标识符" json:"id"`
	UserID uuid.UUID `gorm:"type:uuid;not null;uniqueIndex:idx_static_profile_user;comment:关联用户ID" json:"user_id"`

	// Basic Information
	Age                int            `gorm:"comment:学生年龄" json:"age"`
	Gender             string         `gorm:"size:20;comment:性别" json:"gender"`
	PreferredLanguage  string         `gorm:"size:10;default:zh-CN;comment:首选语言" json:"preferred_language"`

	// Educational Background
	EducationExperience string         `gorm:"size:200;comment:教育经历" json:"education_experience"`
	Major               string         `gorm:"size:100;comment:专业" json:"major"`
	GraduationYear      int            `gorm:"comment:毕业年份" json:"graduation_year"`

	// Professional Background
	CurrentRole        string          `gorm:"size:100;comment:当前职位" json:"current_role"`
	Industry           string          `gorm:"size:100;comment:所在行业" json:"industry"`
	WorkExperience     int             `gorm:"comment:工作经验年数" json:"work_experience"`

	// Learning Preferences
	LearningStyle      string 		 `gorm:"size:50;comment:学习风格偏好" json:"learning_style"`
	StudyTimePerWeek   int           `gorm:"comment:每周期望学习时间(小时)" json:"study_time_per_week"`
	PreferredStudyTime string        `gorm:"size:50;comment:偏好学习时间段" json:"preferred_study_time"`
	LearningPace       string        `gorm:"size:50;comment:学习节奏偏好" json:"learning_pace"`

	// Metadata
	CreatedAt time.Time      `gorm:"autoCreateTime;comment:创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_static_profile_deleted_at;comment:软删除时间戳" json:"-"`
}

// BeforeCreate sets the ID if not provided
func (sp *StaticProfile) BeforeCreate(tx *gorm.DB) error {
	if sp.ID == uuid.Nil {
		sp.ID = uuid.New()
	}
	return nil
}

// TableName returns the table name for the StaticProfile model
func (StaticProfile) TableName() string {
	return "student_static_profiles"
}

// ProfileSummary represents a summary of the static profile
type ProfileSummary struct {
	// Basic Information
	Age                int
	Gender             string
	PreferredLanguage  string

	// Educational Background
	EducationExperience string
	Major               string
	GraduationYear      int

	// Professional Background
	CurrentRole        string
	Industry           string
	WorkExperience     int

	// Learning Preferences
	LearningStyle      string
	StudyTimePerWeek   int
	PreferredStudyTime string
	LearningPace       string     
}

// GetSummary returns a summary of the static profile
func (sp *StaticProfile) GetSummary() *ProfileSummary {
	return &ProfileSummary{
		Age:                sp.Age,
		Gender:             sp.Gender,
		PreferredLanguage:  sp.PreferredLanguage,
		EducationExperience: sp.EducationExperience,
		Major:               sp.Major,
		GraduationYear:      sp.GraduationYear,
		CurrentRole:        sp.CurrentRole,
		Industry:           sp.Industry,
		WorkExperience:     sp.WorkExperience,
		LearningStyle:      sp.LearningStyle,
		StudyTimePerWeek:   sp.StudyTimePerWeek,
		PreferredStudyTime: sp.PreferredStudyTime,
		LearningPace:      sp.LearningPace,
	}
}
