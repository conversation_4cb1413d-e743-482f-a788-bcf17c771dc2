load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "student_profile",
    srcs = [
        "static_profile.go",
        "tech_competency.go",
    ],
    importpath = "pointer/golangp/apps/pointer_center/internal/models/student_profile",
    visibility = ["//golangp/apps/pointer_center:__subpackages__"],
    deps = [
        "@com_github_google_uuid//:uuid",
        "@io_gorm_gorm//:gorm",
        "@org_mongodb_go_mongo_driver//bson/primitive",
    ],
)
