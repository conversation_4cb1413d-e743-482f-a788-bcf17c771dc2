package student_profile

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CompetencyLevel represents skill competency levels (1-5 scale)
type CompetencyLevel int

const (
    CompetencyUnknown      CompetencyLevel = 0 // 未知水平
    CompetencyAware        CompetencyLevel = 1 // 了解概念 - Basic awareness
    CompetencyNovice       CompetencyLevel = 2 // 模仿实现 - Can follow examples
    CompetencyPractitioner CompetencyLevel = 3 // 独立应用 - Independent application
    CompetencyProficient   CompetencyLevel = 4 // 熟练掌握 - Proficient usage
    CompetencyExpert       CompetencyLevel = 5 // 精通并能教授 - Expert level
)

// String returns the string representation of competency level
func (c CompetencyLevel) String() string {
    switch c {
    case CompetencyAware:
        return "了解概念"
    case CompetencyNovice:
        return "模仿实现"
    case CompetencyPractitioner:
        return "独立应用"
    case CompetencyProficient:
        return "熟练掌握"
    case CompetencyExpert:
        return "精通并能教授"
    default:
        return "未知"
    }
}

// IsValid checks if the competency level is within valid range
func (c CompetencyLevel) IsValid() bool {
    return c >= 1 && c <= 5
}

// ActionType represents the type of student learning action
type ActionType string

const (
    ActionResumeUpload     ActionType = "resume_upload"     // 简历上传
    ActionExerciseSubmit   ActionType = "exercise_submitted" // 练习提交
    ActionProjectSubmit    ActionType = "project_submitted" // 项目提交
    ActionQuizComplete     ActionType = "quiz_completed"    // 测验完成
    ActionLessonComplete   ActionType = "lesson_completed"  // 课程完成
	ActionFeedbackProvided ActionType = "feedback_provided" // 反馈提供
	ActionSelfAssessment   ActionType = "self_assessment" // 自我评估
)

// CompetencyNode represents a single competency/skill node in the graph
type CompetencyNode struct {
    ID            string            `bson:"id" json:"id"`                         // 节点唯一标识符 (snake_case)
    Name          string            `bson:"name" json:"name"`                     // 技能名称 (中文全称)
    Level         CompetencyLevel   `bson:"level" json:"level"`                   // 能力等级 (1-5)
    Description   string            `bson:"description" json:"description"`       // 能力描述
    Prerequisites []string          `bson:"prerequisites" json:"prerequisites"`   // 先修技能节点ID列表
    
    // Evidence and Tracking
    EvidenceSources []string        `bson:"evidence_sources" json:"evidence_sources"` // 证据来源
    LastUpdated     time.Time       `bson:"last_updated" json:"last_updated"`         // 最后更新时间
    ConfidenceScore float64         `bson:"confidence_score" json:"confidence_score"` // AI评估置信度 (0-1)
    
    // Learning Progress
    LearningHours   int             `bson:"learning_hours" json:"learning_hours"`     // 投入学习时间
    PracticeCount   int             `bson:"practice_count" json:"practice_count"`     // 练习次数
    ProjectCount    int             `bson:"project_count" json:"project_count"`       // 相关项目数量
}

// StudentAction represents a student learning activity for competency analysis
type StudentAction struct {
    Type        ActionType  `bson:"type" json:"type"`               // 行为类型
    Description string      `bson:"description" json:"description"` // 行为描述
    Content     interface{} `bson:"content" json:"content"`         // 行为内容 (JSON object)
    Timestamp   time.Time   `bson:"timestamp" json:"timestamp"`     // 行为时间戳
    Score       *float64    `bson:"score,omitempty" json:"score,omitempty"` // 分数 (如果适用)
}

// CompetencyUpdateRecord represents a single update operation on the competency graph
type CompetencyUpdateRecord struct {
    UpdateID      primitive.ObjectID `bson:"_id,omitempty" json:"update_id"`
    Action        StudentAction      `bson:"action" json:"action"`                     // 触发更新的学生行为
    AIEvaluation  string            `bson:"ai_evaluation" json:"ai_evaluation"`       // AI评估结果
    UpdateType    string            `bson:"update_type" json:"update_type"`           // 更新类型: "create", "update", "merge"
    AffectedNodes []string          `bson:"affected_nodes" json:"affected_nodes"`     // 受影响的节点ID列表
    UpdatedAt     time.Time         `bson:"updated_at" json:"updated_at"`             // 更新时间
    AgentVersion  string            `bson:"agent_version" json:"agent_version"`       // AI智能体版本
}

// TechCompetencyGraph represents the complete technical competency profile
type TechCompetencyGraph struct {
    ID               primitive.ObjectID       `bson:"_id,omitempty" json:"id"`
    UserID           string                   `bson:"user_id" json:"user_id"`
    ProfileVersion   int                      `bson:"profile_version" json:"profile_version"`
    Nodes            []CompetencyNode         `bson:"nodes" json:"nodes"`                       // 能力节点列表
    
    // Competency Analytics
    OverallScore     string                  `bson:"overall_score" json:"overall_score"`       // 总体能力评价
    StrengthAreas    []string                 `bson:"strength_areas" json:"strength_areas"`     // 优势领域
    ImprovementAreas []string                 `bson:"improvement_areas" json:"improvement_areas"` // 待提升领域
    
    // Metadata
    LastUpdated         time.Time            `bson:"last_updated" json:"last_updated"`                 // 最后更新时间
}

// CompetencyGraphInput represents input for AI agent to process
type CompetencyGraphInput struct {
    CurrentGraph  TechCompetencyGraph `json:"current_competency_graph"`
    StudentAction StudentAction        `json:"student_action"`
    AIEvaluation  string              `json:"ai_evaluation"`
}

// CompetencyGraphNodes represents the nodes structure for AI input/output
type CompetencyGraphNodes struct {
    Nodes []CompetencyNode `json:"nodes"`
}

// CompetencyGraphOutput represents AI agent output
type CompetencyGraphOutput struct {
    CompetencyGraph CompetencyGraphNodes `json:"competency_graph"`
}

// NewTechCompetencyGraph creates a new technical competency graph
func NewTechCompetencyGraph(userID string) *TechCompetencyGraph {
    return &TechCompetencyGraph{
        UserID:              userID,
        ProfileVersion:      1,
        Nodes:               make([]CompetencyNode, 0),

        StrengthAreas:       make([]string, 0),
        ImprovementAreas:    make([]string, 0),

        LastUpdated:         time.Now(),
    }
}

// AddOrUpdateNode adds a new node or updates existing one
func (tcg *TechCompetencyGraph) AddOrUpdateNode(node CompetencyNode) {
    node.LastUpdated = time.Now()
    
    // Find existing node
    for i, existingNode := range tcg.Nodes {
        if existingNode.ID == node.ID {
            // Update existing node
            tcg.Nodes[i] = node
            tcg.LastUpdated = time.Now()
            return
        }
    }
    
    // Add new node
    tcg.Nodes = append(tcg.Nodes, node)
    tcg.LastUpdated = time.Now()
}

// GetNodeByID retrieves a node by its ID
func (tcg *TechCompetencyGraph) GetNodeByID(nodeID string) *CompetencyNode {
    for _, node := range tcg.Nodes {
        if node.ID == nodeID {
            return &node
        }
    }
    return nil
}

// GetNodesByLevel retrieves nodes by competency level
func (tcg *TechCompetencyGraph) GetNodesByLevel(level CompetencyLevel) []CompetencyNode {
    var nodes []CompetencyNode
    for _, node := range tcg.Nodes {
        if node.Level == level {
            nodes = append(nodes, node)
        }
    }
    return nodes
}

// GetPrerequisites returns all prerequisite nodes for a given node
func (tcg *TechCompetencyGraph) GetPrerequisites(nodeID string) []CompetencyNode {
    node := tcg.GetNodeByID(nodeID)
    if node == nil {
        return nil
    }
    
    var prerequisites []CompetencyNode
    for _, prereqID := range node.Prerequisites {
        if prereqNode := tcg.GetNodeByID(prereqID); prereqNode != nil {
            prerequisites = append(prerequisites, *prereqNode)
        }
    }
    return prerequisites
}

// GetDependentNodes returns nodes that depend on the given node
func (tcg *TechCompetencyGraph) GetDependentNodes(nodeID string) []CompetencyNode {
    var dependents []CompetencyNode
    for _, node := range tcg.Nodes {
        for _, prereqID := range node.Prerequisites {
            if prereqID == nodeID {
                dependents = append(dependents, node)
                break
            }
        }
    }
    return dependents
}

// AddUpdateRecord adds a new update record to history
func (tcg *TechCompetencyGraph) AddUpdateRecord(record CompetencyUpdateRecord) {
    record.UpdatedAt = time.Now()
    tcg.LastUpdated = time.Now()
}

// UpdateVersion increments the profile version
func (tcg *TechCompetencyGraph) UpdateVersion() {
    tcg.ProfileVersion++
    tcg.LastUpdated = time.Now()
}

// IsStale checks if the competency graph needs updating
func (tcg *TechCompetencyGraph) IsStale() bool {
    return time.Since(tcg.LastUpdated) > 7*24*time.Hour // 7 days
}

// GetAIInputFormat converts the graph to AI agent input format
func (tcg *TechCompetencyGraph) GetAIInputFormat(action StudentAction, evaluation string) CompetencyGraphInput {
    return CompetencyGraphInput{
        CurrentGraph: *tcg,
        StudentAction: action,
        AIEvaluation:  evaluation,
    }
}

// UpdateFromAIOutput updates the graph from AI agent output
func (tcg *TechCompetencyGraph) UpdateFromAIOutput(output CompetencyGraphOutput, action StudentAction, evaluation string) {
    // Store old nodes for comparison
    oldNodeCount := len(tcg.Nodes)
    
    // Clear current nodes and add new ones
    tcg.Nodes = output.CompetencyGraph.Nodes
    
    // Determine update type
    updateType := "update"
    if oldNodeCount == 0 {
        updateType = "create"
    } else if len(tcg.Nodes) > oldNodeCount {
        updateType = "expand"
    }
    
    // Add update record
    affectedNodes := make([]string, len(tcg.Nodes))
    for i, node := range tcg.Nodes {
        affectedNodes[i] = node.ID
    }
    
    record := CompetencyUpdateRecord{
        Action:        action,
        AIEvaluation:  evaluation,
        UpdateType:    updateType,
        AffectedNodes: affectedNodes,
        UpdatedAt:     time.Now(),
        AgentVersion:  "v1.0", // TODO: get from config
    }
    
    tcg.AddUpdateRecord(record)
    tcg.UpdateVersion()
}

// UpdateFromAICompetencyResponse updates the graph from AI competency response
func (tcg *TechCompetencyGraph) UpdateFromAICompetencyResponse(response interface{}, action StudentAction, evaluation string) {
	// Type assertion to get the AI response structure
	aiResponse, ok := response.(struct {
		CompetencyGraph struct {
			Nodes            []CompetencyNode `json:"nodes"`
			OverallScore     string          `json:"overall_score"`
			StrengthAreas    []string        `json:"strength_areas"`
			ImprovementAreas []string        `json:"improvement_areas"`
		} `json:"competency_graph"`
	})
	
	if !ok {
		// Fallback to the original method if type assertion fails
		return
	}
	
	// Store old nodes for comparison
	oldNodeCount := len(tcg.Nodes)
	
	// Update nodes
	tcg.Nodes = aiResponse.CompetencyGraph.Nodes
	
	// Update overall analytics
	tcg.OverallScore = aiResponse.CompetencyGraph.OverallScore
	tcg.StrengthAreas = aiResponse.CompetencyGraph.StrengthAreas
	tcg.ImprovementAreas = aiResponse.CompetencyGraph.ImprovementAreas
	
	// Determine update type
	updateType := "update"
	if oldNodeCount == 0 {
		updateType = "create"
	} else if len(tcg.Nodes) > oldNodeCount {
		updateType = "expand"
	}
	
	// Add update record
	affectedNodes := make([]string, len(tcg.Nodes))
	for i, node := range tcg.Nodes {
		affectedNodes[i] = node.ID
	}
	
	record := CompetencyUpdateRecord{
		Action:        action,
		AIEvaluation:  evaluation,
		UpdateType:    updateType,
		AffectedNodes: affectedNodes,
		UpdatedAt:     time.Now(),
		AgentVersion:  "v1.0", // TODO: get from config
	}
	
	tcg.AddUpdateRecord(record)
	tcg.UpdateVersion()
}

// ValidateGraph performs basic validation on the competency graph
func (tcg *TechCompetencyGraph) ValidateGraph() []string {
    var errors []string
    
    // Check for duplicate node IDs
    nodeIDs := make(map[string]bool)
    for _, node := range tcg.Nodes {
        if nodeIDs[node.ID] {
            errors = append(errors, "Duplicate node ID: "+node.ID)
        }
        nodeIDs[node.ID] = true
        
        // Validate node level
        if !node.Level.IsValid() {
            errors = append(errors, "Invalid competency level for node: "+node.ID)
        }
        
        // Check prerequisites exist
        for _, prereqID := range node.Prerequisites {
            if !nodeIDs[prereqID] && tcg.GetNodeByID(prereqID) == nil {
                errors = append(errors, "Missing prerequisite node: "+prereqID+" for node: "+node.ID)
            }
        }
    }
    
    return errors
}

// GetCollectionName returns the MongoDB collection name
func (TechCompetencyGraph) GetCollectionName() string {
    return "student_tech_competency_graphs"
}