/*
 * @Description: User model for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserRole represents user role types
type UserRole string

const (
	UserRoleUser  UserRole = "user"
	UserRoleAdmin UserRole = "admin"
)

// UserStatus represents user status types
type UserStatus string

const (
	UserStatusActive    UserStatus = "active"
	UserStatusInactive  UserStatus = "inactive"
	UserStatusSuspended UserStatus = "suspended"
)

// User represents a user in the Pointer Center system
type User struct {
	ID                     uuid.UUID  `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:用户唯一标识符，使用UUID格式" json:"id"`
	Username               string     `gorm:"size:100;not null;uniqueIndex:idx_user_username;comment:用户名，必填且唯一" json:"username"`
	Email                  string     `gorm:"size:255;not null;uniqueIndex:idx_user_email;comment:邮箱地址，必填且唯一" json:"email"`
	Password               string     `gorm:"size:255;comment:加密后的密码，不在JSON中返回" json:"-"`
	FirstName              string     `gorm:"size:100;comment:用户名字，可选" json:"first_name"`
	LastName               string     `gorm:"size:100;comment:用户姓氏，可选" json:"last_name"`
	Avatar                 string     `gorm:"size:500;comment:用户头像URL，可选" json:"avatar"`
	Bio                    string     `gorm:"size:500;comment:用户个人简介，可选" json:"bio"`
	Phone                  string     `gorm:"size:20;comment:用户电话号码，可选" json:"phone"`
	Company                string     `gorm:"size:200;comment:用户所属公司，可选" json:"company"`
	Country                string     `gorm:"size:100;comment:用户所在国家，可选" json:"country"`
	Role                   UserRole   `gorm:"size:50;default:user;index:idx_user_role;comment:用户角色，定义权限级别" json:"role"`
	Status                 UserStatus `gorm:"size:50;default:active;index:idx_user_status;comment:用户账户状态" json:"status"`
	Origin                 string     `gorm:"size:50;default:local;index:idx_user_origin;comment:用户注册来源（local、google等）" json:"origin"`
	EmailVerified          bool       `gorm:"default:false;index:idx_user_email_verified;comment:邮箱是否已验证" json:"email_verified"`
	EmailVerificationToken string     `gorm:"size:255;comment:邮箱验证令牌，不在JSON中返回" json:"-"`
	PendingEmail           string     `gorm:"size:255;comment:待验证的新邮箱地址，不在JSON中返回" json:"-"`
	LastLogin              *time.Time `gorm:"index:idx_user_last_login;comment:最后登录时间，可为空" json:"last_login"`

	// 安全相关预留字段
	LoginProtection        bool `gorm:"default:false;comment:登录保护开关，预留字段" json:"login_protection"`
	PasswordChangeRequired bool `gorm:"default:false;comment:是否需要定期更改密码，预留字段" json:"password_change_required"`
	SecurityLock           bool `gorm:"default:false;comment:安全锁定状态，预留字段" json:"security_lock"`

	CreatedAt time.Time      `gorm:"autoCreateTime;comment:账户创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:账户最后更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_user_deleted_at;comment:软删除时间戳，不在JSON中返回" json:"-"`

	// Relationships
	Sessions      []UserSession  `gorm:"foreignKey:UserID" json:"sessions,omitempty"`      // 用户的所有会话记录
	Subscriptions []Subscription `gorm:"foreignKey:UserID" json:"subscriptions,omitempty"` // 用户的订阅记录
}

// BeforeCreate sets the ID if not provided
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return nil
}

// GetFullName returns the user's full name
func (u *User) GetFullName() string {
	if u.FirstName != "" && u.LastName != "" {
		return u.FirstName + " " + u.LastName
	}
	if u.FirstName != "" {
		return u.FirstName
	}
	if u.LastName != "" {
		return u.LastName
	}
	return u.Username
}

// IsActive checks if the user account is active
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// IsAdmin checks if the user has admin role
func (u *User) IsAdmin() bool {
	return u.Role == UserRoleAdmin
}

// TableName returns the table name for the User model
func (User) TableName() string {
	return "users"
}
