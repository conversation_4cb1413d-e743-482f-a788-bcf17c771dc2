package services

import (
	"context"
	"encoding/json"
	"fmt"
	"pointer/golangp/apps/pointer_center/internal/models/student_profile"
	"pointer/golangp/apps/pointer_center/internal/repositories"
	"pointer/golangp/apps/pointer_center/pkg/ai_agents"
	"pointer/golangp/common/logging"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// AICompetencyResponse represents the AI response structure for competency analysis
type AICompetencyResponse struct {
	CompetencyGraph struct {
		Nodes            []student_profile.CompetencyNode `json:"nodes"`
		OverallScore     string                           `json:"overall_score"`
		StrengthAreas    []string                         `json:"strength_areas"`
		ImprovementAreas []string                         `json:"improvement_areas"`
	} `json:"competency_graph"`
}

// TechCompetencyService handles business logic for technical competency profiles
type TechCompetencyService struct {
	repos     *repositories.Repositories
	aiManager *ai_agents.Manager
}

// NewTechCompetencyService creates a new tech competency service
func NewTechCompetencyService(repos *repositories.Repositories, aiManager *ai_agents.Manager) *TechCompetencyService {
	return &TechCompetencyService{
		repos:     repos,
		aiManager: aiManager,
	}
}

// UpdateTechCompetencyWithAction updates competency graph based on student action using AI
func (s *TechCompetencyService) UpdateTechCompetencyWithAction(ctx context.Context, req *UpdateCompetencyActionRequest) (*student_profile.TechCompetencyGraph, error) {
	// Validate request
	if err := s.validateUpdateActionRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Get current competency graph
	graph, err := s.repos.TechCompetency.GetByUserID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tech competency graph: %w", err)
	}

	// TODO: 需要这里也许需要评估智能体先进行评估，也许不要，即是否将评估与更新都交给画像智能体的问题
	aiInput := graph.GetAIInputFormat(req.Action, req.AIEvaluation)

	// Call AI agent to analyze and update competency
	aiResponse, err := s.callAIAnalyzeCompetency(ctx, aiInput)
	if err != nil {
		logging.Error("AI competency analysis failed: user_id=%s, error=%v", req.UserID, err)
		return nil, fmt.Errorf("AI competency analysis failed: %w", err)
	}

	if !aiResponse.Success {
		logging.Error("AI competency analysis unsuccessful: user_id=%s, error=%s", req.UserID, aiResponse.Error)
		return nil, fmt.Errorf("AI competency analysis failed: %s", aiResponse.Error)
	}

	answer := aiResponse.Data["answer"]
	if answer == nil {
		logging.Error("AI response missing 'answer' field: user_id=%s", req.UserID)
		return nil, fmt.Errorf("AI response missing 'answer' field")
	}

	// Parse AI response
	var aiCompetencyResponse AICompetencyResponse
	answerBytes, err := json.Marshal(answer)
	if err != nil {
		logging.Error("Failed to marshal AI answer: user_id=%s, error=%v", req.UserID, err)
		return nil, fmt.Errorf("failed to marshal AI answer: %w", err)
	}

	if err := json.Unmarshal(answerBytes, &aiCompetencyResponse); err != nil {
		logging.Error("Failed to parse AI competency response: user_id=%s, error=%v", req.UserID, err)
		return nil, fmt.Errorf("failed to parse AI competency response: %w", err)
	}

	// Update graph with AI output
	graph.UpdateFromAICompetencyResponse(aiCompetencyResponse, req.Action, req.AIEvaluation)

	// Save updated graph
	if err := s.repos.TechCompetency.Update(ctx, graph); err != nil {
		logging.Error("Failed to update tech competency graph: user_id=%s, error=%v", req.UserID, err)
		return nil, fmt.Errorf("failed to update tech competency graph: %w", err)
	}

	logging.Info("Tech competency graph updated with AI: user_id=%s, tokens_used=%d", req.UserID, aiResponse.TokensUsed)
	return graph, nil
}

// GetTechCompetencyGraph retrieves the latest competency graph for a user
func (s *TechCompetencyService) GetTechCompetencyGraph(ctx context.Context, userID string) (*student_profile.TechCompetencyGraph, error) {
	graph, err := s.repos.TechCompetency.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tech competency graph: %w", err)
	}
	return graph, nil
}

// GetTechCompetencyGraphByID retrieves a competency graph by its ID
func (s *TechCompetencyService) GetTechCompetencyGraphByID(ctx context.Context, graphID primitive.ObjectID) (*student_profile.TechCompetencyGraph, error) {
	graph, err := s.repos.TechCompetency.GetByID(ctx, graphID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tech competency graph: %w", err)
	}
	return graph, nil
}

// GetNodesByLevel retrieves nodes by competency level for a user
func (s *TechCompetencyService) GetNodesByLevel(ctx context.Context, userID string, level student_profile.CompetencyLevel) ([]student_profile.CompetencyNode, error) {
	nodes, err := s.repos.TechCompetency.GetNodesByLevel(ctx, userID, level)
	if err != nil {
		return nil, fmt.Errorf("failed to get nodes by level: %w", err)
	}
	return nodes, nil
}

// GetUserCompetencyStats retrieves competency statistics for a user
func (s *TechCompetencyService) GetUserCompetencyStats(ctx context.Context, userID string) (map[student_profile.CompetencyLevel]int, error) {
	stats, err := s.repos.TechCompetency.GetUserCompetencyStats(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user competency stats: %w", err)
	}
	return stats, nil
}

// GetStaleGraphs retrieves graphs that need updating
func (s *TechCompetencyService) GetStaleGraphs(ctx context.Context, threshold time.Duration) ([]*student_profile.TechCompetencyGraph, error) {
	graphs, err := s.repos.TechCompetency.GetStaleGraphs(ctx, threshold)
	if err != nil {
		return nil, fmt.Errorf("failed to get stale graphs: %w", err)
	}
	return graphs, nil
}

// RefreshStaleGraphs refreshes graphs that haven't been updated recently
func (s *TechCompetencyService) RefreshStaleGraphs(ctx context.Context) error {
	// Get graphs older than 7 days
	staleGraphs, err := s.GetStaleGraphs(ctx, 7*24*time.Hour)
	if err != nil {
		return fmt.Errorf("failed to get stale graphs: %w", err)
	}

	for _, graph := range staleGraphs {
		// Create a refresh action
		refreshAction := student_profile.StudentAction{
			Type:        "refresh",
			Description: "Periodic graph refresh",
			Content:     map[string]interface{}{"refresh_type": "scheduled"},
			Timestamp:   time.Now(),
		}

		// Update with refresh action
		req := &UpdateCompetencyActionRequest{
			UserID:       graph.UserID,
			Action:       refreshAction,
			AIEvaluation: "Scheduled competency graph refresh for data staleness",
		}

		_, err := s.UpdateTechCompetencyWithAction(ctx, req)
		if err != nil {
			logging.Error("Failed to refresh stale graph: user_id=%s, error=%v", graph.UserID, err)
			continue
		}

		logging.Info("Refreshed stale competency graph: user_id=%s", graph.UserID)
	}

	return nil
}

func (s *TechCompetencyService) validateUpdateActionRequest(req *UpdateCompetencyActionRequest) error {
	if req.UserID == "" {
		return fmt.Errorf("user_id is required")
	}
	if req.Action.Type == "" {
		return fmt.Errorf("action type is required")
	}
	if req.AIEvaluation == "" {
		req.AIEvaluation = "请你自行评估该操作及其结果对用户技术能力的影响"
	}
	return nil
}

// callAIAnalyzeCompetency calls AI agent to analyze competency using the tech profile analyzer
func (s *TechCompetencyService) callAIAnalyzeCompetency(ctx context.Context, input student_profile.CompetencyGraphInput) (*ai_agents.AgentResponse, error) {
	// Prepare profile data for the tech analyzer
	profileData := map[string]interface{}{
		"current_competency_graph": input.CurrentGraph,
		"student_action":         input.StudentAction,
		"ai_evaluation":       input.AIEvaluation,
	}

	// Call the AnalyzeTechProfile method
	return s.aiManager.AnalyzeTechProfile(ctx, "competency_system", profileData)
}

type UpdateCompetencyActionRequest struct {
	UserID       string                           `json:"user_id" validate:"required"`
	Action       student_profile.StudentAction   `json:"action" validate:"required"`
	AIEvaluation string                           `json:"ai_evaluation"`
}