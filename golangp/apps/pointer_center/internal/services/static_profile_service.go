package services

import (
	"context"
	"fmt"
	"pointer/golangp/apps/pointer_center/internal/models/student_profile"
	"pointer/golangp/apps/pointer_center/internal/repositories"
	"pointer/golangp/common/logging"
	"time"

	"github.com/google/uuid"
)

// StaticProfileService handles business logic for static student profiles
type StaticProfileService struct {
	repos *repositories.Repositories
}

// NewStaticProfileService creates a new static profile service
func NewStaticProfileService(repos *repositories.Repositories) *StaticProfileService {
	return &StaticProfileService{
		repos: repos,
	}
}

// CreateStaticProfile creates a new static profile for a student
func (s *StaticProfileService) CreateStaticProfile(ctx context.Context, req *CreateStaticProfileRequest) (*student_profile.StaticProfile, error) {
	// Validate request
	if err := s.validateCreateStaticProfileRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Check if profile already exists for user
	exists, err := s.repos.StaticProfile.ExistsByUserID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check if profile exists: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("static profile already exists for user %s", req.UserID)
	}

	// Create static profile model
	profile := &student_profile.StaticProfile{
		ID:                  uuid.New(),
		UserID:              req.UserID,
		Age:                 req.Age,
		Gender:              req.Gender,
		PreferredLanguage:   req.PreferredLanguage,
		EducationExperience: req.EducationExperience,
		Major:               req.Major,
		GraduationYear:      req.GraduationYear,
		CurrentRole:         req.CurrentRole,
		Industry:            req.Industry,
		WorkExperience:      req.WorkExperience,
		LearningStyle:       req.LearningStyle,
		StudyTimePerWeek:    req.StudyTimePerWeek,
		PreferredStudyTime:  req.PreferredStudyTime,
		LearningPace:        req.LearningPace,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	// Save to database
	if err := s.repos.StaticProfile.Create(ctx, profile); err != nil {
		logging.Error("Failed to create static profile: user_id=%s, error=%v", req.UserID, err)
		return nil, fmt.Errorf("failed to create static profile: %w", err)
	}

	logging.Info("Static profile created successfully: user_id=%s, profile_id=%s", req.UserID, profile.ID)
	return profile, nil
}

// UpdateStaticProfile updates an existing static profile
func (s *StaticProfileService) UpdateStaticProfile(ctx context.Context, userID uuid.UUID, req *UpdateStaticProfileRequest) (*student_profile.StaticProfile, error) {
	// Get existing profile
	existingProfile, err := s.repos.StaticProfile.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get static profile: %w", err)
	}

	// Update fields if provided
	if req.Age != nil {
		existingProfile.Age = *req.Age
	}
	if req.Gender != nil {
		existingProfile.Gender = *req.Gender
	}
	if req.PreferredLanguage != nil {
		existingProfile.PreferredLanguage = *req.PreferredLanguage
	}
	if req.EducationExperience != nil {
		existingProfile.EducationExperience = *req.EducationExperience
	}
	if req.Major != nil {
		existingProfile.Major = *req.Major
	}
	if req.GraduationYear != nil {
		existingProfile.GraduationYear = *req.GraduationYear
	}
	if req.CurrentRole != nil {
		existingProfile.CurrentRole = *req.CurrentRole
	}
	if req.Industry != nil {
		existingProfile.Industry = *req.Industry
	}
	if req.WorkExperience != nil {
		existingProfile.WorkExperience = *req.WorkExperience
	}
	if req.LearningStyle != nil {
		existingProfile.LearningStyle = *req.LearningStyle
	}
	if req.StudyTimePerWeek != nil {
		existingProfile.StudyTimePerWeek = *req.StudyTimePerWeek
	}
	if req.PreferredStudyTime != nil {
		existingProfile.PreferredStudyTime = *req.PreferredStudyTime
	}
	if req.LearningPace != nil {
		existingProfile.LearningPace = *req.LearningPace
	}

	existingProfile.UpdatedAt = time.Now()

	// Save updates
	if err := s.repos.StaticProfile.Update(ctx, existingProfile); err != nil {
		logging.Error("Failed to update static profile: user_id=%s, error=%v", userID, err)
		return nil, fmt.Errorf("failed to update static profile: %w", err)
	}

	logging.Info("Static profile updated successfully: user_id=%s", userID)
	return existingProfile, nil
}

// GetStaticProfile retrieves a static profile by user ID
func (s *StaticProfileService) GetStaticProfile(ctx context.Context, userID uuid.UUID) (*student_profile.StaticProfile, error) {
	profile, err := s.repos.StaticProfile.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get static profile: %w", err)
	}

	// Update last accessed time
	if err := s.repos.StaticProfile.UpdateLastAccessed(ctx, userID); err != nil {
		logging.Warning("Failed to update last accessed time: user_id=%s, error=%v", userID, err)
	}

	return profile, nil
}

// CreateEmptyStaticProfile creates an empty static profile for a user
func (s *StaticProfileService) CreateEmptyStaticProfile(ctx context.Context, userID uuid.UUID) (*student_profile.StaticProfile, error) {
	// Check if profile already exists for user
	exists, err := s.repos.StaticProfile.ExistsByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check if profile exists: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("static profile already exists for user %s", userID)
	}

	// Create empty profile
	profile := &student_profile.StaticProfile{
		ID:                  uuid.New(),
		UserID:              userID,
		Age:                 0,
		Gender:              "",
		PreferredLanguage:   "zh-CN", // Default to Chinese
		EducationExperience: "",
		Major:               "",
		GraduationYear:      0,
		CurrentRole:         "",
		Industry:            "",
		WorkExperience:      0,
		LearningStyle:       "",
		StudyTimePerWeek:    0,
		PreferredStudyTime:  "",
		LearningPace:        "",
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	// Save to database
	if err := s.repos.StaticProfile.Create(ctx, profile); err != nil {
		return nil, fmt.Errorf("failed to create empty static profile: %w", err)
	}

	logging.Info("Empty static profile created successfully: user_id=%s, profile_id=%s", userID, profile.ID)
	return profile, nil
}

// ExistsByUserID checks if a static profile exists for a user
func (s *StaticProfileService) ExistsByUserID(ctx context.Context, userID uuid.UUID) (bool, error) {
	exists, err := s.repos.StaticProfile.ExistsByUserID(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to check if profile exists: %w", err)
	}
	return exists, nil
}

// GetStaticProfileByID retrieves a static profile by profile ID
func (s *StaticProfileService) GetStaticProfileByID(ctx context.Context, profileID uuid.UUID) (*student_profile.StaticProfile, error) {
	profile, err := s.repos.StaticProfile.GetByID(ctx, profileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get static profile: %w", err)
	}
	return profile, nil
}

// DeleteStaticProfile deletes a static profile
func (s *StaticProfileService) DeleteStaticProfile(ctx context.Context, userID uuid.UUID) error {
	// Get existing profile to check if it exists
	existingProfile, err := s.repos.StaticProfile.GetByUserID(ctx, userID)
	if err != nil {
		return fmt.Errorf("static profile not found: %w", err)
	}

	// Delete the profile
	if err := s.repos.StaticProfile.Delete(ctx, existingProfile.ID); err != nil {
		logging.Error("Failed to delete static profile: user_id=%s, error=%v", userID, err)
		return fmt.Errorf("failed to delete static profile: %w", err)
	}

	logging.Info("Static profile deleted successfully: user_id=%s", userID)
	return nil
}

// GetStaticProfiles retrieves static profiles with pagination and filtering
func (s *StaticProfileService) GetStaticProfiles(ctx context.Context, req *GetStaticProfilesRequest) ([]*student_profile.StaticProfile, int64, error) {
	// Set default pagination
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// Calculate offset
	offset := (req.Page - 1) * req.PageSize

	var profiles []*student_profile.StaticProfile
	var err error

	// Apply filters
	if req.Industry != "" {
		profiles, err = s.repos.StaticProfile.GetProfilesByIndustry(ctx, req.Industry)
	} else if req.LearningStyle != "" {
		profiles, err = s.repos.StaticProfile.GetProfilesByLearningStyle(ctx, req.LearningStyle)
	} else if req.MinAge > 0 && req.MaxAge > 0 {
		profiles, err = s.repos.StaticProfile.GetProfilesByAge(ctx, req.MinAge, req.MaxAge)
	} else {
		profiles, err = s.repos.StaticProfile.List(ctx, req.PageSize, offset)
	}

	if err != nil {
		return nil, 0, fmt.Errorf("failed to get static profiles: %w", err)
	}

	// Get total count
	total, err := s.repos.StaticProfile.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}

	return profiles, total, nil
}

// GetProfileSummary retrieves a profile summary for AI context
func (s *StaticProfileService) GetProfileSummary(ctx context.Context, userID uuid.UUID) (*student_profile.ProfileSummary, error) {
	summary, err := s.repos.StaticProfile.GetProfileSummary(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get profile summary: %w", err)
	}
	return summary, nil
}

// GenerateProfileInsights generates insights based on static profile
func (s *StaticProfileService) GenerateProfileInsights(ctx context.Context, userID uuid.UUID) (*ProfileInsights, error) {
	profile, err := s.repos.StaticProfile.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get static profile: %w", err)
	}

	insights := &ProfileInsights{
		UserID:                  userID,
		LearningRecommendations: make([]string, 0),
		CareerAdvice:            make([]string, 0),
		SkillGaps:               make([]string, 0),
		OptimalLearningPath:     "",
		GeneratedAt:             time.Now(),
	}

	// Generate learning recommendations based on profile
	s.generateLearningRecommendations(profile, insights)

	// Generate career advice
	s.generateCareerAdvice(profile, insights)

	// Identify skill gaps
	s.identifySkillGaps(profile, insights)

	logging.Info("Profile insights generated: user_id=%s", userID)
	return insights, nil
}

// Helper methods
func (s *StaticProfileService) validateCreateStaticProfileRequest(req *CreateStaticProfileRequest) error {
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user_id is required")
	}
	if req.Age < 0 || req.Age > 150 {
		return fmt.Errorf("age must be between 0 and 150")
	}
	if req.StudyTimePerWeek < 0 || req.StudyTimePerWeek > 168 {
		return fmt.Errorf("study_time_per_week must be between 0 and 168 hours")
	}
	return nil
}

func (s *StaticProfileService) generateLearningRecommendations(profile *student_profile.StaticProfile, insights *ProfileInsights) {
	// Generate personalized learning recommendations based on profile data
	recommendations := make([]string, 0)

	// Based on learning style
	switch profile.LearningStyle {
	case "visual":
		recommendations = append(recommendations, "推荐视频课程和图表学习", "使用思维导图整理知识")
	case "auditory":
		recommendations = append(recommendations, "推荐音频课程和讲座", "参与讨论和小组学习")
	case "kinesthetic":
		recommendations = append(recommendations, "推荐实践项目和动手实验", "边做边学的编程练习")
	default:
		recommendations = append(recommendations, "推荐多样化的学习方式")
	}

	// Based on study time availability
	if profile.StudyTimePerWeek < 5 {
		recommendations = append(recommendations, "推荐短时高效的微课程", "利用碎片时间学习")
	} else if profile.StudyTimePerWeek > 20 {
		recommendations = append(recommendations, "可以尝试深度学习项目", "参与长期技能训练营")
	}

	// Based on current role and industry
	if profile.Industry == "technology" {
		recommendations = append(recommendations, "重点学习最新技术栈", "关注行业发展趋势")
	}

	insights.LearningRecommendations = recommendations
}

func (s *StaticProfileService) generateCareerAdvice(profile *student_profile.StaticProfile, insights *ProfileInsights) {
	advice := make([]string, 0)

	// Based on work experience
	if profile.WorkExperience < 2 {
		advice = append(advice, "重点建立基础技能", "寻找实习或初级职位机会")
	} else if profile.WorkExperience < 5 {
		advice = append(advice, "发展专业技能", "考虑专业认证")
	} else {
		advice = append(advice, "考虑领导力培养", "探索管理或专家路径")
	}

	// Based on education and current role
	if profile.CurrentRole == "" {
		advice = append(advice, "明确职业方向", "制定学习计划")
	}

	insights.CareerAdvice = advice
}

func (s *StaticProfileService) identifySkillGaps(profile *student_profile.StaticProfile, insights *ProfileInsights) {
	gaps := make([]string, 0)

	// Common skill gaps based on industry
	switch profile.Industry {
	case "technology":
		gaps = append(gaps, "云计算技能", "数据分析能力", "软技能提升")
	case "finance":
		gaps = append(gaps, "金融科技理解", "数据分析", "风险管理")
	default:
		gaps = append(gaps, "数字化技能", "数据分析基础")
	}

	// Based on learning pace preference
	if profile.LearningPace == "slow" {
		gaps = append(gaps, "学习效率优化")
	}

	insights.SkillGaps = gaps
	insights.OptimalLearningPath = s.generateOptimalPath(profile)
}

func (s *StaticProfileService) generateOptimalPath(profile *student_profile.StaticProfile) string {
	// Generate optimal learning path based on profile
	if profile.WorkExperience < 2 {
		return "基础技能 → 实践项目 → 专业认证"
	} else if profile.WorkExperience < 5 {
		return "专业深化 → 行业认证 → 项目管理"
	} else {
		return "领导力发展 → 战略思维 → 团队管理"
	}
}

// Request/Response types
type CreateStaticProfileRequest struct {
	UserID              uuid.UUID `json:"user_id" validate:"required"`
	Age                 int       `json:"age" validate:"min=0,max=150"`
	Gender              string    `json:"gender"`
	PreferredLanguage   string    `json:"preferred_language"`
	EducationExperience string    `json:"education_experience"`
	Major               string    `json:"major"`
	GraduationYear      int       `json:"graduation_year"`
	CurrentRole         string    `json:"current_role"`
	Industry            string    `json:"industry"`
	WorkExperience      int       `json:"work_experience" validate:"min=0"`
	LearningStyle       string    `json:"learning_style"`
	StudyTimePerWeek    int       `json:"study_time_per_week" validate:"min=0,max=168"`
	PreferredStudyTime  string    `json:"preferred_study_time"`
	LearningPace        string    `json:"learning_pace"`
}

type UpdateStaticProfileRequest struct {
	Age                 *int    `json:"age,omitempty"`
	Gender              *string `json:"gender,omitempty"`
	PreferredLanguage   *string `json:"preferred_language,omitempty"`
	EducationExperience *string `json:"education_experience,omitempty"`
	Major               *string `json:"major,omitempty"`
	GraduationYear      *int    `json:"graduation_year,omitempty"`
	CurrentRole         *string `json:"current_role,omitempty"`
	Industry            *string `json:"industry,omitempty"`
	WorkExperience      *int    `json:"work_experience,omitempty"`
	LearningStyle       *string `json:"learning_style,omitempty"`
	StudyTimePerWeek    *int    `json:"study_time_per_week,omitempty"`
	PreferredStudyTime  *string `json:"preferred_study_time,omitempty"`
	LearningPace        *string `json:"learning_pace,omitempty"`
}

type GetStaticProfilesRequest struct {
	Page          int    `json:"page"`
	PageSize      int    `json:"page_size"`
	Industry      string `json:"industry,omitempty"`
	LearningStyle string `json:"learning_style,omitempty"`
	MinAge        int    `json:"min_age,omitempty"`
	MaxAge        int    `json:"max_age,omitempty"`
}

type ProfileInsights struct {
	UserID                  uuid.UUID `json:"user_id"`
	LearningRecommendations []string  `json:"learning_recommendations"`
	CareerAdvice            []string  `json:"career_advice"`
	SkillGaps               []string  `json:"skill_gaps"`
	OptimalLearningPath     string    `json:"optimal_learning_path"`
	GeneratedAt             time.Time `json:"generated_at"`
}
