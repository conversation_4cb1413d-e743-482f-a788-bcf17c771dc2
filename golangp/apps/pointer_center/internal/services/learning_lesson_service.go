package services

import (
	"context"
	"encoding/json"
	"fmt"
	"pointer/golangp/apps/pointer_center/internal/models"
	"pointer/golangp/apps/pointer_center/internal/repositories"
	"pointer/golangp/apps/pointer_center/pkg/ai_agents"
	"pointer/golangp/common/logging"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// LearningLessonService handles business logic for learning lessons
type LearningLessonService struct {
	repos     *repositories.Repositories
	aiManager *ai_agents.Manager
}

// NewLearningLessonService creates a new learning lesson service
func NewLearningLessonService(repos *repositories.Repositories, aiManager *ai_agents.Manager) *LearningLessonService {
	return &LearningLessonService{
		repos:     repos,
		aiManager: aiManager,
	}
}

// CreateLearningLesson creates a new learning lesson
func (s *LearningLessonService) CreateLearningLesson(ctx context.Context, req *CreateLearningLessonRequest) (*models.LearningLesson, error) {
	// Validate request
	if err := s.validateCreateLessonRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	// Create learning lesson model
	lesson := &models.LearningLesson{
		ID:                        primitive.NewObjectID(),
		LessonID:                  req.LessonID,
		Title:                     req.Title,
		Description:               req.Description,
		Type:                      models.LessonType(req.Type),
		EstimatedMinutes:          req.EstimatedMinutes,
		Difficulty:                models.DifficultyLevel(req.Difficulty),
		ContentFlow:               req.ContentFlow,
		LearningObjectives:        req.LearningObjectives,
		CommonMisconceptions:      req.CommonMisconceptions,
		ExtensionIdea:             req.ExtensionIdea,
		StudentProfileAssociation: req.StudentProfileAssociation,
		Status:                    models.LessonStatusDraft,
		CreatedBy:                 req.CreatedBy,
		CreatedAt:                 time.Now(),
		UpdatedAt:                 time.Now(),
	}
	// Save to MongoDB
	if err := s.repos.LearningLesson.Create(ctx, lesson); err != nil {
		logging.Error("Failed to create learning lesson: lesson_id=%v, error=%v", lesson.ID, err)
		return nil, fmt.Errorf("failed to create learning lesson: %w", err)
	}
	logging.Info("Learning lesson created successfully: lesson_id=%v, title=%s", lesson.ID, lesson.Title)
	return lesson, nil
}

// GenerateLearningLessonWithAI generates a learning lesson using AI
func (s *LearningLessonService) GenerateLearningLessonWithAI(ctx context.Context, req *GenerateLessonRequest) (*models.LearningLesson, error) {
	// Validate request
	if err := s.validateGenerateLessonRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	// Get student profile for AI context
	studentProfile := ""
	if req.UserID != "" {
		profileData, err := s.getStudentProfileForAI(ctx, req.UserID)
		if err != nil {
			logging.Error("Failed to get student profile: user_id=%s, error=%v", req.UserID, err)
		} else {
			profileJSON, _ := json.Marshal(profileData)
			studentProfile = string(profileJSON)
		}
	}
	// Call AI agent to generate lesson content
	aiResponse, err := s.aiManager.GenerateLesson(ctx, req.LessonDescription, studentProfile)
	if err != nil {
		logging.Error("AI lesson generation failed: user_id=%s, error=%v", req.UserID, err)
		return nil, fmt.Errorf("AI lesson generation failed: %w", err)
	}
	if !aiResponse.Success {
		logging.Error("AI lesson generation unsuccessful: user_id=%s, error=%s", req.UserID, aiResponse.Error)
		return nil, fmt.Errorf("AI lesson generation failed: %s", aiResponse.Error)
	}
	// Parse AI response to create learning lesson
	lesson, err := s.parseAIResponseToLesson(aiResponse.Data, req)
	if err != nil {
		logging.Error("Failed to parse AI response: user_id=%s, error=%v", req.UserID, err)
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}
	// Save generated lesson to MongoDB
	if err := s.repos.LearningLesson.Create(ctx, lesson); err != nil {
		logging.Error("Failed to save AI-generated lesson: lesson_id=%v, error=%v", lesson.ID, err)
		return nil, fmt.Errorf("failed to save AI-generated lesson: %w", err)
	}
	logging.Info("AI-generated learning lesson created: lesson_id=%v, user_id=%s, tokens_used=%d", lesson.ID, req.UserID, aiResponse.TokensUsed)
	return lesson, nil
}

// UpdateLearningLesson updates an existing learning lesson
func (s *LearningLessonService) UpdateLearningLesson(ctx context.Context, lessonID primitive.ObjectID, req *UpdateLearningLessonRequest) (*models.LearningLesson, error) {
	// Get existing lesson
	existingLesson, err := s.repos.LearningLesson.GetByID(ctx, lessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning lesson: %w", err)
	}
	// Update fields
	if req.LessonID != nil {
		existingLesson.LessonID = *req.LessonID
	}
	if req.Title != nil {
		existingLesson.Title = *req.Title
	}
	if req.Description != nil {
		existingLesson.Description = *req.Description
	}
	if req.Type != nil {
		existingLesson.Type = models.LessonType(*req.Type)
	}
	if req.EstimatedMinutes != nil {
		existingLesson.EstimatedMinutes = *req.EstimatedMinutes
	}
	if req.Difficulty != nil {
		existingLesson.Difficulty = models.DifficultyLevel(*req.Difficulty)
	}
	if req.ContentFlow != nil {
		existingLesson.ContentFlow = req.ContentFlow
	}
	if req.LearningObjectives != nil {
		existingLesson.LearningObjectives = req.LearningObjectives
	}
	if req.CommonMisconceptions != nil {
		existingLesson.CommonMisconceptions = req.CommonMisconceptions
	}
	if req.ExtensionIdea != nil {
		existingLesson.ExtensionIdea = *req.ExtensionIdea
	}
	if req.StudentProfileAssociation != nil {
		existingLesson.StudentProfileAssociation = *req.StudentProfileAssociation
	}
	if req.UpdatedBy != nil {
		existingLesson.UpdatedBy = *req.UpdatedBy
	}
	existingLesson.UpdatedAt = time.Now()
	// Save updates
	if err := s.repos.LearningLesson.Update(ctx, existingLesson); err != nil {
		logging.Error("Failed to update learning lesson: lesson_id=%v, error=%v", lessonID, err)
		return nil, fmt.Errorf("failed to update learning lesson: %w", err)
	}
	logging.Info("Learning lesson updated successfully: lesson_id=%v", lessonID)
	return existingLesson, nil
}

// DeleteLearningLesson soft deletes a learning lesson
func (s *LearningLessonService) DeleteLearningLesson(ctx context.Context, lessonID primitive.ObjectID) error {
	// Check if lesson exists
	_, err := s.repos.LearningLesson.GetByID(ctx, lessonID)
	if err != nil {
		return fmt.Errorf("learning lesson not found: %w", err)
	}
	// Use regular delete since repository doesn't have SoftDelete method
	if err := s.repos.LearningLesson.Delete(ctx, lessonID); err != nil {
		logging.Error("Failed to delete learning lesson: lesson_id=%v, error=%v", lessonID, err)
		return fmt.Errorf("failed to delete learning lesson: %w", err)
	}
	logging.Info("Learning lesson deleted successfully: lesson_id=%v", lessonID)
	return nil
}

// GetLearningLesson retrieves a learning lesson by ID
func (s *LearningLessonService) GetLearningLesson(ctx context.Context, lessonID primitive.ObjectID) (*models.LearningLesson, error) {
	lesson, err := s.repos.LearningLesson.GetByID(ctx, lessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning lesson: %w", err)
	}
	return lesson, nil
}

// GetLearningLessonByLessonID retrieves a learning lesson by lesson_id
func (s *LearningLessonService) GetLearningLessonByLessonID(ctx context.Context, lessonID string) (*models.LearningLesson, error) {
	lesson, err := s.repos.LearningLesson.GetByLessonID(ctx, lessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning lesson by lesson_id: %w", err)
	}
	return lesson, nil
}

// GetLearningLessons retrieves learning lessons with pagination
func (s *LearningLessonService) GetLearningLessons(ctx context.Context, req *GetLearningLessonsRequest) ([]*models.LearningLesson, int64, error) {
	// Set default pagination
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	// Calculate skip
	skip := (req.Page - 1) * req.PageSize
	// Get lessons
	lessons, err := s.repos.LearningLesson.List(ctx, req.PageSize, skip)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get learning lessons: %w", err)
	}
	// Get total count
	total, err := s.repos.LearningLesson.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}
	return lessons, total, nil
}

// GetLessonsByType retrieves lessons filtered by type
func (s *LearningLessonService) GetLessonsByType(ctx context.Context, lessonType models.LessonType, page, pageSize int) ([]*models.LearningLesson, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	// Calculate skip
	skip := (page - 1) * pageSize
	// Get lessons by type
	lessons, err := s.repos.LearningLesson.ListByType(ctx, lessonType, pageSize, skip)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get lessons by type: %w", err)
	}
	// Get total count by type
	total, err := s.repos.LearningLesson.CountByType(ctx, lessonType)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count by type: %w", err)
	}
	return lessons, total, nil
}

// GetLessonsByStatus retrieves lessons filtered by status
func (s *LearningLessonService) GetLessonsByStatus(ctx context.Context, status models.LessonStatus, page, pageSize int) ([]*models.LearningLesson, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	lessons, err := s.repos.LearningLesson.ListByStatus(ctx, status, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get lessons by status: %w", err)
	}

	total, err := s.repos.LearningLesson.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}
	return lessons, total, nil
}

// UpdateLessonStatus updates the status of a learning lesson
func (s *LearningLessonService) UpdateLessonStatus(ctx context.Context, lessonID primitive.ObjectID, status models.LessonStatus) error {
	// Validate status
	if !s.isValidLessonStatus(status) {
		return fmt.Errorf("invalid status: %s", status)
	}
	// Get existing lesson
	lesson, err := s.repos.LearningLesson.GetByID(ctx, lessonID)
	if err != nil {
		return fmt.Errorf("failed to get learning lesson: %w", err)
	}
	// Update status
	lesson.Status = status
	lesson.UpdatedAt = time.Now()
	if err := s.repos.LearningLesson.Update(ctx, lesson); err != nil {
		logging.Error("Failed to update lesson status: lesson_id=%v, status=%v, error=%v", lessonID, status, err)
		return fmt.Errorf("failed to update lesson status: %w", err)
	}
	logging.Info("Lesson status updated: lesson_id=%v, status=%v", lessonID, status)
	return nil
}

// DuplicateLesson creates a copy of an existing lesson
func (s *LearningLessonService) DuplicateLesson(ctx context.Context, lessonID primitive.ObjectID, req *DuplicateLessonRequest) (*models.LearningLesson, error) {
	// Get original lesson
	originalLesson, err := s.repos.LearningLesson.GetByID(ctx, lessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to get original lesson: %w", err)
	}
	// Create duplicate
	duplicateLesson := &models.LearningLesson{
		ID:                        primitive.NewObjectID(),
		LessonID:                  req.NewLessonID,
		Title:                     req.NewTitle,
		Description:               originalLesson.Description,
		Type:                      originalLesson.Type,
		EstimatedMinutes:          originalLesson.EstimatedMinutes,
		Difficulty:                originalLesson.Difficulty,
		ContentFlow:               originalLesson.ContentFlow,
		LearningObjectives:        originalLesson.LearningObjectives,
		CommonMisconceptions:      originalLesson.CommonMisconceptions,
		ExtensionIdea:             originalLesson.ExtensionIdea,
		StudentProfileAssociation: originalLesson.StudentProfileAssociation,
		Status:                    models.LessonStatusDraft,
		CreatedBy:                 req.CreatedBy,
		CreatedAt:                 time.Now(),
		UpdatedAt:                 time.Now(),
	}
	// Save duplicate
	if err := s.repos.LearningLesson.Create(ctx, duplicateLesson); err != nil {
		logging.Error("Failed to create duplicate lesson: original_lesson_id=%v, error=%v", lessonID, err)
		return nil, fmt.Errorf("failed to create duplicate lesson: %w", err)
	}
	logging.Info("Lesson duplicated successfully: original_lesson_id=%v, new_lesson_id=%v", lessonID, duplicateLesson.ID)
	return duplicateLesson, nil
}

// SearchLessons searches lessons by content or title
func (s *LearningLessonService) SearchLessons(ctx context.Context, query string, page, pageSize int) ([]*models.LearningLesson, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	// Calculate skip
	skip := (page - 1) * pageSize
	// Search lessons
	lessons, err := s.repos.LearningLesson.Search(ctx, query, pageSize, skip)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to search lessons: %w", err)
	}
	// Get total count for search
	total := int64(len(lessons)) // Simplified approach since repo doesn't have CountSearch
	return lessons, total, nil
}

// ArchiveLesson archives a lesson (soft delete)
func (s *LearningLessonService) ArchiveLesson(ctx context.Context, lessonID primitive.ObjectID) error {
	return s.UpdateLessonStatus(ctx, lessonID, models.LessonStatusArchived)
}

// PublishLesson publishes a lesson
func (s *LearningLessonService) PublishLesson(ctx context.Context, lessonID primitive.ObjectID) error {
	return s.UpdateLessonStatus(ctx, lessonID, models.LessonStatusActive)
}

// Helper methods
func (s *LearningLessonService) validateCreateLessonRequest(req *CreateLearningLessonRequest) error {
	if req.Title == "" {
		return fmt.Errorf("title is required")
	}
	if req.Type == "" {
		return fmt.Errorf("type is required")
	}
	if req.CreatedBy == "" {
		return fmt.Errorf("created_by is required")
	}
	return nil
}

func (s *LearningLessonService) validateGenerateLessonRequest(req *GenerateLessonRequest) error {
	if req.LessonDescription == "" {
		return fmt.Errorf("lesson_description is required")
	}
	return nil
}

func (s *LearningLessonService) getStudentProfileForAI(ctx context.Context, userID string) (map[string]interface{}, error) {
	// Get tech competency
	techCompetency, err := s.repos.TechCompetency.GetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	// For now, just return tech competency
	// In future, can combine with other profile data
	profileData := map[string]interface{}{
		"tech_competency": techCompetency,
	}
	return profileData, nil
}

func (s *LearningLessonService) parseAIResponseToLesson(aiData map[string]interface{}, req *GenerateLessonRequest) (*models.LearningLesson, error) {
	lesson := &models.LearningLesson{
		ID:        primitive.NewObjectID(),
		Status:    models.LessonStatusDraft,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	// Parse AI response fields
	if title, ok := aiData["title"].(string); ok {
		lesson.Title = title
	} else {
		lesson.Title = "AI Generated Lesson"
	}
	if description, ok := aiData["description"].(string); ok {
		lesson.Description = description
	} else {
		lesson.Description = req.LessonDescription
	}
	if lessonType, ok := aiData["type"].(string); ok {
		lesson.Type = models.LessonType(lessonType)
	} else {
		lesson.Type = models.LessonTypeText // Default
	}
	if minutes, ok := aiData["estimated_minutes"].(float64); ok {
		lesson.EstimatedMinutes = int(minutes)
	} else {
		lesson.EstimatedMinutes = 30 // Default
	}
	if difficulty, ok := aiData["difficulty"].(float64); ok {
		lesson.Difficulty = models.DifficultyLevel(int(difficulty))
	} else {
		lesson.Difficulty = models.DifficultyIntermediate5 // Default
	}
	if objectives, ok := aiData["learning_objectives"].([]interface{}); ok {
		lesson.LearningObjectives = make([]string, len(objectives))
		for i, obj := range objectives {
			if objStr, ok := obj.(string); ok {
				lesson.LearningObjectives[i] = objStr
			}
		}
	}
	if misconceptions, ok := aiData["common_misconceptions"].([]interface{}); ok {
		lesson.CommonMisconceptions = make([]string, len(misconceptions))
		for i, misc := range misconceptions {
			if miscStr, ok := misc.(string); ok {
				lesson.CommonMisconceptions[i] = miscStr
			}
		}
	}
	if extension, ok := aiData["extension_idea"].(string); ok {
		lesson.ExtensionIdea = extension
	}
	return lesson, nil
}

func (s *LearningLessonService) isValidLessonStatus(status models.LessonStatus) bool {
	validStatuses := []models.LessonStatus{
		models.LessonStatusDraft,
		models.LessonStatusActive,
		models.LessonStatusArchived,
		models.LessonStatusDeprecated,
	}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// Request/Response types
type CreateLearningLessonRequest struct {
	LessonID                  string                 `json:"lesson_id"`
	Title                     string                 `json:"title" validate:"required"`
	Description               string                 `json:"description"`
	Type                      string                 `json:"type" validate:"required"`
	EstimatedMinutes          int                    `json:"estimated_minutes"`
	Difficulty                int                    `json:"difficulty"`
	ContentFlow               []models.AtomicContent `json:"content_flow"`
	LearningObjectives        []string               `json:"learning_objectives"`
	CommonMisconceptions      []string               `json:"common_misconceptions"`
	ExtensionIdea             string                 `json:"extension_idea"`
	StudentProfileAssociation string                 `json:"student_profile_association"`
	CreatedBy                 string                 `json:"created_by" validate:"required"`
}

type UpdateLearningLessonRequest struct {
	LessonID                  *string                `json:"lesson_id,omitempty"`
	Title                     *string                `json:"title,omitempty"`
	Description               *string                `json:"description,omitempty"`
	Type                      *string                `json:"type,omitempty"`
	EstimatedMinutes          *int                   `json:"estimated_minutes,omitempty"`
	Difficulty                *int                   `json:"difficulty,omitempty"`
	ContentFlow               []models.AtomicContent `json:"content_flow,omitempty"`
	LearningObjectives        []string               `json:"learning_objectives,omitempty"`
	CommonMisconceptions      []string               `json:"common_misconceptions,omitempty"`
	ExtensionIdea             *string                `json:"extension_idea,omitempty"`
	StudentProfileAssociation *string                `json:"student_profile_association,omitempty"`
	UpdatedBy                 *string                `json:"updated_by,omitempty"`
}

type GenerateLessonRequest struct {
	LessonDescription string `json:"lesson_description" validate:"required"`
	UserID            string `json:"user_id"`
}

type GetLearningLessonsRequest struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

type DuplicateLessonRequest struct {
	NewLessonID string `json:"new_lesson_id" validate:"required"`
	NewTitle    string `json:"new_title" validate:"required"`
	CreatedBy   string `json:"created_by" validate:"required"`
}
