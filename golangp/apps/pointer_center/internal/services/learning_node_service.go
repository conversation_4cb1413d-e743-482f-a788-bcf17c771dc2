package services

import (
	"context"
	"encoding/json"
	"fmt"
	"pointer/golangp/apps/pointer_center/internal/models"
	"pointer/golangp/apps/pointer_center/internal/repositories"
	"pointer/golangp/apps/pointer_center/pkg/ai_agents"
	"pointer/golangp/common/logging"
	"time"

	"github.com/google/uuid"
)

// LearningNodeService handles business logic for learning nodes
type LearningNodeService struct {
	repos     *repositories.Repositories
	aiManager *ai_agents.Manager
}

// NewLearningNodeService creates a new learning node service
func NewLearningNodeService(repos *repositories.Repositories, aiManager *ai_agents.Manager) *LearningNodeService {
	return &LearningNodeService{
		repos:     repos,
		aiManager: aiManager,
	}
}

// CreateLearningNode creates a new learning node
func (s *LearningNodeService) CreateLearningNode(ctx context.Context, req *CreateLearningNodeRequest) (*models.LearningNode, error) {
	// Validate request
	if err := s.validateCreateNodeRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	// Convert prerequisites to JSON string
	prerequisitesJSON, _ := json.Marshal(req.Prerequisites)
	// Create learning node model
	node := &models.LearningNode{
		ID:             uuid.New(),
		Title:          req.Title,
		Description:    req.Description,
		EstimatedHours: req.EstimatedHours,
		Difficulty:     models.DifficultyLevel(req.Difficulty),
		Prerequisites:  string(prerequisitesJSON),
		Status:         models.NodeStatusDraft,
		CreatedBy:      req.CreatedBy,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
	// Save to database
	if err := s.repos.LearningNode.Create(ctx, node); err != nil {
		logging.Error("Failed to create learning node: node_id=%s, error=%v", node.ID, err)
		return nil, fmt.Errorf("failed to create learning node: %w", err)
	}
	logging.Info("Learning node created successfully: node_id=%s, title=%s", node.ID, node.Title)
	return node, nil
}

// GenerateLearningNodeWithAI generates a learning node using AI
func (s *LearningNodeService) GenerateLearningNodeWithAI(ctx context.Context, req *GenerateNodeRequest) (*models.LearningNode, error) {
	// Validate request
	if err := s.validateGenerateNodeRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	// Get student profile for AI context
	studentProfile := ""
	if req.UserID != uuid.Nil {
		profileData, err := s.getStudentProfileForAI(ctx, req.UserID)
		if err != nil {
			logging.Error("Failed to get student profile: user_id=%s, error=%v", req.UserID, err)
		} else {
			profileJSON, _ := json.Marshal(profileData)
			studentProfile = string(profileJSON)
		}
	}
	// Call AI agent to generate course content for the node
	aiResponse, err := s.aiManager.GenerateCourse(ctx, req.NodeDescription, studentProfile)
	if err != nil {
		logging.Error("AI node generation failed: user_id=%s, error=%v", req.UserID, err)
		return nil, fmt.Errorf("AI node generation failed: %w", err)
	}
	if !aiResponse.Success {
		logging.Error("AI node generation unsuccessful: user_id=%s, error=%s", req.UserID, aiResponse.Error)
		return nil, fmt.Errorf("AI node generation failed: %s", aiResponse.Error)
	}
	// Parse AI response to create learning node
	node, err := s.parseAIResponseToNode(aiResponse.Data, req)
	if err != nil {
		logging.Error("Failed to parse AI response: user_id=%s, error=%v", req.UserID, err)
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}
	// Save generated node to database
	if err := s.repos.LearningNode.Create(ctx, node); err != nil {
		logging.Error("Failed to save AI-generated node: node_id=%s, error=%v", node.ID, err)
		return nil, fmt.Errorf("failed to save AI-generated node: %w", err)
	}
	logging.Info("AI-generated learning node created: node_id=%s, user_id=%s, tokens_used=%d", node.ID, req.UserID, aiResponse.TokensUsed)
	return node, nil
}

// UpdateLearningNode updates an existing learning node
func (s *LearningNodeService) UpdateLearningNode(ctx context.Context, nodeID uuid.UUID, req *UpdateLearningNodeRequest) (*models.LearningNode, error) {
	// Get existing node
	existingNode, err := s.repos.LearningNode.GetByID(ctx, nodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning node: %w", err)
	}
	// Update fields
	if req.Title != nil {
		existingNode.Title = *req.Title
	}
	if req.Description != nil {
		existingNode.Description = *req.Description
	}
	if req.EstimatedHours != nil {
		existingNode.EstimatedHours = *req.EstimatedHours
	}
	if req.Difficulty != nil {
		existingNode.Difficulty = models.DifficultyLevel(*req.Difficulty)
	}
	if req.Prerequisites != nil {
		prerequisitesJSON, _ := json.Marshal(req.Prerequisites)
		existingNode.Prerequisites = string(prerequisitesJSON)
	}
	if req.UpdatedBy != nil {
		existingNode.UpdatedBy = *req.UpdatedBy
	}
	existingNode.UpdatedAt = time.Now()
	// Save updates
	if err := s.repos.LearningNode.Update(ctx, existingNode); err != nil {
		logging.Error("Failed to update learning node: node_id=%s, error=%v", nodeID, err)
		return nil, fmt.Errorf("failed to update learning node: %w", err)
	}
	logging.Info("Learning node updated successfully: node_id=%s", nodeID)
	return existingNode, nil
}

// DeleteLearningNode soft deletes a learning node
func (s *LearningNodeService) DeleteLearningNode(ctx context.Context, nodeID uuid.UUID) error {
	// Check if node exists
	_, err := s.repos.LearningNode.GetByID(ctx, nodeID)
	if err != nil {
		return fmt.Errorf("learning node not found: %w", err)
	}
	// Use regular delete since repository doesn't have SoftDelete method
	if err := s.repos.LearningNode.Delete(ctx, nodeID); err != nil {
		logging.Error("Failed to delete learning node: node_id=%s, error=%v", nodeID, err)
		return fmt.Errorf("failed to delete learning node: %w", err)
	}
	logging.Info("Learning node deleted successfully: node_id=%s", nodeID)
	return nil
}

// GetLearningNode retrieves a learning node by ID
func (s *LearningNodeService) GetLearningNode(ctx context.Context, nodeID uuid.UUID) (*models.LearningNode, error) {
	node, err := s.repos.LearningNode.GetByID(ctx, nodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning node: %w", err)
	}
	return node, nil
}

// GetLearningNodes retrieves learning nodes with pagination
func (s *LearningNodeService) GetLearningNodes(ctx context.Context, req *GetLearningNodesRequest) ([]*models.LearningNode, int64, error) {
	// Set default pagination
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	// Calculate offset
	offset := (req.Page - 1) * req.PageSize
	// Get nodes
	nodes, err := s.repos.LearningNode.List(ctx, req.PageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get learning nodes: %w", err)
	}
	// Get total count
	total, err := s.repos.LearningNode.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}
	return nodes, total, nil
}

// GetLearningNodesByStatus retrieves learning nodes filtered by status
func (s *LearningNodeService) GetLearningNodesByStatus(ctx context.Context, status models.NodeStatus, page, pageSize int) ([]*models.LearningNode, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	nodes, err := s.repos.LearningNode.ListByStatus(ctx, status, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get learning nodes by status: %w", err)
	}

	total, err := s.repos.LearningNode.CountByStatus(ctx, status)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count by status: %w", err)
	}
	return nodes, total, nil
}

// GetLearningNodesByDifficulty retrieves learning nodes filtered by difficulty
func (s *LearningNodeService) GetLearningNodesByDifficulty(ctx context.Context, difficulty models.DifficultyLevel, page, pageSize int) ([]*models.LearningNode, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	nodes, err := s.repos.LearningNode.ListByDifficulty(ctx, difficulty, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get learning nodes by difficulty: %w", err)
	}

	total, err := s.repos.LearningNode.Count(ctx) // Using general count as CountByDifficulty may not exist
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}
	return nodes, total, nil
}

// SearchLearningNodes searches learning nodes by query
func (s *LearningNodeService) SearchLearningNodes(ctx context.Context, query string, page, pageSize int) ([]*models.LearningNode, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	nodes, err := s.repos.LearningNode.Search(ctx, query, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to search learning nodes: %w", err)
	}

	// Get total count (simplified - using general count)
	total, err := s.repos.LearningNode.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}
	return nodes, total, nil
}

// GetLearningNodesByCreator retrieves learning nodes by creator
func (s *LearningNodeService) GetLearningNodesByCreator(ctx context.Context, creatorID uuid.UUID, page, pageSize int) ([]*models.LearningNode, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	// Note: Using List method as ListByCreator may not exist in repository
	nodes, err := s.repos.LearningNode.List(ctx, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get learning nodes by creator: %w", err)
	}

	// Filter by creator (this is a simplified implementation)
	// In a real implementation, this should be done at the database level
	var filteredNodes []*models.LearningNode
	for _, node := range nodes {
		if node.CreatedBy == creatorID {
			filteredNodes = append(filteredNodes, node)
		}
	}
	nodes = filteredNodes

	// Get total count (simplified - using general count)
	total, err := s.repos.LearningNode.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}
	return nodes, total, nil
}

// UpdateNodeStatus updates the status of a learning node
func (s *LearningNodeService) UpdateNodeStatus(ctx context.Context, nodeID uuid.UUID, status models.NodeStatus) error {
	// Validate status
	if !s.isValidNodeStatus(status) {
		return fmt.Errorf("invalid status: %s", status)
	}
	// Get existing node
	node, err := s.repos.LearningNode.GetByID(ctx, nodeID)
	if err != nil {
		return fmt.Errorf("failed to get learning node: %w", err)
	}
	// Update status
	node.Status = status
	node.UpdatedAt = time.Now()
	if err := s.repos.LearningNode.Update(ctx, node); err != nil {
		logging.Error("Failed to update node status: node_id=%s, status=%s, error=%v", nodeID, status, err)
		return fmt.Errorf("failed to update node status: %w", err)
	}
	logging.Info("Node status updated: node_id=%s, status=%s", nodeID, status)
	return nil
}

// AddNodeToPath adds a learning node to a learning path
func (s *LearningNodeService) AddNodeToPath(ctx context.Context, req *AddNodeToPathRequest) (*models.LearningPathNode, error) {
	// Validate request
	if err := s.validateAddNodeToPathRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}
	// Check if association already exists
	exists, err := s.repos.LearningPathNode.ExistsByPathAndNode(ctx, req.PathID, req.NodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to check node-path association: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("node already exists in this path")
	}
	// Create path-node association
	pathNode := &models.LearningPathNode{
		ID:             uuid.New(),
		LearningPathID: req.PathID,
		NodeID:         req.NodeID,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
	if err := s.repos.LearningPathNode.Create(ctx, pathNode); err != nil {
		logging.Error("Failed to add node to path: path_id=%s, node_id=%s, error=%v", req.PathID, req.NodeID, err)
		return nil, fmt.Errorf("failed to add node to path: %w", err)
	}
	logging.Info("Node added to path successfully: path_id=%s, node_id=%s", req.PathID, req.NodeID)
	return pathNode, nil
}

// RemoveNodeFromPath removes a learning node from a learning path
func (s *LearningNodeService) RemoveNodeFromPath(ctx context.Context, pathID, nodeID uuid.UUID) error {
	// Check if association exists
	exists, err := s.repos.LearningPathNode.ExistsByPathAndNode(ctx, pathID, nodeID)
	if err != nil {
		return fmt.Errorf("failed to check node-path association: %w", err)
	}
	if !exists {
		return fmt.Errorf("node not found in this path")
	}
	// Get all path-node associations and delete them
	pathNodes, err := s.repos.LearningPathNode.GetByPathID(ctx, pathID)
	if err != nil {
		return fmt.Errorf("failed to get path nodes: %w", err)
	}
	for _, pathNode := range pathNodes {
		if pathNode.NodeID == nodeID {
			if err := s.repos.LearningPathNode.Delete(ctx, pathNode.ID); err != nil {
				logging.Error("Failed to remove node from path: path_id=%s, node_id=%s, error=%v", pathID, nodeID, err)
				return fmt.Errorf("failed to remove node from path: %w", err)
			}
			break
		}
	}
	logging.Info("Node removed from path successfully: path_id=%s, node_id=%s", pathID, nodeID)
	return nil
}

// GetPathNodes retrieves all nodes in a learning path
func (s *LearningNodeService) GetPathNodes(ctx context.Context, pathID uuid.UUID) ([]*models.LearningPathNode, error) {
	nodes, err := s.repos.LearningPathNode.GetByPathID(ctx, pathID)
	if err != nil {
		return nil, fmt.Errorf("failed to get path nodes: %w", err)
	}
	return nodes, nil
}

// AssignLessonToNode assigns a lesson to a learning node
func (s *LearningNodeService) AssignLessonToNode(ctx context.Context, nodeID uuid.UUID, lessonID string, order int) (*models.NodeLesson, error) {
	// Check if assignment already exists
	exists, err := s.repos.NodeLesson.ExistsByNodeAndLesson(ctx, nodeID, lessonID)
	if err != nil {
		return nil, fmt.Errorf("failed to check node-lesson assignment: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("lesson already assigned to this node")
	}
	// Create node-lesson assignment
	nodeLesson := &models.NodeLesson{
		ID:        uuid.New(),
		NodeID:    nodeID,
		LessonID:  lessonID,
		Order:     order,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	if err := s.repos.NodeLesson.Create(ctx, nodeLesson); err != nil {
		logging.Error("Failed to assign lesson to node: node_id=%s, lesson_id=%s, order=%d, error=%v", nodeID, lessonID, order, err)
		return nil, fmt.Errorf("failed to assign lesson to node: %w", err)
	}
	logging.Info("Lesson assigned to node successfully: node_id=%s, lesson_id=%s, order=%d", nodeID, lessonID, order)
	return nodeLesson, nil
}

// GetNodeLessons retrieves all lessons assigned to a node
func (s *LearningNodeService) GetNodeLessons(ctx context.Context, nodeID uuid.UUID) ([]*models.NodeLesson, error) {
	lessons, err := s.repos.NodeLesson.GetByNodeID(ctx, nodeID)
	if err != nil {
		return nil, fmt.Errorf("failed to get node lessons: %w", err)
	}
	return lessons, nil
}

// Helper methods
func (s *LearningNodeService) validateCreateNodeRequest(req *CreateLearningNodeRequest) error {
	if req.Title == "" {
		return fmt.Errorf("title is required")
	}
	if req.Difficulty < 1 || req.Difficulty > 10 {
		return fmt.Errorf("difficulty must be between 1 and 10")
	}
	if req.CreatedBy == uuid.Nil {
		return fmt.Errorf("created_by is required")
	}
	return nil
}

func (s *LearningNodeService) validateGenerateNodeRequest(req *GenerateNodeRequest) error {
	if req.NodeDescription == "" {
		return fmt.Errorf("node_description is required")
	}
	return nil
}

func (s *LearningNodeService) validateAddNodeToPathRequest(req *AddNodeToPathRequest) error {
	if req.PathID == uuid.Nil {
		return fmt.Errorf("path_id is required")
	}
	if req.NodeID == uuid.Nil {
		return fmt.Errorf("node_id is required")
	}
	return nil
}

func (s *LearningNodeService) getStudentProfileForAI(ctx context.Context, userID uuid.UUID) (map[string]interface{}, error) {
	// Get static profile
	staticProfile, err := s.repos.StaticProfile.GetByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	// Get tech competency
	techCompetency, err := s.repos.TechCompetency.GetByUserID(ctx, userID.String())
	if err != nil {
		return nil, err
	}
	// Combine profile data
	profileData := map[string]interface{}{
		"static_profile":  staticProfile,
		"tech_competency": techCompetency,
	}
	return profileData, nil
}

func (s *LearningNodeService) parseAIResponseToNode(aiData map[string]interface{}, req *GenerateNodeRequest) (*models.LearningNode, error) {
	node := &models.LearningNode{
		ID:        uuid.New(),
		Status:    models.NodeStatusDraft,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	// Parse AI response fields
	if title, ok := aiData["title"].(string); ok {
		node.Title = title
	} else {
		node.Title = "AI Generated Node"
	}
	if description, ok := aiData["description"].(string); ok {
		node.Description = description
	} else {
		node.Description = req.NodeDescription
	}
	if difficulty, ok := aiData["difficulty"].(float64); ok {
		node.Difficulty = models.DifficultyLevel(int(difficulty))
	} else {
		node.Difficulty = models.DifficultyIntermediate5 // Default
	}
	if hours, ok := aiData["estimated_hours"].(float64); ok {
		node.EstimatedHours = int(hours)
	} else {
		node.EstimatedHours = 1 // Default
	}
	if prerequisites, ok := aiData["prerequisites"].([]interface{}); ok {
		prerequisitesJSON, _ := json.Marshal(prerequisites)
		node.Prerequisites = string(prerequisitesJSON)
	}
	return node, nil
}

func (s *LearningNodeService) isValidNodeStatus(status models.NodeStatus) bool {
	validStatuses := []models.NodeStatus{
		models.NodeStatusDraft,
		models.NodeStatusActive,
		models.NodeStatusArchived,
		models.NodeStatusDeprecated,
	}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return true
		}
	}
	return false
}

// Request/Response types
type CreateLearningNodeRequest struct {
	Title          string    `json:"title" validate:"required"`
	Description    string    `json:"description"`
	EstimatedHours int       `json:"estimated_hours"`
	Difficulty     int       `json:"difficulty" validate:"min=1,max=10"`
	Prerequisites  []string  `json:"prerequisites"`
	CreatedBy      uuid.UUID `json:"created_by" validate:"required"`
}

type UpdateLearningNodeRequest struct {
	Title          *string    `json:"title,omitempty"`
	Description    *string    `json:"description,omitempty"`
	EstimatedHours *int       `json:"estimated_hours,omitempty"`
	Difficulty     *int       `json:"difficulty,omitempty"`
	Prerequisites  []string   `json:"prerequisites,omitempty"`
	UpdatedBy      *uuid.UUID `json:"updated_by,omitempty"`
}

type GenerateNodeRequest struct {
	NodeDescription string    `json:"node_description" validate:"required"`
	UserID          uuid.UUID `json:"user_id"`
}

type GetLearningNodesRequest struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}

type AddNodeToPathRequest struct {
	PathID uuid.UUID `json:"path_id" validate:"required"`
	NodeID uuid.UUID `json:"node_id" validate:"required"`
}
