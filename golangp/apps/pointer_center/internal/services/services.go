package services

import (
	"pointer/golangp/apps/pointer_center/internal/repositories"
	"pointer/golangp/apps/pointer_center/pkg/ai_agents"
)

// Services contains all business logic services
type Services struct {
	LearningPath      *LearningPathService
	LearningNode      *LearningNodeService
	LearningLesson    *LearningLessonService
	StaticProfile     *StaticProfileService
	TechCompetency    *TechCompetencyService
}

// NewServices creates a new Services instance with all initialized services
func NewServices(repos *repositories.Repositories, aiManager *ai_agents.Manager) *Services {
	return &Services{
		LearningPath:   NewLearningPathService(repos, aiManager),
		LearningNode:   NewLearningNodeService(repos, aiManager),
		LearningLesson: NewLearningLessonService(repos, aiManager),
		StaticProfile:  NewStaticProfileService(repos),
		TechCompetency: NewTechCompetencyService(repos, aiManager),
	}
}
