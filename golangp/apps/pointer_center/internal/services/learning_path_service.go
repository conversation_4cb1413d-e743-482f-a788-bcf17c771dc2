package services

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"pointer/golangp/apps/pointer_center/internal/models"
	"pointer/golangp/apps/pointer_center/internal/repositories"
	"pointer/golangp/apps/pointer_center/pkg/ai_agents"
	"pointer/golangp/common/logging"

	"github.com/google/uuid"
)

// LearningPathService handles business logic for learning paths
type LearningPathService struct {
	repos     *repositories.Repositories
	aiManager *ai_agents.Manager
}

// NewLearningPathService creates a new learning path service
func NewLearningPathService(repos *repositories.Repositories, aiManager *ai_agents.Manager) *LearningPathService {
	return &LearningPathService{
		repos:     repos,
		aiManager: aiManager,
	}
}

// CreateLearningPath creates a new learning path
func (s *LearningPathService) CreateLearningPath(ctx context.Context, req *CreateLearningPathRequest) (*models.LearningPath, error) {
	// Validate request
	if err := s.validateCreateRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Convert arrays to JSON strings
	suitableForJSON, _ := json.Marshal(req.SuitableFor)
	learningOutcomesJSON, _ := json.Marshal(req.LearningOutcomes)

	// Create learning path model
	path := &models.LearningPath{
		ID:               uuid.New(),
		CreatorID:        req.CreatedBy,
		Title:            req.Title,
		Description:      req.Description,
		Goal:             req.LearningGoal,
		GoalCategory:     req.GoalCategory,
		PathType:         models.PathTypeStandard,
		Difficulty:       models.DifficultyLevel(req.DifficultyLevel),
		EstimatedHours:   req.EstimatedHours,
		SuitableFor:      string(suitableForJSON),
		LearningOutcomes: string(learningOutcomesJSON),
		IsPublic:         req.IsPublic,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// Save to database
	if err := s.repos.LearningPath.Create(ctx, path); err != nil {
		logging.Error("Failed to create learning path: %v, path_id: %v", err, path.ID)
		return nil, fmt.Errorf("failed to create learning path: %w", err)
	}

	logging.Info("Learning path created successfully, path_id: %v, title: %s", path.ID, path.Title)
	return path, nil
}

// GenerateLearningPathWithAI generates a learning path using AI
func (s *LearningPathService) GenerateLearningPathWithAI(ctx context.Context, req *GeneratePathRequest) (*models.LearningPath, error) {
	// Validate request
	if err := s.validateGenerateRequest(req); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Get student profile for AI context (commented out to avoid unused variable error)
	// profileData, err := s.getStudentProfileData(ctx, req.UserID)
	// if err != nil {
	//     logging.Error("Failed to get student profile", "user_id", req.UserID, "error", err)
	// }

	// Call AI agent to generate learning path
	aiResponse, err := s.aiManager.GenerateLearningPath(ctx, req.Goal, req.CurrentMap, req.CurrentPath)
	if err != nil {
		logging.Error("AI path generation failed: %v, user_id: %v", err, req.UserID)
		return nil, fmt.Errorf("AI path generation failed: %w", err)
	}

	if !aiResponse.Success {
		logging.Error("AI path generation unsuccessful: %s, user_id: %v", aiResponse.Error, req.UserID)
		return nil, fmt.Errorf("AI path generation failed: %s", aiResponse.Error)
	}

	// Parse AI response to create learning path
	path, err := s.parseAIResponseToPath(aiResponse.Data, req)
	if err != nil {
		logging.Error("Failed to parse AI response: %v, user_id: %v", err, req.UserID)
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	// Save generated path to database
	if err := s.repos.LearningPath.Create(ctx, path); err != nil {
		logging.Error("Failed to save AI-generated path: %v, path_id: %v", err, path.ID)
		return nil, fmt.Errorf("failed to save AI-generated path: %w", err)
	}

	logging.Info("AI-generated learning path created, path_id: %v, user_id: %v, tokens_used: %d", path.ID, req.UserID, aiResponse.TokensUsed)
	return path, nil
}

// UpdateLearningPath updates an existing learning path
func (s *LearningPathService) UpdateLearningPath(ctx context.Context, pathID uuid.UUID, req *UpdateLearningPathRequest) (*models.LearningPath, error) {
	// Get existing path
	existingPath, err := s.repos.LearningPath.GetByID(ctx, pathID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning path: %w", err)
	}

	// Update fields
	if req.Title != nil {
		existingPath.Title = *req.Title
	}
	if req.Description != nil {
		existingPath.Description = *req.Description
	}
	if req.Goal != nil {
		existingPath.Goal = *req.Goal
	}
	if req.GoalCategory != nil {
		existingPath.GoalCategory = *req.GoalCategory
	}
	if req.Difficulty != nil {
		existingPath.Difficulty = models.DifficultyLevel(*req.Difficulty)
	}
	if req.EstimatedHours != nil {
		existingPath.EstimatedHours = *req.EstimatedHours
	}
	if req.SuitableFor != nil {
		suitableForJSON, _ := json.Marshal(req.SuitableFor)
		existingPath.SuitableFor = string(suitableForJSON)
	}
	if req.LearningOutcomes != nil {
		learningOutcomesJSON, _ := json.Marshal(req.LearningOutcomes)
		existingPath.LearningOutcomes = string(learningOutcomesJSON)
	}
	if req.IsPublic != nil {
		existingPath.IsPublic = *req.IsPublic
	}

	existingPath.UpdatedAt = time.Now()

	// Save updates
	if err := s.repos.LearningPath.Update(ctx, existingPath); err != nil {
		logging.Error("Failed to update learning path: %v, path_id: %v", err, pathID)
		return nil, fmt.Errorf("failed to update learning path: %w", err)
	}

	logging.Info("Learning path updated successfully, path_id: %v", pathID)
	return existingPath, nil
}

// DeleteLearningPath soft deletes a learning path
func (s *LearningPathService) DeleteLearningPath(ctx context.Context, pathID uuid.UUID) error {
	// Check if path exists
	_, err := s.repos.LearningPath.GetByID(ctx, pathID)
	if err != nil {
		return fmt.Errorf("learning path not found: %w", err)
	}

	// Use regular delete since repository doesn't have SoftDelete method
	if err := s.repos.LearningPath.Delete(ctx, pathID); err != nil {
		logging.Error("Failed to delete learning path: %v, path_id: %v", err, pathID)
		return fmt.Errorf("failed to delete learning path: %w", err)
	}

	logging.Info("Learning path deleted successfully, path_id: %v", pathID)
	return nil
}

// GetLearningPath retrieves a learning path by ID
func (s *LearningPathService) GetLearningPath(ctx context.Context, pathID uuid.UUID) (*models.LearningPath, error) {
	path, err := s.repos.LearningPath.GetByID(ctx, pathID)
	if err != nil {
		return nil, fmt.Errorf("failed to get learning path: %w", err)
	}
	return path, nil
}

// GetLearningPaths retrieves learning paths with pagination
func (s *LearningPathService) GetLearningPaths(ctx context.Context, req *GetLearningPathsRequest) ([]*models.LearningPath, int64, error) {
	// Set default pagination
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// Calculate offset
	offset := (req.Page - 1) * req.PageSize

	// Get paths
	paths, err := s.repos.LearningPath.List(ctx, req.PageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get learning paths: %w", err)
	}

	// Get total count
	total, err := s.repos.LearningPath.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}

	return paths, total, nil
}

// GetLearningPathsByType retrieves learning paths filtered by type
func (s *LearningPathService) GetLearningPathsByType(ctx context.Context, pathType models.PathType, page, pageSize int) ([]*models.LearningPath, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	paths, err := s.repos.LearningPath.ListByType(ctx, pathType, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get learning paths by type: %w", err)
	}

	total, err := s.repos.LearningPath.CountByType(ctx, pathType)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count by type: %w", err)
	}
	return paths, total, nil
}

// GetLearningPathsByDifficulty retrieves learning paths filtered by difficulty
func (s *LearningPathService) GetLearningPathsByDifficulty(ctx context.Context, difficulty models.DifficultyLevel, page, pageSize int) ([]*models.LearningPath, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	paths, err := s.repos.LearningPath.ListByDifficulty(ctx, difficulty, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get learning paths by difficulty: %w", err)
	}

	total, err := s.repos.LearningPath.Count(ctx) // Using general count as CountByDifficulty may not exist
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}
	return paths, total, nil
}

// GetPublicLearningPaths retrieves public learning paths
func (s *LearningPathService) GetPublicLearningPaths(ctx context.Context, page, pageSize int) ([]*models.LearningPath, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	paths, err := s.repos.LearningPath.GetPublicPaths(ctx, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get public learning paths: %w", err)
	}

	total, err := s.repos.LearningPath.Count(ctx) // Using general count as CountPublicPaths may not exist
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}
	return paths, total, nil
}

// SearchLearningPaths searches learning paths by query
func (s *LearningPathService) SearchLearningPaths(ctx context.Context, query string, page, pageSize int) ([]*models.LearningPath, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	paths, err := s.repos.LearningPath.Search(ctx, query, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to search learning paths: %w", err)
	}

	// Get total count (simplified - using general count)
	total, err := s.repos.LearningPath.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}
	return paths, total, nil
}

// GetLearningPathsByCreator retrieves learning paths by creator
func (s *LearningPathService) GetLearningPathsByCreator(ctx context.Context, creatorID uuid.UUID, page, pageSize int) ([]*models.LearningPath, int64, error) {
	// Set default pagination
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	offset := (page - 1) * pageSize
	paths, err := s.repos.LearningPath.ListByCreator(ctx, creatorID, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get learning paths by creator: %w", err)
	}

	// Get total count (simplified - using general count)
	total, err := s.repos.LearningPath.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get total count: %w", err)
	}
	return paths, total, nil
}

// GetUserMasterPath gets user's master learning path
func (s *LearningPathService) GetUserMasterPath(ctx context.Context, userID uuid.UUID) (*models.UserMasterPath, error) {
	masterPath, err := s.repos.UserMasterPath.GetByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user master path: %w", err)
	}
	return masterPath, nil
}

// CreateUserMasterPath creates a new user master path
func (s *LearningPathService) CreateUserMasterPath(ctx context.Context, userID uuid.UUID, currentGoalTitle string) (*models.UserMasterPath, error) {
	// Check if user already has a master path
	exists, err := s.repos.UserMasterPath.ExistsByUserID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check master path existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("user already has a master path")
	}

	// Create master path record
	now := time.Now()
	masterPath := &models.UserMasterPath{
		ID:               uuid.New(),
		UserID:           userID,
		CurrentGoalTitle: currentGoalTitle,
		TotalPaths:       0,
		CompletedPaths:   0,
		OverallProgress:  0,
		StartedAt:        &now,
		LastAccessedAt:   &now,
		AdaptationCount:  0,
		CreatedAt:        now,
		UpdatedAt:        now,
	}

	if err := s.repos.UserMasterPath.Create(ctx, masterPath); err != nil {
		logging.Error("Failed to create user master path: %v, user_id: %v", err, userID)
		return nil, fmt.Errorf("failed to create user master path: %w", err)
	}

	logging.Info("User master path created, user_id: %v, goal: %s", userID, currentGoalTitle)
	return masterPath, nil
}

// Helper methods

func (s *LearningPathService) validateCreateRequest(req *CreateLearningPathRequest) error {
	if req.Title == "" {
		return fmt.Errorf("title is required")
	}
	if req.CreatedBy == uuid.Nil {
		return fmt.Errorf("created_by is required")
	}
	if req.DifficultyLevel < 1 || req.DifficultyLevel > 10 {
		return fmt.Errorf("difficulty_level must be between 1 and 10")
	}
	return nil
}

func (s *LearningPathService) validateGenerateRequest(req *GeneratePathRequest) error {
	if req.Goal == "" {
		return fmt.Errorf("goal is required")
	}
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user_id is required")
	}
	return nil
}

func (s *LearningPathService) parseAIResponseToPath(aiData map[string]interface{}, req *GeneratePathRequest) (*models.LearningPath, error) {
	path := &models.LearningPath{
		ID:        uuid.New(),
		CreatorID: req.UserID,
		PathType:  models.PathTypePersonalized,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Parse AI response fields
	if title, ok := aiData["title"].(string); ok {
		path.Title = title
	} else {
		path.Title = "AI Generated Learning Path"
	}

	if description, ok := aiData["description"].(string); ok {
		path.Description = description
	}

	if goal, ok := aiData["learning_goal"].(string); ok {
		path.Goal = goal
	} else {
		path.Goal = req.Goal
	}

	if category, ok := aiData["goal_category"].(string); ok {
		path.GoalCategory = category
	}

	if difficulty, ok := aiData["difficulty_level"].(float64); ok {
		path.Difficulty = models.DifficultyLevel(int(difficulty))
	} else {
		path.Difficulty = models.DifficultyIntermediate5 // Default
	}

	if hours, ok := aiData["estimated_hours"].(float64); ok {
		path.EstimatedHours = int(hours)
	}

	if suitableFor, ok := aiData["suitable_for"].([]interface{}); ok {
		suitableForJSON, _ := json.Marshal(suitableFor)
		path.SuitableFor = string(suitableForJSON)
	}

	if outcomes, ok := aiData["learning_outcomes"].([]interface{}); ok {
		outcomesJSON, _ := json.Marshal(outcomes)
		path.LearningOutcomes = string(outcomesJSON)
	}

	// AI-generated paths are initially private
	path.IsPublic = false

	return path, nil
}

// Request/Response types

type CreateLearningPathRequest struct {
	Title            string    `json:"title" validate:"required"`
	Description      string    `json:"description"`
	LearningGoal     string    `json:"learning_goal"`
	GoalCategory     string    `json:"goal_category"`
	DifficultyLevel  int       `json:"difficulty_level" validate:"min=1,max=10"`
	EstimatedHours   int       `json:"estimated_hours"`
	SuitableFor      []string  `json:"suitable_for"`
	LearningOutcomes []string  `json:"learning_outcomes"`
	IsPublic         bool      `json:"is_public"`
	CreatedBy        uuid.UUID `json:"created_by" validate:"required"`
}

type UpdateLearningPathRequest struct {
	Title            *string  `json:"title,omitempty"`
	Description      *string  `json:"description,omitempty"`
	Goal             *string  `json:"goal,omitempty"`
	GoalCategory     *string  `json:"goal_category,omitempty"`
	Difficulty       *int     `json:"difficulty,omitempty"`
	EstimatedHours   *int     `json:"estimated_hours,omitempty"`
	SuitableFor      []string `json:"suitable_for,omitempty"`
	LearningOutcomes []string `json:"learning_outcomes,omitempty"`
	IsPublic         *bool    `json:"is_public,omitempty"`
}

type GeneratePathRequest struct {
	Goal        string    `json:"goal" validate:"required"`
	CurrentMap  string    `json:"current_map"`
	CurrentPath string    `json:"current_path"`
	UserID      uuid.UUID `json:"user_id" validate:"required"`
}

type GetLearningPathsRequest struct {
	Page     int `json:"page"`
	PageSize int `json:"page_size"`
}
