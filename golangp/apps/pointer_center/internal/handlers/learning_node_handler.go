/*
 * @Description: Learning node handlers for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-08-07
 */
package handlers

import (
	"strconv"
	"time"

	"pointer/golangp/apps/pointer_center/internal/models"
	"pointer/golangp/apps/pointer_center/internal/services"
	"pointer/golangp/apps/pointer_center/pkg/response"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// LearningNodeHandler handles learning node-related requests
type LearningNodeHandler struct {
	service *services.LearningNodeService
	logger  *logging.Logger
}

// NewLearningNodeHandler creates a new learning node handler with service
func NewLearningNodeHandler(service *services.LearningNodeService) *LearningNodeHandler {
	logger := logging.GetLogger("learning_node_handler")

	return &LearningNodeHandler{
		service: service,
		logger:  logger,
	}
}

// CreateNodeRequest represents a request to create a learning node
type CreateNodeRequest struct {
	Title          string                 `json:"title" binding:"required"`
	Description    string                 `json:"description"`
	EstimatedHours int                    `json:"estimated_hours"`
	Difficulty     models.DifficultyLevel `json:"difficulty"`
	Prerequisites  string                 `json:"prerequisites"`
}

// UpdateNodeRequest represents a request to update a learning node
type UpdateNodeRequest struct {
	Title          string                 `json:"title"`
	Description    string                 `json:"description"`
	EstimatedHours int                    `json:"estimated_hours"`
	Difficulty     models.DifficultyLevel `json:"difficulty"`
	Prerequisites  string                 `json:"prerequisites"`
	Status         models.NodeStatus      `json:"status"`
}

// CreateNode creates a new learning node
func (h *LearningNodeHandler) CreateNode(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Creating learning node - ClientIP: %s", clientIP)

	var req CreateNodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Convert handler request to service request
	serviceReq := &services.CreateLearningNodeRequest{
		Title:          req.Title,
		Description:    req.Description,
		EstimatedHours: req.EstimatedHours,
		Difficulty:     int(req.Difficulty),
		Prerequisites:  []string{req.Prerequisites}, // Convert string to slice
		CreatedBy:      uuid.Nil,                    // TODO: Get from auth context
	}

	// Set defaults if not provided
	if serviceReq.EstimatedHours == 0 {
		serviceReq.EstimatedHours = 1
	}
	if serviceReq.Difficulty == 0 {
		serviceReq.Difficulty = int(models.DifficultyIntermediate5)
	}

	// Call service to create learning node
	ctx := c.Request.Context()
	node, err := h.service.CreateLearningNode(ctx, serviceReq)
	if err != nil {
		h.logger.Error("❌ Failed to create learning node - Error: %v, Title: %s, ClientIP: %s", err, req.Title, clientIP)
		response.InternalServerError(c, "Failed to create learning node", err)
		return
	}

	h.logger.Info("✅ Learning node created successfully - ID: %s, Title: %s, Duration: %v, ClientIP: %s",
		node.ID, node.Title, time.Since(startTime), clientIP)

	response.Created(c, "Learning node created successfully", node)
}

// GetNode retrieves a learning node by ID
func (h *LearningNodeHandler) GetNode(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	nodeID := c.Param("id")

	h.logger.Info("🔄 Getting learning node - ID: %s, ClientIP: %s", nodeID, clientIP)

	// Parse UUID
	nodeUUID, err := uuid.Parse(nodeID)
	if err != nil {
		h.logger.Warning("❌ Invalid node ID format - ID: %s, ClientIP: %s", nodeID, clientIP)
		response.BadRequest(c, "Invalid node ID format", err)
		return
	}

	ctx := c.Request.Context()
	node, err := h.service.GetLearningNode(ctx, nodeUUID)
	if err != nil {
		h.logger.Warning("❌ Learning node not found - ID: %s, Error: %v, ClientIP: %s", nodeID, err, clientIP)
		response.NotFound(c, "Learning node not found", err)
		return
	}

	h.logger.Info("✅ Learning node retrieved successfully - ID: %s, Duration: %v, ClientIP: %s",
		nodeID, time.Since(startTime), clientIP)

	response.Success(c, "Learning node retrieved successfully", node)
}

// ListNodes retrieves a paginated list of learning nodes
func (h *LearningNodeHandler) ListNodes(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	h.logger.Info("🔄 Listing learning nodes - ClientIP: %s", clientIP)

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// Parse filter parameters
	status := c.Query("status")
	difficulty := c.Query("difficulty")

	// Get nodes based on filters
	var nodes []*models.LearningNode
	var total int64
	var err error

	ctx := c.Request.Context()

	if status != "" {
		nodes, total, err = h.service.GetLearningNodesByStatus(ctx, models.NodeStatus(status), page, pageSize)
	} else if difficulty != "" {
		diffLevel, parseErr := strconv.Atoi(difficulty)
		if parseErr == nil {
			nodes, total, err = h.service.GetLearningNodesByDifficulty(ctx, models.DifficultyLevel(diffLevel), page, pageSize)
		} else {
			h.logger.Warning("❌ Invalid difficulty format - Difficulty: %s, ClientIP: %s", difficulty, clientIP)
			response.BadRequest(c, "Invalid difficulty format", parseErr)
			return
		}
	} else {
		serviceReq := &services.GetLearningNodesRequest{
			Page:     page,
			PageSize: pageSize,
		}
		nodes, total, err = h.service.GetLearningNodes(ctx, serviceReq)
	}

	if err != nil {
		h.logger.Error("❌ Failed to list learning nodes - Error: %v, ClientIP: %s", err, clientIP)
		response.InternalServerError(c, "Failed to list learning nodes", err)
		return
	}

	h.logger.Info("✅ Learning nodes listed successfully - Count: %d, Total: %d, Duration: %v, ClientIP: %s",
		len(nodes), total, time.Since(startTime), clientIP)

	response.Paginated(c, "Learning nodes retrieved successfully", nodes, total, page, pageSize)
}

// UpdateNode updates an existing learning node
func (h *LearningNodeHandler) UpdateNode(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	nodeID := c.Param("id")

	h.logger.Info("🔄 Updating learning node - ID: %s, ClientIP: %s", nodeID, clientIP)

	// Parse UUID
	nodeUUID, err := uuid.Parse(nodeID)
	if err != nil {
		h.logger.Warning("❌ Invalid node ID format - ID: %s, ClientIP: %s", nodeID, clientIP)
		response.BadRequest(c, "Invalid node ID format", err)
		return
	}

	var req UpdateNodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	ctx := c.Request.Context()

	// Convert handler request to service request
	serviceReq := &services.UpdateLearningNodeRequest{}

	// Set fields that are provided
	if req.Title != "" {
		serviceReq.Title = &req.Title
	}
	if req.Description != "" {
		serviceReq.Description = &req.Description
	}
	if req.EstimatedHours > 0 {
		serviceReq.EstimatedHours = &req.EstimatedHours
	}
	if req.Difficulty > 0 {
		difficultyInt := int(req.Difficulty)
		serviceReq.Difficulty = &difficultyInt
	}
	if req.Prerequisites != "" {
		prerequisitesSlice := []string{req.Prerequisites}
		serviceReq.Prerequisites = prerequisitesSlice
	}

	// Call service to update learning node
	updatedNode, err := h.service.UpdateLearningNode(ctx, nodeUUID, serviceReq)
	if err != nil {
		h.logger.Error("❌ Failed to update learning node - Error: %v, ID: %s, ClientIP: %s", err, nodeID, clientIP)
		response.InternalServerError(c, "Failed to update learning node", err)
		return
	}

	h.logger.Info("✅ Learning node updated successfully - ID: %s, Duration: %v, ClientIP: %s",
		nodeID, time.Since(startTime), clientIP)

	response.Success(c, "Learning node updated successfully", updatedNode)
}

// DeleteNode deletes a learning node
func (h *LearningNodeHandler) DeleteNode(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	nodeID := c.Param("id")

	h.logger.Info("🔄 Deleting learning node - ID: %s, ClientIP: %s", nodeID, clientIP)

	// Parse UUID
	nodeUUID, err := uuid.Parse(nodeID)
	if err != nil {
		h.logger.Warning("❌ Invalid node ID format - ID: %s, ClientIP: %s", nodeID, clientIP)
		response.BadRequest(c, "Invalid node ID format", err)
		return
	}

	ctx := c.Request.Context()

	// Call service to delete learning node
	if err := h.service.DeleteLearningNode(ctx, nodeUUID); err != nil {
		h.logger.Error("❌ Failed to delete learning node - Error: %v, ID: %s, ClientIP: %s", err, nodeID, clientIP)
		response.InternalServerError(c, "Failed to delete learning node", err)
		return
	}

	h.logger.Info("✅ Learning node deleted successfully - ID: %s, Duration: %v, ClientIP: %s",
		nodeID, time.Since(startTime), clientIP)

	response.Success(c, "Learning node deleted successfully", nil)
}

// SearchNodes searches for learning nodes based on query parameters
func (h *LearningNodeHandler) SearchNodes(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	query := c.Query("q")

	h.logger.Info("🔄 Searching learning nodes - Query: %s, ClientIP: %s", query, clientIP)

	if query == "" {
		h.logger.Warning("❌ Empty search query - ClientIP: %s", clientIP)
		response.BadRequest(c, "Search query is required", nil)
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	ctx := c.Request.Context()
	nodes, total, err := h.service.SearchLearningNodes(ctx, query, page, pageSize)
	if err != nil {
		h.logger.Error("❌ Failed to search learning nodes - Error: %v, Query: %s, ClientIP: %s", err, query, clientIP)
		response.InternalServerError(c, "Failed to search learning nodes", err)
		return
	}

	h.logger.Info("✅ Learning nodes searched successfully - Query: %s, Count: %d, Total: %d, Duration: %v, ClientIP: %s",
		query, len(nodes), total, time.Since(startTime), clientIP)

	response.Paginated(c, "Learning nodes searched successfully", nodes, total, page, pageSize)
}

// GetNodesByCreator retrieves learning nodes created by a specific user
func (h *LearningNodeHandler) GetNodesByCreator(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	creatorID := c.Param("creator_id")

	h.logger.Info("🔄 Getting learning nodes by creator - CreatorID: %s, ClientIP: %s", creatorID, clientIP)

	// Parse UUID
	creatorUUID, err := uuid.Parse(creatorID)
	if err != nil {
		h.logger.Warning("❌ Invalid creator ID format - ID: %s, ClientIP: %s", creatorID, clientIP)
		response.BadRequest(c, "Invalid creator ID format", err)
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	ctx := c.Request.Context()
	nodes, total, err := h.service.GetLearningNodesByCreator(ctx, creatorUUID, page, pageSize)
	if err != nil {
		h.logger.Error("❌ Failed to get learning nodes by creator - Error: %v, CreatorID: %s, ClientIP: %s", err, creatorID, clientIP)
		response.InternalServerError(c, "Failed to get learning nodes by creator", err)
		return
	}

	h.logger.Info("✅ Learning nodes by creator retrieved successfully - CreatorID: %s, Count: %d, Total: %d, Duration: %v, ClientIP: %s",
		creatorID, len(nodes), total, time.Since(startTime), clientIP)

	response.Paginated(c, "Learning nodes by creator retrieved successfully", nodes, total, page, pageSize)
}
