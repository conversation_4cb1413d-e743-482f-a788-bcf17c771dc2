package handlers

import (
	"errors"
	"fmt"
	"net/http"

	"pointer/golangp/apps/pointer_center/internal/config"
	"pointer/golangp/apps/pointer_center/pkg/response"
	"pointer/golangp/common/payment/alipay"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AlipayHandler 支付宝支付处理器
type AlipayHandler struct {
	alipayService *alipay.Service
	db            *gorm.DB
}

// NewAlipayHandler 创建支付宝处理器
func NewAlipayHandler(cfg *config.Config, db *gorm.DB) (*<PERSON>payHandler, error) {
	return NewAlipayHandlerWithRedis(cfg, db, nil)
}

// NewAlipayHandlerWithRedis 创建支付宝处理器（带 Redis 支持）
func NewAlipayHandlerWithRedis(cfg *config.Config, db *gorm.DB, redisClient interface{}) (*AlipayHandler, error) {
	alipayConfig := cfg.GetAlipayConfig()
	alipayService, err := alipay.NewServiceWithRedis(alipayConfig, db, redisClient)
	if err != nil {
		return nil, fmt.Errorf("failed to create alipay service: %w", err)
	}

	return &AlipayHandler{
		alipayService: alipayService,
		db:            db,
	}, nil
}

// CreateQRCode 创建支付二维码
// @Summary 创建支付二维码
// @Description 生成支付宝扫码支付的二维码链接，携带用户信息
// @Tags 支付宝支付
// @Accept json
// @Produce json
// @Param request body alipay.CreateQRCodeRequest true "创建二维码请求"
// @Success 200 {object} response.Response{data=alipay.CreateQRCodeResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/alipay/qrcode [post]
func (h *AlipayHandler) CreateQRCode(c *gin.Context) {
	var req alipay.CreateQRCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	// 验证用户ID格式
	userUUID, err := alipay.ValidateUserID(req.UserID)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "用户ID格式错误", err)
		return
	}

	// 生成商户订单号
	outTradeNo := alipay.GenerateOutTradeNo("QR")

	// 设置默认过期时间
	expireTime := req.ExpireTime
	if expireTime == 0 {
		expireTime = alipay.GetDefaultExpireTime()
	}

	// 构建回传参数，包含用户信息
	passbackParams := alipay.BuildPassbackParams(req.UserID, req.OrderID, req.ExtraData)

	// 创建支付请求
	// TODO: 将来可以改为使用 alipay.PaymentMethodQR (预下单模式) 替代当前的网站支付二维码模式
	paymentReq := &alipay.CreatePaymentRequest{
		OutTradeNo:     outTradeNo,
		TotalAmount:    req.Amount,
		Subject:        req.Subject,
		Body:           req.Body,
		PaymentMethod:  alipay.PaymentMethodWebQR, // 使用网站支付二维码模式
		TimeoutExpress: expireTime,
		PassbackParams: passbackParams,
	}

	// 创建支付
	paymentResp, err := h.alipayService.CreatePayment(paymentReq)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "创建支付失败", err)
		return
	}

	// 更新支付记录，关联用户ID
	var paymentRecord alipay.PaymentRecord
	if err := h.db.Where("out_trade_no = ?", outTradeNo).First(&paymentRecord).Error; err == nil {
		h.db.Model(&paymentRecord).Updates(map[string]interface{}{
			"user_id": userUUID,
		})
	}

	// 计算过期时间
	expireTimeStr := alipay.FormatExpireTime(expireTime)

	resp := alipay.CreateQRCodeResponse{
		OutTradeNo: paymentResp.OutTradeNo,
		QRCode:     paymentResp.QRCode,
		ExpireTime: expireTimeStr,
	}

	response.Success(c, "二维码创建成功", resp)
}

type CreateWebPaymentResponse struct {
	OutTradeNo string `json:"out_trade_no"` // 商户订单号
	PaymentURL string `json:"payment_url"`  // 支付链接
	ExpireTime string `json:"expire_time"`  // 过期时间
}

// CreateWebPayment 创建网页支付
// @Summary 创建网页支付
// @Description 生成支付宝网页支付链接，用户点击后跳转到支付宝完成支付
// @Tags 支付宝支付
// @Accept json
// @Produce json
// @Param request body alipay.CreateQRCodeRequest true "创建网页支付请求"
// @Success 200 {object} response.Response{data=CreateWebPaymentResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/alipay/web [post]
func (h *AlipayHandler) CreateWebPayment(c *gin.Context) {
	var req alipay.CreateQRCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	// 验证用户ID格式
	userUUID, err := alipay.ValidateUserID(req.UserID)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "用户ID格式错误", err)
		return
	}

	// 生成商户订单号
	outTradeNo := alipay.GenerateOutTradeNo("WEB")

	// 设置默认过期时间
	expireTime := req.ExpireTime
	if expireTime == 0 {
		expireTime = alipay.GetDefaultExpireTime()
	}

	// 构建回传参数，包含用户信息
	passbackParams := alipay.BuildPassbackParams(req.UserID, req.OrderID, req.ExtraData)

	// 创建支付请求
	paymentReq := &alipay.CreatePaymentRequest{
		OutTradeNo:     outTradeNo,
		TotalAmount:    req.Amount,
		Subject:        req.Subject,
		Body:           req.Body,
		PaymentMethod:  alipay.PaymentMethodWeb, // 使用网页支付模式
		TimeoutExpress: expireTime,
		PassbackParams: passbackParams,
	}

	// 创建支付
	paymentResp, err := h.alipayService.CreatePayment(paymentReq)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "创建支付失败", err)
		return
	}

	// 更新支付记录，关联用户ID
	var paymentRecord alipay.PaymentRecord
	if err := h.db.Where("out_trade_no = ?", outTradeNo).First(&paymentRecord).Error; err == nil {
		h.db.Model(&paymentRecord).Updates(map[string]interface{}{
			"user_id": userUUID,
		})
	}

	// 计算过期时间
	expireTimeStr := alipay.FormatExpireTime(expireTime)

	resp := CreateWebPaymentResponse{
		OutTradeNo: paymentResp.OutTradeNo,
		PaymentURL: paymentResp.PaymentData, // 网页支付返回支付链接
		ExpireTime: expireTimeStr,
	}

	response.Success(c, "网页支付创建成功", resp)
}

// 使用 common/alipay 中的轮询响应类型
type PaymentStatusResponse = alipay.PaymentPollingResponse

// QueryPaymentStatus 查询支付状态（轮询接口）
// @Summary 查询支付状态
// @Description 轮询查询支付状态，用于前端定时检查支付结果，返回简化的支付状态信息
// @Tags 支付宝支付
// @Accept json
// @Produce json
// @Param out_trade_no query string true "商户订单号"
// @Success 200 {object} response.Response{data=PaymentStatusResponse} "支付状态信息"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "订单不存在"
// @Router /api/alipay/status [get]
func (h *AlipayHandler) QueryPaymentStatus(c *gin.Context) {
	outTradeNo := c.Query("out_trade_no")
	if err := alipay.ValidateOutTradeNo(outTradeNo); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	// 使用带缓存的查询方法
	ctx := c.Request.Context()
	resp, err := h.alipayService.QueryPaymentWithCache(ctx, outTradeNo)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.Error(c, http.StatusNotFound, "订单不存在", errors.New("未找到对应的支付订单"))
			return
		}
		response.Error(c, http.StatusInternalServerError, "查询失败", err)
		return
	}

	response.Success(c, "查询成功", resp)
}

// PaymentCallback 支付同步回调处理
// @Summary 支付同步回调
// @Description 处理支付宝支付成功后的同步回调，验证支付结果并返回简单文本响应
// @Tags 支付宝支付
// @Accept application/x-www-form-urlencoded
// @Produce plain
// @Param out_trade_no formData string true "商户订单号"
// @Param trade_no formData string false "支付宝交易号"
// @Param total_amount formData string false "订单金额"
// @Success 200 {string} string "支付成功文本"
// @Failure 400 {string} string "错误信息"
// @Router /alipay/callback [post]
func (h *AlipayHandler) PaymentCallback(c *gin.Context) {
	// 解析表单数据（支付宝回调使用 POST 方法）
	c.Request.ParseForm()

	// 验证签名（如果公钥设置成功的话）
	callbackData := alipay.ParseFormToMap(c.Request.Form)

	// 验证回调签名
	if isValid, err := h.alipayService.VerifyCallback(callbackData); err != nil {
		fmt.Printf("⚠️  回调验证签名失败: %v\n", err)
		fmt.Printf("💡 提示：可能是公钥配置问题，继续处理回调（仅用于测试）\n")
		// 在测试环境中，即使签名验证失败也继续处理
	} else if isValid {
		fmt.Printf("✅ 回调验证签名通过\n")
	}

	// 获取订单号
	outTradeNo := c.Request.Form.Get("out_trade_no")
	if err := alipay.ValidateOutTradeNo(outTradeNo); err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}

	// 查询订单状态来验证支付结果
	queryReq := &alipay.QueryPaymentRequest{
		OutTradeNo: outTradeNo,
	}

	queryResp, err := h.alipayService.QueryPayment(queryReq)
	if err != nil {
		fmt.Printf("❌ 验证订单 %s 信息失败: %v\n", outTradeNo, err)
		c.String(http.StatusBadRequest, "验证订单 %s 信息失败: %s", outTradeNo, err.Error())
		return
	}

	// 检查支付状态
	if queryResp.TradeStatus != alipay.PaymentStatusTradeSuccess &&
		queryResp.TradeStatus != alipay.PaymentStatusTradeFinished {
		fmt.Printf("❌ 订单 %s 支付状态异常: %s\n", outTradeNo, queryResp.TradeStatus)
		c.String(http.StatusBadRequest, "订单 %s 支付状态异常: %s", outTradeNo, queryResp.TradeStatus)
		return
	}

	fmt.Printf("✅ 订单 %s 支付成功\n", outTradeNo)
	c.String(http.StatusOK, "🎉 订单 %s 支付成功！", outTradeNo)
}

// PaymentNotify 支付异步通知处理
// @Summary 支付异步通知
// @Description 接收支付宝的异步通知，更新订单状态
// @Tags 支付宝支付
// @Accept application/x-www-form-urlencoded
// @Produce plain
// @Success 200 {string} string "success"
// @Failure 400 {string} string "fail"
// @Router /api/alipay/notify [post]
func (h *AlipayHandler) PaymentNotify(c *gin.Context) {
	// 解析表单数据
	if err := c.Request.ParseForm(); err != nil {
		c.String(http.StatusBadRequest, "fail")
		return
	}

	// 将表单数据转换为map
	notifyData := alipay.ParseFormToMap(c.Request.PostForm)

	// 验证通知签名
	isValid, err := h.alipayService.VerifyNotify(notifyData)
	if err != nil || !isValid {
		fmt.Printf("Alipay notify verification failed: %v\n", err)
		c.String(http.StatusBadRequest, "fail")
		return
	}

	// 处理通知
	if err := h.alipayService.ProcessNotify(notifyData); err != nil {
		fmt.Printf("Process alipay notify failed: %v\n", err)
		c.String(http.StatusInternalServerError, "fail")
		return
	}

	// TODO : 额外的业务逻辑，如支付玩出修改付费表状态

	// 返回成功响应
	c.String(http.StatusOK, "success")
}
