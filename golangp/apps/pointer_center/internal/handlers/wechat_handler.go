package handlers

import (
	"errors"
	"fmt"
	"net/http"

	"pointer/golangp/apps/pointer_center/internal/config"
	"pointer/golangp/apps/pointer_center/pkg/response"
	"pointer/golangp/common/payment/wechat"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// WeChatHandler 微信支付处理器
type WeChatHandler struct {
	wechatService *wechat.Service
	db            *gorm.DB
}

// NewWeChatHandler 创建微信支付处理器
func NewWeChatHandler(cfg *config.Config, db *gorm.DB) (*WeChatHandler, error) {
	return NewWeChatHandlerWithRedis(cfg, db, nil)
}

// NewWeChatHandlerWithRedis 创建微信支付处理器（带 Redis 支持）
func NewWeChatHandlerWithRedis(cfg *config.Config, db *gorm.DB, redisClient interface{}) (*WeChatHandler, error) {
	wechatConfig := cfg.GetWeChatConfig()
	wechatService, err := wechat.NewServiceWithRedis(wechatConfig, db, redisClient)
	if err != nil {
		return nil, fmt.Errorf("failed to create wechat service: %w", err)
	}

	return &WeChatHandler{
		wechatService: wechatService,
		db:            db,
	}, nil
}

// CreateQRCode 创建微信支付二维码
// @Summary 创建微信支付二维码
// @Description 创建微信扫码支付二维码，用于PC端或移动端扫码支付
// @Tags 微信支付
// @Accept json
// @Produce json
// @Param request body wechat.CreateQRCodeRequest true "二维码支付请求参数"
// @Success 200 {object} response.Response{data=wechat.CreateQRCodeResponse} "二维码创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/wechat/qrcode [post]
func (h *WeChatHandler) CreateQRCode(c *gin.Context) {
	var req wechat.CreateQRCodeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	// 验证用户ID
	userUUID, err := wechat.ValidateUserID(req.UserID)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "用户ID无效", err)
		return
	}

	// 生成商户订单号
	outTradeNo := wechat.GenerateOutTradeNo("WX")

	// 设置默认过期时间
	expireTime := req.ExpireTime
	if expireTime == 0 {
		expireTime = wechat.GetDefaultExpireTime()
	}

	// 构建附加数据，包含用户信息
	attach := wechat.BuildAttachData(req.UserID, req.OrderID, req.ExtraData)

	// 创建支付请求
	paymentReq := &wechat.CreatePaymentRequest{
		OutTradeNo:     outTradeNo,
		TotalFee:       req.TotalFee,
		Body:           req.Body,
		Detail:         req.Detail,
		PaymentMethod:  wechat.PaymentMethodNative, // 使用扫码支付
		TimeoutExpress: expireTime,
		Attach:         attach,
		GoodsTag:       req.GoodsTag,
		LimitPay:       req.LimitPay,
	}

	// 创建支付
	paymentResp, err := h.wechatService.CreatePayment(paymentReq)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "创建支付失败", err)
		return
	}

	// 更新支付记录，关联用户ID
	var paymentRecord wechat.PaymentRecord
	if err := h.db.Where("out_trade_no = ?", outTradeNo).First(&paymentRecord).Error; err == nil {
		h.db.Model(&paymentRecord).Updates(map[string]interface{}{
			"user_id": userUUID,
		})
	}

	// 计算过期时间
	expireTimeStr := wechat.FormatExpireTime(expireTime)

	resp := wechat.CreateQRCodeResponse{
		OutTradeNo: paymentResp.OutTradeNo,
		QRCode:     paymentResp.CodeURL,
		ExpireTime: expireTimeStr,
	}

	response.Success(c, "二维码创建成功", resp)
}

// 使用 common/wechat 中的类型定义
type CreateJSAPIRequest = wechat.CreateJSAPIRequest
type CreateJSAPIResponse = wechat.CreateJSAPIResponse

// CreateJSAPI 创建微信JSAPI支付
// @Summary 创建微信JSAPI支付
// @Description 创建微信公众号内支付，用于在微信内H5页面调起支付
// @Tags 微信支付
// @Accept json
// @Produce json
// @Param request body CreateJSAPIRequest true "JSAPI支付请求参数"
// @Success 200 {object} response.Response{data=CreateJSAPIResponse} "JSAPI支付创建成功"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 500 {object} response.Response "服务器错误"
// @Router /api/wechat/jsapi [post]
func (h *WeChatHandler) CreateJSAPI(c *gin.Context) {
	var req CreateJSAPIRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	// 验证用户ID
	userUUID, err := wechat.ValidateUserID(req.UserID)
	if err != nil {
		response.Error(c, http.StatusBadRequest, "用户ID无效", err)
		return
	}

	// 生成商户订单号
	outTradeNo := wechat.GenerateOutTradeNo("JSAPI")

	// 设置默认过期时间
	expireTime := req.ExpireTime
	if expireTime == 0 {
		expireTime = wechat.GetDefaultExpireTime()
	}

	// 构建附加数据，包含用户信息
	attach := wechat.BuildAttachData(req.UserID, req.OrderID, req.ExtraData)

	// 创建支付请求
	paymentReq := &wechat.CreatePaymentRequest{
		OutTradeNo:     outTradeNo,
		TotalFee:       req.TotalFee,
		Body:           req.Body,
		Detail:         req.Detail,
		PaymentMethod:  wechat.PaymentMethodJSAPI, // 使用JSAPI支付
		OpenID:         req.OpenID,                // JSAPI支付必需
		TimeoutExpress: expireTime,
		Attach:         attach,
	}

	// 创建支付
	paymentResp, err := h.wechatService.CreatePayment(paymentReq)
	if err != nil {
		response.Error(c, http.StatusInternalServerError, "创建支付失败", err)
		return
	}

	// 更新支付记录，关联用户ID
	var paymentRecord wechat.PaymentRecord
	if err := h.db.Where("out_trade_no = ?", outTradeNo).First(&paymentRecord).Error; err == nil {
		h.db.Model(&paymentRecord).Updates(map[string]interface{}{
			"user_id": userUUID,
		})
	}

	// 计算过期时间
	expireTimeStr := wechat.FormatExpireTime(expireTime)

	resp := CreateJSAPIResponse{
		OutTradeNo:  paymentResp.OutTradeNo,
		PaymentData: paymentResp.PaymentData, // JSAPI支付返回支付配置
		ExpireTime:  expireTimeStr,
	}

	response.Success(c, "JSAPI支付创建成功", resp)
}

// 使用 common/wechat 中的轮询响应类型
// type PaymentStatusResponse = wechat.PaymentPollingResponse // 已在上面定义

// QueryPaymentStatus 查询支付状态（轮询接口）
// @Summary 查询支付状态
// @Description 轮询查询支付状态，用于前端定时检查支付结果，返回简化的支付状态信息
// @Tags 微信支付
// @Accept json
// @Produce json
// @Param out_trade_no query string true "商户订单号"
// @Success 200 {object} response.Response{data=PaymentStatusResponse} "支付状态信息"
// @Failure 400 {object} response.Response "参数错误"
// @Failure 404 {object} response.Response "订单不存在"
// @Router /api/wechat/status [get]
func (h *WeChatHandler) QueryPaymentStatus(c *gin.Context) {
	outTradeNo := c.Query("out_trade_no")
	if err := wechat.ValidateOutTradeNo(outTradeNo); err != nil {
		response.Error(c, http.StatusBadRequest, "参数错误", err)
		return
	}

	// 使用带缓存的查询方法
	ctx := c.Request.Context()
	pollingResp, err := h.wechatService.QueryPaymentWithCache(ctx, outTradeNo)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.Error(c, http.StatusNotFound, "订单不存在", err)
		} else {
			response.Error(c, http.StatusInternalServerError, "查询支付状态失败", err)
		}
		return
	}

	response.Success(c, "查询成功", pollingResp)
}

// PaymentCallback 支付同步回调处理
// @Summary 支付同步回调
// @Description 处理微信支付成功后的同步回调，验证支付结果并返回简单文本响应
// @Tags 微信支付
// @Accept application/x-www-form-urlencoded
// @Produce plain
// @Param out_trade_no formData string true "商户订单号"
// @Param transaction_id formData string false "微信支付订单号"
// @Param total_fee formData string false "订单金额"
// @Success 200 {string} string "支付成功文本"
// @Failure 400 {string} string "错误信息"
// @Router /wechat/callback [post]
func (h *WeChatHandler) PaymentCallback(c *gin.Context) {
	// 解析表单数据（微信回调使用 POST 方法）
	c.Request.ParseForm()

	// 验证签名（如果配置正确的话）
	callbackData := wechat.ParseFormToMap(c.Request.Form)

	// 验证回调签名
	if isValid, err := h.wechatService.VerifyCallback(callbackData); err != nil {
		fmt.Printf("⚠️  回调验证签名失败: %v\n", err)
		fmt.Printf("💡 提示：可能是配置问题，继续处理回调（仅用于测试）\n")
		// 在测试环境中，即使签名验证失败也继续处理
	} else if isValid {
		fmt.Printf("✅ 回调验证签名通过\n")
	}

	// 获取订单号
	outTradeNo := c.Request.Form.Get("out_trade_no")
	if err := wechat.ValidateOutTradeNo(outTradeNo); err != nil {
		c.String(http.StatusBadRequest, err.Error())
		return
	}

	// 查询订单状态来验证支付结果
	queryReq := &wechat.QueryPaymentRequest{
		OutTradeNo: outTradeNo,
	}

	queryResp, err := h.wechatService.QueryPayment(queryReq)
	if err != nil {
		fmt.Printf("❌ 验证订单 %s 信息失败: %v\n", outTradeNo, err)
		c.String(http.StatusBadRequest, "验证订单 %s 信息失败: %s", outTradeNo, err.Error())
		return
	}

	// 检查支付状态
	if queryResp.TradeState == wechat.PaymentStatusSuccess {
		fmt.Printf("✅ 订单 %s 支付成功\n", outTradeNo)
		c.String(http.StatusOK, "支付成功！订单号：%s，微信支付订单号：%s", outTradeNo, queryResp.TransactionID)
	} else {
		fmt.Printf("⚠️  订单 %s 支付状态：%s\n", outTradeNo, queryResp.TradeState)
		c.String(http.StatusOK, "支付状态：%s", queryResp.TradeStateDesc)
	}
}

// PaymentNotify 支付异步通知处理
// @Summary 支付异步通知
// @Description 接收微信的异步通知，更新订单状态
// @Tags 微信支付
// @Accept application/x-www-form-urlencoded
// @Produce plain
// @Success 200 {string} string "success"
// @Failure 400 {string} string "fail"
// @Router /api/wechat/notify [post]
func (h *WeChatHandler) PaymentNotify(c *gin.Context) {
	// 解析表单数据
	if err := c.Request.ParseForm(); err != nil {
		c.String(http.StatusBadRequest, "fail")
		return
	}

	// 将表单数据转换为map
	notifyData := wechat.ParseFormToMap(c.Request.PostForm)

	// 验证通知签名
	isValid, err := h.wechatService.VerifyNotify(notifyData)
	if err != nil || !isValid {
		fmt.Printf("WeChat notify verification failed: %v\n", err)
		c.String(http.StatusBadRequest, "fail")
		return
	}

	// 处理通知
	if err := h.wechatService.ProcessNotify(notifyData); err != nil {
		fmt.Printf("Process wechat notify failed: %v\n", err)
		c.String(http.StatusInternalServerError, "fail")
		return
	}

	// TODO: 额外的业务逻辑，如支付完成修改付费表状态

	// 返回成功响应
	c.String(http.StatusOK, "success")
}
