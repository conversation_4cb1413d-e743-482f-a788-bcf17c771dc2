/*
 * @Description: Tech competency handlers for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-08-07
 */
package handlers

import (
	"context"
	"strconv"
	"time"

	"pointer/golangp/apps/pointer_center/internal/models/student_profile"
	"pointer/golangp/apps/pointer_center/internal/services"
	"pointer/golangp/apps/pointer_center/pkg/response"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// TechCompetencyHandler handles tech competency-related requests
type TechCompetencyHandler struct {
	service *services.TechCompetencyService
	logger  *logging.Logger
}

// NewTechCompetencyHandler creates a new tech competency handler with service
func NewTechCompetencyHandler(service *services.TechCompetencyService) *TechCompetencyHandler {
	logger := logging.GetLogger("tech_competency_handler")

	return &TechCompetencyHandler{
		service: service,
		logger:  logger,
	}
}

// CreateCompetencyRequest represents a request to create a tech competency graph
type CreateCompetencyRequest struct {
	UserID           string                           `json:"user_id" binding:"required"`
	Nodes            []student_profile.CompetencyNode `json:"nodes"`
	OverallScore     string                           `json:"overall_score"`
	StrengthAreas    []string                         `json:"strength_areas"`
	ImprovementAreas []string                         `json:"improvement_areas"`
}

// UpdateCompetencyRequest represents a request to update a tech competency graph
type UpdateCompetencyRequest struct {
	Nodes            []student_profile.CompetencyNode `json:"nodes"`
	OverallScore     string                           `json:"overall_score"`
	StrengthAreas    []string                         `json:"strength_areas"`
	ImprovementAreas []string                         `json:"improvement_areas"`
}

// CreateCompetencyGraph creates a new tech competency graph
func (h *TechCompetencyHandler) CreateCompetencyGraph(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Creating tech competency graph - ClientIP: %s", clientIP)

	var req CreateCompetencyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Check if competency graph already exists for this user
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	exists, err := h.repo.ExistsByUserID(ctx, req.UserID)
	if err != nil {
		h.logger.Error("❌ Failed to check existing competency graph - Error: %v, UserID: %s, ClientIP: %s", err, req.UserID, clientIP)
		response.InternalServerError(c, "Failed to check existing competency graph", err)
		return
	}

	if exists {
		h.logger.Warning("❌ Competency graph already exists - UserID: %s, ClientIP: %s", req.UserID, clientIP)
		response.Conflict(c, "Competency graph already exists for this user", nil)
		return
	}

	// Create new competency graph
	graph := &student_profile.TechCompetencyGraph{
		UserID:           req.UserID,
		ProfileVersion:   1,
		Nodes:            req.Nodes,
		OverallScore:     req.OverallScore,
		StrengthAreas:    req.StrengthAreas,
		ImprovementAreas: req.ImprovementAreas,
		LastUpdated:      time.Now(),
	}

	// Set defaults if not provided
	if graph.Nodes == nil {
		graph.Nodes = make([]student_profile.CompetencyNode, 0)
	}
	if graph.StrengthAreas == nil {
		graph.StrengthAreas = make([]string, 0)
	}
	if graph.ImprovementAreas == nil {
		graph.ImprovementAreas = make([]string, 0)
	}
	if graph.OverallScore == "" {
		graph.OverallScore = "初级"
	}

	// Save to database
	if err := h.repo.Create(ctx, graph); err != nil {
		h.logger.Error("❌ Failed to create competency graph - Error: %v, UserID: %s, ClientIP: %s", err, req.UserID, clientIP)
		response.InternalServerError(c, "Failed to create competency graph", err)
		return
	}

	h.logger.Info("✅ Competency graph created successfully - UserID: %s, Duration: %v, ClientIP: %s",
		req.UserID, time.Since(startTime), clientIP)

	response.Created(c, "Competency graph created successfully", graph)
}

// GetCompetencyGraph retrieves a tech competency graph by user ID
func (h *TechCompetencyHandler) GetCompetencyGraph(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.Param("user_id")

	h.logger.Info("🔄 Getting competency graph - UserID: %s, ClientIP: %s", userID, clientIP)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	graph, err := h.repo.GetByUserID(ctx, userID)
	if err != nil {
		h.logger.Warning("❌ Competency graph not found - UserID: %s, Error: %v, ClientIP: %s", userID, err, clientIP)
		response.NotFound(c, "Competency graph not found", err)
		return
	}

	h.logger.Info("✅ Competency graph retrieved successfully - UserID: %s, Duration: %v, ClientIP: %s",
		userID, time.Since(startTime), clientIP)

	response.Success(c, "Competency graph retrieved successfully", graph)
}

// GetCompetencyGraphByVersion retrieves a specific version of a tech competency graph
func (h *TechCompetencyHandler) GetCompetencyGraphByVersion(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.Param("user_id")
	versionStr := c.Param("version")

	h.logger.Info("🔄 Getting competency graph by version - UserID: %s, Version: %s, ClientIP: %s", userID, versionStr, clientIP)

	version, err := strconv.Atoi(versionStr)
	if err != nil {
		h.logger.Warning("❌ Invalid version format - Version: %s, ClientIP: %s", versionStr, clientIP)
		response.BadRequest(c, "Invalid version format", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	graph, err := h.repo.GetByUserAndVersion(ctx, userID, version)
	if err != nil {
		h.logger.Warning("❌ Competency graph not found - UserID: %s, Version: %d, Error: %v, ClientIP: %s", userID, version, err, clientIP)
		response.NotFound(c, "Competency graph not found", err)
		return
	}

	h.logger.Info("✅ Competency graph retrieved successfully - UserID: %s, Version: %d, Duration: %v, ClientIP: %s",
		userID, version, time.Since(startTime), clientIP)

	response.Success(c, "Competency graph retrieved successfully", graph)
}

// UpdateCompetencyGraph updates an existing tech competency graph
func (h *TechCompetencyHandler) UpdateCompetencyGraph(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.Param("user_id")

	h.logger.Info("🔄 Updating competency graph - UserID: %s, ClientIP: %s", userID, clientIP)

	var req UpdateCompetencyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Check if competency graph exists
	existingGraph, err := h.repo.GetByUserID(ctx, userID)
	if err != nil {
		h.logger.Warning("❌ Competency graph not found - UserID: %s, Error: %v, ClientIP: %s", userID, err, clientIP)
		response.NotFound(c, "Competency graph not found", err)
		return
	}

	// Update graph fields
	if req.Nodes != nil {
		existingGraph.Nodes = req.Nodes
	}
	if req.OverallScore != "" {
		existingGraph.OverallScore = req.OverallScore
	}
	if req.StrengthAreas != nil {
		existingGraph.StrengthAreas = req.StrengthAreas
	}
	if req.ImprovementAreas != nil {
		existingGraph.ImprovementAreas = req.ImprovementAreas
	}

	existingGraph.LastUpdated = time.Now()
	existingGraph.ProfileVersion++

	// Save to database
	if err := h.repo.Update(ctx, existingGraph); err != nil {
		h.logger.Error("❌ Failed to update competency graph - Error: %v, UserID: %s, ClientIP: %s", err, userID, clientIP)
		response.InternalServerError(c, "Failed to update competency graph", err)
		return
	}

	h.logger.Info("✅ Competency graph updated successfully - UserID: %s, Duration: %v, ClientIP: %s",
		userID, time.Since(startTime), clientIP)

	response.Success(c, "Competency graph updated successfully", existingGraph)
}

// DeleteCompetencyGraph deletes a tech competency graph
func (h *TechCompetencyHandler) DeleteCompetencyGraph(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.Param("user_id")

	h.logger.Info("🔄 Deleting competency graph - UserID: %s, ClientIP: %s", userID, clientIP)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Check if competency graph exists
	_, err := h.repo.GetByUserID(ctx, userID)
	if err != nil {
		h.logger.Warning("❌ Competency graph not found - UserID: %s, Error: %v, ClientIP: %s", userID, err, clientIP)
		response.NotFound(c, "Competency graph not found", err)
		return
	}

	// Delete competency graph
	objectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		h.logger.Error("❌ Invalid userID format - UserID: %s, ClientIP: %s", userID, clientIP)
		response.BadRequest(c, "Invalid userID format", err)
		return
	}
	if err := h.repo.Delete(ctx, objectID); err != nil {
		h.logger.Error("❌ Failed to delete competency graph - Error: %v, UserID: %s, ClientIP: %s", err, userID, clientIP)
		response.InternalServerError(c, "Failed to delete competency graph", err)
		return
	}

	h.logger.Info("✅ Competency graph deleted successfully - UserID: %s, Duration: %v, ClientIP: %s",
		userID, time.Since(startTime), clientIP)

	response.Success(c, "Competency graph deleted successfully", nil)
}

// GetCompetencyStats retrieves competency statistics for a user
func (h *TechCompetencyHandler) GetCompetencyStats(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.Param("user_id")

	h.logger.Info("🔄 Getting competency stats - UserID: %s, ClientIP: %s", userID, clientIP)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	stats, err := h.repo.GetUserCompetencyStats(ctx, userID)
	if err != nil {
		h.logger.Error("❌ Failed to get competency stats - Error: %v, UserID: %s, ClientIP: %s", err, userID, clientIP)
		response.InternalServerError(c, "Failed to get competency stats", err)
		return
	}

	h.logger.Info("✅ Competency stats retrieved successfully - UserID: %s, Duration: %v, ClientIP: %s",
		userID, time.Since(startTime), clientIP)

	response.Success(c, "Competency stats retrieved successfully", stats)
}

// ListCompetencyGraphs retrieves a paginated list of competency graphs
func (h *TechCompetencyHandler) ListCompetencyGraphs(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	h.logger.Info("🔄 Listing competency graphs - ClientIP: %s", clientIP)

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// Calculate offset
	offset := (page - 1) * pageSize

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	graphs, err := h.repo.List(ctx, pageSize, offset)
	if err != nil {
		h.logger.Error("❌ Failed to list competency graphs - Error: %v, ClientIP: %s", err, clientIP)
		response.InternalServerError(c, "Failed to list competency graphs", err)
		return
	}

	// Get total count
	total, err := h.repo.Count(ctx)
	if err != nil {
		h.logger.Error("❌ Failed to count competency graphs - Error: %v, ClientIP: %s", err, clientIP)
		response.InternalServerError(c, "Failed to count competency graphs", err)
		return
	}

	h.logger.Info("✅ Competency graphs listed successfully - Count: %d, Total: %d, Duration: %v, ClientIP: %s",
		len(graphs), total, time.Since(startTime), clientIP)

	response.Paginated(c, "Competency graphs retrieved successfully", graphs, total, page, pageSize)
}

// GetVersionHistory retrieves version history for a user's competency graph
func (h *TechCompetencyHandler) GetVersionHistory(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.Param("user_id")

	h.logger.Info("🔄 Getting competency graph version history - UserID: %s, ClientIP: %s", userID, clientIP)

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Get latest version for the user (simplified implementation)
	graph, err := h.repo.GetByUserID(ctx, userID)
	if err != nil {
		h.logger.Error("❌ Failed to get competency graph - Error: %v, UserID: %s, ClientIP: %s", err, userID, clientIP)
		response.InternalServerError(c, "Failed to get competency graph", err)
		return
	}

	// Return as a single-item list for now (can be extended later)
	versions := []*student_profile.TechCompetencyGraph{graph}
	total := int64(1)

	h.logger.Info("✅ Version history retrieved successfully - UserID: %s, Count: %d, Total: %d, Duration: %v, ClientIP: %s",
		userID, len(versions), total, time.Since(startTime), clientIP)

	response.Paginated(c, "Version history retrieved successfully", versions, total, page, pageSize)
}

// CreateEmptyCompetencyGraph creates an empty competency graph for a new user
func (h *TechCompetencyHandler) CreateEmptyCompetencyGraph(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.Param("user_id")

	h.logger.Info("🔄 Creating empty competency graph - UserID: %s, ClientIP: %s", userID, clientIP)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Check if competency graph already exists for this user
	exists, err := h.repo.ExistsByUserID(ctx, userID)
	if err != nil {
		h.logger.Error("❌ Failed to check existing competency graph - Error: %v, UserID: %s, ClientIP: %s", err, userID, clientIP)
		response.InternalServerError(c, "Failed to check existing competency graph", err)
		return
	}

	if exists {
		h.logger.Warning("❌ Competency graph already exists - UserID: %s, ClientIP: %s", userID, clientIP)
		response.Conflict(c, "Competency graph already exists for this user", nil)
		return
	}

	// Create empty competency graph
	graph := &student_profile.TechCompetencyGraph{
		UserID:           userID,
		ProfileVersion:   1,
		Nodes:            make([]student_profile.CompetencyNode, 0),
		OverallScore:     "初级",
		StrengthAreas:    make([]string, 0),
		ImprovementAreas: make([]string, 0),
		LastUpdated:      time.Now(),
	}

	// Save to database
	if err := h.repo.Create(ctx, graph); err != nil {
		h.logger.Error("❌ Failed to create empty competency graph - Error: %v, UserID: %s, ClientIP: %s", err, userID, clientIP)
		response.InternalServerError(c, "Failed to create empty competency graph", err)
		return
	}

	h.logger.Info("✅ Empty competency graph created successfully - UserID: %s, Duration: %v, ClientIP: %s",
		userID, time.Since(startTime), clientIP)

	response.Created(c, "Empty competency graph created successfully", graph)
}
