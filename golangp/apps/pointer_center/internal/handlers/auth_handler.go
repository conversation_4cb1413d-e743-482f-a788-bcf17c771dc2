/*
 * @Description: Authentication handlers for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package handlers

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"pointer/golangp/apps/pointer_center/internal/config"
	"pointer/golangp/apps/pointer_center/internal/models"
	"pointer/golangp/apps/pointer_center/internal/models/student_profile"
	"pointer/golangp/apps/pointer_center/internal/repositories"
	"pointer/golangp/apps/pointer_center/pkg/response"
	"pointer/golangp/common/auth/googleauth"
	"pointer/golangp/common/logging"
	"pointer/golangp/common/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/mongo"
	"gorm.io/gorm"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	db                 *gorm.DB
	mongoDB            *mongo.Database
	googleService      *googleauth.Service
	logger             *logging.Logger
	techCompetencyRepo repositories.TechCompetencyRepository
	staticProfileRepo  repositories.StaticProfileRepository
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(db *gorm.DB, mongoDB *mongo.Database, googleClientID, googleClientSecret string) *AuthHandler {
	// Mask sensitive information for logging
	maskedClientID := googleClientID
	if len(googleClientID) > 10 {
		maskedClientID = googleClientID[:10] + "..."
	}
	maskedClientSecret := googleClientSecret
	if len(googleClientSecret) > 10 {
		maskedClientSecret = googleClientSecret[:10] + "..."
	}

	logger := logging.GetLogger("auth_handler")
	logger.Info("🔧 Initializing Google OAuth - ClientID: %s, ClientSecret: %s", maskedClientID, maskedClientSecret)

	googleConfig := googleauth.NewConfig(googleClientID, googleClientSecret)
	googleService := googleauth.NewService(googleConfig)

	// Initialize repositories
	techCompetencyRepo := repositories.NewTechCompetencyRepository(mongoDB)
	staticProfileRepo := repositories.NewStaticProfileRepository(db)

	return &AuthHandler{
		db:                 db,
		mongoDB:            mongoDB,
		googleService:      googleService,
		logger:             logger,
		techCompetencyRepo: techCompetencyRepo,
		staticProfileRepo:  staticProfileRepo,
	}
}

// LoginRequest represents the unified login request
type LoginRequest struct {
	// For normal login
	Email    string `json:"email"`
	Password string `json:"password"`

	// For Google login with ID Token (frontend flow)
	IDToken string `json:"id_token"`

	// For Google login with authorization code (server flow)
	AuthCode    string `json:"auth_code"`
	RedirectURI string `json:"redirect_uri"`
}

// RegisterRequest represents a user registration request
type RegisterRequest struct {
	Username  string `json:"username" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=8"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

// LoginResponse represents the login response
type LoginResponse struct {
	User         *models.User `json:"user"`
	AccessToken  string       `json:"access_token"`
	RefreshToken string       `json:"refresh_token"`
	ExpiresAt    time.Time    `json:"expires_at"`
	IsNewUser    bool         `json:"is_new_user"`
	LoginTime    time.Time    `json:"login_time"`
	Message      string       `json:"message"`
}

// HandleRegister handles user registration
func (h *AuthHandler) HandleRegister(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Starting user registration - ClientIP: %s", clientIP)

	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid registration request - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request format", err)
		return
	}

	// Validate email format
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	if !emailRegex.MatchString(req.Email) {
		h.logger.Warning("❌ Invalid email format - Email: %s, ClientIP: %s", req.Email, clientIP)
		response.BadRequest(c, "Invalid email format", nil)
		return
	}

	// Check if user already exists
	var existingUser models.User
	if err := h.db.Where("email = ? OR username = ?", req.Email, req.Username).First(&existingUser).Error; err == nil {
		h.logger.Warning("❌ User already exists - Email: %s, Username: %s, ClientIP: %s", req.Email, req.Username, clientIP)
		response.Conflict(c, "User with this email or username already exists", nil)
		return
	}

	// Hash password
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		h.logger.Error("❌ Failed to hash password - Error: %v, ClientIP: %s", err, clientIP)
		response.InternalServerError(c, "Failed to process password", err)
		return
	}

	// Create new user
	newUser := &models.User{
		ID:        uuid.New(),
		Username:  req.Username,
		Email:     req.Email,
		Password:  hashedPassword,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Role:      models.UserRoleUser,
		Status:    models.UserStatusActive,
		Origin:    "local",
	}

	// Start database transaction for atomic operations
	tx := h.db.Begin()
	if tx.Error != nil {
		h.logger.Error("❌ Failed to start transaction - Error: %v, Email: %s, ClientIP: %s", tx.Error, req.Email, clientIP)
		response.InternalServerError(c, "Failed to start transaction", tx.Error)
		return
	}

	// Create user within transaction
	if err := tx.Create(newUser).Error; err != nil {
		tx.Rollback()
		h.logger.Error("❌ Failed to create user - Error: %v, Email: %s, ClientIP: %s", err, req.Email, clientIP)
		response.InternalServerError(c, "Failed to create user", err)
		return
	}

	h.logger.Info("✅ User created successfully - UserID: %s, Email: %s, Duration: %v, ClientIP: %s",
		newUser.ID, newUser.Email, time.Since(startTime), clientIP)

	// Create empty static profile
	staticProfile := &student_profile.StaticProfile{
		UserID:              newUser.ID,
		Age:                 0,
		Gender:              "",
		PreferredLanguage:   "zh-CN",
		EducationExperience: "",
		Major:               "",
		GraduationYear:      0,
		CurrentRole:         "",
		Industry:            "",
		WorkExperience:      0,
		LearningStyle:       "visual",
		StudyTimePerWeek:    10,
		PreferredStudyTime:  "",
		LearningPace:        "moderate",
	}

	ctx := context.Background()
	if err := h.staticProfileRepo.Create(ctx, staticProfile); err != nil {
		tx.Rollback()
		h.logger.Error("❌ Failed to create static profile - Error: %v, UserID: %s, ClientIP: %s", err, newUser.ID, clientIP)
		response.InternalServerError(c, "Failed to create user profile", err)
		return
	}

	h.logger.Info("✅ Static profile created successfully - UserID: %s, ClientIP: %s", newUser.ID, clientIP)

	// Create empty tech competency graph
	competencyGraph := &student_profile.TechCompetencyGraph{
		UserID:           newUser.ID.String(),
		ProfileVersion:   1,
		Nodes:            make([]student_profile.CompetencyNode, 0),
		OverallScore:     "初级",
		StrengthAreas:    make([]string, 0),
		ImprovementAreas: make([]string, 0),
		LastUpdated:      time.Now(),
	}

	if err := h.techCompetencyRepo.Create(ctx, competencyGraph); err != nil {
		tx.Rollback()
		h.logger.Error("❌ Failed to create competency graph - Error: %v, UserID: %s, ClientIP: %s", err, newUser.ID, clientIP)
		response.InternalServerError(c, "Failed to create competency graph", err)
		return
	}

	h.logger.Info("✅ Competency graph created successfully - UserID: %s, ClientIP: %s", newUser.ID, clientIP)

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		h.logger.Error("❌ Failed to commit transaction - Error: %v, UserID: %s, ClientIP: %s", err, newUser.ID, clientIP)
		response.InternalServerError(c, "Failed to complete user registration", err)
		return
	}

	h.logger.Info("✅ User registration completed successfully - UserID: %s, Email: %s, Duration: %v, ClientIP: %s",
		newUser.ID, newUser.Email, time.Since(startTime), clientIP)

	// Generate tokens
	accessToken, refreshToken, accessExpiresAt, _, err := h.generateTokens(newUser)
	if err != nil {
		h.logger.Error("❌ Failed to generate tokens - Error: %v, UserID: %s, ClientIP: %s", err, newUser.ID, clientIP)
		response.InternalServerError(c, "Failed to generate authentication tokens", err)
		return
	}

	// Create session record
	session := &models.UserSession{
		ID:        uuid.New(),
		UserID:    newUser.ID,
		Token:     accessToken,
		ExpiresAt: accessExpiresAt,
		IPAddress: clientIP,
		UserAgent: c.GetHeader("User-Agent"),
		IsActive:  true,
	}

	if err := h.db.Create(session).Error; err != nil {
		h.logger.Error("❌ Failed to create session - Error: %v, UserID: %s, ClientIP: %s", err, newUser.ID, clientIP)
		response.InternalServerError(c, "Failed to create session", err)
		return
	}

	// Set HTTP cookies
	cfg := config.Load()
	c.SetCookie("access_token", accessToken, cfg.AccessTokenMaxAge*60, "/", cfg.Domain, false, true)
	c.SetCookie("refresh_token", refreshToken, cfg.RefreshTokenMaxAge*60, "/", cfg.Domain, false, true)

	response.Success(c, "Registration successful", LoginResponse{
		User:         newUser,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    accessExpiresAt,
		IsNewUser:    true,
		LoginTime:    time.Now(),
		Message:      "Registration successful",
	})
}

// HandleLogin handles unified login (normal + Google)
func (h *AuthHandler) HandleLogin(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Starting login process - ClientIP: %s", clientIP)

	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid login request - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request format", err)
		return
	}

	// Check if this is a Google login or normal login
	if req.IDToken != "" {
		// Google login with ID Token (frontend flow)
		h.logger.Info("🔄 Processing Google ID token login - Email: %s, ClientIP: %s", req.Email, clientIP)
		h.handleGoogleLogin(c, req.IDToken)
		h.logger.Info("✅ HandleLogin completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	} else if req.AuthCode != "" && req.RedirectURI != "" {
		// Google login with authorization code (server flow)
		h.logger.Info("🔄 Processing Google auth code login - Email: %s, ClientIP: %s", req.Email, clientIP)
		h.handleGoogleAuthCode(c, req.AuthCode, req.RedirectURI)
		h.logger.Info("✅ HandleLogin completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	} else if req.Email != "" && req.Password != "" {
		// Normal login
		h.logger.Info("🔄 Processing normal email/password login - Email: %s, ClientIP: %s", req.Email, clientIP)
		h.handleNormalLogin(c, req.Email, req.Password)
		h.logger.Info("✅ HandleLogin completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	} else {
		h.logger.Warning("❌ Invalid login request - Missing required fields, ClientIP: %s", clientIP)
		response.BadRequest(c, "Either ID token, auth code with redirect URI, or email/password must be provided", nil)
		h.logger.Info("❌ HandleLogin failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}
}

// generateTokens generates both access and refresh tokens for a user
func (h *AuthHandler) generateTokens(user *models.User) (string, string, time.Time, time.Time, error) {
	accessToken, accessExpiresAt, err := h.generateAccessToken(user)
	if err != nil {
		return "", "", time.Time{}, time.Time{}, fmt.Errorf("failed to generate access token: %w", err)
	}

	refreshToken, refreshExpiresAt, err := h.generateRefreshToken(user)
	if err != nil {
		return "", "", time.Time{}, time.Time{}, fmt.Errorf("failed to generate refresh token: %w", err)
	}

	return accessToken, refreshToken, accessExpiresAt, refreshExpiresAt, nil
}

// generateAccessToken generates a JWT access token for the user
func (h *AuthHandler) generateAccessToken(user *models.User) (string, time.Time, error) {
	cfg := config.Load()

	// Set token expiry
	expiresAt := time.Now().Add(time.Duration(cfg.AccessTokenMaxAge) * time.Minute)
	ttl := time.Duration(cfg.AccessTokenMaxAge) * time.Minute

	// Create token payload
	payload := map[string]interface{}{
		"user_id": user.ID.String(),
		"email":   user.Email,
		"role":    string(user.Role),
		"type":    "access",
	}

	// Generate JWT token using common utils
	token, err := utils.CreateToken(ttl, payload, cfg.AccessTokenPrivateKey)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to create access token: %w", err)
	}

	return token, expiresAt, nil
}

// generateRefreshToken generates a JWT refresh token for the user
func (h *AuthHandler) generateRefreshToken(user *models.User) (string, time.Time, error) {
	cfg := config.Load()

	// Set token expiry
	expiresAt := time.Now().Add(time.Duration(cfg.RefreshTokenMaxAge) * time.Minute)
	ttl := time.Duration(cfg.RefreshTokenMaxAge) * time.Minute

	// Create token payload
	payload := map[string]interface{}{
		"user_id": user.ID.String(),
		"email":   user.Email,
		"role":    string(user.Role),
		"type":    "refresh",
	}

	// Generate JWT token using common utils
	token, err := utils.CreateToken(ttl, payload, cfg.RefreshTokenPrivateKey)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to create refresh token: %w", err)
	}

	return token, expiresAt, nil
}

// handleGoogleAuthCode handles Google OAuth login with authorization code
func (h *AuthHandler) handleGoogleAuthCode(c *gin.Context, authCode string, redirectURI string) {
	ctx := context.Background()

	// Mask auth code for logging
	maskedCode := authCode
	if len(authCode) > 10 {
		maskedCode = authCode[:10] + "..."
	}
	h.logger.Info("🔄 Exchanging Google auth code - Code: %s, RedirectURI: %s", maskedCode, redirectURI)

	// Exchange authorization code for ID token
	idToken, err := h.googleService.ExchangeAuthCode(ctx, authCode, redirectURI)
	if err != nil {
		h.logger.Error("❌ Failed to exchange auth code: %v", err)
		response.Error(c, http.StatusUnauthorized, "Failed to exchange authorization code", err)
		return
	}

	h.logger.Info("✅ Successfully exchanged auth code for ID token")

	// Now that we have the ID token, proceed with normal Google login flow
	h.handleGoogleLogin(c, idToken)
}

// handleGoogleLogin handles Google OAuth login with ID token
func (h *AuthHandler) handleGoogleLogin(c *gin.Context, idToken string) {
	ctx := context.Background()

	// Verify Google ID Token
	googleUser, err := h.googleService.VerifyIDToken(ctx, idToken)
	if err != nil {
		response.Error(c, http.StatusUnauthorized, "Invalid Google ID token", err)
		return
	}

	// Check if user exists
	var existingUser models.User
	err = h.db.Where("email = ?", googleUser.Email).First(&existingUser).Error
	isNewUser := false

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// User doesn't exist, create new user
		newUser := &models.User{
			ID:        uuid.New(),
			Username:  generateUsernameFromEmail(googleUser.Email),
			Email:     googleUser.Email,
			FirstName: googleUser.GivenName,
			LastName:  googleUser.FamilyName,
			Role:      models.UserRoleUser,
			Status:    models.UserStatusActive,
			Origin:    "google",
			LastLogin: &time.Time{},
		}

		// Set current time as last login
		now := time.Now()
		newUser.LastLogin = &now

		if err := h.db.Create(newUser).Error; err != nil {
			response.InternalServerError(c, "Failed to create user", err)
			return
		}

		existingUser = *newUser
		isNewUser = true
		h.logger.Info("✅ Created new Google user - UserID: %s, Email: %s", existingUser.ID, existingUser.Email)
	} else if err != nil {
		response.InternalServerError(c, "Database error", err)
		return
	} else {
		// Update existing user's last login
		now := time.Now()
		existingUser.LastLogin = &now
		if err := h.db.Save(&existingUser).Error; err != nil {
			response.InternalServerError(c, "Failed to update user", err)
			return
		}
		h.logger.Info("✅ Existing Google user login - UserID: %s, Email: %s", existingUser.ID, existingUser.Email)
	}

	// Generate tokens
	accessToken, refreshToken, accessExpiresAt, _, err := h.generateTokens(&existingUser)
	if err != nil {
		response.InternalServerError(c, "Failed to generate tokens", err)
		return
	}

	// Create session record
	session := &models.UserSession{
		ID:        uuid.New(),
		UserID:    existingUser.ID,
		Token:     accessToken, // Store access token in session
		ExpiresAt: accessExpiresAt,
		IPAddress: c.ClientIP(),
		UserAgent: c.GetHeader("User-Agent"),
		IsActive:  true,
	}

	if err := h.db.Create(session).Error; err != nil {
		response.InternalServerError(c, "Failed to create session", err)
		return
	}

	// Set HTTP cookies
	cfg := config.Load()
	c.SetCookie("access_token", accessToken, cfg.AccessTokenMaxAge*60, "/", cfg.Domain, false, true)
	c.SetCookie("refresh_token", refreshToken, cfg.RefreshTokenMaxAge*60, "/", cfg.Domain, false, true)

	// Return success response
	message := "Login successful"
	if isNewUser {
		message = "Account created and login successful"
	}

	response.Success(c, message, LoginResponse{
		User:         &existingUser,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    accessExpiresAt,
		IsNewUser:    isNewUser,
		LoginTime:    time.Now(),
		Message:      message,
	})
}

// handleNormalLogin handles normal login with email and password
func (h *AuthHandler) handleNormalLogin(c *gin.Context, email, password string) {
	// Get user by email
	var user models.User
	if err := h.db.Where("email = ?", email).First(&user).Error; err != nil {
		h.logger.Warning("❌ User not found - Email: %s, ClientIP: %s", email, c.ClientIP())
		response.Unauthorized(c, "User not found", err)
		return
	}

	// Verify password
	if err := utils.VerifyPassword(user.Password, password); err != nil {
		h.logger.Warning("❌ Password verification failed - Email: %s, ClientIP: %s", email, c.ClientIP())
		response.Unauthorized(c, "Password incorrect", err)
		return
	}

	// Update last login
	now := time.Now()
	user.LastLogin = &now
	if err := h.db.Save(&user).Error; err != nil {
		response.InternalServerError(c, "Failed to update user", err)
		return
	}

	// Generate tokens
	accessToken, refreshToken, accessExpiresAt, _, err := h.generateTokens(&user)
	if err != nil {
		response.InternalServerError(c, "Failed to generate tokens", err)
		return
	}

	// Create session record
	session := &models.UserSession{
		ID:        uuid.New(),
		UserID:    user.ID,
		Token:     accessToken, // Store access token in session
		ExpiresAt: accessExpiresAt,
		IPAddress: c.ClientIP(),
		UserAgent: c.GetHeader("User-Agent"),
		IsActive:  true,
	}

	if err := h.db.Create(session).Error; err != nil {
		response.InternalServerError(c, "Failed to create session", err)
		return
	}

	// Set HTTP cookies
	cfg := config.Load()
	c.SetCookie("access_token", accessToken, cfg.AccessTokenMaxAge*60, "/", cfg.Domain, false, true)
	c.SetCookie("refresh_token", refreshToken, cfg.RefreshTokenMaxAge*60, "/", cfg.Domain, false, true)

	response.Success(c, "Login successful", LoginResponse{
		User:         &user,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    accessExpiresAt,
		IsNewUser:    false,
		LoginTime:    time.Now(),
		Message:      "Login successful",
	})
}

// HandleLogout handles user logout
func (h *AuthHandler) HandleLogout(c *gin.Context) {
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Processing logout - ClientIP: %s", clientIP)

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warning("❌ User ID not found in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		h.logger.Error("❌ Invalid user ID format - UserID: %s, ClientIP: %s", userID, clientIP)
		response.BadRequest(c, "Invalid user ID", err)
		return
	}

	// Deactivate all user sessions
	if err := h.db.Model(&models.UserSession{}).Where("user_id = ?", userUUID).Update("is_active", false).Error; err != nil {
		h.logger.Error("❌ Failed to deactivate sessions - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.InternalServerError(c, "Failed to logout", err)
		return
	}

	// Clear cookies
	cfg := config.Load()
	c.SetCookie("access_token", "", -1, "/", cfg.Domain, false, true)
	c.SetCookie("refresh_token", "", -1, "/", cfg.Domain, false, true)

	h.logger.Info("✅ User logged out successfully - UserID: %s, ClientIP: %s", userUUID, clientIP)
	response.Success(c, "Logout successful", nil)
}

// RefreshToken handles token refresh
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Processing token refresh - ClientIP: %s", clientIP)

	// Get refresh token from request
	var req struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid refresh token request - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request format", err)
		return
	}

	// Validate refresh token
	cfg := config.Load()
	payload, err := utils.ValidateToken(req.RefreshToken, cfg.RefreshTokenPublicKey)
	if err != nil {
		h.logger.Warning("❌ Invalid refresh token - Error: %v, ClientIP: %s", err, clientIP)
		response.Unauthorized(c, "Invalid refresh token", err)
		return
	}

	// Extract user information from token payload
	payloadMap, ok := payload.(map[string]interface{})
	if !ok {
		response.Unauthorized(c, "Invalid token payload", nil)
		return
	}

	userID, ok := payloadMap["user_id"].(string)
	if !ok {
		response.Unauthorized(c, "Invalid user ID in token", nil)
		return
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		response.Unauthorized(c, "Invalid user ID format", err)
		return
	}

	// Get user from database
	var user models.User
	if err := h.db.First(&user, "id = ?", userUUID).Error; err != nil {
		response.Unauthorized(c, "User not found", err)
		return
	}

	// Generate new tokens
	newAccessToken, newRefreshToken, accessExpiresAt, _, err := h.generateTokens(&user)
	if err != nil {
		response.InternalServerError(c, "Failed to generate new tokens", err)
		return
	}

	// Update session with new access token
	if err := h.db.Model(&models.UserSession{}).Where("user_id = ? AND is_active = ?", userUUID, true).Update("token", newAccessToken).Error; err != nil {
		h.logger.Warning("❌ Failed to update session - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
	}

	// Set new cookies
	c.SetCookie("access_token", newAccessToken, cfg.AccessTokenMaxAge*60, "/", cfg.Domain, false, true)
	c.SetCookie("refresh_token", newRefreshToken, cfg.RefreshTokenMaxAge*60, "/", cfg.Domain, false, true)

	h.logger.Info("✅ Token refreshed successfully - UserID: %s, ClientIP: %s", userUUID, clientIP)
	response.Success(c, "Token refreshed successfully", gin.H{
		"access_token":  newAccessToken,
		"refresh_token": newRefreshToken,
		"expires_at":    accessExpiresAt,
	})
}

// GetCurrentUser returns current user information
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	clientIP := c.ClientIP()

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warning("❌ User ID not found in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		h.logger.Error("❌ Invalid user ID format - UserID: %s, ClientIP: %s", userID, clientIP)
		response.BadRequest(c, "Invalid user ID", err)
		return
	}

	// Get user from database
	var user models.User
	if err := h.db.First(&user, "id = ?", userUUID).Error; err != nil {
		h.logger.Error("❌ User not found - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.NotFound(c, "User not found", err)
		return
	}

	h.logger.Info("✅ Current user retrieved - UserID: %s, Email: %s, ClientIP: %s", user.ID, user.Email, clientIP)
	response.Success(c, "User information retrieved successfully", user)
}

// UserUpdateProfileRequest represents a user profile update request
type UserUpdateProfileRequest struct {
	Username  string `json:"username"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Bio       string `json:"bio"`
	Phone     string `json:"phone"`
	Company   string `json:"company"`
	Country   string `json:"country"`
}

// UpdateProfile updates user profile information
func (h *AuthHandler) UpdateProfile(c *gin.Context) {
	clientIP := c.ClientIP()

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warning("❌ User ID not found in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		h.logger.Error("❌ Invalid user ID format - UserID: %s, ClientIP: %s", userID, clientIP)
		response.BadRequest(c, "Invalid user ID", err)
		return
	}

	var req UserUpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid profile update request - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request format", err)
		return
	}

	// Get current user
	var user models.User
	if err := h.db.First(&user, "id = ?", userUUID).Error; err != nil {
		h.logger.Error("❌ User not found - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.NotFound(c, "User not found", err)
		return
	}

	// Check if username is being changed and if it's already taken
	if req.Username != "" && req.Username != user.Username {
		var existingUser models.User
		if err := h.db.Where("username = ? AND id != ?", req.Username, userUUID).First(&existingUser).Error; err == nil {
			h.logger.Warning("❌ Username already taken - Username: %s, UserID: %s, ClientIP: %s", req.Username, userUUID, clientIP)
			response.Conflict(c, "Username already taken", nil)
			return
		}
	}

	// Update user fields
	if req.Username != "" {
		user.Username = req.Username
	}
	if req.FirstName != "" {
		user.FirstName = req.FirstName
	}
	if req.LastName != "" {
		user.LastName = req.LastName
	}
	if req.Bio != "" {
		user.Bio = req.Bio
	}
	if req.Phone != "" {
		user.Phone = req.Phone
	}
	if req.Company != "" {
		user.Company = req.Company
	}
	if req.Country != "" {
		user.Country = req.Country
	}

	// Save updated user
	if err := h.db.Save(&user).Error; err != nil {
		h.logger.Error("❌ Failed to update user profile - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.InternalServerError(c, "Failed to update profile", err)
		return
	}

	h.logger.Info("✅ User profile updated successfully - UserID: %s, ClientIP: %s", userUUID, clientIP)
	response.Success(c, "Profile updated successfully", user)
}

// ChangePasswordRequest represents a password change request
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=8"`
}

// ChangePassword handles password change
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	clientIP := c.ClientIP()

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warning("❌ User ID not found in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		h.logger.Error("❌ Invalid user ID format - UserID: %s, ClientIP: %s", userID, clientIP)
		response.BadRequest(c, "Invalid user ID", err)
		return
	}

	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid password change request - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request format", err)
		return
	}

	// Get current user
	var user models.User
	if err := h.db.First(&user, "id = ?", userUUID).Error; err != nil {
		h.logger.Error("❌ User not found - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.NotFound(c, "User not found", err)
		return
	}

	// Verify current password
	if err := utils.VerifyPassword(user.Password, req.CurrentPassword); err != nil {
		h.logger.Warning("❌ Current password verification failed - UserID: %s, ClientIP: %s", userUUID, clientIP)
		response.Unauthorized(c, "Current password is incorrect", err)
		return
	}

	// Hash new password
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		h.logger.Error("❌ Failed to hash new password - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.InternalServerError(c, "Failed to process new password", err)
		return
	}

	// Update password
	user.Password = hashedPassword
	if err := h.db.Save(&user).Error; err != nil {
		h.logger.Error("❌ Failed to update password - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.InternalServerError(c, "Failed to update password", err)
		return
	}

	// Invalidate all existing sessions for security
	if err := h.db.Model(&models.UserSession{}).Where("user_id = ?", userUUID).Update("is_active", false).Error; err != nil {
		h.logger.Warning("❌ Failed to invalidate sessions after password change - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
	}

	h.logger.Info("✅ Password changed successfully - UserID: %s, ClientIP: %s", userUUID, clientIP)
	response.Success(c, "Password changed successfully", nil)
}

// UploadAvatar handles avatar upload
func (h *AuthHandler) UploadAvatar(c *gin.Context) {
	clientIP := c.ClientIP()

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warning("❌ User ID not found in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		h.logger.Error("❌ Invalid user ID format - UserID: %s, ClientIP: %s", userID, clientIP)
		response.BadRequest(c, "Invalid user ID", err)
		return
	}

	// Get uploaded file
	file, err := c.FormFile("avatar")
	if err != nil {
		h.logger.Warning("❌ No avatar file provided - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.BadRequest(c, "No avatar file provided", err)
		return
	}

	// Validate file size (e.g., max 5MB)
	maxSize := int64(5 * 1024 * 1024) // 5MB
	if file.Size > maxSize {
		h.logger.Warning("❌ Avatar file too large - UserID: %s, Size: %d, ClientIP: %s", userUUID, file.Size, clientIP)
		response.BadRequest(c, "Avatar file too large (max 5MB)", nil)
		return
	}

	// Validate file type
	allowedTypes := []string{"image/jpeg", "image/jpg", "image/png", "image/webp"}
	fileHeader, err := file.Open()
	if err != nil {
		h.logger.Error("❌ Failed to open uploaded file - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.InternalServerError(c, "Failed to process uploaded file", err)
		return
	}
	defer fileHeader.Close()

	// Read first 512 bytes to detect content type
	buffer := make([]byte, 512)
	_, err = fileHeader.Read(buffer)
	if err != nil {
		h.logger.Error("❌ Failed to read file content - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.InternalServerError(c, "Failed to read file content", err)
		return
	}

	contentType := http.DetectContentType(buffer)
	isValidType := false
	for _, allowedType := range allowedTypes {
		if contentType == allowedType {
			isValidType = true
			break
		}
	}

	if !isValidType {
		h.logger.Warning("❌ Invalid avatar file type - UserID: %s, ContentType: %s, ClientIP: %s", userUUID, contentType, clientIP)
		response.BadRequest(c, "Invalid file type. Only JPEG, PNG, and WebP are allowed", nil)
		return
	}

	// TODO: Implement actual file upload to storage service (S3, MinIO, etc.)
	// For now, we'll just simulate the upload and return a placeholder URL
	avatarURL := fmt.Sprintf("/uploads/avatars/%s_%d.jpg", userUUID, time.Now().Unix())

	// Update user avatar in database
	if err := h.db.Model(&models.User{}).Where("id = ?", userUUID).Update("avatar", avatarURL).Error; err != nil {
		h.logger.Error("❌ Failed to update user avatar - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.InternalServerError(c, "Failed to update avatar", err)
		return
	}

	h.logger.Info("✅ Avatar uploaded successfully - UserID: %s, URL: %s, ClientIP: %s", userUUID, avatarURL, clientIP)
	response.Success(c, "Avatar uploaded successfully", gin.H{
		"avatar_url": avatarURL,
	})
}

// RequestEmailChangeRequest represents an email change request
type RequestEmailChangeRequest struct {
	NewEmail string `json:"new_email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// RequestEmailChange handles email change request
func (h *AuthHandler) RequestEmailChange(c *gin.Context) {
	clientIP := c.ClientIP()

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warning("❌ User ID not found in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "User not authenticated", nil)
		return
	}

	userUUID, err := uuid.Parse(userID.(string))
	if err != nil {
		h.logger.Error("❌ Invalid user ID format - UserID: %s, ClientIP: %s", userID, clientIP)
		response.BadRequest(c, "Invalid user ID", err)
		return
	}

	var req RequestEmailChangeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("❌ Invalid email change request - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request format", err)
		return
	}

	// Get current user
	var user models.User
	if err := h.db.First(&user, "id = ?", userUUID).Error; err != nil {
		h.logger.Error("❌ User not found - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.NotFound(c, "User not found", err)
		return
	}

	// Verify password
	if err := utils.VerifyPassword(user.Password, req.Password); err != nil {
		h.logger.Warning("❌ Password verification failed for email change - UserID: %s, ClientIP: %s", userUUID, clientIP)
		response.Unauthorized(c, "Password is incorrect", err)
		return
	}

	// Check if new email is already in use
	var existingUser models.User
	if err := h.db.Where("email = ?", req.NewEmail).First(&existingUser).Error; err == nil {
		h.logger.Warning("❌ Email already in use - Email: %s, UserID: %s, ClientIP: %s", req.NewEmail, userUUID, clientIP)
		response.Conflict(c, "Email address is already in use", nil)
		return
	}

	// Generate email verification token
	verificationToken := utils.GenerateSecureToken(32)

	// Store pending email change
	user.PendingEmail = req.NewEmail
	user.EmailVerificationToken = verificationToken
	if err := h.db.Save(&user).Error; err != nil {
		h.logger.Error("❌ Failed to save pending email change - UserID: %s, Error: %v, ClientIP: %s", userUUID, err, clientIP)
		response.InternalServerError(c, "Failed to process email change request", err)
		return
	}

	// TODO: Send verification email to new email address
	// For now, we'll just return the token for testing purposes
	h.logger.Info("✅ Email change requested - UserID: %s, NewEmail: %s, ClientIP: %s", userUUID, req.NewEmail, clientIP)
	response.Success(c, "Email change verification sent", gin.H{
		"message": "Please check your new email address for verification instructions",
		"token":   verificationToken, // Remove this in production
	})
}

// VerifyEmailChange handles email change verification
func (h *AuthHandler) VerifyEmailChange(c *gin.Context) {
	clientIP := c.ClientIP()
	token := c.Query("token")

	if token == "" {
		h.logger.Warning("❌ No verification token provided - ClientIP: %s", clientIP)
		response.BadRequest(c, "Verification token is required", nil)
		return
	}

	// Find user by verification token
	var user models.User
	if err := h.db.Where("email_verification_token = ? AND pending_email != ''", token).First(&user).Error; err != nil {
		h.logger.Warning("❌ Invalid or expired verification token - Token: %s, ClientIP: %s", token, clientIP)
		response.BadRequest(c, "Invalid or expired verification token", err)
		return
	}

	// Update email address
	user.Email = user.PendingEmail
	user.PendingEmail = ""
	user.EmailVerificationToken = ""
	user.EmailVerified = true

	if err := h.db.Save(&user).Error; err != nil {
		h.logger.Error("❌ Failed to update email address - UserID: %s, Error: %v, ClientIP: %s", user.ID, err, clientIP)
		response.InternalServerError(c, "Failed to update email address", err)
		return
	}

	h.logger.Info("✅ Email address updated successfully - UserID: %s, NewEmail: %s, ClientIP: %s", user.ID, user.Email, clientIP)
	response.Success(c, "Email address updated successfully", gin.H{
		"new_email": user.Email,
	})
}

// generateUsernameFromEmail generates a username from email
func generateUsernameFromEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) > 0 {
		return parts[0]
	}
	return email
}
