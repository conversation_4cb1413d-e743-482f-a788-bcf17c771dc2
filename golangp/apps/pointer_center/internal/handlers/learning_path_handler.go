/*
 * @Description: Learning path handlers for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-08-07
 */
package handlers

import (
	"strconv"
	"time"

	"pointer/golangp/apps/pointer_center/internal/models"
	"pointer/golangp/apps/pointer_center/internal/services"
	"pointer/golangp/apps/pointer_center/pkg/response"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// LearningPathHandler handles learning path-related requests
type LearningPathHandler struct {
	service *services.LearningPathService
	logger  *logging.Logger
}

// NewLearningPathHandler creates a new learning path handler with service
func NewLearningPathHandler(service *services.LearningPathService) *LearningPathHandler {
	logger := logging.GetLogger("learning_path_handler")

	return &LearningPathHandler{
		service: service,
		logger:  logger,
	}
}

// CreatePathRequest represents a request to create a learning path
type CreatePathRequest struct {
	Title            string                 `json:"title" binding:"required"`
	Description      string                 `json:"description"`
	PathType         models.PathType        `json:"path_type" binding:"required"`
	Difficulty       models.DifficultyLevel `json:"difficulty"`
	EstimatedHours   int                    `json:"estimated_hours"`
	SuitableFor      string                 `json:"suitable_for"`
	LearningOutcomes string                 `json:"learning_outcomes"`
	IsPublic         bool                   `json:"is_public"`
}

// UpdatePathRequest represents a request to update a learning path
type UpdatePathRequest struct {
	Title            string                 `json:"title"`
	Description      string                 `json:"description"`
	PathType         models.PathType        `json:"path_type"`
	Difficulty       models.DifficultyLevel `json:"difficulty"`
	EstimatedHours   int                    `json:"estimated_hours"`
	SuitableFor      string                 `json:"suitable_for"`
	LearningOutcomes string                 `json:"learning_outcomes"`
	IsPublic         bool                   `json:"is_public"`
}

// CreatePath creates a new learning path
func (h *LearningPathHandler) CreatePath(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Creating learning path - ClientIP: %s", clientIP)

	var req CreatePathRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Convert handler request to service request
	serviceReq := &services.CreateLearningPathRequest{
		Title:            req.Title,
		Description:      req.Description,
		LearningGoal:     req.Title, // Using title as goal for now
		GoalCategory:     "general", // Default category
		DifficultyLevel:  int(req.Difficulty),
		EstimatedHours:   req.EstimatedHours,
		SuitableFor:      []string{req.SuitableFor},      // Convert string to slice
		LearningOutcomes: []string{req.LearningOutcomes}, // Convert string to slice
		IsPublic:         req.IsPublic,
		CreatedBy:        uuid.Nil, // TODO: Get from auth context
	}

	// Set defaults if not provided
	if serviceReq.DifficultyLevel == 0 {
		serviceReq.DifficultyLevel = int(models.DifficultyIntermediate5)
	}
	if serviceReq.EstimatedHours == 0 {
		serviceReq.EstimatedHours = 10
	}

	// Call service to create learning path
	ctx := c.Request.Context()
	path, err := h.service.CreateLearningPath(ctx, serviceReq)
	if err != nil {
		h.logger.Error("❌ Failed to create learning path - Error: %v, Title: %s, ClientIP: %s", err, req.Title, clientIP)
		response.InternalServerError(c, "Failed to create learning path", err)
		return
	}

	h.logger.Info("✅ Learning path created successfully - ID: %s, Title: %s, Duration: %v, ClientIP: %s",
		path.ID, path.Title, time.Since(startTime), clientIP)

	response.Created(c, "Learning path created successfully", path)
}

// GetPath retrieves a learning path by ID
func (h *LearningPathHandler) GetPath(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	pathID := c.Param("id")

	h.logger.Info("🔄 Getting learning path - ID: %s, ClientIP: %s", pathID, clientIP)

	// Parse UUID
	pathUUID, err := uuid.Parse(pathID)
	if err != nil {
		h.logger.Warning("❌ Invalid path ID format - ID: %s, ClientIP: %s", pathID, clientIP)
		response.BadRequest(c, "Invalid path ID format", err)
		return
	}

	ctx := c.Request.Context()
	path, err := h.service.GetLearningPath(ctx, pathUUID)
	if err != nil {
		h.logger.Warning("❌ Learning path not found - ID: %s, Error: %v, ClientIP: %s", pathID, err, clientIP)
		response.NotFound(c, "Learning path not found", err)
		return
	}

	h.logger.Info("✅ Learning path retrieved successfully - ID: %s, Duration: %v, ClientIP: %s",
		pathID, time.Since(startTime), clientIP)

	response.Success(c, "Learning path retrieved successfully", path)
}

// ListPaths retrieves a paginated list of learning paths
func (h *LearningPathHandler) ListPaths(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	h.logger.Info("🔄 Listing learning paths - ClientIP: %s", clientIP)

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// Parse filter parameters
	pathType := c.Query("type")
	difficulty := c.Query("difficulty")
	isPublic := c.Query("is_public")

	// Get paths based on filters
	var paths []*models.LearningPath
	var total int64
	var err error

	ctx := c.Request.Context()

	if pathType != "" {
		paths, total, err = h.service.GetLearningPathsByType(ctx, models.PathType(pathType), page, pageSize)
	} else if difficulty != "" {
		diffLevel, parseErr := strconv.Atoi(difficulty)
		if parseErr == nil {
			paths, total, err = h.service.GetLearningPathsByDifficulty(ctx, models.DifficultyLevel(diffLevel), page, pageSize)
		} else {
			h.logger.Warning("❌ Invalid difficulty format - Difficulty: %s, ClientIP: %s", difficulty, clientIP)
			response.BadRequest(c, "Invalid difficulty format", parseErr)
			return
		}
	} else if isPublic == "true" {
		paths, total, err = h.service.GetPublicLearningPaths(ctx, page, pageSize)
	} else {
		serviceReq := &services.GetLearningPathsRequest{
			Page:     page,
			PageSize: pageSize,
		}
		paths, total, err = h.service.GetLearningPaths(ctx, serviceReq)
	}

	if err != nil {
		h.logger.Error("❌ Failed to list learning paths - Error: %v, ClientIP: %s", err, clientIP)
		response.InternalServerError(c, "Failed to list learning paths", err)
		return
	}

	h.logger.Info("✅ Learning paths listed successfully - Count: %d, Total: %d, Duration: %v, ClientIP: %s",
		len(paths), total, time.Since(startTime), clientIP)

	response.Paginated(c, "Learning paths retrieved successfully", paths, total, page, pageSize)
}

// UpdatePath updates an existing learning path
func (h *LearningPathHandler) UpdatePath(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	pathID := c.Param("id")

	h.logger.Info("🔄 Updating learning path - ID: %s, ClientIP: %s", pathID, clientIP)

	// Parse UUID
	pathUUID, err := uuid.Parse(pathID)
	if err != nil {
		h.logger.Warning("❌ Invalid path ID format - ID: %s, ClientIP: %s", pathID, clientIP)
		response.BadRequest(c, "Invalid path ID format", err)
		return
	}

	var req UpdatePathRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	ctx := c.Request.Context()

	// Convert handler request to service request
	serviceReq := &services.UpdateLearningPathRequest{}

	// Set fields that are provided
	if req.Title != "" {
		serviceReq.Title = &req.Title
	}
	if req.Description != "" {
		serviceReq.Description = &req.Description
	}
	if req.PathType != "" {
		// Convert PathType to string for service layer
		pathTypeStr := string(req.PathType)
		serviceReq.Goal = &pathTypeStr // Using Goal field for PathType
	}
	if req.Difficulty > 0 {
		difficultyInt := int(req.Difficulty)
		serviceReq.Difficulty = &difficultyInt
	}
	if req.EstimatedHours > 0 {
		serviceReq.EstimatedHours = &req.EstimatedHours
	}
	if req.SuitableFor != "" {
		suitableForSlice := []string{req.SuitableFor}
		serviceReq.SuitableFor = suitableForSlice
	}
	if req.LearningOutcomes != "" {
		outcomesSlice := []string{req.LearningOutcomes}
		serviceReq.LearningOutcomes = outcomesSlice
	}
	serviceReq.IsPublic = &req.IsPublic

	// Call service to update learning path
	updatedPath, err := h.service.UpdateLearningPath(ctx, pathUUID, serviceReq)
	if err != nil {
		h.logger.Error("❌ Failed to update learning path - Error: %v, ID: %s, ClientIP: %s", err, pathID, clientIP)
		response.InternalServerError(c, "Failed to update learning path", err)
		return
	}

	h.logger.Info("✅ Learning path updated successfully - ID: %s, Duration: %v, ClientIP: %s",
		pathID, time.Since(startTime), clientIP)

	response.Success(c, "Learning path updated successfully", updatedPath)
}

// DeletePath deletes a learning path
func (h *LearningPathHandler) DeletePath(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	pathID := c.Param("id")

	h.logger.Info("🔄 Deleting learning path - ID: %s, ClientIP: %s", pathID, clientIP)

	// Parse UUID
	pathUUID, err := uuid.Parse(pathID)
	if err != nil {
		h.logger.Warning("❌ Invalid path ID format - ID: %s, ClientIP: %s", pathID, clientIP)
		response.BadRequest(c, "Invalid path ID format", err)
		return
	}

	ctx := c.Request.Context()

	// Call service to delete learning path
	if err := h.service.DeleteLearningPath(ctx, pathUUID); err != nil {
		h.logger.Error("❌ Failed to delete learning path - Error: %v, ID: %s, ClientIP: %s", err, pathID, clientIP)
		response.InternalServerError(c, "Failed to delete learning path", err)
		return
	}

	h.logger.Info("✅ Learning path deleted successfully - ID: %s, Duration: %v, ClientIP: %s",
		pathID, time.Since(startTime), clientIP)

	response.Success(c, "Learning path deleted successfully", nil)
}

// SearchPaths searches for learning paths based on query parameters
func (h *LearningPathHandler) SearchPaths(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	query := c.Query("q")

	h.logger.Info("🔄 Searching learning paths - Query: %s, ClientIP: %s", query, clientIP)

	if query == "" {
		h.logger.Warning("❌ Empty search query - ClientIP: %s", clientIP)
		response.BadRequest(c, "Search query is required", nil)
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	ctx := c.Request.Context()
	paths, total, err := h.service.SearchLearningPaths(ctx, query, page, pageSize)
	if err != nil {
		h.logger.Error("❌ Failed to search learning paths - Error: %v, Query: %s, ClientIP: %s", err, query, clientIP)
		response.InternalServerError(c, "Failed to search learning paths", err)
		return
	}

	h.logger.Info("✅ Learning paths searched successfully - Query: %s, Count: %d, Total: %d, Duration: %v, ClientIP: %s",
		query, len(paths), total, time.Since(startTime), clientIP)

	response.Paginated(c, "Learning paths searched successfully", paths, total, page, pageSize)
}

// GetPathsByCreator retrieves learning paths created by a specific user
func (h *LearningPathHandler) GetPathsByCreator(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	creatorID := c.Param("creator_id")

	h.logger.Info("🔄 Getting learning paths by creator - CreatorID: %s, ClientIP: %s", creatorID, clientIP)

	// Parse UUID
	creatorUUID, err := uuid.Parse(creatorID)
	if err != nil {
		h.logger.Warning("❌ Invalid creator ID format - ID: %s, ClientIP: %s", creatorID, clientIP)
		response.BadRequest(c, "Invalid creator ID format", err)
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	ctx := c.Request.Context()
	paths, total, err := h.service.GetLearningPathsByCreator(ctx, creatorUUID, page, pageSize)
	if err != nil {
		h.logger.Error("❌ Failed to get learning paths by creator - Error: %v, CreatorID: %s, ClientIP: %s", err, creatorID, clientIP)
		response.InternalServerError(c, "Failed to get learning paths by creator", err)
		return
	}

	h.logger.Info("✅ Learning paths by creator retrieved successfully - CreatorID: %s, Count: %d, Total: %d, Duration: %v, ClientIP: %s",
		creatorID, len(paths), total, time.Since(startTime), clientIP)

	response.Paginated(c, "Learning paths by creator retrieved successfully", paths, total, page, pageSize)
}
