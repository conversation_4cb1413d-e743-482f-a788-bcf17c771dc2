/*
 * @Description: Learning lesson handlers for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-08-07
 */
package handlers

import (
	"context"
	"strconv"
	"time"

	"pointer/golangp/apps/pointer_center/internal/models"
	"pointer/golangp/apps/pointer_center/internal/services"
	"pointer/golangp/apps/pointer_center/pkg/response"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// LearningLessonHandler handles learning lesson-related requests
type LearningLessonHandler struct {
	service *services.LearningLessonService
	logger  *logging.Logger
}

// NewLearningLessonHandler creates a new learning lesson handler with service
func NewLearningLessonHandler(service *services.LearningLessonService) *LearningLessonHandler {
	logger := logging.GetLogger("learning_lesson_handler")

	return &LearningLessonHandler{
		service: service,
		logger:  logger,
	}
}

// CreateLessonRequest represents a request to create a learning lesson
type CreateLessonRequest struct {
	LessonID                  string                 `json:"lesson_id" binding:"required"`
	Title                     string                 `json:"title" binding:"required"`
	Description               string                 `json:"description"`
	Type                      models.LessonType      `json:"type" binding:"required"`
	EstimatedMinutes          int                    `json:"estimated_minutes"`
	Difficulty                models.DifficultyLevel `json:"difficulty"`
	ContentFlow               []models.AtomicContent `json:"content_flow"`
	LearningObjectives        []string               `json:"learning_objectives"`
	CommonMisconceptions      []string               `json:"common_misconceptions"`
	ExtensionIdea             string                 `json:"extension_idea"`
	StudentProfileAssociation string                 `json:"student_profile_association"`
}

// UpdateLessonRequest represents a request to update a learning lesson
type UpdateLessonRequest struct {
	Title                     string                 `json:"title"`
	Description               string                 `json:"description"`
	Type                      models.LessonType      `json:"type"`
	EstimatedMinutes          int                    `json:"estimated_minutes"`
	Difficulty                models.DifficultyLevel `json:"difficulty"`
	ContentFlow               []models.AtomicContent `json:"content_flow"`
	LearningObjectives        []string               `json:"learning_objectives"`
	CommonMisconceptions      []string               `json:"common_misconceptions"`
	ExtensionIdea             string                 `json:"extension_idea"`
	StudentProfileAssociation string                 `json:"student_profile_association"`
	Status                    models.LessonStatus    `json:"status"`
}

// CreateLesson creates a new learning lesson
func (h *LearningLessonHandler) CreateLesson(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Creating learning lesson - ClientIP: %s", clientIP)

	var req CreateLessonRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Extract context for database operations
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Call business logic
	lesson, err := h.createLessonLogic(ctx, req, clientIP)
	if err != nil {
		h.handleCreateLessonError(c, err, req.LessonID, clientIP)
		return
	}

	h.logger.Info("✅ Lesson created successfully - ID: %s, LessonID: %s, Duration: %v, ClientIP: %s",
		lesson.ID.Hex(), lesson.LessonID, time.Since(startTime), clientIP)

	response.Created(c, "Lesson created successfully", lesson)
}

// createLessonLogic contains the business logic for creating a lesson
func (h *LearningLessonHandler) createLessonLogic(ctx context.Context, req CreateLessonRequest, clientIP string) (*models.LearningLesson, error) {
	// Convert handler request to service request
	serviceReq := &services.CreateLearningLessonRequest{
		LessonID:                  req.LessonID,
		Title:                     req.Title,
		Description:               req.Description,
		Type:                      req.Type,
		EstimatedMinutes:          req.EstimatedMinutes,
		Difficulty:                req.Difficulty,
		ContentFlow:               req.ContentFlow,
		LearningObjectives:        req.LearningObjectives,
		CommonMisconceptions:      req.CommonMisconceptions,
		ExtensionIdea:             req.ExtensionIdea,
		StudentProfileAssociation: req.StudentProfileAssociation,
		CreatedBy:                 "system", // TODO: Get from auth context
	}

	// Call service to create lesson
	lesson, err := h.service.CreateLearningLesson(ctx, serviceReq)
	if err != nil {
		h.logger.Error("❌ Failed to create lesson - Error: %v, LessonID: %s, ClientIP: %s", err, req.LessonID, clientIP)
		return nil, err
	}

	return lesson, nil
}

// handleCreateLessonError handles errors from lesson creation
func (h *LearningLessonHandler) handleCreateLessonError(c *gin.Context, err error, lessonID, clientIP string) {
	switch err.Error() {
	case "lesson_already_exists":
		response.Conflict(c, "Lesson with this ID already exists", nil)
	default:
		response.InternalServerError(c, "Failed to create lesson", err)
	}
}

// GetLesson retrieves a learning lesson by ID
func (h *LearningLessonHandler) GetLesson(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	lessonID := c.Param("id")

	h.logger.Info("🔄 Getting lesson - ID: %s, ClientIP: %s", lessonID, clientIP)

	// Parse ObjectID
	objectID, err := primitive.ObjectIDFromHex(lessonID)
	if err != nil {
		h.logger.Warning("❌ Invalid lesson ID format - ID: %s, ClientIP: %s", lessonID, clientIP)
		response.BadRequest(c, "Invalid lesson ID format", err)
		return
	}

	// Extract context for database operations
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Call business logic
	lesson, err := h.getLessonLogic(ctx, objectID)
	if err != nil {
		h.logger.Warning("❌ Lesson not found - ID: %s, Error: %v, ClientIP: %s", lessonID, err, clientIP)
		response.NotFound(c, "Lesson not found", err)
		return
	}

	h.logger.Info("✅ Lesson retrieved successfully - ID: %s, Duration: %v, ClientIP: %s",
		lessonID, time.Since(startTime), clientIP)

	response.Success(c, "Lesson retrieved successfully", lesson)
}

// getLessonLogic contains the business logic for retrieving a lesson
func (h *LearningLessonHandler) getLessonLogic(ctx context.Context, objectID primitive.ObjectID) (*models.LearningLesson, error) {
	return h.service.GetLearningLesson(ctx, objectID)
}

// GetLessonByLessonID retrieves a learning lesson by lesson_id
func (h *LearningLessonHandler) GetLessonByLessonID(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	lessonID := c.Param("lesson_id")

	h.logger.Info("🔄 Getting lesson by lesson_id - LessonID: %s, ClientIP: %s", lessonID, clientIP)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	lesson, err := h.service.GetLearningLessonByLessonID(ctx, lessonID)
	if err != nil {
		h.logger.Warning("❌ Lesson not found - LessonID: %s, Error: %v, ClientIP: %s", lessonID, err, clientIP)
		response.NotFound(c, "Lesson not found", err)
		return
	}

	h.logger.Info("✅ Lesson retrieved successfully - LessonID: %s, Duration: %v, ClientIP: %s",
		lessonID, time.Since(startTime), clientIP)

	response.Success(c, "Lesson retrieved successfully", lesson)
}

// ListLessonsParams contains parameters for listing lessons
type ListLessonsParams struct {
	Page       int
	PageSize   int
	Status     string
	LessonType string
}

// ListLessons retrieves a paginated list of learning lessons
func (h *LearningLessonHandler) ListLessons(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	h.logger.Info("🔄 Listing lessons - ClientIP: %s", clientIP)

	// Parse parameters
	params := h.parseListLessonsParams(c)

	// Extract context for database operations
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Call business logic
	lessons, total, err := h.listLessonsLogic(ctx, params)
	if err != nil {
		h.logger.Error("❌ Failed to list lessons - Error: %v, ClientIP: %s", err, clientIP)
		response.InternalServerError(c, "Failed to list lessons", err)
		return
	}

	h.logger.Info("✅ Lessons listed successfully - Count: %d, Total: %d, Duration: %v, ClientIP: %s",
		len(lessons), total, time.Since(startTime), clientIP)

	response.Paginated(c, "Lessons retrieved successfully", lessons, total, params.Page, params.PageSize)
}

// parseListLessonsParams parses and validates list parameters from gin.Context
func (h *LearningLessonHandler) parseListLessonsParams(c *gin.Context) ListLessonsParams {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	return ListLessonsParams{
		Page:       page,
		PageSize:   pageSize,
		Status:     c.Query("status"),
		LessonType: c.Query("type"),
	}
}

// listLessonsLogic contains the business logic for listing lessons
func (h *LearningLessonHandler) listLessonsLogic(ctx context.Context, params ListLessonsParams) ([]*models.LearningLesson, int64, error) {
	// Get lessons based on filters using service layer
	var lessons []*models.LearningLesson
	var total int64
	var err error

	if params.Status != "" {
		lessons, total, err = h.service.GetLessonsByStatus(ctx, models.LessonStatus(params.Status), params.Page, params.PageSize)
	} else if params.LessonType != "" {
		lessons, total, err = h.service.GetLessonsByType(ctx, models.LessonType(params.LessonType), params.Page, params.PageSize)
	} else {
		serviceReq := &services.GetLearningLessonsRequest{
			Page:     params.Page,
			PageSize: params.PageSize,
		}
		lessons, total, err = h.service.GetLearningLessons(ctx, serviceReq)
	}

	if err != nil {
		return nil, 0, err
	}

	return lessons, total, nil
}

// UpdateLesson updates an existing learning lesson
func (h *LearningLessonHandler) UpdateLesson(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	lessonID := c.Param("id")

	h.logger.Info("🔄 Updating lesson - ID: %s, ClientIP: %s", lessonID, clientIP)

	// Parse ObjectID
	objectID, err := primitive.ObjectIDFromHex(lessonID)
	if err != nil {
		h.logger.Warning("❌ Invalid lesson ID format - ID: %s, ClientIP: %s", lessonID, clientIP)
		response.BadRequest(c, "Invalid lesson ID format", err)
		return
	}

	var req UpdateLessonRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Convert handler request to service request
	serviceReq := &services.UpdateLearningLessonRequest{
		UpdatedBy: stringPtr("system"), // TODO: Get from auth context
	}

	// Set fields that are provided
	if req.Title != "" {
		serviceReq.Title = &req.Title
	}
	if req.Description != "" {
		serviceReq.Description = &req.Description
	}
	if req.Type != "" {
		serviceReq.Type = &req.Type
	}
	if req.EstimatedMinutes > 0 {
		serviceReq.EstimatedMinutes = &req.EstimatedMinutes
	}
	if req.Difficulty > 0 {
		serviceReq.Difficulty = &req.Difficulty
	}
	if req.ContentFlow != nil {
		serviceReq.ContentFlow = req.ContentFlow
	}
	if req.LearningObjectives != nil {
		serviceReq.LearningObjectives = req.LearningObjectives
	}
	if req.CommonMisconceptions != nil {
		serviceReq.CommonMisconceptions = req.CommonMisconceptions
	}
	if req.ExtensionIdea != "" {
		serviceReq.ExtensionIdea = &req.ExtensionIdea
	}
	if req.StudentProfileAssociation != "" {
		serviceReq.StudentProfileAssociation = &req.StudentProfileAssociation
	}

	// Call service to update lesson
	updatedLesson, err := h.service.UpdateLearningLesson(ctx, objectID, serviceReq)
	if err != nil {
		h.logger.Error("❌ Failed to update lesson - Error: %v, ID: %s, ClientIP: %s", err, lessonID, clientIP)
		response.InternalServerError(c, "Failed to update lesson", err)
		return
	}

	h.logger.Info("✅ Lesson updated successfully - ID: %s, Duration: %v, ClientIP: %s",
		lessonID, time.Since(startTime), clientIP)

	response.Success(c, "Lesson updated successfully", updatedLesson)
}

// DeleteLesson deletes a learning lesson
func (h *LearningLessonHandler) DeleteLesson(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	lessonID := c.Param("id")

	h.logger.Info("🔄 Deleting lesson - ID: %s, ClientIP: %s", lessonID, clientIP)

	// Parse ObjectID
	objectID, err := primitive.ObjectIDFromHex(lessonID)
	if err != nil {
		h.logger.Warning("❌ Invalid lesson ID format - ID: %s, ClientIP: %s", lessonID, clientIP)
		response.BadRequest(c, "Invalid lesson ID format", err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Call service to delete lesson
	if err := h.service.DeleteLearningLesson(ctx, objectID); err != nil {
		h.logger.Error("❌ Failed to delete lesson - Error: %v, ID: %s, ClientIP: %s", err, lessonID, clientIP)
		response.InternalServerError(c, "Failed to delete lesson", err)
		return
	}

	h.logger.Info("✅ Lesson deleted successfully - ID: %s, Duration: %v, ClientIP: %s",
		lessonID, time.Since(startTime), clientIP)

	response.Success(c, "Lesson deleted successfully", nil)
}

// SearchLessons searches for lessons based on query parameters
func (h *LearningLessonHandler) SearchLessons(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	query := c.Query("q")

	h.logger.Info("🔄 Searching lessons - Query: %s, ClientIP: %s", query, clientIP)

	if query == "" {
		h.logger.Warning("❌ Empty search query - ClientIP: %s", clientIP)
		response.BadRequest(c, "Search query is required", nil)
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Call service to search lessons
	lessons, total, err := h.service.SearchLessons(ctx, query, page, pageSize)
	if err != nil {
		h.logger.Error("❌ Failed to search lessons - Error: %v, Query: %s, ClientIP: %s", err, query, clientIP)
		response.InternalServerError(c, "Failed to search lessons", err)
		return
	}

	h.logger.Info("✅ Lessons searched successfully - Query: %s, Count: %d, Total: %d, Duration: %v, ClientIP: %s",
		query, len(lessons), total, time.Since(startTime), clientIP)

	response.Paginated(c, "Lessons searched successfully", lessons, total, page, pageSize)
}

// GenerateLessonWithAI generates a learning lesson using AI
func (h *LearningLessonHandler) GenerateLessonWithAI(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	h.logger.Info("🔄 Generating lesson with AI - ClientIP: %s", clientIP)

	// Create context with timeout
	ctx, cancel := context.WithTimeout(c.Request.Context(), 60*time.Second) // Longer timeout for AI generation
	defer cancel()

	// Parse request body
	var req GenerateLessonRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Validate required fields
	if req.LessonDescription == "" {
		h.logger.Warning("❌ Missing lesson description - ClientIP: %s", clientIP)
		response.BadRequest(c, "Lesson description is required", nil)
		return
	}

	// Check if service is available
	if h.service == nil {
		h.logger.Error("❌ Service not available - ClientIP: %s", clientIP)
		response.InternalServerError(c, "AI service not available", nil)
		return
	}

	// Call service to generate lesson
	serviceReq := &services.GenerateLessonRequest{
		LessonDescription: req.LessonDescription,
		UserID:            req.UserID,
	}

	lesson, err := h.service.GenerateLearningLessonWithAI(ctx, serviceReq)
	if err != nil {
		h.logger.Error("❌ Failed to generate lesson with AI - Error: %v, UserID: %s, ClientIP: %s", err, req.UserID, clientIP)
		response.InternalServerError(c, "Failed to generate lesson with AI", err)
		return
	}

	h.logger.Info("✅ Lesson generated with AI successfully - LessonID: %s, UserID: %s, Duration: %v, ClientIP: %s",
		lesson.ID.Hex(), req.UserID, time.Since(startTime), clientIP)

	response.Created(c, "Lesson generated with AI successfully", lesson)
}

// EnhanceLessonWithAI enhances an existing lesson using AI
func (h *LearningLessonHandler) EnhanceLessonWithAI(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	lessonID := c.Param("id")

	h.logger.Info("🔄 Enhancing lesson with AI - ID: %s, ClientIP: %s", lessonID, clientIP)

	// Parse ObjectID
	objectID, err := primitive.ObjectIDFromHex(lessonID)
	if err != nil {
		h.logger.Warning("❌ Invalid lesson ID format - ID: %s, ClientIP: %s", lessonID, clientIP)
		response.BadRequest(c, "Invalid lesson ID format", err)
		return
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(c.Request.Context(), 60*time.Second)
	defer cancel()

	// Parse request body
	var req EnhanceLessonRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Get existing lesson
	existingLesson, err := h.service.GetLearningLesson(ctx, objectID)
	if err != nil {
		h.logger.Error("❌ Failed to get lesson - Error: %v, ID: %s, ClientIP: %s", err, lessonID, clientIP)
		response.InternalServerError(c, "Failed to get lesson", err)
		return
	}

	// Check if service is available
	if h.service == nil {
		h.logger.Error("❌ Service not available - ClientIP: %s", clientIP)
		response.InternalServerError(c, "AI service not available", nil)
		return
	}

	// Prepare enhancement request
	enhancementDescription := req.EnhancementType
	if req.CustomDescription != "" {
		enhancementDescription = req.CustomDescription
	}

	// Generate enhanced content using AI
	serviceReq := &services.GenerateLessonRequest{
		LessonDescription: enhancementDescription + " for lesson: " + existingLesson.Title + ". " + existingLesson.Description,
		UserID:            req.UserID,
	}

	enhancedLesson, err := h.service.GenerateLearningLessonWithAI(ctx, serviceReq)
	if err != nil {
		h.logger.Error("❌ Failed to enhance lesson with AI - Error: %v, ID: %s, ClientIP: %s", err, lessonID, clientIP)
		response.InternalServerError(c, "Failed to enhance lesson with AI", err)
		return
	}

	// Merge enhanced content with existing lesson based on enhancement type
	h.mergeEnhancedContent(existingLesson, enhancedLesson, req.EnhancementType)

	// Create update request for service
	updateReq := &services.UpdateLearningLessonRequest{
		UpdatedBy: stringPtr("system"), // TODO: Get from auth context
	}

	// Set the enhanced fields based on enhancement type
	switch req.EnhancementType {
	case "content_flow":
		updateReq.ContentFlow = existingLesson.ContentFlow
	case "learning_objectives":
		updateReq.LearningObjectives = existingLesson.LearningObjectives
	case "misconceptions":
		updateReq.CommonMisconceptions = existingLesson.CommonMisconceptions
	case "extension_ideas":
		updateReq.ExtensionIdea = &existingLesson.ExtensionIdea
	case "full_enhancement":
		updateReq.ContentFlow = existingLesson.ContentFlow
		updateReq.LearningObjectives = existingLesson.LearningObjectives
		updateReq.CommonMisconceptions = existingLesson.CommonMisconceptions
		updateReq.ExtensionIdea = &existingLesson.ExtensionIdea
		updateReq.Description = &existingLesson.Description
	}

	// Update the lesson through service
	updatedLesson, err := h.service.UpdateLearningLesson(ctx, objectID, updateReq)
	if err != nil {
		h.logger.Error("❌ Failed to update enhanced lesson - Error: %v, ID: %s, ClientIP: %s", err, lessonID, clientIP)
		response.InternalServerError(c, "Failed to update enhanced lesson", err)
		return
	}

	h.logger.Info("✅ Lesson enhanced with AI successfully - ID: %s, Enhancement: %s, Duration: %v, ClientIP: %s",
		lessonID, req.EnhancementType, time.Since(startTime), clientIP)

	response.Success(c, "Lesson enhanced with AI successfully", updatedLesson)
}

// mergeEnhancedContent merges AI-enhanced content with existing lesson
func (h *LearningLessonHandler) mergeEnhancedContent(existing, enhanced *models.LearningLesson, enhancementType string) {
	switch enhancementType {
	case "content_flow":
		if len(enhanced.ContentFlow) > 0 {
			existing.ContentFlow = enhanced.ContentFlow
		}
	case "learning_objectives":
		if len(enhanced.LearningObjectives) > 0 {
			existing.LearningObjectives = enhanced.LearningObjectives
		}
	case "misconceptions":
		if len(enhanced.CommonMisconceptions) > 0 {
			existing.CommonMisconceptions = enhanced.CommonMisconceptions
		}
	case "extension_ideas":
		if enhanced.ExtensionIdea != "" {
			existing.ExtensionIdea = enhanced.ExtensionIdea
		}
	case "full_enhancement":
		// Merge all enhanced content
		if len(enhanced.ContentFlow) > 0 {
			existing.ContentFlow = enhanced.ContentFlow
		}
		if len(enhanced.LearningObjectives) > 0 {
			existing.LearningObjectives = enhanced.LearningObjectives
		}
		if len(enhanced.CommonMisconceptions) > 0 {
			existing.CommonMisconceptions = enhanced.CommonMisconceptions
		}
		if enhanced.ExtensionIdea != "" {
			existing.ExtensionIdea = enhanced.ExtensionIdea
		}
		if enhanced.Description != "" {
			existing.Description = enhanced.Description
		}
	}
	existing.UpdatedAt = time.Now()
}

// Request structures for AI generation
type GenerateLessonRequest struct {
	LessonDescription string `json:"lesson_description" binding:"required"`
	UserID            string `json:"user_id"`
	LessonType        string `json:"lesson_type,omitempty"`
	Difficulty        string `json:"difficulty,omitempty"`
	EstimatedMinutes  int    `json:"estimated_minutes,omitempty"`
}

type EnhanceLessonRequest struct {
	EnhancementType   string `json:"enhancement_type" binding:"required"` // content_flow, learning_objectives, misconceptions, extension_ideas, full_enhancement
	CustomDescription string `json:"custom_description,omitempty"`
	UserID            string `json:"user_id"`
}

// Helper functions for pointer conversion
func stringPtr(s string) *string {
	return &s
}

func intPtr(i int) *int {
	return &i
}
