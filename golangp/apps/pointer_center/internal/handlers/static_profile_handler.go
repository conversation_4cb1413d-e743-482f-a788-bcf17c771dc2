/*
 * @Description: Static profile handlers for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-08-07
 */
package handlers

import (
	"strconv"
	"time"

	"pointer/golangp/apps/pointer_center/internal/services"
	"pointer/golangp/apps/pointer_center/pkg/response"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// StaticProfileHandler handles static profile-related requests
type StaticProfileHandler struct {
	service *services.StaticProfileService
	logger  *logging.Logger
}

// NewStaticProfileHandler creates a new static profile handler with service
func NewStaticProfileHandler(service *services.StaticProfileService) *StaticProfileHandler {
	logger := logging.GetLogger("static_profile_handler")

	return &StaticProfileHandler{
		service: service,
		logger:  logger,
	}
}

// CreateProfileRequest represents a request to create a static profile
type CreateProfileRequest struct {
	UserID              string `json:"user_id" binding:"required"`
	Age                 int    `json:"age"`
	Gender              string `json:"gender"`
	PreferredLanguage   string `json:"preferred_language"`
	EducationExperience string `json:"education_experience"`
	Major               string `json:"major"`
	GraduationYear      int    `json:"graduation_year"`
	CurrentRole         string `json:"current_role"`
	Industry            string `json:"industry"`
	WorkExperience      int    `json:"work_experience"`
	LearningStyle       string `json:"learning_style"`
	StudyTimePerWeek    int    `json:"study_time_per_week"`
	PreferredStudyTime  string `json:"preferred_study_time"`
	LearningPace        string `json:"learning_pace"`
}

// UpdateProfileRequest represents a request to update a static profile
type UpdateProfileRequest struct {
	Age                 int    `json:"age"`
	Gender              string `json:"gender"`
	PreferredLanguage   string `json:"preferred_language"`
	EducationExperience string `json:"education_experience"`
	Major               string `json:"major"`
	GraduationYear      int    `json:"graduation_year"`
	CurrentRole         string `json:"current_role"`
	Industry            string `json:"industry"`
	WorkExperience      int    `json:"work_experience"`
	LearningStyle       string `json:"learning_style"`
	StudyTimePerWeek    int    `json:"study_time_per_week"`
	PreferredStudyTime  string `json:"preferred_study_time"`
	LearningPace        string `json:"learning_pace"`
}

// CreateProfile creates a new static profile
func (h *StaticProfileHandler) CreateProfile(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	h.logger.Info("🔄 Creating static profile - ClientIP: %s", clientIP)

	var req CreateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	// Parse user ID
	userUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		h.logger.Warning("❌ Invalid user ID format - UserID: %s, ClientIP: %s", req.UserID, clientIP)
		response.BadRequest(c, "Invalid user ID format", err)
		return
	}

	// Convert handler request to service request
	ctx := c.Request.Context()
	serviceReq := &services.CreateStaticProfileRequest{
		UserID:              userUUID,
		Age:                 req.Age,
		Gender:              req.Gender,
		PreferredLanguage:   req.PreferredLanguage,
		EducationExperience: req.EducationExperience,
		Major:               req.Major,
		GraduationYear:      req.GraduationYear,
		CurrentRole:         req.CurrentRole,
		Industry:            req.Industry,
		WorkExperience:      req.WorkExperience,
		LearningStyle:       req.LearningStyle,
		StudyTimePerWeek:    req.StudyTimePerWeek,
		PreferredStudyTime:  req.PreferredStudyTime,
		LearningPace:        req.LearningPace,
	}

	// Set defaults if not provided
	if serviceReq.PreferredLanguage == "" {
		serviceReq.PreferredLanguage = "zh-CN"
	}
	if serviceReq.LearningStyle == "" {
		serviceReq.LearningStyle = "visual"
	}
	if serviceReq.StudyTimePerWeek == 0 {
		serviceReq.StudyTimePerWeek = 10
	}
	if serviceReq.LearningPace == "" {
		serviceReq.LearningPace = "moderate"
	}

	// Call service to create static profile
	profile, err := h.service.CreateStaticProfile(ctx, serviceReq)
	if err != nil {
		h.logger.Error("❌ Failed to create static profile - Error: %v, UserID: %s, ClientIP: %s", err, req.UserID, clientIP)
		response.InternalServerError(c, "Failed to create static profile", err)
		return
	}

	h.logger.Info("✅ Static profile created successfully - UserID: %s, Duration: %v, ClientIP: %s",
		req.UserID, time.Since(startTime), clientIP)

	response.Created(c, "Static profile created successfully", profile)
}

// GetProfile retrieves a static profile by user ID
func (h *StaticProfileHandler) GetProfile(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.Param("user_id")

	h.logger.Info("🔄 Getting static profile - UserID: %s, ClientIP: %s", userID, clientIP)

	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		h.logger.Warning("❌ Invalid user ID format - UserID: %s, ClientIP: %s", userID, clientIP)
		response.BadRequest(c, "Invalid user ID format", err)
		return
	}

	ctx := c.Request.Context()
	profile, err := h.service.GetStaticProfile(ctx, userUUID)
	if err != nil {
		h.logger.Warning("❌ Static profile not found - UserID: %s, Error: %v, ClientIP: %s", userID, err, clientIP)
		response.NotFound(c, "Static profile not found", err)
		return
	}

	h.logger.Info("✅ Static profile retrieved successfully - UserID: %s, Duration: %v, ClientIP: %s",
		userID, time.Since(startTime), clientIP)

	response.Success(c, "Static profile retrieved successfully", profile)
}

// CreateEmptyProfile creates an empty static profile for a new user
func (h *StaticProfileHandler) CreateEmptyProfile(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.Param("user_id")

	h.logger.Info("🔄 Creating empty static profile - UserID: %s, ClientIP: %s", userID, clientIP)

	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		h.logger.Warning("❌ Invalid user ID format - UserID: %s, ClientIP: %s", userID, clientIP)
		response.BadRequest(c, "Invalid user ID format", err)
		return
	}

	ctx := c.Request.Context()

	// Call service to create empty static profile
	profile, err := h.service.CreateEmptyStaticProfile(ctx, userUUID)
	if err != nil {
		h.logger.Error("❌ Failed to create empty static profile - Error: %v, UserID: %s, ClientIP: %s", err, userID, clientIP)
		response.InternalServerError(c, "Failed to create empty static profile", err)
		return
	}

	h.logger.Info("✅ Empty static profile created successfully - UserID: %s, Duration: %v, ClientIP: %s",
		userID, time.Since(startTime), clientIP)

	response.Created(c, "Empty static profile created successfully", profile)
}

// UpdateProfile updates an existing static profile
func (h *StaticProfileHandler) UpdateProfile(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.Param("user_id")

	h.logger.Info("🔄 Updating static profile - UserID: %s, ClientIP: %s", userID, clientIP)

	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		h.logger.Warning("❌ Invalid user ID format - UserID: %s, ClientIP: %s", userID, clientIP)
		response.BadRequest(c, "Invalid user ID format", err)
		return
	}

	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Invalid request body - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid request body", err)
		return
	}

	ctx := c.Request.Context()

	// Convert handler request to service request
	serviceReq := &services.UpdateStaticProfileRequest{}

	// Set fields that are provided
	if req.Age > 0 {
		serviceReq.Age = &req.Age
	}
	if req.Gender != "" {
		serviceReq.Gender = &req.Gender
	}
	if req.PreferredLanguage != "" {
		serviceReq.PreferredLanguage = &req.PreferredLanguage
	}
	if req.EducationExperience != "" {
		serviceReq.EducationExperience = &req.EducationExperience
	}
	if req.Major != "" {
		serviceReq.Major = &req.Major
	}
	if req.GraduationYear > 0 {
		serviceReq.GraduationYear = &req.GraduationYear
	}
	if req.CurrentRole != "" {
		serviceReq.CurrentRole = &req.CurrentRole
	}
	if req.Industry != "" {
		serviceReq.Industry = &req.Industry
	}
	if req.WorkExperience >= 0 {
		serviceReq.WorkExperience = &req.WorkExperience
	}
	if req.LearningStyle != "" {
		serviceReq.LearningStyle = &req.LearningStyle
	}
	if req.StudyTimePerWeek > 0 {
		serviceReq.StudyTimePerWeek = &req.StudyTimePerWeek
	}
	if req.PreferredStudyTime != "" {
		serviceReq.PreferredStudyTime = &req.PreferredStudyTime
	}
	if req.LearningPace != "" {
		serviceReq.LearningPace = &req.LearningPace
	}

	// Call service to update static profile
	updatedProfile, err := h.service.UpdateStaticProfile(ctx, userUUID, serviceReq)
	if err != nil {
		h.logger.Error("❌ Failed to update static profile - Error: %v, UserID: %s, ClientIP: %s", err, userID, clientIP)
		response.InternalServerError(c, "Failed to update static profile", err)
		return
	}

	h.logger.Info("✅ Static profile updated successfully - UserID: %s, Duration: %v, ClientIP: %s",
		userID, time.Since(startTime), clientIP)

	response.Success(c, "Static profile updated successfully", updatedProfile)
}

// DeleteProfile deletes a static profile
func (h *StaticProfileHandler) DeleteProfile(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userID := c.Param("user_id")

	h.logger.Info("🔄 Deleting static profile - UserID: %s, ClientIP: %s", userID, clientIP)

	// Parse user ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		h.logger.Warning("❌ Invalid user ID format - UserID: %s, ClientIP: %s", userID, clientIP)
		response.BadRequest(c, "Invalid user ID format", err)
		return
	}

	ctx := c.Request.Context()

	// Call service to delete static profile
	if err := h.service.DeleteStaticProfile(ctx, userUUID); err != nil {
		h.logger.Error("❌ Failed to delete static profile - Error: %v, UserID: %s, ClientIP: %s", err, userID, clientIP)
		response.InternalServerError(c, "Failed to delete static profile", err)
		return
	}

	h.logger.Info("✅ Static profile deleted successfully - UserID: %s, Duration: %v, ClientIP: %s",
		userID, time.Since(startTime), clientIP)

	response.Success(c, "Static profile deleted successfully", nil)
}

// ListProfiles retrieves a paginated list of static profiles
func (h *StaticProfileHandler) ListProfiles(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	h.logger.Info("🔄 Listing static profiles - ClientIP: %s", clientIP)

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	ctx := c.Request.Context()

	// Call service to get static profiles
	serviceReq := &services.GetStaticProfilesRequest{
		Page:     page,
		PageSize: pageSize,
	}
	profiles, total, err := h.service.GetStaticProfiles(ctx, serviceReq)
	if err != nil {
		h.logger.Error("❌ Failed to list static profiles - Error: %v, ClientIP: %s", err, clientIP)
		response.InternalServerError(c, "Failed to list static profiles", err)
		return
	}

	h.logger.Info("✅ Static profiles listed successfully - Count: %d, Total: %d, Duration: %v, ClientIP: %s",
		len(profiles), total, time.Since(startTime), clientIP)

	response.Paginated(c, "Static profiles retrieved successfully", profiles, total, page, pageSize)
}
