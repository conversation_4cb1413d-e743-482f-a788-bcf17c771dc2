#!/bin/bash

# Pointer Center 启动脚本
# 用于快速启动开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查依赖
check_dependencies() {
    print_message $BLUE "🔍 检查依赖..."
    
    # 检查 Go
    if ! command -v go &> /dev/null; then
        print_message $RED "❌ Go 未安装，请先安装 Go 1.23+"
        exit 1
    fi
    
    # 检查 Bazel
    if ! command -v bazel &> /dev/null; then
        print_message $RED "❌ Bazel 未安装，请先安装 Bazel"
        exit 1
    fi
    
    # 检查 PostgreSQL
    if ! command -v psql &> /dev/null; then
        print_message $YELLOW "⚠️  PostgreSQL 客户端未找到，请确保 PostgreSQL 已安装并运行"
    fi
    
    print_message $GREEN "✅ 依赖检查完成"
}

# 设置环境配置
setup_environment() {
    print_message $BLUE "⚙️  设置环境配置..."
    
    if [ ! -f "app.env" ]; then
        if [ -f "app.env.example" ]; then
            cp app.env.example app.env
            print_message $GREEN "✅ 已创建 app.env 文件，请根据需要修改配置"
        else
            print_message $RED "❌ app.env.example 文件不存在"
            exit 1
        fi
    else
        print_message $GREEN "✅ app.env 文件已存在"
    fi
}

# 运行数据库迁移
run_migration() {
    print_message $BLUE "🗄️  运行数据库迁移..."
    
    if bazel run //golangp/apps/pointer_center:pointer_center -- migrate --seed; then
        print_message $GREEN "✅ 数据库迁移完成"
    else
        print_message $RED "❌ 数据库迁移失败"
        exit 1
    fi
}

# 启动服务器
start_server() {
    local port=${1:-8080}
    local host=${2:-localhost}
    
    print_message $BLUE "🚀 启动 Pointer Center 服务器..."
    print_message $YELLOW "📍 服务器地址: http://${host}:${port}"
    print_message $YELLOW "📊 健康检查: http://${host}:${port}/health"
    print_message $YELLOW "🔗 API 基础路径: http://${host}:${port}/api/v1"
    print_message $YELLOW "按 Ctrl+C 停止服务器"
    echo
    
    bazel run //golangp/apps/pointer_center:pointer_center -- server --host $host --port $port
}

# 显示帮助信息
show_help() {
    echo "Pointer Center 启动脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -p, --port PORT         指定服务器端口 (默认: 8080)"
    echo "  -H, --host HOST         指定服务器主机 (默认: localhost)"
    echo "  --skip-migration        跳过数据库迁移"
    echo "  --migration-only        仅运行数据库迁移"
    echo
    echo "示例:"
    echo "  $0                      # 使用默认配置启动"
    echo "  $0 -p 9090              # 在端口 9090 启动"
    echo "  $0 -H 0.0.0.0 -p 8080   # 绑定所有接口"
    echo "  $0 --migration-only     # 仅运行数据库迁移"
}

# 主函数
main() {
    local port=8080
    local host="localhost"
    local skip_migration=false
    local migration_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -p|--port)
                port="$2"
                shift 2
                ;;
            -H|--host)
                host="$2"
                shift 2
                ;;
            --skip-migration)
                skip_migration=true
                shift
                ;;
            --migration-only)
                migration_only=true
                shift
                ;;
            *)
                print_message $RED "❌ 未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_message $GREEN "🎯 Pointer Center 启动脚本"
    echo
    
    # 检查依赖
    check_dependencies
    
    # 设置环境
    setup_environment
    
    # 运行数据库迁移
    if [ "$skip_migration" = false ]; then
        run_migration
    fi
    
    # 如果只运行迁移，则退出
    if [ "$migration_only" = true ]; then
        print_message $GREEN "🎉 数据库迁移完成，退出"
        exit 0
    fi
    
    echo
    
    # 启动服务器
    start_server $port $host
}

# 运行主函数
main "$@"
