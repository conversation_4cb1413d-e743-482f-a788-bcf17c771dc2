/*
 * @Description: Server command for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package main

import (
	"log"
	"os"

	"pointer/golangp/apps/pointer_center/internal/config"
	"pointer/golangp/apps/pointer_center/internal/routes"
	"pointer/golangp/common/database/mongodb"
	"pointer/golangp/common/database/postgres"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
)

var serverCmd = &cobra.Command{
	Use:   "server",
	Short: "Start the Pointer Center HTTP server",
	Long:  `Start the Pointer Center HTTP server with REST API endpoints for data management and analytics.`,
	Run:   runServer,
}

var (
	serverPort string
	serverHost string
)

func init() {
	serverCmd.Flags().StringVarP(&serverPort, "port", "p", "", "Port to run the server on (overrides config)")
	serverCmd.Flags().StringVarP(&serverHost, "host", "H", "", "Host to bind the server to (overrides config)")
}

func runServer(cmd *cobra.Command, args []string) {
	log.Println("🚀 Starting Pointer Center server...")

	// Load configuration
	cfg := config.Load()

	// Override config with command line flags if provided
	if serverPort != "" {
		cfg.ServerPort = serverPort
	}
	// Note: serverHost is handled directly in the address construction

	// Initialize logger
	logLevel := logging.INFO
	if cfg.LogLevel == "debug" {
		logLevel = logging.DEBUG
	} else if cfg.LogLevel == "warning" {
		logLevel = logging.WARNING
	} else if cfg.LogLevel == "error" {
		logLevel = logging.ERROR
	}

	loggerConfig := &logging.LoggerConfig{
		Level:    logLevel,
		LogPath:  cfg.LogPath,
		FileName: "pointer_center",
		Env:      cfg.Environment,
	}
	logging.InitLogger(loggerConfig)
	logger := logging.GetLogger("pointer_center")

	// Connect to PostgreSQL database
	postgres.ConnectDB(&postgres.Config{
		DBHost:     cfg.DBHost,
		DBUserName: cfg.DBUsername,
		DBPassword: cfg.DBPassword,
		DBName:     cfg.DBName,
		DBPort:     cfg.DBPort,
		DBSslMode:  cfg.DBSSLMode,
	})

	// Connect to MongoDB database
	mongodb.ConnectDB(&mongodb.Config{
		Host:     cfg.MongoHost,
		Port:     cfg.MongoPort,
		Username: cfg.MongoUsername,
		Password: cfg.MongoPassword,
		Database: cfg.MongoDatabase,
		AuthDB:   cfg.MongoAuthDB,
	})

	// Set Gin mode based on environment
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// Create Gin router
	router := gin.New()

	// Setup routes
	routes.SetupRoutes(router, cfg, postgres.DB, cfg.GoogleClientID, cfg.GoogleClientSecret)

	// Start server
	// Use serverHost from command line flag, or default to localhost
	host := serverHost
	if host == "" {
		host = "localhost"
	}
	address := host + ":" + cfg.ServerPort
	log.Printf("🌟 Server starting on %s", address)
	log.Printf("📊 Environment: %s", cfg.Environment)
	log.Printf("🗄️  Database: %s@%s:%s/%s", cfg.DBUsername, cfg.DBHost, cfg.DBPort, cfg.DBName)

	if err := router.Run(address); err != nil {
		logger.Error("Failed to start server: %v", err)
		os.Exit(1)
	}
}
