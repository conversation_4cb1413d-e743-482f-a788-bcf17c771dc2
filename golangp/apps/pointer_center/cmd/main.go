/*
 * @Description: Pointer Center Application - CLI Entry Point
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package main

import (
	"os"

	"github.com/spf13/cobra"
)

var rootCmd = &cobra.Command{
	Use:   "pointer_center",
	Short: "Pointer Center is a comprehensive data management and analytics platform",
	Long:  `Pointer Center is a modern data management platform that provides analytics, reporting, and data visualization capabilities for enterprise applications.`,
}

func init() {
	rootCmd.AddCommand(serverCmd)
	rootCmd.AddCommand(migrateCmd)
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}
