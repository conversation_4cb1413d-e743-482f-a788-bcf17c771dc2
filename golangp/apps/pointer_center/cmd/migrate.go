/*
 * @Description: Database migration command for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package main

import (
	"fmt"
	"log"

	"pointer/golangp/apps/pointer_center/internal/config"
	"pointer/golangp/apps/pointer_center/internal/models"
	"pointer/golangp/common/database/postgres"
	"pointer/golangp/common/payment/alipay"
	"pointer/golangp/common/payment/wechat"

	"github.com/spf13/cobra"
	"gorm.io/gorm"
)

var migrateCmd = &cobra.Command{
	Use:   "migrate",
	Short: "Run database migrations",
	Long:  `Run database migrations to create tables, indexes, and seed initial data for Pointer Center.`,
	Run:   runMigrate,
}

var (
	seedData   bool
	dropTables bool
)

func init() {
	migrateCmd.Flags().BoolVarP(&seedData, "seed", "s", false, "Seed initial data after migration")
	migrateCmd.Flags().BoolVarP(&dropTables, "drop", "d", false, "Drop existing tables before migration (DANGEROUS)")
}

func runMigrate(cmd *cobra.Command, args []string) {
	log.Println("🚀 Starting database migration...")

	// Load configuration
	cfg := config.Load()

	// Connect to database
	postgres.ConnectDB(&postgres.Config{
		DBHost:     cfg.DBHost,
		DBUserName: cfg.DBUsername,
		DBPassword: cfg.DBPassword,
		DBName:     cfg.DBName,
		DBPort:     cfg.DBPort,
		DBSslMode:  cfg.DBSSLMode,
	})

	// Migrate tables using the improved approach
	migrateTables()

	log.Println("🎉 Database setup completed!")
}

func migrateTables() {
	if err := postgres.DB.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";").Error; err != nil {
		log.Fatalf("❌ Failed to create uuid-ossp extension: %v", err)
	}
	log.Println("📋 Starting table migrations...")

	// migrate 是一个辅助函数，用于迁移单个表
	migrate := func(table interface{}, comment string, skipIfExists bool) {
		// 获取表名
		stmt := &gorm.Statement{DB: postgres.DB}
		stmt.Parse(table)
		tableName := stmt.Schema.Table

		if skipIfExists && postgres.DB.Migrator().HasTable(table) {
			log.Printf("⚠️ 表已存在，跳过迁移: %s", tableName)
			return
		}

		// 设置表选项（PostgreSQL使用不同的语法）
		if err := postgres.DB.Set("gorm:table_options", "").AutoMigrate(table); err != nil {
			log.Fatalf("❌ 迁移失败 [%s]: %v", tableName, err)
		}

		// 为表添加注释（PostgreSQL语法）
		if comment != "" {
			// 使用双引号包围表名以处理保留关键字
			commentSQL := fmt.Sprintf("COMMENT ON TABLE \"%s\" IS '%s'", tableName, comment)
			if err := postgres.DB.Exec(commentSQL).Error; err != nil {
				log.Printf("⚠️ 添加表注释失败 [%s]: %v", tableName, err)
			}
		}

		log.Printf("✅ 表迁移成功: %s", tableName)
	}

	// 用户相关表
	migrate(&models.User{}, "用户表", true)
	migrate(&models.UserSession{}, "用户会话表", true)
	migrate(&models.Subscription{}, "用户订阅表", true)

	// 学习路径相关表
	migrate(&models.LearningPath{}, "学习路径表", true)
	migrate(&models.UserMasterPath{}, "用户掌握路径表", true)
	migrate(&models.UserPathProgress{}, "用户路径进度表", true)
	migrate(&models.UserNodeProgress{}, "用户节点进度表", true)
	migrate(&models.UserLessonProgress{}, "用户课程进度表", true)
	migrate(&models.LearningAnalytics{}, "学习分析表", true)

	// 学习节点相关表
	migrate(&models.LearningNode{}, "学习节点表", true)
	migrate(&models.LearningPathNode{}, "学习路径节点关联表", true)
	migrate(&models.NodeLesson{}, "节点课程表", true)

	// 标签相关表
	migrate(&models.Tag{}, "标签表", true)
	migrate(&models.LearningPathTag{}, "学习路径标签关联表", true)

	// 支付宝支付相关表
	migrate(&alipay.PaymentRecord{}, "支付宝支付记录表", true)
	migrate(&alipay.RefundRecord{}, "支付宝退款记录表", true)
	migrate(&alipay.NotifyRecord{}, "支付宝异步通知记录表", true)
	migrate(&alipay.PaymentStatistics{}, "支付宝支付统计表", true)

	// 微信支付相关表
	migrate(&wechat.PaymentRecord{}, "微信支付记录表", true)
	migrate(&wechat.RefundRecord{}, "微信退款记录表", true)
	migrate(&wechat.NotifyRecord{}, "微信异步通知记录表", true)
	migrate(&wechat.PaymentStatistics{}, "微信支付统计表", true)

	log.Println("✅ 所有表的数据库迁移已完成")
}
