load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "cmd",
    srcs = [
        "main.go",
        "migrate.go",
        "server.go",
    ],
    importpath = "pointer/golangp/apps/pointer_center/cmd",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/pointer_center/internal/config",
        "//golangp/apps/pointer_center/internal/models",
        "//golangp/apps/pointer_center/internal/routes",
        "//golangp/common/database/mongodb",
        "//golangp/common/database/postgres",
        "//golangp/common/logging:logger",
        "//golangp/common/payment/alipay",
        "//golangp/common/payment/wechat",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_spf13_cobra//:cobra",
        "@io_gorm_gorm//:gorm",
    ],
)
