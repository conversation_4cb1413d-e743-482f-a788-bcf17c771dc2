load("@rules_go//go:def.bzl", "go_library")

# This is a meta package that aggregates all pkg packages
go_library(
    name = "pkg",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/pointer_center/pkg/errors",
        "//golangp/apps/pointer_center/pkg/response",
        "//golangp/apps/pointer_center/pkg/types",
        "//golangp/apps/pointer_center/pkg/utils",
    ],
)
