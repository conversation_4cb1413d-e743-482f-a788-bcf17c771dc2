/*
 * @Description: Custom error types for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package errors

import (
	"fmt"
)

// AppError represents an application error with additional context
type AppError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("%s: %s (%s)", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// NewAppError creates a new application error
func NewAppError(code, message string) *AppError {
	return &AppError{
		Code:    code,
		Message: message,
	}
}

// NewAppErrorWithDetails creates a new application error with details
func NewAppErrorWithDetails(code, message, details string) *AppError {
	return &AppError{
		Code:    code,
		Message: message,
		Details: details,
	}
}

// Common error codes and messages
var (
	// Authentication errors
	ErrInvalidCredentials = NewAppError("AUTH_001", "Invalid credentials")
	ErrUserNotFound       = NewAppError("AUTH_002", "User not found")
	ErrUserDisabled       = NewAppError("AUTH_003", "User account is disabled")
	ErrInvalidToken       = NewAppError("AUTH_004", "Invalid or expired token")
	ErrTokenExpired       = NewAppError("AUTH_005", "Token has expired")

	// Authorization errors
	ErrUnauthorized     = NewAppError("AUTHZ_001", "Unauthorized access")
	ErrInsufficientRole = NewAppError("AUTHZ_002", "Insufficient permissions")
	ErrForbidden        = NewAppError("AUTHZ_003", "Access forbidden")

	// Validation errors
	ErrInvalidInput     = NewAppError("VAL_001", "Invalid input data")
	ErrMissingField     = NewAppError("VAL_002", "Required field is missing")
	ErrInvalidFormat    = NewAppError("VAL_003", "Invalid data format")
	ErrValueTooLong     = NewAppError("VAL_004", "Value exceeds maximum length")
	ErrValueTooShort    = NewAppError("VAL_005", "Value is below minimum length")

	// Resource errors
	ErrResourceNotFound    = NewAppError("RES_001", "Resource not found")
	ErrResourceExists      = NewAppError("RES_002", "Resource already exists")
	ErrResourceInUse       = NewAppError("RES_003", "Resource is currently in use")
	ErrResourceUnavailable = NewAppError("RES_004", "Resource is unavailable")

	// Database errors
	ErrDatabaseConnection = NewAppError("DB_001", "Database connection failed")
	ErrDatabaseQuery      = NewAppError("DB_002", "Database query failed")
	ErrDatabaseConstraint = NewAppError("DB_003", "Database constraint violation")
	ErrDatabaseTimeout    = NewAppError("DB_004", "Database operation timeout")

	// System errors
	ErrInternalServer = NewAppError("SYS_001", "Internal server error")
	ErrServiceDown    = NewAppError("SYS_002", "Service temporarily unavailable")
	ErrRateLimit      = NewAppError("SYS_003", "Rate limit exceeded")
	ErrTimeout        = NewAppError("SYS_004", "Operation timeout")

	// User-specific errors
	ErrUserAlreadyExists = NewAppError("USER_001", "User already exists")
	ErrEmailInUse        = NewAppError("USER_002", "Email address is already in use")
	ErrUsernameInUse     = NewAppError("USER_003", "Username is already taken")
	ErrWeakPassword      = NewAppError("USER_004", "Password does not meet security requirements")
	ErrPasswordMismatch  = NewAppError("USER_005", "Password confirmation does not match")
)

// ValidationError represents a validation error with field-specific details
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Value   string `json:"value,omitempty"`
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation error on field '%s': %s", e.Field, e.Message)
}

// NewValidationError creates a new validation error
func NewValidationError(field, message string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Message: message,
	}
}

// ValidationErrors represents multiple validation errors
type ValidationErrors struct {
	Errors []ValidationError `json:"errors"`
}

func (e *ValidationErrors) Error() string {
	if len(e.Errors) == 1 {
		return e.Errors[0].Error()
	}
	return fmt.Sprintf("validation failed with %d errors", len(e.Errors))
}

// AddError adds a validation error to the collection
func (e *ValidationErrors) AddError(field, message string) {
	e.Errors = append(e.Errors, ValidationError{
		Field:   field,
		Message: message,
	})
}

// HasErrors returns true if there are validation errors
func (e *ValidationErrors) HasErrors() bool {
	return len(e.Errors) > 0
}

// NewValidationErrors creates a new validation errors collection
func NewValidationErrors() *ValidationErrors {
	return &ValidationErrors{
		Errors: make([]ValidationError, 0),
	}
}
