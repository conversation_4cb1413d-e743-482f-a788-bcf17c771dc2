package ai_agents

import (
	"context"
	"fmt"
	"sync"
	"time"

	"pointer/golangp/common/dify"
)

// AgentType represents different types of AI agents
type AgentType string

const (
	AgentTypeGoalPlanner        AgentType = "goal_planner"
	AgentTypePathGenerator      AgentType = "path_generator"
	AgentTypeCourseGenerator    AgentType = "course_generator"
	AgentTypeLessonGenerator    AgentType = "lesson_generator"
	AgentTypeQualityAssurance   AgentType = "quality_assurance"
	AgentTypeProfileTechAnalyzer    AgentType = "profile_analyzer"
)

// AgentRequest represents a request to an AI agent
type AgentRequest struct {
	AgentType AgentType              `json:"agent_type"`
	UserID    string                 `json:"user_id"`
	Inputs    map[string]interface{} `json:"inputs"`
}

// AgentResponse represents a response from an AI agent
type AgentResponse struct {
	AgentType    AgentType              `json:"agent_type"`
	Success      bool                   `json:"success"`
	Data         map[string]interface{} `json:"data"`
	Error        string                 `json:"error,omitempty"`
	Metadata     map[string]interface{} `json:"metadata"`
	ProcessingTime time.Duration        `json:"processing_time"`
	TokensUsed   int                    `json:"tokens_used"`
	Cost         float64                `json:"cost"`
}

// Manager manages AI agents and their interactions
type Manager struct {
	difyClient *dify.Client
	agentAPIKeys map[AgentType]string
	mutex      sync.RWMutex
	maxRetries int
	timeout    int
}

// NewManager creates a new AI agents manager
func NewManager(difyBaseURL string, agentAPIKeys map[AgentType]string) *Manager {
	// Create dify client configuration
	difyConfig := dify.Config{
		BaseURL: difyBaseURL,
		Timeout: 60 * time.Second,
	}
	
	// Create dify client
	difyClient := dify.NewClient(difyConfig)
	
	manager := &Manager{
		difyClient:   difyClient,
		agentAPIKeys: agentAPIKeys,
		maxRetries:   3,
		timeout:      30,
	}
	
	return manager
}

// GetAgent retrieves an agent's API key
func (m *Manager) GetAgent(agentType AgentType) (string, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	
	apiKey, exists := m.agentAPIKeys[agentType]
	return apiKey, exists
}

// CallAgent calls an AI agent with the given request
func (m *Manager) CallAgent(ctx context.Context, request *AgentRequest) (*AgentResponse, error) {
	startTime := time.Now()
	
	// Get agent API key
	apiKey, exists := m.GetAgent(request.AgentType)
	if !exists {
		return &AgentResponse{
			AgentType:      request.AgentType,
			Success:        false,
			Error:          fmt.Sprintf("agent %s not registered", request.AgentType),
			ProcessingTime: time.Since(startTime),
		}, nil
	}
	
	// Create context with timeout
	agentCtx, cancel := context.WithTimeout(ctx, time.Duration(m.timeout)*time.Second)
	defer cancel()
		
	// Call agent with retries
	var response *AgentResponse
	var err error
	
	for attempt := 0; attempt <= m.maxRetries; attempt++ {
		response, err = m.callAgentOnce(agentCtx, request.AgentType, apiKey, request.Inputs, request.UserID)
		if err == nil && response.Success {
			break
		}
		
		if attempt < m.maxRetries {
			// Wait before retry
			time.Sleep(time.Duration(attempt+1) * time.Second)
		}
	}
	
	if response == nil {
		response = &AgentResponse{
			AgentType: request.AgentType,
			Success:   false,
			Error:     fmt.Sprintf("failed after %d attempts: %v", m.maxRetries+1, err),
		}
	}
	
	response.ProcessingTime = time.Since(startTime)
	return response, nil
}

// CallAgentAsync calls an AI agent asynchronously
func (m *Manager) CallAgentAsync(ctx context.Context, request *AgentRequest, callback func(*AgentResponse, error)) {
	go func() {
		response, err := m.CallAgent(ctx, request)
		callback(response, err)
	}()
}

// GenerateLearningPath generates a personalized learning path
func (m *Manager) GenerateLearningPath(ctx context.Context, goal, current_map, current_path string) (*AgentResponse, error) {
	request := &AgentRequest{
		AgentType: AgentTypePathGenerator,
		UserID:    "pageflux_system",
		Inputs: map[string]interface{}{
			"goal": goal,
			"current_map": current_map,
			"current_path": current_path,
		},
	}
	
	return m.CallAgent(ctx, request)
}

// GenerateCourse generates a course using AI
func (m *Manager) GenerateCourse(ctx context.Context, node, student_profile string) (*AgentResponse, error) {
	request := &AgentRequest{
		AgentType: AgentTypeCourseGenerator,
		UserID:    "pageflux_system",
		Inputs: map[string]interface{}{
			"node": node,
			"student_profile": student_profile,
		},
	}
	
	return m.CallAgent(ctx, request)
}

// GenerateLesson generates a lesson using AI
func (m *Manager) GenerateLesson(ctx context.Context, lesson, student_profile string) (*AgentResponse, error) {
	request := &AgentRequest{
		AgentType: AgentTypeLessonGenerator,
		UserID:    "pageflux_system",
		Inputs: map[string]interface{}{
			"lesson": lesson,
			"student_profile": student_profile,
		},
	}
	
	return m.CallAgent(ctx, request)
}

// AnalyzeProfile analyzes a student profile
func (m *Manager) AnalyzeTechProfile(ctx context.Context, userID string, profileData map[string]interface{}) (*AgentResponse, error) {
	request := &AgentRequest{
		AgentType: AgentTypeProfileTechAnalyzer,
		UserID:    userID,
		Inputs: map[string]interface{}{
			"current_competency_graph": map[string]interface{}{
				"nodes":            []interface{}{}, // 当前能力图节点列表，若为空表示首次生成
				"overall_score":    profileData["overall_score"],
				"strength_areas":   profileData["strength_areas"],
				"improvement_areas": profileData["improvement_areas"],
			},
			"student_action": map[string]interface{}{
				"type":        profileData["action_type"],
				"description": profileData["action_description"],
				"content":     profileData["action_content"],
			},
			"ai_evaluation": profileData["ai_evaluation"],
		},
	}
	
	return m.CallAgent(ctx, request)
}

// QualityCheck performs quality assurance on generated content
func (m *Manager) QualityCheck(ctx context.Context, contentData map[string]interface{}) (*AgentResponse, error) {
	request := &AgentRequest{
		AgentType: AgentTypeQualityAssurance,
		UserID:    "system",
		Inputs: map[string]interface{}{
			"content_data": contentData,
			"check_type":   "comprehensive",
		},
	}
	
	return m.CallAgent(ctx, request)
}

// callAgentOnce makes a single call to an AI agent
func (m *Manager) callAgentOnce(ctx context.Context, agentType AgentType, apiKey string, inputs map[string]interface{}, userID string) (*AgentResponse, error) {
	// Prepare Dify workflow request
	difyRequest := dify.WorkflowRequest{
		Inputs:       inputs,
		ResponseMode: "blocking",
		User:         userID,
	}
	
	// Call Dify Workflow API with agent's API key
	difyResponse, err := m.difyClient.RunWorkflow(ctx, difyRequest, apiKey)
	if err != nil {
		return &AgentResponse{
			AgentType: agentType,
			Success:   false,
			Error:     err.Error(),
		}, err
	}
	
	// Parse response
	response := &AgentResponse{
		AgentType: agentType,
		Success:   true,
		Data:      make(map[string]interface{}),
		Metadata:  make(map[string]interface{}),
	}
	
	// Extract data from workflow response
	if difyResponse.Data.Outputs != nil {
		response.Data = difyResponse.Data.Outputs
	}
	
	// Add workflow metadata
	response.Metadata["workflow_run_id"] = difyResponse.WorkflowRunID
	response.Metadata["task_id"] = difyResponse.TaskID
	response.Metadata["status"] = difyResponse.Data.Status
	response.Metadata["elapsed_time"] = difyResponse.Data.ElapsedTime
	
	// Extract token usage
	if difyResponse.Data.TotalTokens > 0 {
		response.TokensUsed = difyResponse.Data.TotalTokens
	}
	
	// Handle workflow errors
	if difyResponse.Data.Status == "failed" || difyResponse.Data.Error != "" {
		response.Success = false
		response.Error = difyResponse.Data.Error
	}
	
	return response, nil
}
