/*
 * @Description: User-related types for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package types

// CreateUserRequest represents a user creation request
type CreateUserRequest struct {
	Email     string `json:"email" binding:"required,email"`
	Username  string `json:"username" binding:"required,min=3,max=50"`
	Password  string `json:"password" binding:"required,min=6"`
	FirstName string `json:"first_name" binding:"max=100"`
	LastName  string `json:"last_name" binding:"max=100"`
	Origin    string `json:"origin"`
}

// UpdateUserRequest represents a user update request
type UpdateUserRequest struct {
	FirstName *string `json:"first_name,omitempty"`
	LastName  *string `json:"last_name,omitempty"`
	Avatar    *string `json:"avatar,omitempty"`
	Phone     *string `json:"phone,omitempty"`
	Company   *string `json:"company,omitempty"`
	Country   *string `json:"country,omitempty"`

	// 安全相关预留字段
	LoginProtection        *bool `json:"login_protection,omitempty"`
	PasswordChangeRequired *bool `json:"password_change_required,omitempty"`
	SecurityLock           *bool `json:"security_lock,omitempty"`
}

// ChangePasswordRequest represents a password change request
type ChangePasswordRequest struct {
	OldPassword     string `json:"old_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=6"`
	ConfirmPassword string `json:"confirm_password" binding:"required"`
}

// UserListRequest represents a user list request with filters
type UserListRequest struct {
	Page     int    `json:"page" form:"page"`
	PageSize int    `json:"page_size" form:"page_size"`
	Search   string `json:"search" form:"search"`
}

// UserResponse represents a user response (without sensitive data)
type UserResponse struct {
	ID        string `json:"id"`
	Email     string `json:"email"`
	Username  string `json:"username"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Avatar    string `json:"avatar"`
	Phone     string `json:"phone"`
	Company   string `json:"company"`
	Country   string `json:"country"`
	Origin    string `json:"origin"`
	IsActive  bool   `json:"is_active"`
	IsAdmin   bool   `json:"is_admin"`

	// 安全相关预留字段
	LoginProtection        bool `json:"login_protection"`
	PasswordChangeRequired bool `json:"password_change_required"`
	SecurityLock           bool `json:"security_lock"`

	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}
