/*
 * @Description: Authentication-related types for Pointer Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package types

// LoginRequest represents a login request
type LoginRequest struct {
	Identifier string `json:"identifier"` // Email or username
	Password   string `json:"password"`
	IDToken    string `json:"id_token,omitempty"` // For Google login
}

// LoginResponse represents a login response
type LoginResponse struct {
	User  interface{} `json:"user"`
	Token string      `json:"token"`
}

// GoogleUserInfo represents user information from Google OAuth
type GoogleUserInfo struct {
	Email     string `json:"email"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Picture   string `json:"picture"`
}

// JWTClaims represents JWT token claims
type JWTClaims struct {
	UserID  string `json:"user_id"`
	Email   string `json:"email"`
	IsAdmin bool   `json:"is_admin"`
}

// RefreshTokenRequest represents a token refresh request
type RefreshTokenRequest struct {
	Token string `json:"token" binding:"required"`
}

// TokenResponse represents a token response
type TokenResponse struct {
	Token     string `json:"token"`
	ExpiresAt string `json:"expires_at"`
}
