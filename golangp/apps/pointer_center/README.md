# Pointer Center

Pointer Center 是一个现代化的用户管理和认证平台，基于 Go 语言开发，采用 Gin 框架和 PostgreSQL 数据库。

## 功能特性

### 用户管理
- ✅ 用户注册和登录
- ✅ Google OAuth 集成
- ✅ JWT 令牌认证
- ✅ 用户资料管理
- ✅ 密码修改
- ✅ 用户权限管理
- ✅ 管理员功能

### 技术特性
- 🚀 基于 Gin 的高性能 HTTP 服务器
- 🗄️ PostgreSQL 数据库支持
- 🔐 JWT 令牌认证
- 🌐 CORS 跨域支持
- 📝 结构化日志记录
- 🔧 Viper 配置管理
- 🏗️ Bazel 构建系统
- 📊 健康检查端点

## 项目结构

```
golangp/apps/pointer_center/
├── cmd/                        # CLI命令入口
│   ├── main.go                 # 主CLI入口
│   ├── server.go               # 服务器命令
│   ├── migrate.go              # 数据库迁移命令
│   └── BUILD.bazel             # Bazel构建配置
├── internal/                   # 内部包
│   ├── config/                 # 配置管理
│   │   ├── config.go           # Viper配置加载
│   │   └── BUILD.bazel
│   ├── handlers/               # HTTP处理器
│   │   ├── auth_handler.go     # 认证API
│   │   ├── user_handler.go     # 用户管理API
│   │   └── BUILD.bazel
│   ├── services/               # 业务逻辑层
│   │   ├── user_service.go     # 用户业务逻辑
│   │   └── BUILD.bazel
│   ├── models/                 # 数据模型
│   │   ├── user.go             # 用户模型
│   │   ├── user_session.go     # 用户会话模型
│   │   ├── user_permission.go  # 用户权限模型
│   │   ├── user_setting.go     # 用户设置模型
│   │   └── BUILD.bazel
│   ├── middleware/             # 中间件
│   │   ├── auth.go             # 认证中间件
│   │   ├── cors.go             # CORS中间件
│   │   ├── logger.go           # 日志中间件
│   │   └── BUILD.bazel
│   └── routes/                 # 路由配置
│       ├── routes.go           # 主路由配置
│       ├── auth_routes.go      # 认证路由
│       ├── user_routes.go      # 用户路由
│       ├── health_routes.go    # 健康检查路由
│       └── BUILD.bazel
├── pkg/                        # 公共包
│   ├── types/                  # 类型定义
│   ├── response/               # 响应处理
│   ├── errors/                 # 错误处理
│   ├── utils/                  # 工具函数
│   └── BUILD.bazel
├── docs/                       # 文档
├── scripts/                    # 脚本文件
├── app.env.example            # 环境变量示例
├── BUILD.bazel                # 主构建配置
├── README.md                  # 项目说明
└── ARCHITECTURE.md            # 架构文档
```

## 快速开始

### 1. 环境准备

确保您的系统已安装：

- Go 1.23+
- PostgreSQL 12+
- Git
- Bazel

### 2. 配置环境变量

复制环境变量示例文件并根据您的环境进行配置：

```bash
cp app.env.example app.env
# 编辑 app.env 文件，配置数据库连接等信息
```

### 3. 数据库迁移

运行数据库迁移以创建必要的表结构：

```bash
# 基本迁移
bazel run //golangp/apps/pointer_center:pointer_center -- migrate

# 迁移并填充示例数据
bazel run //golangp/apps/pointer_center:pointer_center -- migrate --seed
```

### 4. 启动服务器

```bash
# 使用默认配置启动服务器
bazel run //golangp/apps/pointer_center:pointer_center -- server

# 指定端口启动服务器
bazel run //golangp/apps/pointer_center:pointer_center -- server --port 9090
```

服务器启动后，您可以访问：

- 健康检查: http://localhost:8080/health
- API 文档: http://localhost:8080/api/v1

## API 端点

### 认证相关

- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录（支持 Google OAuth）
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/refresh` - 刷新令牌

### 用户管理

- `GET /api/v1/users/profile` - 获取当前用户资料
- `PUT /api/v1/users/profile` - 更新用户资料
- `POST /api/v1/users/change-password` - 修改密码

### 管理员功能

- `GET /api/v1/users` - 获取用户列表（分页）
- `GET /api/v1/users/:id` - 获取指定用户信息
- `POST /api/v1/users/:id/activate` - 激活用户
- `POST /api/v1/users/:id/deactivate` - 停用用户

### 健康检查

- `GET /health` - 基本健康检查
- `GET /ready` - 就绪检查
- `GET /live` - 存活检查

## CLI 命令

Pointer Center 提供了命令行界面来管理应用：

### 查看帮助

```bash
# 查看所有可用命令
bazel run //golangp/apps/pointer_center:pointer_center -- --help

# 查看特定命令的帮助
bazel run //golangp/apps/pointer_center:pointer_center -- server --help
bazel run //golangp/apps/pointer_center:pointer_center -- migrate --help
```

### 数据库迁移命令

```bash
# 基本迁移
bazel run //golangp/apps/pointer_center:pointer_center -- migrate

# 迁移并填充示例数据
bazel run //golangp/apps/pointer_center:pointer_center -- migrate --seed

# 删除所有表后重新迁移（危险操作）
bazel run //golangp/apps/pointer_center:pointer_center -- migrate --drop --seed
```

### 服务器命令

```bash
# 使用默认配置启动服务器
bazel run //golangp/apps/pointer_center:pointer_center -- server

# 指定端口启动服务器
bazel run //golangp/apps/pointer_center:pointer_center -- server --port 9090

# 指定主机和端口
bazel run //golangp/apps/pointer_center:pointer_center -- server --host 0.0.0.0 --port 8080
```

## 配置说明

主要配置项说明：

- `ENVIRONMENT`: 运行环境 (development/production)
- `SERVER_PORT`: 服务器端口
- `DB_*`: 数据库连接配置
- `JWT_SECRET`: JWT 签名密钥
- `GOOGLE_CLIENT_ID`: Google OAuth 客户端 ID
- `GOOGLE_CLIENT_SECRET`: Google OAuth 客户端密钥

详细配置请参考 `app.env.example` 文件。

## 开发指南

### 添加新功能

1. 在 `internal/models/` 中定义数据模型
2. 在 `internal/services/` 中实现业务逻辑
3. 在 `internal/handlers/` 中创建 HTTP 处理器
4. 在 `internal/routes/` 中配置路由
5. 更新相应的 BUILD.bazel 文件

### 代码规范

- 遵循 Go 语言官方代码规范
- 使用有意义的变量和函数名
- 添加适当的注释和文档
- 编写单元测试

## 许可证

本项目采用 MIT 许可证。详情请参阅 LICENSE 文件。
