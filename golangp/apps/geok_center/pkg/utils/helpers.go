/*
 * @Description: Helper utilities for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package utils

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
)

// GenerateID generates a random ID string
func GenerateID() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback to timestamp-based ID
		return fmt.Sprintf("%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

// FormatNumber formats a number with appropriate units (K, M, B)
func FormatNumber(num int64) string {
	if num < 1000 {
		return strconv.FormatInt(num, 10)
	}
	
	units := []string{"", "K", "M", "B", "T"}
	unitIndex := 0
	value := float64(num)
	
	for value >= 1000 && unitIndex < len(units)-1 {
		value /= 1000
		unitIndex++
	}
	
	if value == math.Trunc(value) {
		return fmt.Sprintf("%.0f%s", value, units[unitIndex])
	}
	return fmt.Sprintf("%.1f%s", value, units[unitIndex])
}

// FormatPercentage formats a decimal as percentage
func FormatPercentage(value float64) string {
	return fmt.Sprintf("%.1f%%", value*100)
}

// CalculatePercentageChange calculates percentage change between two values
func CalculatePercentageChange(oldValue, newValue float64) float64 {
	if oldValue == 0 {
		if newValue == 0 {
			return 0
		}
		return 100 // 100% increase from 0
	}
	return ((newValue - oldValue) / oldValue) * 100
}

// GetTrendDirection determines trend direction based on percentage change
func GetTrendDirection(percentageChange float64) string {
	if percentageChange > 5 {
		return "up"
	} else if percentageChange < -5 {
		return "down"
	}
	return "stable"
}

// TruncateString truncates a string to specified length with ellipsis
func TruncateString(str string, length int) string {
	if len(str) <= length {
		return str
	}
	return str[:length-3] + "..."
}

// SlugifyString converts a string to URL-friendly slug
func SlugifyString(str string) string {
	// Convert to lowercase
	slug := strings.ToLower(str)
	
	// Replace spaces and special characters with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")
	slug = strings.ReplaceAll(slug, "_", "-")
	
	// Remove multiple consecutive hyphens
	for strings.Contains(slug, "--") {
		slug = strings.ReplaceAll(slug, "--", "-")
	}
	
	// Trim hyphens from start and end
	slug = strings.Trim(slug, "-")
	
	return slug
}

// ParseDateRange parses date range string (e.g., "7d", "30d", "1m", "3m", "1y")
func ParseDateRange(rangeStr string) (time.Time, time.Time, error) {
	now := time.Now()
	endDate := now
	
	if rangeStr == "" {
		// Default to last 30 days
		startDate := now.AddDate(0, 0, -30)
		return startDate, endDate, nil
	}
	
	rangeStr = strings.ToLower(strings.TrimSpace(rangeStr))
	
	var startDate time.Time
	switch {
	case strings.HasSuffix(rangeStr, "d"):
		// Days
		daysStr := strings.TrimSuffix(rangeStr, "d")
		days, err := strconv.Atoi(daysStr)
		if err != nil {
			return time.Time{}, time.Time{}, fmt.Errorf("invalid days format: %s", rangeStr)
		}
		startDate = now.AddDate(0, 0, -days)
		
	case strings.HasSuffix(rangeStr, "w"):
		// Weeks
		weeksStr := strings.TrimSuffix(rangeStr, "w")
		weeks, err := strconv.Atoi(weeksStr)
		if err != nil {
			return time.Time{}, time.Time{}, fmt.Errorf("invalid weeks format: %s", rangeStr)
		}
		startDate = now.AddDate(0, 0, -weeks*7)
		
	case strings.HasSuffix(rangeStr, "m"):
		// Months
		monthsStr := strings.TrimSuffix(rangeStr, "m")
		months, err := strconv.Atoi(monthsStr)
		if err != nil {
			return time.Time{}, time.Time{}, fmt.Errorf("invalid months format: %s", rangeStr)
		}
		startDate = now.AddDate(0, -months, 0)
		
	case strings.HasSuffix(rangeStr, "y"):
		// Years
		yearsStr := strings.TrimSuffix(rangeStr, "y")
		years, err := strconv.Atoi(yearsStr)
		if err != nil {
			return time.Time{}, time.Time{}, fmt.Errorf("invalid years format: %s", rangeStr)
		}
		startDate = now.AddDate(-years, 0, 0)
		
	default:
		return time.Time{}, time.Time{}, fmt.Errorf("invalid date range format: %s", rangeStr)
	}
	
	return startDate, endDate, nil
}

// GetColorForPercentage returns a color based on percentage value
func GetColorForPercentage(percentage float64) string {
	if percentage >= 70 {
		return "#22c55e" // Green
	} else if percentage >= 40 {
		return "#f59e0b" // Yellow
	} else if percentage >= 20 {
		return "#ef4444" // Red
	}
	return "#6b7280" // Gray
}

// CalculateGrowthRate calculates growth rate between two time periods
func CalculateGrowthRate(currentValue, previousValue float64, periods int) float64 {
	if previousValue == 0 || periods == 0 {
		return 0
	}
	
	growthRate := math.Pow(currentValue/previousValue, 1.0/float64(periods)) - 1
	return growthRate * 100
}

// RoundToDecimalPlaces rounds a float to specified decimal places
func RoundToDecimalPlaces(value float64, places int) float64 {
	multiplier := math.Pow(10, float64(places))
	return math.Round(value*multiplier) / multiplier
}

// Contains checks if a slice contains a specific string
func Contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// RemoveDuplicates removes duplicate strings from a slice
func RemoveDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string
	
	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}
	
	return result
}

// GetFileExtension returns the file extension from a filename
func GetFileExtension(filename string) string {
	parts := strings.Split(filename, ".")
	if len(parts) < 2 {
		return ""
	}
	return strings.ToLower(parts[len(parts)-1])
}
