/*
 * @Description: Validation utilities for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package utils

import (
	"regexp"
	"strings"
)

// ValidateEmail validates email format
func ValidateEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// ValidateURL validates URL format
func ValidateURL(url string) bool {
	urlRegex := regexp.MustCompile(`^https?://[^\s/$.?#].[^\s]*$`)
	return urlRegex.MatchString(url)
}

// ValidateBrandName validates brand name
func ValidateBrandName(name string) bool {
	// Brand name should be 2-100 characters, alphanumeric with spaces and common punctuation
	if len(name) < 2 || len(name) > 100 {
		return false
	}
	
	nameRegex := regexp.MustCompile(`^[a-zA-Z0-9\s\-_&.()]+$`)
	return nameRegex.MatchString(name)
}

// ValidateRegion validates region code
func ValidateRegion(region string) bool {
	validRegions := []string{
		"US", "CN", "JP", "KR", "GB", "DE", "FR", "CA", "AU", "IN",
		"BR", "MX", "RU", "IT", "ES", "NL", "SE", "NO", "DK", "FI",
	}
	
	for _, validRegion := range validRegions {
		if strings.ToUpper(region) == validRegion {
			return true
		}
	}
	return false
}

// ValidatePlatform validates AI platform name
func ValidatePlatform(platform string) bool {
	validPlatforms := []string{
		"openai", "anthropic", "google", "microsoft", "amazon",
		"baidu", "alibaba", "tencent", "bytedance", "meta",
	}
	
	for _, validPlatform := range validPlatforms {
		if strings.ToLower(platform) == validPlatform {
			return true
		}
	}
	return false
}

// ValidateExportFormat validates export format
func ValidateExportFormat(format string) bool {
	validFormats := []string{"csv", "json", "excel", "pdf"}
	
	for _, validFormat := range validFormats {
		if strings.ToLower(format) == validFormat {
			return true
		}
	}
	return false
}

// ValidateDataType validates data type for export
func ValidateDataType(dataType string) bool {
	validDataTypes := []string{
		"dashboard", "brands", "search_metrics", 
		"ai_appearances", "competitor_data",
	}
	
	for _, validType := range validDataTypes {
		if strings.ToLower(dataType) == validType {
			return true
		}
	}
	return false
}

// SanitizeString removes potentially harmful characters from string
func SanitizeString(input string) string {
	// Remove control characters and trim whitespace
	sanitized := strings.TrimSpace(input)
	
	// Remove null bytes and other control characters
	sanitized = strings.ReplaceAll(sanitized, "\x00", "")
	sanitized = strings.ReplaceAll(sanitized, "\r", "")
	sanitized = strings.ReplaceAll(sanitized, "\n", " ")
	sanitized = strings.ReplaceAll(sanitized, "\t", " ")
	
	// Collapse multiple spaces into single space
	spaceRegex := regexp.MustCompile(`\s+`)
	sanitized = spaceRegex.ReplaceAllString(sanitized, " ")
	
	return sanitized
}

// ValidatePassword validates password strength
func ValidatePassword(password string) bool {
	// Password should be at least 8 characters long
	if len(password) < 8 {
		return false
	}
	
	// Check for at least one uppercase letter
	hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(password)
	
	// Check for at least one lowercase letter
	hasLower := regexp.MustCompile(`[a-z]`).MatchString(password)
	
	// Check for at least one digit
	hasDigit := regexp.MustCompile(`[0-9]`).MatchString(password)
	
	// Check for at least one special character
	hasSpecial := regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`).MatchString(password)
	
	return hasUpper && hasLower && hasDigit && hasSpecial
}
