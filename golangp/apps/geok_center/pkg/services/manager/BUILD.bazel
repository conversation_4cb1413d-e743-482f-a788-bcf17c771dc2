load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "manager",
    srcs = ["service_manager.go"],
    importpath = "pointer/golangp/apps/geok_center/pkg/services/manager",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/geok_center/internal/config",
        "//golangp/apps/geok_center/pkg/services/upload",
        "//golangp/common/logging:logger",
        "//golangp/common/storage",
    ],
)
