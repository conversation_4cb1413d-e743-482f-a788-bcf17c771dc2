/*
 * @Description: API types and structures for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package types

import "time"

// APIResponse represents a standard API response
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Meta    *Meta       `json:"meta,omitempty"`
}

// Meta represents metadata for paginated responses
type Meta struct {
	Page       int `json:"page"`
	PageSize   int `json:"page_size"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}

// PaginationParams represents pagination parameters
type PaginationParams struct {
	Page     int `json:"page" form:"page"`
	PageSize int `json:"page_size" form:"page_size"`
}

// FilterParams represents common filtering parameters
type FilterParams struct {
	StartDate *time.Time `json:"start_date" form:"start_date"`
	EndDate   *time.Time `json:"end_date" form:"end_date"`
	Region    string     `json:"region" form:"region"`
	Platform  string     `json:"platform" form:"platform"`
	Category  string     `json:"category" form:"category"`
	SortBy    string     `json:"sort_by" form:"sort_by"`
	SortOrder string     `json:"sort_order" form:"sort_order"`
	UserID    string     `json:"user_id" form:"user_id"` // For filtering by user ID
}

// DashboardData represents the main dashboard data structure
type DashboardData struct {
	BrandSummary       BrandSummaryData       `json:"brand_summary"`
	SearchMetrics      SearchMetricsData      `json:"search_metrics"`
	AIAppearance       AIAppearanceData       `json:"ai_appearance"`
	CompetitorAnalysis CompetitorAnalysisData `json:"competitor_analysis"`
	RecentActivity     []ActivityItem         `json:"recent_activity"`
	LastUpdated        time.Time              `json:"last_updated"`
}

// BrandSummaryData represents brand summary for dashboard
type BrandSummaryData struct {
	TotalBrands       int     `json:"total_brands"`
	ActiveBrands      int     `json:"active_brands"`
	TotalSearchVolume int64   `json:"total_search_volume"`
	AverageVisibility float64 `json:"average_visibility"`
	TrendDirection    string  `json:"trend_direction"`
	ChangePercentage  float64 `json:"change_percentage"`
}

// SearchMetricsData represents search metrics for dashboard
type SearchMetricsData struct {
	TotalSearches int64            `json:"total_searches"`
	SearchRate    float64          `json:"search_rate"`
	TopKeywords   []KeywordMetric  `json:"top_keywords"`
	RegionalData  []RegionalMetric `json:"regional_data"`
	TrendData     []TrendDataPoint `json:"trend_data"`
}

// AIAppearanceData represents AI appearance metrics
type AIAppearanceData struct {
	TotalAppearances  int64                `json:"total_appearances"`
	AppearanceRate    float64              `json:"appearance_rate"`
	TopPlatforms      []PlatformMetric     `json:"top_platforms"`
	SentimentAnalysis SentimentData        `json:"sentiment_analysis"`
	FrequencyData     []FrequencyDataPoint `json:"frequency_data"`
}

// CompetitorAnalysisData represents competitor analysis
type CompetitorAnalysisData struct {
	MarketPosition    int                     `json:"market_position"`
	MarketShare       float64                 `json:"market_share"`
	TopCompetitors    []CompetitorMetric      `json:"top_competitors"`
	CompetitiveGaps   []CompetitiveGap        `json:"competitive_gaps"`
	BrandDistribution []BrandDistributionItem `json:"brand_distribution"`
}

// Supporting data structures
type ActivityItem struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Timestamp   time.Time              `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

type KeywordMetric struct {
	Keyword   string  `json:"keyword"`
	Volume    int64   `json:"volume"`
	Frequency float64 `json:"frequency"`
	Trend     string  `json:"trend"`
}

type RegionalMetric struct {
	Region     string  `json:"region"`
	Volume     int64   `json:"volume"`
	Percentage float64 `json:"percentage"`
}

type TrendDataPoint struct {
	Date  time.Time `json:"date"`
	Value float64   `json:"value"`
	Label string    `json:"label,omitempty"`
}

type PlatformMetric struct {
	Platform    string  `json:"platform"`
	Appearances int64   `json:"appearances"`
	Percentage  float64 `json:"percentage"`
}

type SentimentData struct {
	Positive float64 `json:"positive"`
	Neutral  float64 `json:"neutral"`
	Negative float64 `json:"negative"`
}

type FrequencyDataPoint struct {
	Date      time.Time `json:"date"`
	Frequency float64   `json:"frequency"`
	Platform  string    `json:"platform"`
}

type CompetitorMetric struct {
	Name        string  `json:"name"`
	MarketShare float64 `json:"market_share"`
	SearchShare float64 `json:"search_share"`
	AIShare     float64 `json:"ai_share"`
	Trend       string  `json:"trend"`
}

type CompetitiveGap struct {
	Area        string  `json:"area"`
	Gap         float64 `json:"gap"`
	Opportunity string  `json:"opportunity"`
}

type BrandDistributionItem struct {
	Brand      string  `json:"brand"`
	Percentage float64 `json:"percentage"`
	Color      string  `json:"color"`
}

// Export types
type ExportRequest struct {
	Format   string       `json:"format" binding:"required"`
	DataType string       `json:"data_type" binding:"required"`
	Filters  FilterParams `json:"filters"`
	Columns  []string     `json:"columns,omitempty"`
	FileName string       `json:"file_name,omitempty"`
}

type ExportResponse struct {
	DownloadURL string    `json:"download_url"`
	FileName    string    `json:"file_name"`
	FileSize    int64     `json:"file_size"`
	ExpiresAt   time.Time `json:"expires_at"`
}
