load("@rules_go//go:def.bzl", "go_library")

# This is a meta package that aggregates all pkg packages
go_library(
    name = "pkg",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/geok_center/pkg/errors",
        "//golangp/apps/geok_center/pkg/response",
        "//golangp/apps/geok_center/pkg/types",
        "//golangp/apps/geok_center/pkg/upload",
        "//golangp/apps/geok_center/pkg/utils",
    ],
)
