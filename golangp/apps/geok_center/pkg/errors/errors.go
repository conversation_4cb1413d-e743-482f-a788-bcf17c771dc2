/*
 * @Description: Unified error handling system for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package errors

import (
	"fmt"
	"net/http"
	"time"
)

// ErrorCode represents standardized error codes
type ErrorCode string

const (
	// Authentication errors
	ErrCodeAuthRequired       ErrorCode = "AUTH_REQUIRED"
	ErrCodeInvalidToken       ErrorCode = "INVALID_TOKEN"
	ErrCodeTokenExpired       ErrorCode = "TOKEN_EXPIRED"
	ErrCodeInvalidCredentials ErrorCode = "INVALID_CREDENTIALS"
	ErrCodeUserNotFound       ErrorCode = "USER_NOT_FOUND"
	ErrCodeUserExists         ErrorCode = "USER_EXISTS"
	ErrCodePermissionDenied   ErrorCode = "PERMISSION_DENIED"

	// Validation errors
	ErrCodeInvalidParams    ErrorCode = "INVALID_PARAMS"
	ErrCodeMissingParams    ErrorCode = "MISSING_PARAMS"
	ErrCodeInvalidFormat    ErrorCode = "INVALID_FORMAT"
	ErrCodeValidationFailed ErrorCode = "VALIDATION_FAILED"

	// Resource errors
	ErrCodeResourceNotFound ErrorCode = "RESOURCE_NOT_FOUND"
	ErrCodeResourceExists   ErrorCode = "RESOURCE_EXISTS"
	ErrCodeResourceConflict ErrorCode = "RESOURCE_CONFLICT"
	ErrCodeResourceLocked   ErrorCode = "RESOURCE_LOCKED"

	// Business logic errors
	ErrCodeBusinessLogic     ErrorCode = "BUSINESS_LOGIC_ERROR"
	ErrCodeInsufficientQuota ErrorCode = "INSUFFICIENT_QUOTA"
	ErrCodeOperationFailed   ErrorCode = "OPERATION_FAILED"

	// System errors
	ErrCodeInternalError      ErrorCode = "INTERNAL_ERROR"
	ErrCodeDatabaseError      ErrorCode = "DATABASE_ERROR"
	ErrCodeCacheError         ErrorCode = "CACHE_ERROR"
	ErrCodeExternalService    ErrorCode = "EXTERNAL_SERVICE_ERROR"
	ErrCodeRateLimitExceeded  ErrorCode = "RATE_LIMIT_EXCEEDED"
	ErrCodeServiceUnavailable ErrorCode = "SERVICE_UNAVAILABLE"

	// Data processing errors
	ErrCodeDataProcessing ErrorCode = "DATA_PROCESSING_ERROR"
	ErrCodeInvalidData    ErrorCode = "INVALID_DATA"
	ErrCodeDataCorrupted  ErrorCode = "DATA_CORRUPTED"
)

// APIError represents a structured API error
type APIError struct {
	Code       ErrorCode   `json:"code"`
	Message    string      `json:"message"`
	Details    interface{} `json:"details,omitempty"`
	Timestamp  time.Time   `json:"timestamp"`
	Path       string      `json:"path,omitempty"`
	RequestID  string      `json:"request_id,omitempty"`
	StatusCode int         `json:"-"`
}

// Error implements the error interface
func (e *APIError) Error() string {
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// ErrorResponse represents the standard error response format
type ErrorResponse struct {
	Success   bool      `json:"success"`
	Error     *APIError `json:"error"`
	Timestamp time.Time `json:"timestamp"`
	Path      string    `json:"path,omitempty"`
	RequestID string    `json:"request_id,omitempty"`
}

// New creates a new APIError
func New(code ErrorCode, message string) *APIError {
	return &APIError{
		Code:       code,
		Message:    message,
		Timestamp:  time.Now(),
		StatusCode: getDefaultStatusCode(code),
	}
}

// NewWithDetails creates a new APIError with additional details
func NewWithDetails(code ErrorCode, message string, details interface{}) *APIError {
	return &APIError{
		Code:       code,
		Message:    message,
		Details:    details,
		Timestamp:  time.Now(),
		StatusCode: getDefaultStatusCode(code),
	}
}

// WithStatusCode sets a custom status code
func (e *APIError) WithStatusCode(statusCode int) *APIError {
	e.StatusCode = statusCode
	return e
}

// WithPath sets the request path
func (e *APIError) WithPath(path string) *APIError {
	e.Path = path
	return e
}

// WithRequestID sets the request ID
func (e *APIError) WithRequestID(requestID string) *APIError {
	e.RequestID = requestID
	return e
}

// getDefaultStatusCode returns the default HTTP status code for an error code
func getDefaultStatusCode(code ErrorCode) int {
	switch code {
	case ErrCodeAuthRequired, ErrCodeInvalidToken, ErrCodeTokenExpired, ErrCodeInvalidCredentials:
		return http.StatusUnauthorized
	case ErrCodePermissionDenied:
		return http.StatusForbidden
	case ErrCodeInvalidParams, ErrCodeMissingParams, ErrCodeInvalidFormat, ErrCodeValidationFailed, ErrCodeInvalidData:
		return http.StatusBadRequest
	case ErrCodeResourceNotFound, ErrCodeUserNotFound:
		return http.StatusNotFound
	case ErrCodeResourceExists, ErrCodeUserExists, ErrCodeResourceConflict:
		return http.StatusConflict
	case ErrCodeResourceLocked:
		return http.StatusLocked
	case ErrCodeRateLimitExceeded:
		return http.StatusTooManyRequests
	case ErrCodeServiceUnavailable:
		return http.StatusServiceUnavailable
	case ErrCodeInternalError, ErrCodeDatabaseError, ErrCodeCacheError, ErrCodeExternalService, ErrCodeDataProcessing, ErrCodeDataCorrupted:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

// Predefined common errors
var (
	ErrAuthRequired       = New(ErrCodeAuthRequired, "Authentication required")
	ErrInvalidToken       = New(ErrCodeInvalidToken, "Invalid authentication token")
	ErrTokenExpired       = New(ErrCodeTokenExpired, "Authentication token has expired")
	ErrInvalidCredentials = New(ErrCodeInvalidCredentials, "Invalid username or password")
	ErrUserNotFound       = New(ErrCodeUserNotFound, "User not found")
	ErrUserExists         = New(ErrCodeUserExists, "User already exists")
	ErrPermissionDenied   = New(ErrCodePermissionDenied, "Permission denied")

	ErrInvalidParams    = New(ErrCodeInvalidParams, "Invalid request parameters")
	ErrMissingParams    = New(ErrCodeMissingParams, "Missing required parameters")
	ErrValidationFailed = New(ErrCodeValidationFailed, "Validation failed")

	ErrResourceNotFound = New(ErrCodeResourceNotFound, "Resource not found")
	ErrResourceExists   = New(ErrCodeResourceExists, "Resource already exists")
	ErrResourceConflict = New(ErrCodeResourceConflict, "Resource conflict")

	ErrInternalError      = New(ErrCodeInternalError, "Internal server error")
	ErrDatabaseError      = New(ErrCodeDatabaseError, "Database operation failed")
	ErrCacheError         = New(ErrCodeCacheError, "Cache operation failed")
	ErrRateLimitExceeded  = New(ErrCodeRateLimitExceeded, "Rate limit exceeded")
	ErrServiceUnavailable = New(ErrCodeServiceUnavailable, "Service temporarily unavailable")
)

// Helper functions for common error scenarios

// NewValidationError creates a validation error with field details
func NewValidationError(field string, message string) *APIError {
	return NewWithDetails(ErrCodeValidationFailed, "Validation failed", map[string]string{
		"field":   field,
		"message": message,
	})
}

// NewNotFoundError creates a not found error for a specific resource
func NewNotFoundError(resource string, id string) *APIError {
	return NewWithDetails(ErrCodeResourceNotFound, fmt.Sprintf("%s not found", resource), map[string]string{
		"resource": resource,
		"id":       id,
	})
}

// NewConflictError creates a conflict error for a specific resource
func NewConflictError(resource string, field string, value string) *APIError {
	return NewWithDetails(ErrCodeResourceConflict, fmt.Sprintf("%s already exists", resource), map[string]string{
		"resource": resource,
		"field":    field,
		"value":    value,
	})
}

// NewBusinessLogicError creates a business logic error
func NewBusinessLogicError(message string) *APIError {
	return New(ErrCodeBusinessLogic, message)
}

// WrapError wraps a standard error into an APIError
func WrapError(err error, code ErrorCode, message string) *APIError {
	return NewWithDetails(code, message, map[string]string{
		"original_error": err.Error(),
	})
}

// IsAPIError checks if an error is an APIError
func IsAPIError(err error) bool {
	_, ok := err.(*APIError)
	return ok
}

// FromError converts a standard error to APIError
func FromError(err error) *APIError {
	if apiErr, ok := err.(*APIError); ok {
		return apiErr
	}
	return WrapError(err, ErrCodeInternalError, "Internal server error")
}
