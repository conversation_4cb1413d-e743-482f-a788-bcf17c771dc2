/*
 * @Description: Unified response package for GEOK Center API
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package response

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// StandardResponse represents the unified API response structure
type StandardResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Error     string      `json:"error,omitempty"`
	Details   interface{} `json:"details,omitempty"`
	Meta      *Meta       `json:"meta,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}

// Meta represents metadata for paginated responses
type Meta struct {
	Page       int `json:"page"`
	PageSize   int `json:"page_size"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}

// Success sends a successful response with data
func Success(c *gin.Context, statusCode int, message string, data interface{}) {
	response := StandardResponse{
		Success:   true,
		Message:   message,
		Data:      data,
		Timestamp: time.Now(),
	}
	c.<PERSON>(statusCode, response)
}

// SuccessWithMeta sends a successful response with data and pagination metadata
func SuccessWithMeta(c *gin.Context, statusCode int, message string, data interface{}, meta *Meta) {
	response := StandardResponse{
		Success:   true,
		Message:   message,
		Data:      data,
		Meta:      meta,
		Timestamp: time.Now(),
	}
	c.JSON(statusCode, response)
}

// Error sends an error response
func Error(c *gin.Context, statusCode int, errorMessage string) {
	response := StandardResponse{
		Success:   false,
		Error:     errorMessage,
		Timestamp: time.Now(),
	}
	c.JSON(statusCode, response)
}

// ErrorWithDetails sends an error response with additional details
func ErrorWithDetails(c *gin.Context, statusCode int, errorMessage string, details interface{}) {
	response := StandardResponse{
		Success:   false,
		Error:     errorMessage,
		Details:   details,
		Timestamp: time.Now(),
	}
	c.JSON(statusCode, response)
}

// Created sends a 201 Created response
func Created(c *gin.Context, message string, data interface{}) {
	Success(c, http.StatusCreated, message, data)
}

// OK sends a 200 OK response
func OK(c *gin.Context, message string, data interface{}) {
	Success(c, http.StatusOK, message, data)
}

// OKWithMeta sends a 200 OK response with pagination metadata
func OKWithMeta(c *gin.Context, message string, data interface{}, meta *Meta) {
	SuccessWithMeta(c, http.StatusOK, message, data, meta)
}

// BadRequest sends a 400 Bad Request response
func BadRequest(c *gin.Context, message string) {
	Error(c, http.StatusBadRequest, message)
}

// BadRequestWithDetails sends a 400 Bad Request response with details
func BadRequestWithDetails(c *gin.Context, message string, details interface{}) {
	ErrorWithDetails(c, http.StatusBadRequest, message, details)
}

// Unauthorized sends a 401 Unauthorized response
func Unauthorized(c *gin.Context, message string) {
	Error(c, http.StatusUnauthorized, message)
}

// Forbidden sends a 403 Forbidden response
func Forbidden(c *gin.Context, message string) {
	Error(c, http.StatusForbidden, message)
}

// NotFound sends a 404 Not Found response
func NotFound(c *gin.Context, message string) {
	Error(c, http.StatusNotFound, message)
}

// Conflict sends a 409 Conflict response
func Conflict(c *gin.Context, message string) {
	Error(c, http.StatusConflict, message)
}

// InternalServerError sends a 500 Internal Server Error response
func InternalServerError(c *gin.Context, message string) {
	Error(c, http.StatusInternalServerError, message)
}

// InternalServerErrorWithDetails sends a 500 Internal Server Error response with details
func InternalServerErrorWithDetails(c *gin.Context, message string, details interface{}) {
	ErrorWithDetails(c, http.StatusInternalServerError, message, details)
}

// ValidationError sends a 400 Bad Request response for validation errors
func ValidationError(c *gin.Context, details interface{}) {
	ErrorWithDetails(c, http.StatusBadRequest, "Validation failed", details)
}

// ServiceUnavailable sends a 503 Service Unavailable response
func ServiceUnavailable(c *gin.Context, message string) {
	Error(c, http.StatusServiceUnavailable, message)
}

// Custom sends a custom response with specified status code
func Custom(c *gin.Context, statusCode int, success bool, message string, data interface{}, errorMsg string, details interface{}) {
	response := StandardResponse{
		Success:   success,
		Message:   message,
		Data:      data,
		Error:     errorMsg,
		Details:   details,
		Timestamp: time.Now(),
	}
	c.JSON(statusCode, response)
}

// CalculateMeta calculates pagination metadata
func CalculateMeta(page, perPage, total int) *Meta {
	totalPages := (total + perPage - 1) / perPage
	if totalPages < 1 {
		totalPages = 1
	}

	return &Meta{
		Page:       page,
		PageSize:   perPage,
		Total:      total,
		TotalPages: totalPages,
	}
}

// Data sends a successful response with only data (for backward compatibility)
func Data(c *gin.Context, statusCode int, data interface{}) {
	Success(c, statusCode, "", data)
}

// NoContent sends a 204 No Content response
func NoContent(c *gin.Context) {
	c.Status(http.StatusNoContent)
}
