# GEOK Center Application

GEOK Center 是一个基于 Go 语言开发的品牌可见性和 AI 出现频率分析平台，提供全面的品牌监控、搜索趋势分析和竞争对手分析功能。

## 功能特性

### 🎯 核心功能

- **品牌管理**: 创建、更新、删除和搜索品牌信息
- **仪表板分析**: 综合的品牌可见性和搜索趋势数据
- **AI 出现分析**: 监控品牌在 AI 平台中的出现频率和情感分析
- **竞争对手分析**: 品牌在市场中的分布和竞争地位
- **数据导出**: 支持 CSV、JSON 等格式的数据导出

### 📊 分析功能

- 品牌搜索率和可见性指标
- 区域性数据分析
- AI 平台出现频率统计
- 情感分析和趋势预测
- 实时活动监控

## 技术栈

- **后端**: Go 1.23+ with Gin Framework
- **数据库**: PostgreSQL
- **ORM**: GORM
- **架构**: 分层架构 (Handlers → Services → Models)

## 项目结构

```
golangp/apps/geok_center/
├── cmd/
│   └── main.go                 # 应用入口点
├── internal/
│   ├── config/                 # 配置管理
│   ├── handlers/               # HTTP处理器
│   ├── services/               # 业务逻辑层
│   ├── models/                 # 数据模型
│   ├── middleware/             # 中间件
│   └── routes/                 # 路由配置
├── pkg/
│   ├── types/                  # 公共类型定义
│   └── utils/                  # 工具函数
├── docs/                       # 文档
├── scripts/                    # 脚本文件
├── app.env.example            # 环境变量示例
├── BUILD.bazel                # Bazel构建文件
└── README.md                  # 项目说明
```

## 快速开始

### 1. 环境准备

确保您的系统已安装：

- Go 1.23+
- PostgreSQL 12+
- Git

### 2. 配置环境变量

复制环境变量示例文件并根据您的环境进行配置：

```bash
cp app.env.example app.env
```

编辑 `app.env` 文件，设置数据库连接和其他配置：

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=your_password
DB_NAME=geok_center
DB_SSL_MODE=disable

# 服务器配置
SERVER_PORT=8080
ENVIRONMENT=development

# JWT配置
JWT_SECRET=your-super-secret-key
```

### 3. 数据库设置

创建 PostgreSQL 数据库：

```sql
CREATE DATABASE geok_center;
```

### 4. 数据库迁移

首先运行数据库迁移：

```bash
# 使用Bazel运行迁移（推荐）
bazel run //golangp/apps/geok_center:geok_center -- migrate --seed

# 或使用Go直接运行
cd golangp/apps/geok_center
go run cmd/*.go migrate --seed
```

### 5. 运行应用

启动 HTTP 服务器：

```bash
# 使用Bazel构建和运行（推荐）
bazel run //golangp/apps/geok_center:geok_center -- server

# 或使用Go直接运行
cd golangp/apps/geok_center
go run cmd/*.go server --port 8080
```

### 6. 验证安装

访问健康检查端点：

```bash
curl http://localhost:8080/api/v1/health
```

应该返回：

```json
{
  "status": "ok",
  "service": "geok_center",
  "version": "1.0.0"
}
```

## CLI 命令

GEOK Center 提供了命令行界面来管理应用：

### 查看帮助

```bash
# 查看所有可用命令
bazel run //golangp/apps/geok_center:geok_center -- --help

# 查看特定命令的帮助
bazel run //golangp/apps/geok_center:geok_center -- server --help
bazel run //golangp/apps/geok_center:geok_center -- migrate --help
```

### 数据库迁移命令

```bash
# 基本迁移
bazel run //golangp/apps/geok_center:geok_center -- migrate

# 迁移并填充示例数据
bazel run //golangp/apps/geok_center:geok_center -- migrate --seed

# 删除所有表后重新迁移（危险操作）
bazel run //golangp/apps/geok_center:geok_center -- migrate --drop --seed
```

### 服务器命令

```bash
# 使用默认配置启动服务器
bazel run //golangp/apps/geok_center:geok_center -- server

# 指定端口启动服务器
bazel run //golangp/apps/geok_center:geok_center -- server --port 9090

# 指定主机和端口
bazel run //golangp/apps/geok_center:geok_center -- server --host 127.0.0.1 --port 8080
```

## API 文档

### 品牌管理 API

- `GET /api/v1/brands` - 获取品牌列表
- `GET /api/v1/brands/:id` - 获取单个品牌
- `POST /api/v1/brands` - 创建品牌
- `PUT /api/v1/brands/:id` - 更新品牌
- `DELETE /api/v1/brands/:id` - 删除品牌
- `GET /api/v1/brands/search?q=keyword` - 搜索品牌

### 分析 API

- `GET /api/v1/analytics/dashboard` - 获取仪表板数据
- `GET /api/v1/analytics/brand-distribution` - 获取品牌分布数据
- `GET /api/v1/analytics/search-trends` - 获取搜索趋势
- `GET /api/v1/analytics/ai-appearance` - 获取 AI 出现指标
- `GET /api/v1/analytics/competitor-analysis` - 获取竞争对手分析

### 导出 API

- `POST /api/v1/exports/dashboard` - 导出仪表板数据
- `POST /api/v1/exports/analytics` - 导出分析数据
- `GET /api/v1/exports/download/:filename` - 下载导出文件

## 开发指南

### 添加新功能

1. 在 `internal/models/` 中定义数据模型
2. 在 `internal/services/` 中实现业务逻辑
3. 在 `internal/handlers/` 中创建 HTTP 处理器
4. 在 `internal/routes/` 中添加路由配置

### 代码规范

- 遵循 Go 官方代码规范
- 使用有意义的变量和函数名
- 添加适当的注释和文档
- 编写单元测试

## 部署

### Docker 部署

```dockerfile
# 示例Dockerfile
FROM golang:1.23-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o geok_center cmd/main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/geok_center .
CMD ["./geok_center"]
```

### 环境变量

生产环境中请确保设置以下关键环境变量：

- `ENVIRONMENT=production`
- `JWT_SECRET=强密码`
- `DB_PASSWORD=数据库密码`
- `DB_SSL_MODE=require`

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

本项目采用 MIT 许可证。
