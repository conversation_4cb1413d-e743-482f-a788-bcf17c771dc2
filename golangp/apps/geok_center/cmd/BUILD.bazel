load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "cmd",
    srcs = [
        "main.go",
        "migrate.go",
        "mockdata.go",
        "server.go",
    ],
    importpath = "pointer/golangp/apps/geok_center/cmd",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/geok_center/internal/config",
        "//golangp/apps/geok_center/internal/models",
        "//golangp/apps/geok_center/internal/routes",
        "//golangp/apps/geok_center/mockdata",
        "//golangp/apps/geok_center/pkg/services/manager",
        "//golangp/common/database/postgres",
        "//golangp/common/logging:logger",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_google_uuid//:uuid",
        "@com_github_spf13_cobra//:cobra",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
    ],
)
