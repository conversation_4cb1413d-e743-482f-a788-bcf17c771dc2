/*
 * @Description: Mock data command for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package main

import (
	"log"

	"pointer/golangp/apps/geok_center/internal/config"
	"pointer/golangp/apps/geok_center/mockdata"
	"pointer/golangp/common/database/postgres"
	"pointer/golangp/common/logging"

	"github.com/spf13/cobra"
)

var mockdataCmd = &cobra.Command{
	Use:   "mockdata",
	Short: "Load mock data into the database",
	Long:  `Load mock data into the database for development and testing purposes.`,
	Run:   runMockdata,
}

func runMockdata(cmd *cobra.Command, args []string) {
	log.Println("📊 Loading mock data...")

	// Load configuration
	cfg := config.Load()

	// Initialize logging system
	logging.InitLogger(&logging.LoggerConfig{
		Level:    logging.INFO,
		LogPath:  cfg.LogPath,
		FileName: "geok_center_mockdata",
		Env:      cfg.Environment,
	})

	// Use our logging system from now on
	logging.Info("📊 Starting mock data loading...")
	logging.Info("📋 Configuration loaded - Environment: %s", cfg.Environment)

	// Connect to database
	logging.Info("🔌 Connecting to database...")
	postgres.ConnectDB(&postgres.Config{
		DBHost:     cfg.DBHost,
		DBUserName: cfg.DBUsername,
		DBPassword: cfg.DBPassword,
		DBName:     cfg.DBName,
		DBPort:     cfg.DBPort,
		DBSslMode:  cfg.DBSSLMode,
	})
	logging.Info("✅ Database connected successfully")

	// Load mock data
	logging.Info("📊 Loading mock data...")
	if err := mockdata.LoadAllMockData(); err != nil {
		logging.Fatal("❌ Failed to load mock data: %v", err)
	}

	logging.Info("🎉 Mock data loaded successfully!")
}
