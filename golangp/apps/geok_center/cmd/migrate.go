/*
 * @Description: Database migration command for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package main

import (
	"fmt"
	"log"

	"pointer/golangp/apps/geok_center/internal/config"
	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/mockdata"
	"pointer/golangp/common/database/postgres"

	"github.com/spf13/cobra"
	"gorm.io/gorm"
)

var migrateCmd = &cobra.Command{
	Use:   "migrate",
	Short: "Run database migrations",
	Long:  `Run database migrations to create tables, indexes, and seed initial data for GEOK Center.`,
	Run:   runMigrate,
}

var (
	seedData      bool
	dropTables    bool
	mockData      bool
	mockBlogData  bool
	cleanMockData bool
)

func init() {
	migrateCmd.Flags().BoolVarP(&seedData, "seed", "s", false, "Seed initial data after migration")
	migrateCmd.Flags().BoolVarP(&dropTables, "drop", "d", false, "Drop existing tables before migration (DANGEROUS)")
	migrateCmd.Flags().BoolVarP(&mockData, "mock", "m", false, "Generate brand-centric mock data into database")
	migrateCmd.Flags().BoolVarP(&mockBlogData, "mockblog", "b", false, "Generate blog mock data only")
	migrateCmd.Flags().BoolVarP(&cleanMockData, "clean-mock", "c", false, "Clean all mock data from database")
}

func runMigrate(cmd *cobra.Command, args []string) {
	log.Println("🚀 Starting database migration...")

	// Load configuration
	cfg := config.Load()

	// Connect to database
	postgres.ConnectDB(&postgres.Config{
		DBHost:     cfg.DBHost,
		DBUserName: cfg.DBUsername,
		DBPassword: cfg.DBPassword,
		DBName:     cfg.DBName,
		DBPort:     cfg.DBPort,
		DBSslMode:  cfg.DBSSLMode,
	})

	// Migrate tables using the improved approach
	migrateTables()

	// Clean mock data if requested
	if cleanMockData {
		if err := mockdata.CleanAllMockData(); err != nil {
			log.Fatalf("❌ Failed to clean mock data: %v", err)
		}
	}

	// Load mock data if requested
	if mockData {
		if err := mockdata.LoadAllMockData(); err != nil {
			log.Fatalf("❌ Failed to load mock data: %v", err)
		}
	}

	log.Println("🎉 Database setup completed!")
}

// migrateTables 自动迁移表结构
func migrateTables() {
	// Ensure the uuid-ossp extension is created
	if err := postgres.DB.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";").Error; err != nil {
		log.Fatalf("❌ Failed to create uuid-ossp extension: %v", err)
	}
	log.Println("📊 Starting table migrations...")

	// migrate 是一个辅助函数，用于迁移单个表
	migrate := func(table interface{}, comment string, skipIfExists bool) {
		// 获取表名
		stmt := &gorm.Statement{DB: postgres.DB}
		stmt.Parse(table)
		tableName := stmt.Schema.Table

		if skipIfExists && postgres.DB.Migrator().HasTable(table) {
			log.Printf("⚠️ 表已存在，跳过迁移: %s", tableName)
			return
		}

		// 设置表选项（PostgreSQL使用不同的语法）
		if err := postgres.DB.Set("gorm:table_options", "").AutoMigrate(table); err != nil {
			log.Fatalf("❌ 迁移失败 [%s]: %v", tableName, err)
		}

		// 为表添加注释（PostgreSQL语法）
		if comment != "" {
			// 使用双引号包围表名以处理保留关键字
			commentSQL := fmt.Sprintf("COMMENT ON TABLE \"%s\" IS '%s'", tableName, comment)
			if err := postgres.DB.Exec(commentSQL).Error; err != nil {
				log.Printf("⚠️ 添加表注释失败 [%s]: %v", tableName, err)
			}
		}

		log.Printf("✅ 表迁移成功: %s", tableName)
	}

	// 用户相关表 - Force migration for User table to add new fields
	migrate(&models.User{}, "用户表", false) // Force migration to add google_email field
	migrate(&models.UserSession{}, "用户会话表", true)
	migrate(&models.Subscription{}, "用户订阅表", true)

	// 品牌相关表
	migrate(&models.Brand{}, "品牌表", true)

	// 新的业务流程表
	// AI搜索相关表
	migrate(&models.AISearch{}, "AI搜索表", true)
	migrate(&models.AISearchResponse{}, "AI搜索响应表", true)
	migrate(&models.AISearchBatch{}, "AI搜索批次表", true)

	// AI可见性指标表 (统一的可见性指标系统)
	migrate(&models.AIVisibilityMetrics{}, "AI可见性指标表", true)
	migrate(&models.AIVisibilityAggregation{}, "AI可见性聚合表", true)

	// 重构后的提示表
	migrate(&models.Prompt{}, "提示表", true)
	migrate(&models.PromptMetric{}, "提示指标表", true)
	migrate(&models.PromptKeyword{}, "提示关键词表", true)

	// 引用表
	migrate(&models.Reference{}, "引用表", true)
	migrate(&models.ReferenceAnalysis{}, "引用分析表", true)

	// GEO优化表 - Force migration to add new fields
	migrate(&models.GEOOptimization{}, "GEO优化表", false)
	migrate(&models.GEODatabase{}, "GEO数据库表", true)

	// 通知系统表
	migrate(&models.Notification{}, "通知表", true)
	migrate(&models.NotificationSetting{}, "通知设置表", true)

	// 博客内容相关表
	migrate(&models.Blog{}, "博客内容表", true)

	log.Println("✅ 所有表的数据库迁移已完成")
}
