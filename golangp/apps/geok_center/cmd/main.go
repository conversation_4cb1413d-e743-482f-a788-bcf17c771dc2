/*
 * @Description: GEOK Center Application - CLI Entry Point
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package main

import (
	"os"

	"github.com/spf13/cobra"
)

var rootCmd = &cobra.Command{
	Use:   "geok_center",
	Short: "GEOK Center is a brand visibility and AI appearance analysis platform",
	Long:  `GEOK Center is a comprehensive brand monitoring platform that provides analytics for brand visibility, search trends, and AI appearance frequency.`,
}

func init() {
	rootCmd.AddCommand(serverCmd)
	rootCmd.AddCommand(migrateCmd)
	rootCmd.AddCommand(mockdataCmd)
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}
