/*
 * @Description: Server command for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package main

import (
	"log"
	"os"

	"pointer/golangp/apps/geok_center/internal/config"
	"pointer/golangp/apps/geok_center/internal/routes"
	"pointer/golangp/apps/geok_center/pkg/services/manager"
	"pointer/golangp/common/database/postgres"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cobra"
)

var serverCmd = &cobra.Command{
	Use:   "server",
	Short: "Start the GEOK Center HTTP server",
	Long:  `Start the GEOK Center HTTP server with REST API endpoints for brand analytics and data management.`,
	Run:   runServer,
}

var (
	serverPort string
	serverHost string
)

func init() {
	serverCmd.Flags().StringVarP(&serverPort, "port", "p", "", "Port to run the server on (overrides config)")
	serverCmd.Flags().StringVarP(&serverHost, "host", "H", "", "Host to bind the server to")
}

func runServer(cmd *cobra.Command, args []string) {
	log.Println("🚀 Starting GEOK Center server...")

	// Load configuration
	cfg := config.Load()

	// Initialize logging system
	logging.InitLogger(&logging.LoggerConfig{
		Level:    logging.INFO,
		LogPath:  cfg.LogPath,
		FileName: "geok_center",
		Env:      cfg.Environment,
	})

	// Use our logging system from now on
	logging.Info("🚀 GEOK Center server starting...")
	logging.Info("📋 Configuration loaded - Environment: %s, LogPath: %s", cfg.Environment, cfg.LogPath)

	// Connect to database
	logging.Info("🔌 Connecting to database...")
	postgres.ConnectDB(&postgres.Config{
		DBHost:     cfg.DBHost,
		DBUserName: cfg.DBUsername,
		DBPassword: cfg.DBPassword,
		DBName:     cfg.DBName,
		DBPort:     cfg.DBPort,
		DBSslMode:  cfg.DBSSLMode,
	})
	logging.Info("✅ Database connected successfully")

	// Initialize global services
	logging.Info("🔧 Initializing services...")
	if err := manager.Initialize(cfg); err != nil {
		logging.Fatal("❌ Failed to initialize services: %v", err)
	}
	logging.Info("✅ Services initialized successfully")

	// Setup Gin mode
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
		logging.Info("🔧 Gin set to release mode")
	} else {
		logging.Info("🔧 Gin set to debug mode")
	}

	// Create router
	router := gin.New()
	logging.Info("🔧 Gin router created")

	// Setup all routes
	routes.SetupRoutes(router, postgres.DB, cfg.GoogleClientID, cfg.GoogleClientSecret)
	logging.Info("🔧 Routes configured")

	// Determine port
	port := serverPort
	if port == "" {
		port = os.Getenv("PORT")
	}
	if port == "" {
		port = cfg.ServerPort
	}

	// Determine host
	host := serverHost
	if host == "" {
		host = "0.0.0.0"
	}

	// Start server
	address := host + ":" + port
	logging.Info("🚀 GEOK Center server starting on %s", address)
	logging.Info("📊 Environment: %s", cfg.Environment)
	logging.Info("🗄️  Database: %s:%s/%s", cfg.DBHost, cfg.DBPort, cfg.DBName)
	logging.Info("🌐 API Documentation: http://%s/api/v1/health", address)

	if err := router.Run(address); err != nil {
		logging.Fatal("❌ Failed to start server: %v", err)
	}
}
