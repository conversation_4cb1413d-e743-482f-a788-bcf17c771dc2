/*
 * @Description: User authentication middleware for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package middleware

import (
	"net/http"
	"strings"

	"pointer/golangp/apps/geok_center/internal/config"
	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/common/database/postgres"
	"pointer/golangp/common/utils"

	"github.com/gin-gonic/gin"
)

// DeserializeUser returns a gin.HandlerFunc for session token authentication
func DeserializeUser() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var accessToken string

		authorizationHeader := ctx.Request.Header.Get("Authorization")
		fields := strings.Fields(authorizationHeader)

		// 检查 fields 的长度
		if len(fields) == 2 && fields[0] == "Bearer" && fields[1] != "undefined" {
			accessToken = fields[1]
		} else {
			cookie, err := ctx.Cookie("access_token")
			if err == nil {
				// Clean the cookie value - remove any trailing semicolon and whitespace
				accessToken = strings.TrimSpace(strings.Split(cookie, ";")[0])
			}
		}

		if accessToken == "" {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"status": "fail", "message": "You are not logged in"})
			return
		}

		// Validate JWT token using common utils
		cfg := config.Load()
		payload, err := utils.ValidateToken(accessToken, cfg.AccessTokenPublicKey)
		if err != nil {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"status": "fail", "message": "Invalid or expired token"})
			return
		}

		// Extract user information from token payload
		payloadMap, ok := payload.(map[string]interface{})
		if !ok {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"status": "fail", "message": "Invalid token payload"})
			return
		}

		userID, ok := payloadMap["user_id"].(string)
		if !ok {
			ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"status": "fail", "message": "Invalid user ID in token"})
			return
		}

		// Get user from database to ensure they still exist
		var user models.User
		result := postgres.DB.First(&user, "id = ?", userID)
		if result.Error != nil {
			ctx.AbortWithStatusJSON(http.StatusForbidden, gin.H{"status": "fail", "message": "The user belonging to this token no longer exists"})
			return
		}

		ctx.Set("currentUser", user)
		ctx.Set("user_id", user.ID)
		ctx.Set("role", string(user.Role))
		ctx.Next()
	}
}
