/*
 * @Description: Middleware components for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package middleware

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"pointer/golangp/apps/geok_center/pkg/errors"

	"github.com/gin-gonic/gin"
)

// CORS returns a gin.HandlerFunc for handling CORS
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON><PERSON>("Access-Control-Allow-Origin", "*")
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length")
		c.<PERSON>er("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// ErrorHandler returns a gin.HandlerFunc for handling errors
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Handle any errors that occurred during request processing
		if len(c.Errors) > 0 {
			ginErr := c.Errors.Last()

			// Get request context information
			requestID, _ := c.Get("request_id")
			requestIDStr := ""
			if requestID != nil {
				requestIDStr = requestID.(string)
			}
			path := c.Request.URL.Path

			var apiErr *errors.APIError

			// Check if the error is already an APIError
			if errors.IsAPIError(ginErr.Err) {
				apiErr = ginErr.Err.(*errors.APIError)
			} else {
				// Convert gin error to APIError based on type
				switch ginErr.Type {
				case gin.ErrorTypeBind:
					apiErr = errors.ErrInvalidParams.WithPath(path).WithRequestID(requestIDStr)
				case gin.ErrorTypePublic:
					apiErr = errors.New(errors.ErrCodeBusinessLogic, ginErr.Error()).WithPath(path).WithRequestID(requestIDStr)
				default:
					apiErr = errors.ErrInternalError.WithPath(path).WithRequestID(requestIDStr)
					log.Printf("Internal error [%s]: %v", requestIDStr, ginErr.Error())
				}
			}

			// Ensure path and request ID are set
			if apiErr.Path == "" {
				apiErr.Path = path
			}
			if apiErr.RequestID == "" {
				apiErr.RequestID = requestIDStr
			}

			// Send structured error response
			SendErrorResponse(c, apiErr)
		}
	}
}

// RateLimiter returns a gin.HandlerFunc for rate limiting
func RateLimiter() gin.HandlerFunc {
	// This is a simple in-memory rate limiter
	// In production, you might want to use Redis or another distributed solution
	clients := make(map[string][]time.Time)

	return func(c *gin.Context) {
		clientIP := c.ClientIP()
		now := time.Now()

		// Clean old entries (older than 1 minute)
		if requests, exists := clients[clientIP]; exists {
			var validRequests []time.Time
			for _, reqTime := range requests {
				if now.Sub(reqTime) < time.Minute {
					validRequests = append(validRequests, reqTime)
				}
			}
			clients[clientIP] = validRequests
		}

		// Check rate limit (100 requests per minute)
		if len(clients[clientIP]) >= 100 {
			SendErrorResponse(c, errors.ErrRateLimitExceeded)
			c.Abort()
			return
		}

		// Add current request
		clients[clientIP] = append(clients[clientIP], now)

		c.Next()
	}
}

// RequestID adds a unique request ID to each request
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := generateRequestID()
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// Security adds security headers
func Security() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		c.Header("Content-Security-Policy", "default-src 'self'")
		c.Next()
	}
}

// RequireRole ensures the user has one of the specified roles
func RequireRole(allowedRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			SendErrorResponse(c, errors.ErrPermissionDenied)
			c.Abort()
			return
		}

		userRole := role.(string)
		for _, allowedRole := range allowedRoles {
			if userRole == allowedRole {
				c.Next()
				return
			}
		}

		SendErrorResponse(c, errors.ErrPermissionDenied)
		c.Abort()
	}
}

// AdminMiddleware ensures the user has admin privileges (for backward compatibility)
func AdminMiddleware() gin.HandlerFunc {
	return RequireRole("admin")
}

// generateRequestID generates a unique request ID
func generateRequestID() string {
	// Simple implementation - in production you might want to use UUID
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// SendErrorResponse sends a standardized error response
func SendErrorResponse(c *gin.Context, apiErr *errors.APIError) {
	// Ensure path and request ID are set
	if apiErr.Path == "" {
		apiErr.Path = c.Request.URL.Path
	}
	if apiErr.RequestID == "" {
		if requestID, exists := c.Get("request_id"); exists {
			apiErr.RequestID = requestID.(string)
		}
	}

	c.JSON(apiErr.StatusCode, errors.ErrorResponse{
		Success:   false,
		Error:     apiErr,
		Timestamp: apiErr.Timestamp,
		Path:      apiErr.Path,
		RequestID: apiErr.RequestID,
	})
}
