load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "middleware",
    srcs = [
        "deserialize-user.go",
        "middleware.go",
    ],
    importpath = "pointer/golangp/apps/geok_center/internal/middleware",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/geok_center/internal/config",
        "//golangp/apps/geok_center/internal/models",
        "//golangp/apps/geok_center/pkg/errors",
        "//golangp/apps/geok_center/pkg/types",
        "//golangp/common/database/postgres",
        "//golangp/common/utils",
        "@com_github_gin_gonic_gin//:gin",
    ],
)
