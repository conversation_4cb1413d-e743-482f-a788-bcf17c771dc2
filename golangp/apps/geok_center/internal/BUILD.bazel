load("@rules_go//go:def.bzl", "go_library")

# This is a meta package that aggregates all internal packages
go_library(
    name = "internal",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/geok_center/internal/config",
        "//golangp/apps/geok_center/internal/handlers",
        "//golangp/apps/geok_center/internal/middleware",
        "//golangp/apps/geok_center/internal/models",
        "//golangp/apps/geok_center/internal/routes",
        "//golangp/apps/geok_center/internal/services",
    ],
)
