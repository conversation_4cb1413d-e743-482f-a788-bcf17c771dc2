package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Subscription represents a user subscription in the GEOK Center system
type Subscription struct {
	ID          uuid.UUID           `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:订阅唯一标识符，使用UUID格式" json:"id"`
	UserID      uuid.UUID           `gorm:"type:uuid;not null;index:idx_subscription_user_id;comment:关联的用户ID" json:"user_id"`
	PlanType    SubscriptionPlan    `gorm:"size:50;not null;index:idx_subscription_plan;comment:订阅计划类型" json:"plan_type"`
	Status      SubscriptionStatus  `gorm:"size:50;default:active;index:idx_subscription_status;comment:订阅状态" json:"status"`
	StartDate   time.Time           `gorm:"not null;comment:订阅开始日期" json:"start_date"`
	EndDate     *time.Time          `gorm:"comment:订阅结束日期，可为空表示永久订阅" json:"end_date"`
	AutoRenew   bool                `gorm:"default:false;comment:是否自动续费" json:"auto_renew"`
	Price       float64             `gorm:"type:decimal(10,2);comment:订阅价格" json:"price"`
	Currency    string              `gorm:"size:10;default:USD;comment:货币类型" json:"currency"`
	PaymentID   string              `gorm:"size:255;comment:支付系统中的订阅ID" json:"payment_id"`
	
	// 订阅特性配置
	Features    SubscriptionFeatures `gorm:"type:jsonb;comment:订阅包含的功能特性，JSON格式存储" json:"features"`
	
	// 使用统计
	UsageCount  int                 `gorm:"default:0;comment:当前周期使用次数" json:"usage_count"`
	UsageLimit  int                 `gorm:"default:-1;comment:使用限制，-1表示无限制" json:"usage_limit"`
	
	// 试用相关
	IsTrialPeriod bool              `gorm:"default:false;comment:是否为试用期" json:"is_trial_period"`
	TrialEndDate  *time.Time        `gorm:"comment:试用期结束日期" json:"trial_end_date"`
	
	// 取消相关
	CancelledAt   *time.Time        `gorm:"comment:取消订阅的时间" json:"cancelled_at"`
	CancelReason  string            `gorm:"size:500;comment:取消原因" json:"cancel_reason"`
	
	CreatedAt     time.Time         `gorm:"autoCreateTime;comment:订阅创建时间" json:"created_at"`
	UpdatedAt     time.Time         `gorm:"autoUpdateTime;comment:订阅最后更新时间" json:"updated_at"`
	DeletedAt     gorm.DeletedAt    `gorm:"index:idx_subscription_deleted_at;comment:软删除时间戳，不在JSON中返回" json:"-"`

	// Relationships
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"` // 关联的用户信息
}

// SubscriptionPlan represents subscription plan types
type SubscriptionPlan string

const (
	SubscriptionPlanFree       SubscriptionPlan = "free"       // 免费计划
	SubscriptionPlanBasic      SubscriptionPlan = "basic"      // 基础计划
	SubscriptionPlanPro        SubscriptionPlan = "pro"        // 专业计划
	SubscriptionPlanEnterprise SubscriptionPlan = "enterprise" // 企业计划
)

// SubscriptionStatus represents subscription status
type SubscriptionStatus string

const (
	SubscriptionStatusActive    SubscriptionStatus = "active"    // 活跃状态
	SubscriptionStatusInactive  SubscriptionStatus = "inactive"  // 非活跃状态
	SubscriptionStatusExpired   SubscriptionStatus = "expired"   // 已过期
	SubscriptionStatusCancelled SubscriptionStatus = "cancelled" // 已取消
	SubscriptionStatusSuspended SubscriptionStatus = "suspended" // 已暂停
	SubscriptionStatusTrial     SubscriptionStatus = "trial"     // 试用期
)

// SubscriptionFeatures represents the features included in a subscription
type SubscriptionFeatures struct {
	APICallsPerMonth    int  `json:"api_calls_per_month"`    // 每月API调用次数
	DataExportEnabled   bool `json:"data_export_enabled"`    // 是否支持数据导出
	AdvancedAnalytics   bool `json:"advanced_analytics"`     // 是否支持高级分析
	PrioritySupport     bool `json:"priority_support"`       // 是否有优先支持
	CustomIntegrations  bool `json:"custom_integrations"`    // 是否支持自定义集成
	MultiUserAccess     bool `json:"multi_user_access"`      // 是否支持多用户访问
	WhiteLabelEnabled   bool `json:"white_label_enabled"`    // 是否支持白标
	MaxProjects         int  `json:"max_projects"`           // 最大项目数量
	MaxBrands           int  `json:"max_brands"`             // 最大品牌数量
	StorageGB           int  `json:"storage_gb"`             // 存储空间（GB）
}

// IsActive checks if the subscription is currently active
func (s *Subscription) IsActive() bool {
	now := time.Now()
	
	// 检查状态
	if s.Status != SubscriptionStatusActive && s.Status != SubscriptionStatusTrial {
		return false
	}
	
	// 检查是否在有效期内
	if s.EndDate != nil && now.After(*s.EndDate) {
		return false
	}
	
	// 检查试用期
	if s.IsTrialPeriod && s.TrialEndDate != nil && now.After(*s.TrialEndDate) {
		return false
	}
	
	return true
}

// IsExpired checks if the subscription has expired
func (s *Subscription) IsExpired() bool {
	now := time.Now()
	
	if s.EndDate != nil && now.After(*s.EndDate) {
		return true
	}
	
	if s.IsTrialPeriod && s.TrialEndDate != nil && now.After(*s.TrialEndDate) {
		return true
	}
	
	return false
}

// CanUseFeature checks if a specific feature is available in the subscription
func (s *Subscription) CanUseFeature(feature string) bool {
	if !s.IsActive() {
		return false
	}
	
	switch feature {
	case "data_export":
		return s.Features.DataExportEnabled
	case "advanced_analytics":
		return s.Features.AdvancedAnalytics
	case "priority_support":
		return s.Features.PrioritySupport
	case "custom_integrations":
		return s.Features.CustomIntegrations
	case "multi_user_access":
		return s.Features.MultiUserAccess
	case "white_label":
		return s.Features.WhiteLabelEnabled
	default:
		return false
	}
}

// HasUsageLimit checks if the subscription has reached its usage limit
func (s *Subscription) HasUsageLimit() bool {
	if s.UsageLimit == -1 {
		return false // 无限制
	}
	return s.UsageCount >= s.UsageLimit
}

// GetRemainingUsage returns the remaining usage count
func (s *Subscription) GetRemainingUsage() int {
	if s.UsageLimit == -1 {
		return -1 // 无限制
	}
	remaining := s.UsageLimit - s.UsageCount
	if remaining < 0 {
		return 0
	}
	return remaining
}

// TableName returns the table name for the Subscription model
func (Subscription) TableName() string {
	return "subscriptions"
}
