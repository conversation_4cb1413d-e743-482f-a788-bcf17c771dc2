/*
 * @Description: Blog models for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-18
 */
package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

// BlogStatus represents blog status
type BlogStatus string

const (
	BlogStatusDraft     BlogStatus = "draft"
	BlogStatusPublished BlogStatus = "published"
	BlogStatusScheduled BlogStatus = "scheduled"
	BlogStatusArchived  BlogStatus = "archived"
)

// Blog represents blog content with markdown support
type Blog struct {
	ID            uuid.UUID      `gorm:"type:uuid;primary_key;default:uuid_generate_v4();comment:博客唯一标识符" json:"id"`
	BrandID       uuid.UUID      `gorm:"type:uuid;not null;index:idx_blog_brand_id;comment:关联的品牌ID" json:"brand_id"`
	Title         string         `gorm:"not null;comment:博客标题" json:"title"`
	Slug          string         `gorm:"not null;uniqueIndex;comment:博客URL别名，唯一" json:"slug"`
	Excerpt       string         `gorm:"type:text;comment:博客摘要" json:"excerpt"`
	Content       string         `gorm:"type:text;comment:博客正文内容（Markdown格式）" json:"content"`
	Author        datatypes.JSON `gorm:"type:jsonb;comment:作者信息（JSON格式）" json:"author"`
	Category      string         `gorm:"not null;comment:博客分类" json:"category"`
	Tags          datatypes.JSON `gorm:"type:jsonb;comment:博客标签（JSON数组格式）" json:"tags"`
	Status        BlogStatus     `gorm:"not null;default:draft;comment:博客状态" json:"status"`
	FeaturedImage string         `gorm:"comment:特色图片URL" json:"featured_image"`

	// Content metrics
	WordCount   int `gorm:"not null;default:0;comment:字数统计" json:"word_count"`
	ReadingTime int `gorm:"not null;default:0;comment:预计阅读时间（分钟）" json:"reading_time"`

	// Engagement metrics
	ViewCount    int64 `gorm:"not null;default:0;comment:浏览次数" json:"view_count"`
	LikeCount    int64 `gorm:"not null;default:0;comment:点赞次数" json:"like_count"`
	ShareCount   int64 `gorm:"not null;default:0;comment:分享次数" json:"share_count"`
	CommentCount int64 `gorm:"not null;default:0;comment:评论次数" json:"comment_count"`

	// Brand mention metrics (根据Figma设计)
	BrandMentions datatypes.JSON `gorm:"type:jsonb;comment:品牌提及信息（JSON格式）" json:"brand_mentions"`
	MentionRate   float64        `gorm:"not null;default:0;comment:提及率百分比" json:"mention_rate"`

	// SEO and metadata
	MetaTitle       string         `gorm:"comment:SEO标题" json:"meta_title"`
	MetaDescription string         `gorm:"comment:SEO描述" json:"meta_description"`
	MetaKeywords    datatypes.JSON `gorm:"type:jsonb;comment:SEO关键词（JSON数组格式）" json:"meta_keywords"`

	// Publishing
	PublishedAt *time.Time `gorm:"comment:发布时间，可为空" json:"published_at"`
	ScheduledAt *time.Time `gorm:"comment:定时发布时间，可为空" json:"scheduled_at"`

	// Timestamps
	CreatedAt time.Time `gorm:"autoCreateTime;comment:记录创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`

	// Relationships
	Brand Brand `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
}

// TableName returns the table name for Blog
func (Blog) TableName() string {
	return "blogs"
}
