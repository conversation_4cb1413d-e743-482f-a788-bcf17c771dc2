/*
 * @Description: AI搜索可见性指标模型 - 定义AI搜索问题的可见性指标数据结构
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// AIVisibilityMetrics represents visibility metrics for AI search questions
type AIVisibilityMetrics struct {
	ID           uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:可见性指标记录唯一标识符" json:"id"`
	AISearchID   uuid.UUID `gorm:"type:uuid;not null;index:idx_ai_visibility_search_id;comment:关联的AI搜索记录ID" json:"ai_search_id"`
	AIResponseID uuid.UUID `gorm:"type:uuid;not null;index:idx_ai_visibility_response_id;comment:关联的AI搜索响应ID" json:"ai_response_id"`
	BrandID      uuid.UUID `gorm:"type:uuid;not null;index:idx_ai_visibility_brand_id;comment:关联的品牌ID" json:"brand_id"`

	// 可见性指标数据 (JSON格式，支持中文key)
	VisibilityData datatypes.JSON `gorm:"type:jsonb;comment:可见性指标数据(JSON格式)" json:"visibility_data"`

	// 关键词相关指标 (JSON格式，支持中文key)
	KeywordData datatypes.JSON `gorm:"type:jsonb;comment:关键词相关指标数据(JSON格式)" json:"keyword_data"`

	// 聚合得分 (便于快速查询和排序)
	OverallScore        float64 `gorm:"comment:综合可见性得分(0-100)" json:"overall_score"`
	FrequencyScore      float64 `gorm:"comment:AI中出现频率得分(0-100)" json:"frequency_score"`
	RecommendationScore float64 `gorm:"comment:品牌推荐率得分(0-100)" json:"recommendation_score"`
	SearchRateScore     float64 `gorm:"comment:品牌搜索率得分(0-100)" json:"search_rate_score"`
	FirstChoiceScore    float64 `gorm:"comment:AI市场首推率得分(0-100)" json:"first_choice_score"`

	// 元数据
	CalculatedAt time.Time      `gorm:"comment:指标计算时间" json:"calculated_at"`
	CreatedAt    time.Time      `gorm:"autoCreateTime;comment:记录创建时间" json:"created_at"`
	UpdatedAt    time.Time      `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index:idx_ai_visibility_deleted_at;comment:软删除时间戳" json:"-"`

	// 关联关系
	AISearch   AISearch         `gorm:"foreignKey:AISearchID" json:"ai_search,omitempty"`
	AIResponse AISearchResponse `gorm:"foreignKey:AIResponseID" json:"ai_response,omitempty"`
	Brand      Brand            `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
}

// VisibilityMetricsData represents the structure of visibility metrics data
type VisibilityMetricsData struct {
	AIFrequency        float64                `json:"AI中出现频率"`
	BrandRecommendRate float64                `json:"品牌推荐率"`
	BrandSearchRate    float64                `json:"品牌搜索率"`
	FirstChoiceRate    float64                `json:"品牌在AI市场的首推率"`
	BrandPosition      int                    `json:"品牌提及位置"`
	BrandSentiment     string                 `json:"品牌情感倾向"`
	CompetitorCount    int                    `json:"竞争对手数量"`
	MarketShare        float64                `json:"市场占有率"`
	ExtraMetrics       map[string]interface{} `json:"额外指标,omitempty"`
}

// KeywordMetricsData represents the structure of keyword metrics data
type KeywordMetricsData struct {
	KeywordFrequency  map[string]int         `json:"关键词频率"`
	MainKeywords      []string               `json:"主要关键词"`
	KeywordDensity    float64                `json:"关键词密度"`
	SemanticRelevance float64                `json:"语义相关性"`
	IntentMatch       float64                `json:"搜索意图匹配度"`
	LongTailKeywords  []string               `json:"长尾关键词,omitempty"`
	KeywordCategories map[string][]string    `json:"关键词分类,omitempty"`
	ExtraKeywordData  map[string]interface{} `json:"额外关键词数据,omitempty"`
}

// TableName returns the table name for AIVisibilityMetrics
func (AIVisibilityMetrics) TableName() string {
	return "ai_visibility_metrics"
}

// Helper methods for AIVisibilityMetrics

// GetVisibilityData returns parsed visibility metrics data
func (avm *AIVisibilityMetrics) GetVisibilityData() (*VisibilityMetricsData, error) {
	var data VisibilityMetricsData
	if avm.VisibilityData != nil {
		if err := json.Unmarshal(avm.VisibilityData, &data); err != nil {
			return nil, err
		}
	}
	return &data, nil
}

// SetVisibilityData sets visibility metrics data
func (avm *AIVisibilityMetrics) SetVisibilityData(data *VisibilityMetricsData) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	avm.VisibilityData = jsonData
	return nil
}

// GetKeywordData returns parsed keyword metrics data
func (avm *AIVisibilityMetrics) GetKeywordData() (*KeywordMetricsData, error) {
	var data KeywordMetricsData
	if avm.KeywordData != nil {
		if err := json.Unmarshal(avm.KeywordData, &data); err != nil {
			return nil, err
		}
	}
	return &data, nil
}

// SetKeywordData sets keyword metrics data
func (avm *AIVisibilityMetrics) SetKeywordData(data *KeywordMetricsData) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	avm.KeywordData = jsonData
	return nil
}

// CalculateOverallScore calculates the overall visibility score
func (avm *AIVisibilityMetrics) CalculateOverallScore() {
	// 权重分配：频率30%，推荐率25%，搜索率25%，首推率20%
	avm.OverallScore = (avm.FrequencyScore*0.3 +
		avm.RecommendationScore*0.25 +
		avm.SearchRateScore*0.25 +
		avm.FirstChoiceScore*0.2)
}

// IsHighVisibility checks if the metrics indicate high visibility
func (avm *AIVisibilityMetrics) IsHighVisibility() bool {
	return avm.OverallScore >= 70.0
}

// IsMediumVisibility checks if the metrics indicate medium visibility
func (avm *AIVisibilityMetrics) IsMediumVisibility() bool {
	return avm.OverallScore >= 40.0 && avm.OverallScore < 70.0
}

// IsLowVisibility checks if the metrics indicate low visibility
func (avm *AIVisibilityMetrics) IsLowVisibility() bool {
	return avm.OverallScore < 40.0
}

// GetVisibilityLevel returns the visibility level as string
func (avm *AIVisibilityMetrics) GetVisibilityLevel() string {
	if avm.IsHighVisibility() {
		return "高"
	} else if avm.IsMediumVisibility() {
		return "中"
	}
	return "低"
}

// AIVisibilityAggregation represents aggregated visibility metrics for a brand
type AIVisibilityAggregation struct {
	ID      uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:聚合指标记录唯一标识符" json:"id"`
	BrandID uuid.UUID `gorm:"type:uuid;not null;index:idx_ai_visibility_agg_brand_id;comment:关联的品牌ID" json:"brand_id"`

	// 时间范围
	StartDate time.Time `gorm:"not null;index:idx_ai_visibility_agg_date_range;comment:统计开始日期" json:"start_date"`
	EndDate   time.Time `gorm:"not null;index:idx_ai_visibility_agg_date_range;comment:统计结束日期" json:"end_date"`

	// 聚合统计数据
	TotalQuestions             int     `gorm:"not null;default:0;comment:总问题数量" json:"total_questions"`
	AverageOverallScore        float64 `gorm:"comment:平均综合得分" json:"average_overall_score"`
	AverageFrequencyScore      float64 `gorm:"comment:平均频率得分" json:"average_frequency_score"`
	AverageRecommendationScore float64 `gorm:"comment:平均推荐率得分" json:"average_recommendation_score"`
	AverageSearchRateScore     float64 `gorm:"comment:平均搜索率得分" json:"average_search_rate_score"`
	AverageFirstChoiceScore    float64 `gorm:"comment:平均首推率得分" json:"average_first_choice_score"`

	// 聚合的详细数据
	AggregatedVisibilityData datatypes.JSON `gorm:"type:jsonb;comment:聚合的可见性数据" json:"aggregated_visibility_data"`
	AggregatedKeywordData    datatypes.JSON `gorm:"type:jsonb;comment:聚合的关键词数据" json:"aggregated_keyword_data"`

	// 趋势分析
	TrendDirection   string  `gorm:"size:20;comment:趋势方向(上升/下降/稳定)" json:"trend_direction"`
	ChangePercentage float64 `gorm:"comment:相比上期变化百分比" json:"change_percentage"`

	// 元数据
	CreatedAt time.Time      `gorm:"autoCreateTime;comment:记录创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_ai_visibility_agg_deleted_at;comment:软删除时间戳" json:"-"`

	// 关联关系
	Brand Brand `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
}

// TableName returns the table name for AIVisibilityAggregation
func (AIVisibilityAggregation) TableName() string {
	return "ai_visibility_aggregations"
}
