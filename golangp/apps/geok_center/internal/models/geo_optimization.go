/*
 * @Description: GEO optimization models for GEOK Center - Based on AI Search Data
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// GEOOptimization represents geographic optimization data based on AI search analysis
type GEOOptimization struct {
	// Primary key
	ID uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:GEO优化记录唯一标识符" json:"id"`

	// Core associations
	BrandID    uuid.UUID  `gorm:"type:uuid;not null;index:idx_geo_brand_id;comment:关联的品牌ID" json:"brand_id"`
	AISearchID *uuid.UUID `gorm:"type:uuid;index:idx_geo_ai_search_id;comment:关联的AI搜索ID，可选" json:"ai_search_id,omitempty"`

	// Geographic information
	Region  string `gorm:"size:100;not null;index:idx_geo_region;comment:地理区域（如北美、欧洲、亚太）" json:"region"`
	Country string `gorm:"size:100;not null;index:idx_geo_country;comment:国家名称" json:"country"`
	City    string `gorm:"size:100;index:idx_geo_city;comment:城市名称，可选" json:"city"`

	// Brand overview (品牌概述)
	BrandOverview datatypes.JSON `gorm:"type:jsonb;comment:品牌概述信息，包含品牌定位、核心价值等" json:"brand_overview"`

	// Advantages analysis (优势分析)
	Advantages datatypes.JSON `gorm:"type:jsonb;comment:品牌优势分析，基于AI搜索结果提取" json:"advantages"`

	// Weaknesses analysis (弱点分析)
	Weaknesses datatypes.JSON `gorm:"type:jsonb;comment:品牌弱点分析，基于AI搜索结果提取" json:"weaknesses"`

	// Main expertise areas (主要专业领域)
	MainExpertiseAreas datatypes.JSON `gorm:"type:jsonb;comment:主要专业领域，从AI搜索关键词中提取" json:"main_expertise_areas"`

	// Common associations (常见关联词)
	CommonAssociations datatypes.JSON `gorm:"type:jsonb;comment:常见关联词，从AI搜索问题和回答中提取" json:"common_associations"`

	// Content strategy recommendations (内容投放建议)
	ContentStrategyRecommendations datatypes.JSON `gorm:"type:jsonb;comment:内容投放建议，基于地理位置和搜索趋势" json:"content_strategy_recommendations"`

	// Competitor analysis (竞争对手分析)
	CompetitorAnalysis datatypes.JSON `gorm:"type:jsonb;comment:竞争对手分析，基于AI搜索中的品牌对比" json:"competitor_analysis"`

	// References data (引用数据)
	ReferencesData datatypes.JSON `gorm:"type:jsonb;comment:引用数据，来源于AI搜索响应中的引用信息" json:"references_data"`

	// Statistics (统计指标)
	Statistics datatypes.JSON `gorm:"type:jsonb;comment:统计指标，包含搜索量、提及频率、情感分析等" json:"statistics"`

	// Extracted keywords from AI searches (从AI搜索中提取的关键词)
	ExtractedKeywords datatypes.JSON `gorm:"type:jsonb;comment:从AI搜索问题中提取的关键词及其统计信息" json:"extracted_keywords"`

	// Performance metrics (基于AI搜索数据的表现指标)
	VisibilityScore   float64 `gorm:"comment:可见性评分（0-100），基于AI搜索中的品牌提及" json:"visibility_score"`
	MarketPenetration float64 `gorm:"comment:市场渗透率（0-100），基于搜索量和提及频率" json:"market_penetration"`
	SearchVolume      int64   `gorm:"comment:相关搜索量，从AI搜索数据统计" json:"search_volume"`
	MentionFrequency  int64   `gorm:"comment:品牌提及频率，从AI搜索响应中统计" json:"mention_frequency"`
	SentimentScore    float64 `gorm:"comment:情感评分（-1到1），基于AI搜索响应的情感分析" json:"sentiment_score"`
	CompetitionLevel  string  `gorm:"size:50;comment:竞争水平（low、medium、high、very_high）" json:"competition_level"`
	OpportunityScore  float64 `gorm:"comment:机会评分（0-10），综合多个指标计算" json:"opportunity_score"`

	// Data source tracking (数据来源追踪)
	SourceAISearchCount int       `gorm:"comment:来源AI搜索记录数量" json:"source_ai_search_count"`
	LastAnalyzedAt      time.Time `gorm:"comment:最后分析时间" json:"last_analyzed_at"`
	DataVersion         int       `gorm:"default:1;comment:数据版本号，用于追踪更新" json:"data_version"`

	// Status and priority
	Status   GEOOptimizationStatus `gorm:"size:50;default:active;index:idx_geo_status;comment:状态" json:"status"`
	Priority int                   `gorm:"default:1;comment:优先级（1-10）" json:"priority"`

	// Timestamps
	CreatedAt time.Time      `gorm:"autoCreateTime;index:idx_geo_created_at;comment:记录创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_geo_deleted_at;comment:软删除时间戳" json:"-"`

	// Associations
	Brand        Brand         `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
	AISearch     *AISearch     `gorm:"foreignKey:AISearchID" json:"ai_search,omitempty"`
	GeoDatabases []GEODatabase `gorm:"foreignKey:GeoOptimizationID" json:"geo_databases,omitempty"`
}

// GEODatabase represents AI search content related to geographic optimization
type GEODatabase struct {
	// Primary key
	ID uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:GEO数据库记录唯一标识符" json:"id"`

	// Associations
	BrandID           uuid.UUID  `gorm:"type:uuid;not null;index:idx_geo_db_brand_id;comment:关联的品牌ID" json:"brand_id"`
	GeoOptimizationID uuid.UUID  `gorm:"type:uuid;not null;index:idx_geo_db_geo_id;comment:关联的GEO优化记录ID" json:"geo_optimization_id"`
	AISearchID        *uuid.UUID `gorm:"type:uuid;index:idx_geo_db_ai_search_id;comment:关联的AI搜索记录ID，可选" json:"ai_search_id,omitempty"`

	// Content information
	ContentType string `gorm:"size:100;not null;index:idx_geo_db_content_type;comment:内容类型（search_result、keyword_analysis、market_insight等）" json:"content_type"`
	Title       string `gorm:"size:500;not null;comment:内容标题" json:"title"`
	Content     string `gorm:"type:text;comment:内容详情" json:"content"`

	// Geographic context
	Region   string `gorm:"size:100;not null;index:idx_geo_db_region;comment:地理区域" json:"region"`
	Country  string `gorm:"size:100;not null;index:idx_geo_db_country;comment:国家" json:"country"`
	Language string `gorm:"size:10;default:zh;comment:内容语言" json:"language"`

	// Metrics data
	SearchCount int64   `gorm:"not null;default:0;comment:搜索量" json:"search_count"`
	MentionRate float64 `gorm:"not null;default:0;comment:提及率百分比" json:"mention_rate"`

	// Timestamps
	CreatedAt time.Time      `gorm:"autoCreateTime;index:idx_geo_db_created_at;comment:记录创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_geo_db_deleted_at;comment:软删除时间戳" json:"-"`

	// Associations
	Brand           Brand           `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
	GeoOptimization GEOOptimization `gorm:"foreignKey:GeoOptimizationID" json:"geo_optimization,omitempty"`
	AISearch        *AISearch       `gorm:"foreignKey:AISearchID" json:"ai_search,omitempty"`
}

// GEOOptimizationStatus represents the status of GEO optimization
type GEOOptimizationStatus string

const (
	GEOOptimizationStatusActive   GEOOptimizationStatus = "active"   // 活跃状态
	GEOOptimizationStatusInactive GEOOptimizationStatus = "inactive" // 非活跃状态
	GEOOptimizationStatusPending  GEOOptimizationStatus = "pending"  // 待处理状态
	GEOOptimizationStatusArchived GEOOptimizationStatus = "archived" // 已归档状态
)

// GEODatabaseStatus represents the status of GEO database entries
type GEODatabaseStatus string

const (
	GEODatabaseStatusActive   GEODatabaseStatus = "active"   // 活跃状态
	GEODatabaseStatusInactive GEODatabaseStatus = "inactive" // 非活跃状态
	GEODatabaseStatusPending  GEODatabaseStatus = "pending"  // 待处理状态
	GEODatabaseStatusArchived GEODatabaseStatus = "archived" // 已归档状态
)

// TableName returns the table name for GEOOptimization
func (GEOOptimization) TableName() string {
	return "geo_optimizations"
}

// TableName returns the table name for GEODatabase
func (GEODatabase) TableName() string {
	return "geo_databases"
}

// GEOContentType constants for different types of GEO database content
const (
	GEOContentTypeSearchResult       = "search_result"       // AI搜索结果
	GEOContentTypeKeywordAnalysis    = "keyword_analysis"    // 关键词分析
	GEOContentTypeMarketInsight      = "market_insight"      // 市场洞察
	GEOContentTypeCompetitorAnalysis = "competitor_analysis" // 竞争对手分析
	GEOContentTypeTrendAnalysis      = "trend_analysis"      // 趋势分析
	GEOContentTypeUserBehavior       = "user_behavior"       // 用户行为分析
)

// CompetitionLevel constants for competition levels
const (
	CompetitionLevelLow      = "low"
	CompetitionLevelMedium   = "medium"
	CompetitionLevelHigh     = "high"
	CompetitionLevelVeryHigh = "very_high"
)

// Data structures for JSON fields

// BrandOverviewData represents brand overview information
type BrandOverviewData struct {
	Positioning    string   `json:"positioning"`     // 品牌定位
	CoreValues     []string `json:"core_values"`     // 核心价值
	TargetAudience string   `json:"target_audience"` // 目标受众
	MarketSegment  string   `json:"market_segment"`  // 市场细分
	Description    string   `json:"description"`     // 品牌描述
}

// AdvantagesData represents brand advantages analysis
type AdvantagesData struct {
	TechnicalAdvantages []string `json:"technical_advantages"` // 技术优势
	MarketAdvantages    []string `json:"market_advantages"`    // 市场优势
	ProductAdvantages   []string `json:"product_advantages"`   // 产品优势
	ServiceAdvantages   []string `json:"service_advantages"`   // 服务优势
	OverallStrengths    []string `json:"overall_strengths"`    // 整体优势
}

// WeaknessesData represents brand weaknesses analysis
type WeaknessesData struct {
	TechnicalWeaknesses []string `json:"technical_weaknesses"` // 技术弱点
	MarketWeaknesses    []string `json:"market_weaknesses"`    // 市场弱点
	ProductWeaknesses   []string `json:"product_weaknesses"`   // 产品弱点
	ServiceWeaknesses   []string `json:"service_weaknesses"`   // 服务弱点
	ImprovementAreas    []string `json:"improvement_areas"`    // 改进领域
}

// ExpertiseAreasData represents main expertise areas
type ExpertiseAreasData struct {
	PrimaryAreas   []string `json:"primary_areas"`   // 主要领域
	SecondaryAreas []string `json:"secondary_areas"` // 次要领域
	EmergingAreas  []string `json:"emerging_areas"`  // 新兴领域
	Specialties    []string `json:"specialties"`     // 专业特长
}

// CommonAssociationsData represents common word associations
type CommonAssociationsData struct {
	PositiveAssociations []string `json:"positive_associations"` // 正面关联词
	NeutralAssociations  []string `json:"neutral_associations"`  // 中性关联词
	NegativeAssociations []string `json:"negative_associations"` // 负面关联词
	FrequentPairs        []string `json:"frequent_pairs"`        // 高频词对
	ContextualWords      []string `json:"contextual_words"`      // 上下文词汇
}

// ContentStrategyData represents content strategy recommendations
type ContentStrategyData struct {
	RecommendedTopics  []string `json:"recommended_topics"`  // 推荐话题
	ContentTypes       []string `json:"content_types"`       // 内容类型
	TargetKeywords     []string `json:"target_keywords"`     // 目标关键词
	PublishingChannels []string `json:"publishing_channels"` // 发布渠道
	OptimalTiming      []string `json:"optimal_timing"`      // 最佳时机
	LocalizationTips   []string `json:"localization_tips"`   // 本地化建议
}

// CompetitorAnalysisData represents competitor analysis
type CompetitorAnalysisData struct {
	MainCompetitors      []string            `json:"main_competitors"`      // 主要竞争对手
	CompetitorStrengths  map[string][]string `json:"competitor_strengths"`  // 竞争对手优势
	CompetitorWeaknesses map[string][]string `json:"competitor_weaknesses"` // 竞争对手弱点
	MarketPositioning    map[string]string   `json:"market_positioning"`    // 市场定位对比
	CompetitiveAdvantage []string            `json:"competitive_advantage"` // 竞争优势
}

// ReferencesData represents references and citations data
type ReferencesData struct {
	SourceDomains   []string           `json:"source_domains"`   // 来源域名
	AuthorityScores map[string]float64 `json:"authority_scores"` // 权威性评分
	CitationCount   int                `json:"citation_count"`   // 引用次数
	QualityMetrics  map[string]float64 `json:"quality_metrics"`  // 质量指标
	TrustScores     map[string]float64 `json:"trust_scores"`     // 信任评分
}

// StatisticsData represents statistical metrics
type StatisticsData struct {
	TotalSearches        int64            `json:"total_searches"`        // 总搜索次数
	TotalMentions        int64            `json:"total_mentions"`        // 总提及次数
	PositiveMentions     int64            `json:"positive_mentions"`     // 正面提及
	NegativeMentions     int64            `json:"negative_mentions"`     // 负面提及
	NeutralMentions      int64            `json:"neutral_mentions"`      // 中性提及
	AveragePosition      float64          `json:"average_position"`      // 平均位置
	TopQuestions         []string         `json:"top_questions"`         // 热门问题
	ResponseDistribution map[string]int64 `json:"response_distribution"` // 响应分布
	TimeSeriesData       map[string]int64 `json:"time_series_data"`      // 时间序列数据
}

// ExtractedKeywordsData represents extracted keywords and their statistics
type ExtractedKeywordsData struct {
	Keywords          map[string]int64    `json:"keywords"`           // 关键词及其频率
	KeywordCategories map[string][]string `json:"keyword_categories"` // 关键词分类
	SearchVolumes     map[string]int64    `json:"search_volumes"`     // 搜索量
	TrendingKeywords  []string            `json:"trending_keywords"`  // 趋势关键词
	SeasonalKeywords  []string            `json:"seasonal_keywords"`  // 季节性关键词
	LongTailKeywords  []string            `json:"long_tail_keywords"` // 长尾关键词
	KeywordDifficulty map[string]float64  `json:"keyword_difficulty"` // 关键词难度
}

// Helper methods for GEOOptimization

// GetBrandOverview returns parsed brand overview data
func (g *GEOOptimization) GetBrandOverview() (*BrandOverviewData, error) {
	var data BrandOverviewData
	if g.BrandOverview != nil {
		if err := json.Unmarshal(g.BrandOverview, &data); err != nil {
			return nil, err
		}
	}
	return &data, nil
}

// SetBrandOverview sets brand overview data
func (g *GEOOptimization) SetBrandOverview(data *BrandOverviewData) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	g.BrandOverview = jsonData
	return nil
}

// GetAdvantages returns parsed advantages data
func (g *GEOOptimization) GetAdvantages() (*AdvantagesData, error) {
	var data AdvantagesData
	if g.Advantages != nil {
		if err := json.Unmarshal(g.Advantages, &data); err != nil {
			return nil, err
		}
	}
	return &data, nil
}

// SetAdvantages sets advantages data
func (g *GEOOptimization) SetAdvantages(data *AdvantagesData) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	g.Advantages = jsonData
	return nil
}

// GetWeaknesses returns parsed weaknesses data
func (g *GEOOptimization) GetWeaknesses() (*WeaknessesData, error) {
	var data WeaknessesData
	if g.Weaknesses != nil {
		if err := json.Unmarshal(g.Weaknesses, &data); err != nil {
			return nil, err
		}
	}
	return &data, nil
}

// SetWeaknesses sets weaknesses data
func (g *GEOOptimization) SetWeaknesses(data *WeaknessesData) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	g.Weaknesses = jsonData
	return nil
}

// GetExtractedKeywords returns parsed extracted keywords data
func (g *GEOOptimization) GetExtractedKeywords() (*ExtractedKeywordsData, error) {
	var data ExtractedKeywordsData
	if g.ExtractedKeywords != nil {
		if err := json.Unmarshal(g.ExtractedKeywords, &data); err != nil {
			return nil, err
		}
	}
	return &data, nil
}

// SetExtractedKeywords sets extracted keywords data
func (g *GEOOptimization) SetExtractedKeywords(data *ExtractedKeywordsData) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	g.ExtractedKeywords = jsonData
	return nil
}

// GetStatistics returns parsed statistics data
func (g *GEOOptimization) GetStatistics() (*StatisticsData, error) {
	var data StatisticsData
	if g.Statistics != nil {
		if err := json.Unmarshal(g.Statistics, &data); err != nil {
			return nil, err
		}
	}
	return &data, nil
}

// SetStatistics sets statistics data
func (g *GEOOptimization) SetStatistics(data *StatisticsData) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	g.Statistics = jsonData
	return nil
}

// IsActive checks if the GEO optimization is active
func (g *GEOOptimization) IsActive() bool {
	return g.Status == GEOOptimizationStatusActive
}

// NeedsUpdate checks if the GEO optimization needs update based on data version and last analyzed time
func (g *GEOOptimization) NeedsUpdate() bool {
	// Consider update needed if last analyzed more than 7 days ago
	return time.Since(g.LastAnalyzedAt) > 7*24*time.Hour
}

// CalculateOverallScore calculates an overall performance score based on multiple metrics
func (g *GEOOptimization) CalculateOverallScore() float64 {
	// Weighted average of different scores
	// Visibility: 30%, Market Penetration: 25%, Opportunity: 25%, Sentiment: 20%
	visibilityWeight := 0.30
	marketWeight := 0.25
	opportunityWeight := 0.25
	sentimentWeight := 0.20

	// Normalize sentiment score from [-1,1] to [0,100]
	normalizedSentiment := (g.SentimentScore + 1) * 50

	score := (g.VisibilityScore*visibilityWeight +
		g.MarketPenetration*marketWeight +
		g.OpportunityScore*10*opportunityWeight + // OpportunityScore is 0-10, normalize to 0-100
		normalizedSentiment*sentimentWeight)

	return score
}
