/*
 * @Description: AI搜索表 - 存储品牌相关问题和各AI模型回答
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package models

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// AIModelType represents different AI model types
type AIModelType string

const (
	AIModelChatGPT  AIModelType = "chatgpt"
	AIModelClaude   AIModelType = "claude"
	AIModelDeepseek AIModelType = "deepseek"
	AIModelGemini   AIModelType = "gemini"
	AIModelLlama    AIModelType = "llama"
	AIModelOther    AIModelType = "other"
)

// AISearchStatus represents the status of AI search
type AISearchStatus string

const (
	AISearchStatusPending    AISearchStatus = "pending"    // 待处理
	AISearchStatusProcessing AISearchStatus = "processing" // 处理中
	AISearchStatusCompleted  AISearchStatus = "completed"  // 已完成
	AISearchStatusFailed     AISearchStatus = "failed"     // 失败
)

// AISearch represents AI search questions and responses for brands
type AISearch struct {
	ID      uuid.UUID  `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:AI搜索记录唯一标识符" json:"id"`
	BrandID uuid.UUID  `gorm:"type:uuid;not null;index:idx_ai_search_brand_id;comment:关联的品牌ID" json:"brand_id"`
	BatchID *uuid.UUID `gorm:"type:uuid;index:idx_ai_search_batch_id;comment:关联的批次ID，可为空" json:"batch_id"`

	// Question information
	Question     string `gorm:"type:text;not null;comment:AI交互问题内容，必填" json:"question"`
	QuestionType string `gorm:"size:100;index:idx_ai_search_question_type;comment:问题类型分类" json:"question_type"`
	Keywords     string `gorm:"type:text;comment:问题相关关键词，逗号分隔" json:"keywords"`

	// Search metadata
	Status   AISearchStatus `gorm:"size:50;default:pending;index:idx_ai_search_status;comment:搜索状态" json:"status"`
	Priority int            `gorm:"default:0;index:idx_ai_search_priority;comment:搜索优先级" json:"priority"`
	Region   string         `gorm:"size:100;index:idx_ai_search_region;comment:搜索地理区域" json:"region"`
	Language string         `gorm:"size:10;default:zh;comment:搜索语言" json:"language"`

	// Timing
	ScheduledAt *time.Time `gorm:"index:idx_ai_search_scheduled;comment:计划执行时间" json:"scheduled_at"`
	StartedAt   *time.Time `gorm:"comment:开始处理时间" json:"started_at"`
	CompletedAt *time.Time `gorm:"comment:完成处理时间" json:"completed_at"`

	// Metadata
	CreatedAt time.Time      `gorm:"autoCreateTime;comment:记录创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_ai_search_deleted_at;comment:软删除时间戳" json:"-"`

	// Relationships
	Brand     Brand              `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
	Responses []AISearchResponse `gorm:"foreignKey:AISearchID" json:"responses,omitempty"`
}

// AISearchResponse represents responses from different AI models
type AISearchResponse struct {
	ID         uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:AI响应唯一标识符" json:"id"`
	AISearchID uuid.UUID `gorm:"type:uuid;not null;index:idx_ai_response_search_id;comment:关联的AI搜索ID" json:"ai_search_id"`

	// Model information
	ModelType    AIModelType `gorm:"size:50;not null;index:idx_ai_response_model;comment:AI模型类型" json:"model_type"`
	ModelVersion string      `gorm:"size:100;comment:AI模型版本" json:"model_version"`
	Provider     string      `gorm:"size:100;comment:AI服务提供商" json:"provider"`

	// Response content
	Response     string `gorm:"type:text;not null;comment:AI模型回答内容，必填" json:"response"`
	ResponseTime int    `gorm:"comment:响应时间（毫秒）" json:"response_time"`
	TokensUsed   int    `gorm:"comment:使用的token数量" json:"tokens_used"`

	// Quality metrics
	Confidence   float64 `gorm:"comment:回答置信度(0-1)" json:"confidence"`
	Relevance    float64 `gorm:"comment:回答相关性(0-1)" json:"relevance"`
	Completeness float64 `gorm:"comment:回答完整性(0-1)" json:"completeness"`

	// Brand mention analysis
	BrandMentioned bool   `gorm:"default:false;index:idx_ai_response_brand_mentioned;comment:是否提及品牌" json:"brand_mentioned"`
	BrandPosition  int    `gorm:"comment:品牌在回答中的位置排名" json:"brand_position"`
	BrandSentiment string `gorm:"size:50;comment:品牌提及情感(positive/negative/neutral)" json:"brand_sentiment"`
	BrandContext   string `gorm:"type:text;comment:品牌提及的上下文" json:"brand_context"`

	// Extracted data
	ExtractedData datatypes.JSON `gorm:"type:jsonb;comment:从回答中提取的结构化数据" json:"extracted_data"`
	Citations     datatypes.JSON `gorm:"type:jsonb;comment:回答中的引用信息" json:"citations"`
	Keywords      datatypes.JSON `gorm:"type:jsonb;comment:回答中的关键词" json:"keywords"`

	// Visibility metrics (新增可见性指标字段)
	VisibilityData      datatypes.JSON `gorm:"type:jsonb;comment:可见性指标数据(JSON格式，支持中文key)" json:"visibility_data"`
	KeywordMetrics      datatypes.JSON `gorm:"type:jsonb;comment:关键词指标数据(JSON格式，支持中文key)" json:"keyword_metrics"`
	VisibilityScore     float64        `gorm:"comment:综合可见性得分(0-100)" json:"visibility_score"`
	MetricsCalculatedAt *time.Time     `gorm:"comment:指标计算时间" json:"metrics_calculated_at"`

	// Status and error handling
	Status       string `gorm:"size:50;default:success;comment:响应状态(success/failed/timeout)" json:"status"`
	ErrorMessage string `gorm:"type:text;comment:错误信息" json:"error_message"`

	// Metadata
	CreatedAt time.Time `gorm:"autoCreateTime;comment:记录创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`

	// Relationships
	AISearch          AISearch              `gorm:"foreignKey:AISearchID" json:"ai_search,omitempty"`
	VisibilityMetrics []AIVisibilityMetrics `gorm:"foreignKey:AIResponseID" json:"visibility_metrics_records,omitempty"`
}

// AISearchBatch represents batch processing of AI searches
type AISearchBatch struct {
	ID          uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:AI搜索批次唯一标识符" json:"id"`
	BrandID     uuid.UUID `gorm:"type:uuid;not null;index:idx_ai_batch_brand_id;comment:关联的品牌ID" json:"brand_id"`
	BatchName   string    `gorm:"size:200;not null;comment:批次名称" json:"batch_name"`
	Description string    `gorm:"type:text;comment:批次描述" json:"description"`

	// Batch configuration
	TotalQuestions     int            `gorm:"not null;comment:总问题数量" json:"total_questions"`
	CompletedQuestions int            `gorm:"default:0;comment:已完成问题数量" json:"completed_questions"`
	FailedQuestions    int            `gorm:"default:0;comment:失败问题数量" json:"failed_questions"`
	TargetModels       datatypes.JSON `gorm:"type:jsonb;comment:目标AI模型列表" json:"target_models"`

	// Batch status
	Status      AISearchStatus `gorm:"size:50;default:pending;index:idx_ai_batch_status;comment:批次状态" json:"status"`
	Progress    float64        `gorm:"default:0;comment:完成进度(0-100)" json:"progress"`
	StartedAt   *time.Time     `gorm:"comment:批次开始时间" json:"started_at"`
	CompletedAt *time.Time     `gorm:"comment:批次完成时间" json:"completed_at"`

	// Configuration
	Config datatypes.JSON `gorm:"type:jsonb;comment:批次配置参数" json:"config"`

	// Metadata
	CreatedAt time.Time      `gorm:"autoCreateTime;comment:记录创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_ai_batch_deleted_at;comment:软删除时间戳" json:"-"`

	// Relationships
	Brand    Brand      `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
	Searches []AISearch `gorm:"foreignKey:BatchID" json:"searches,omitempty"`
}

// TableName methods for custom table names
func (AISearch) TableName() string {
	return "ai_searches"
}

func (AISearchResponse) TableName() string {
	return "ai_search_responses"
}

func (AISearchBatch) TableName() string {
	return "ai_search_batches"
}

// Helper methods for AISearch
func (a *AISearch) IsCompleted() bool {
	return a.Status == AISearchStatusCompleted
}

func (a *AISearch) IsPending() bool {
	return a.Status == AISearchStatusPending
}

func (a *AISearch) GetResponseByModel(modelType AIModelType) *AISearchResponse {
	for _, response := range a.Responses {
		if response.ModelType == modelType {
			return &response
		}
	}
	return nil
}

// Helper methods for AISearchResponse
func (r *AISearchResponse) IsSuccessful() bool {
	return r.Status == "success"
}

func (r *AISearchResponse) HasBrandMention() bool {
	return r.BrandMentioned
}

// Helper methods for AISearchBatch
func (b *AISearchBatch) CalculateProgress() float64 {
	if b.TotalQuestions == 0 {
		return 0
	}
	return float64(b.CompletedQuestions) / float64(b.TotalQuestions) * 100
}

func (b *AISearchBatch) IsCompleted() bool {
	return b.Status == AISearchStatusCompleted
}

// GenerateBrandQuestions generates AI search questions based on brand information
func GenerateBrandQuestions(brand Brand) []string {
	questions := []string{
		fmt.Sprintf("什么是%s？", brand.Name),
		fmt.Sprintf("%s有什么特点？", brand.Name),
		fmt.Sprintf("%s的主要产品和服务是什么？", brand.Name),
		fmt.Sprintf("如何评价%s？", brand.Name),
		fmt.Sprintf("%s与竞争对手相比有什么优势？", brand.Name),
		fmt.Sprintf("%s的用户体验如何？", brand.Name),
		fmt.Sprintf("推荐使用%s吗？", brand.Name),
		fmt.Sprintf("%s的价格怎么样？", brand.Name),
		fmt.Sprintf("%s适合什么类型的用户？", brand.Name),
		fmt.Sprintf("%s的发展前景如何？", brand.Name),
	}

	// Add keyword-based questions if keywords exist
	if brand.Keywords != "" {
		keywords := strings.Split(brand.Keywords, ",")
		for _, keyword := range keywords {
			keyword = strings.TrimSpace(keyword)
			if keyword != "" {
				questions = append(questions,
					fmt.Sprintf("关于%s的%s，有什么推荐？", keyword, brand.Name),
					fmt.Sprintf("在%s领域，%s表现如何？", keyword, brand.Name),
				)
			}
		}
	}

	return questions
}

// CreateAISearchFunction creates a function to interact with AI (placeholder for actual implementation)
func CreateAISearchFunction(search *AISearch) error {
	// This is a placeholder function that would be implemented to actually interact with AI models
	// For now, it just marks the search as processing
	search.Status = AISearchStatusProcessing
	search.StartedAt = &time.Time{}
	now := time.Now()
	search.StartedAt = &now

	// TODO: Implement actual AI interaction logic here
	// This would involve:
	// 1. Sending the question to different AI models (ChatGPT, Claude, Deepseek, etc.)
	// 2. Collecting responses
	// 3. Analyzing responses for brand mentions
	// 4. Storing results in AISearchResponse table

	return nil
}

// Helper methods for AISearchResponse visibility metrics

// GetVisibilityData returns parsed visibility metrics data from AISearchResponse
func (asr *AISearchResponse) GetVisibilityData() (*VisibilityMetricsData, error) {
	var data VisibilityMetricsData
	if asr.VisibilityData != nil {
		if err := json.Unmarshal(asr.VisibilityData, &data); err != nil {
			return nil, err
		}
	}
	return &data, nil
}

// SetVisibilityData sets visibility metrics data in AISearchResponse
func (asr *AISearchResponse) SetVisibilityData(data *VisibilityMetricsData) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	asr.VisibilityData = jsonData
	now := time.Now()
	asr.MetricsCalculatedAt = &now
	return nil
}

// GetKeywordMetrics returns parsed keyword metrics data from AISearchResponse
func (asr *AISearchResponse) GetKeywordMetrics() (*KeywordMetricsData, error) {
	var data KeywordMetricsData
	if asr.KeywordMetrics != nil {
		if err := json.Unmarshal(asr.KeywordMetrics, &data); err != nil {
			return nil, err
		}
	}
	return &data, nil
}

// SetKeywordMetrics sets keyword metrics data in AISearchResponse
func (asr *AISearchResponse) SetKeywordMetrics(data *KeywordMetricsData) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	asr.KeywordMetrics = jsonData
	now := time.Now()
	asr.MetricsCalculatedAt = &now
	return nil
}

// CalculateVisibilityScore calculates and sets the visibility score based on metrics
func (asr *AISearchResponse) CalculateVisibilityScore() error {
	visibilityData, err := asr.GetVisibilityData()
	if err != nil {
		return err
	}

	// 计算综合可见性得分
	// 权重分配：AI中出现频率30%，品牌推荐率25%，品牌搜索率25%，首推率20%
	score := (visibilityData.AIFrequency*0.3 +
		visibilityData.BrandRecommendRate*0.25 +
		visibilityData.BrandSearchRate*0.25 +
		visibilityData.FirstChoiceRate*0.2)

	asr.VisibilityScore = score
	return nil
}

// HasVisibilityMetrics checks if the response has visibility metrics calculated
func (asr *AISearchResponse) HasVisibilityMetrics() bool {
	return asr.MetricsCalculatedAt != nil && asr.VisibilityData != nil
}
