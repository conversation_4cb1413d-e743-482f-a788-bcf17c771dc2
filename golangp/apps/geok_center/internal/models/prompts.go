/*
 * @Description: 重构后的提示表 - 包含内容、评分、排名层级、份额占比、点击数量、关键词频率等
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// PromptStatus represents the status of a prompt
type PromptStatus string

const (
	PromptStatusActive   PromptStatus = "active"
	PromptStatusInactive PromptStatus = "inactive"
	PromptStatusPending  PromptStatus = "pending"
	PromptStatusArchived PromptStatus = "archived"
)

// PromptRankingTier represents the ranking tier/level
type PromptRankingTier string

const (
	PromptRankingTierTop    PromptRankingTier = "top"    // 顶级(1-3名)
	PromptRankingTierHigh   PromptRankingTier = "high"   // 高级(4-10名)
	PromptRankingTierMedium PromptRankingTier = "medium" // 中级(11-20名)
	PromptRankingTierLow    PromptRankingTier = "low"    // 低级(21名以下)
)

// Prompt represents AI prompt content with comprehensive metrics
type Prompt struct {
	ID         uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:提示内容唯一标识符" json:"id"`
	BrandID    uuid.UUID `gorm:"type:uuid;not null;index:idx_prompt_brand_id;comment:关联的品牌ID" json:"brand_id"`
	AISearchID uuid.UUID `gorm:"type:uuid;not null;index:idx_prompt_ai_search_id;comment:关联的AI搜索ID" json:"ai_search_id"`

	// Basic prompt information (从AI搜索问题内容关联而来)
	Content  string `gorm:"type:text;not null;comment:提示内容正文，来自AI搜索问题" json:"content"`
	Category string `gorm:"size:100;index:idx_prompt_category;comment:提示分类" json:"category"`

	// Scoring and ranking
	Score       float64           `gorm:"not null;default:0;comment:提示评分(0-100)" json:"score"`
	Ranking     int               `gorm:"not null;default:0;index:idx_prompt_ranking;comment:提示排名位置" json:"ranking"`
	RankingTier PromptRankingTier `gorm:"size:20;index:idx_prompt_ranking_tier;comment:排名层级" json:"ranking_tier"`
	ShareRate   float64           `gorm:"not null;default:0;comment:份额占比(0-100)" json:"share_rate"`

	// Click and engagement metrics
	ClickCount     int64   `gorm:"not null;default:0;comment:点击数量" json:"click_count"`
	ViewCount      int64   `gorm:"not null;default:0;comment:浏览数量" json:"view_count"`
	ShareCount     int64   `gorm:"not null;default:0;comment:分享数量" json:"share_count"`
	ReferenceCount int64   `gorm:"not null;default:0;comment:引用数量" json:"reference_count"`
	EngagementRate float64 `gorm:"not null;default:0;comment:参与率(0-100)" json:"engagement_rate"`
	ConversionRate float64 `gorm:"not null;default:0;comment:转化率(0-100)" json:"conversion_rate"`

	// Keyword analysis
	MainKeywords     datatypes.JSON `gorm:"type:jsonb;comment:主要关键词列表(JSON数组)" json:"main_keywords"`
	KeywordFrequency datatypes.JSON `gorm:"type:jsonb;comment:关键词频率数据(JSON对象)" json:"keyword_frequency"`
	KeywordDensity   float64        `gorm:"not null;default:0;comment:关键词密度" json:"keyword_density"`
	SemanticTags     datatypes.JSON `gorm:"type:jsonb;comment:语义标签(JSON数组)" json:"semantic_tags"`

	// Performance metrics
	PerformanceData datatypes.JSON `gorm:"type:jsonb;comment:性能数据(JSON对象)" json:"performance_data"`
	TrendData       datatypes.JSON `gorm:"type:jsonb;comment:趋势数据(JSON对象)" json:"trend_data"`
	CompetitorData  datatypes.JSON `gorm:"type:jsonb;comment:竞争对手数据(JSON对象)" json:"competitor_data"`

	// Quality indicators
	QualityScore     float64 `gorm:"not null;default:0;comment:质量得分(0-100)" json:"quality_score"`
	RelevanceScore   float64 `gorm:"not null;default:0;comment:相关性得分(0-100)" json:"relevance_score"`
	OriginalityScore float64 `gorm:"not null;default:0;comment:原创性得分(0-100)" json:"originality_score"`

	// Geographic and platform data
	Region   string `gorm:"size:100;index:idx_prompt_region;comment:地理区域" json:"region"`
	Platform string `gorm:"size:100;index:idx_prompt_platform;comment:平台" json:"platform"`
	Language string `gorm:"size:10;default:zh;comment:语言" json:"language"`

	// Status and metadata
	Status    PromptStatus `gorm:"size:50;default:active;index:idx_prompt_status;comment:提示状态" json:"status"`
	Priority  int          `gorm:"default:0;index:idx_prompt_priority;comment:优先级" json:"priority"`
	IsFeature bool         `gorm:"default:false;index:idx_prompt_featured;comment:是否为特色提示" json:"is_featured"`

	// Timing information
	LastAnalyzedAt *time.Time `gorm:"comment:最后分析时间" json:"last_analyzed_at"`
	LastUpdatedAt  *time.Time `gorm:"comment:内容最后更新时间" json:"last_updated_at"`

	// Timestamps
	CreatedAt time.Time      `gorm:"autoCreateTime;comment:记录创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_prompt_deleted_at;comment:软删除时间戳" json:"-"`

	// Relationships
	Brand    Brand          `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
	AISearch AISearch       `gorm:"foreignKey:AISearchID" json:"ai_search,omitempty"`
	Metrics  []PromptMetric `gorm:"foreignKey:PromptID" json:"metrics,omitempty"`
}

// PromptMetric represents detailed metrics for prompts over time
type PromptMetric struct {
	ID       uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:提示指标唯一标识符" json:"id"`
	PromptID uuid.UUID `gorm:"type:uuid;not null;index:idx_prompt_metric_prompt_id;comment:关联的提示ID" json:"prompt_id"`

	// Time period
	Date   time.Time `gorm:"not null;index:idx_prompt_metric_date;comment:指标记录日期" json:"date"`
	Period string    `gorm:"size:20;not null;comment:统计周期(daily/weekly/monthly)" json:"period"`

	// Core metrics
	Views      int64   `gorm:"not null;default:0;comment:浏览次数" json:"views"`
	Clicks     int64   `gorm:"not null;default:0;comment:点击次数" json:"clicks"`
	Shares     int64   `gorm:"not null;default:0;comment:分享次数" json:"shares"`
	CTR        float64 `gorm:"not null;default:0;comment:点击率" json:"ctr"`
	Engagement float64 `gorm:"not null;default:0;comment:参与度" json:"engagement"`
	Conversion float64 `gorm:"not null;default:0;comment:转化率" json:"conversion"`

	// Ranking and positioning
	Ranking    int     `gorm:"not null;default:0;comment:排名位置" json:"ranking"`
	ShareRate  float64 `gorm:"not null;default:0;comment:份额占比" json:"share_rate"`
	Visibility float64 `gorm:"not null;default:0;comment:可见性得分" json:"visibility"`

	// Keyword performance
	KeywordRankings datatypes.JSON `gorm:"type:jsonb;comment:关键词排名数据" json:"keyword_rankings"`
	KeywordClicks   datatypes.JSON `gorm:"type:jsonb;comment:关键词点击数据" json:"keyword_clicks"`

	// Geographic and platform breakdown
	Region   string `gorm:"size:100;index:idx_prompt_metric_region;comment:地理区域" json:"region"`
	Platform string `gorm:"size:100;index:idx_prompt_metric_platform;comment:平台" json:"platform"`

	// Additional data
	MetricData datatypes.JSON `gorm:"type:jsonb;comment:额外指标数据" json:"metric_data"`

	// Timestamps
	CreatedAt time.Time `gorm:"autoCreateTime;comment:记录创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`

	// Relationships
	Prompt Prompt `gorm:"foreignKey:PromptID" json:"prompt,omitempty"`
}

// PromptKeyword represents keyword analysis for prompts
type PromptKeyword struct {
	ID       uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:提示关键词唯一标识符" json:"id"`
	PromptID uuid.UUID `gorm:"type:uuid;not null;index:idx_prompt_keyword_prompt_id;comment:关联的提示ID" json:"prompt_id"`

	// Keyword information
	Keyword    string  `gorm:"size:200;not null;index:idx_prompt_keyword_word;comment:关键词" json:"keyword"`
	Frequency  int     `gorm:"not null;default:0;comment:出现频率" json:"frequency"`
	Density    float64 `gorm:"not null;default:0;comment:关键词密度" json:"density"`
	Position   int     `gorm:"not null;default:0;comment:关键词位置" json:"position"`
	Importance float64 `gorm:"not null;default:0;comment:重要性得分" json:"importance"`

	// Performance metrics
	ClickCount   int64   `gorm:"not null;default:0;comment:关键词点击数" json:"click_count"`
	SearchVolume int64   `gorm:"not null;default:0;comment:搜索量" json:"search_volume"`
	Competition  float64 `gorm:"not null;default:0;comment:竞争度" json:"competition"`
	CPC          float64 `gorm:"not null;default:0;comment:每次点击成本" json:"cpc"`

	// Classification
	KeywordType string `gorm:"size:50;comment:关键词类型(primary/secondary/long-tail)" json:"keyword_type"`
	Category    string `gorm:"size:100;comment:关键词分类" json:"category"`
	Intent      string `gorm:"size:50;comment:搜索意图(informational/commercial/navigational)" json:"intent"`

	// Timestamps
	CreatedAt time.Time `gorm:"autoCreateTime;comment:记录创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`

	// Relationships
	Prompt Prompt `gorm:"foreignKey:PromptID" json:"prompt,omitempty"`
}

// TableName methods for custom table names
func (Prompt) TableName() string {
	return "prompts"
}

func (PromptMetric) TableName() string {
	return "prompt_metrics"
}

func (PromptKeyword) TableName() string {
	return "prompt_keywords"
}

// Helper methods for Prompt
func (p *Prompt) IsActive() bool {
	return p.Status == PromptStatusActive
}

func (p *Prompt) IsTopRanking() bool {
	return p.RankingTier == PromptRankingTierTop
}

func (p *Prompt) GetCTR() float64 {
	if p.ViewCount == 0 {
		return 0
	}
	return float64(p.ClickCount) / float64(p.ViewCount) * 100
}

func (p *Prompt) UpdateRankingTier() {
	if p.Ranking >= 1 && p.Ranking <= 3 {
		p.RankingTier = PromptRankingTierTop
	} else if p.Ranking >= 4 && p.Ranking <= 10 {
		p.RankingTier = PromptRankingTierHigh
	} else if p.Ranking >= 11 && p.Ranking <= 20 {
		p.RankingTier = PromptRankingTierMedium
	} else {
		p.RankingTier = PromptRankingTierLow
	}
}

// Helper methods for PromptMetric
func (m *PromptMetric) CalculateCTR() float64 {
	if m.Views == 0 {
		return 0
	}
	return float64(m.Clicks) / float64(m.Views) * 100
}

func (m *PromptMetric) CalculateEngagement() float64 {
	if m.Views == 0 {
		return 0
	}
	return float64(m.Clicks+m.Shares) / float64(m.Views) * 100
}

// Helper methods for PromptKeyword
func (k *PromptKeyword) IsPrimaryKeyword() bool {
	return k.KeywordType == "primary"
}

func (k *PromptKeyword) IsHighImportance() bool {
	return k.Importance >= 80
}
