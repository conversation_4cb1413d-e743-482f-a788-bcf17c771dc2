/*
 * @Description: 简化的品牌模型 - 适配新的业务流程
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// Brand represents a brand entity in the system for monitoring and analysis
type Brand struct {
	// ID is the unique identifier for the brand using UUID
	ID uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:品牌唯一标识符，使用UUID格式" json:"id"`

	// UserID is the ID of the user who owns this brand
	UserID uuid.UUID `gorm:"type:uuid;not null;index:idx_brand_user_id;comment:品牌所属用户ID" json:"user_id"`

	// Name is the brand name (required, unique per user)
	Name string `gorm:"size:200;not null;uniqueIndex:idx_brand_user_name,composite:user_id;comment:品牌名称，必填且在用户内唯一" json:"name"`

	// Domain is the brand's domain name (required)
	Domain string `gorm:"size:255;not null;comment:品牌域名，必填" json:"domain"`

	// Keywords are the keywords associated with the brand (optional, can be empty)
	Keywords string `gorm:"size:1000;comment:品牌关联关键词，可选" json:"keywords"`

	// LinkedURL is the URL that the brand links to (optional, can be empty)
	LinkedURL string `gorm:"size:500;comment:品牌链接URL，可选" json:"linked_url"`

	// Description is the brand description
	Description string `gorm:"type:text;comment:品牌描述" json:"description"`

	// Suggestions is an optional JSON array of suggestions/prompts for the brand
	Suggestions datatypes.JSON `gorm:"type:jsonb;comment:品牌提示建议，可选的JSON数组" json:"suggestions,omitempty"`

	// IsAssetVerified indicates whether the brand has passed asset verification (defaults to false)
	IsAssetVerified bool `gorm:"default:false;index:idx_brand_asset_verified;comment:品牌是否通过资产验证" json:"is_asset_verified"`

	// Status represents the current status of the brand (active, inactive, pending)
	Status BrandStatus `gorm:"size:50;default:active;index:idx_brand_status;comment:品牌当前状态（active、inactive、pending）" json:"status"`

	// OriginStartType represents how the user started with this brand monitoring
	OriginStartType BrandOriginStartType `gorm:"size:50;default:geo_keywords;index:idx_brand_origin_start_type;comment:品牌监控起始类型" json:"origin_start_type"`

	// CreatedAt is the timestamp when the brand was created
	CreatedAt time.Time `gorm:"autoCreateTime;index:idx_brand_created_at;comment:品牌创建时间" json:"created_at"`

	// UpdatedAt is the timestamp when the brand was last updated
	UpdatedAt time.Time `gorm:"autoUpdateTime;comment:品牌最后更新时间" json:"updated_at"`

	// DeletedAt is the soft delete timestamp (hidden from JSON response)
	DeletedAt gorm.DeletedAt `gorm:"index:idx_brand_deleted_at;comment:软删除时间戳，不在JSON中返回" json:"-"`

	// Relationships - 简化的关联关系
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// BrandStatus represents the status of a brand
type BrandStatus string

const (
	BrandStatusActive   BrandStatus = "active"
	BrandStatusInactive BrandStatus = "inactive"
	BrandStatusPending  BrandStatus = "pending"
)

// BrandOriginStartType represents how the user started with brand monitoring
type BrandOriginStartType string

const (
	BrandOriginStartTypeGeoKeywords   BrandOriginStartType = "geo_keywords"   // 我有GEO关键词，希望有人能帮我把它们变成搜索提示
	BrandOriginStartTypeSearchPrompts BrandOriginStartType = "search_prompts" // 我已经有想要监控的搜索提示
	BrandOriginStartTypeSpecificURL   BrandOriginStartType = "specific_url"   // 我有一个特定的URL，并想发现那些提示可以带来流量
)
