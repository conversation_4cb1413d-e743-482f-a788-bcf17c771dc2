/*
 * @Description: 引用表 - 存储AI回答中的引用信息，包括排名、域名、类别、数量、分享等数据
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// ReferenceType represents different types of references
type ReferenceType string

const (
	ReferenceTypeWebsite    ReferenceType = "website"    // 网站引用
	ReferenceTypeArticle    ReferenceType = "article"    // 文章引用
	ReferenceTypeDocument   ReferenceType = "document"   // 文档引用
	ReferenceTypeVideo      ReferenceType = "video"      // 视频引用
	ReferenceTypeImage      ReferenceType = "image"      // 图片引用
	ReferenceTypeDatabase   ReferenceType = "database"   // 数据库引用
	ReferenceTypeAPI        ReferenceType = "api"        // API引用
	ReferenceTypeOther      ReferenceType = "other"      // 其他类型
)

// ReferenceCategory represents the category of the reference
type ReferenceCategory string

const (
	ReferenceCategoryNews        ReferenceCategory = "news"        // 新闻
	ReferenceCategoryEducation   ReferenceCategory = "education"   // 教育
	ReferenceCategoryTechnology  ReferenceCategory = "technology"  // 技术
	ReferenceCategoryBusiness    ReferenceCategory = "business"    // 商业
	ReferenceCategoryHealth      ReferenceCategory = "health"      // 健康
	ReferenceCategoryScience     ReferenceCategory = "science"     // 科学
	ReferenceCategoryEntertainment ReferenceCategory = "entertainment" // 娱乐
	ReferenceCategorySports      ReferenceCategory = "sports"      // 体育
	ReferenceCategoryGovernment  ReferenceCategory = "government"  // 政府
	ReferenceCategoryOther       ReferenceCategory = "other"       // 其他
)

// Reference represents references found in AI responses
type Reference struct {
	ID             uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:引用唯一标识符" json:"id"`
	AISearchID     uuid.UUID `gorm:"type:uuid;not null;index:idx_reference_ai_search_id;comment:关联的AI搜索ID" json:"ai_search_id"`
	AIResponseID   uuid.UUID `gorm:"type:uuid;not null;index:idx_reference_ai_response_id;comment:关联的AI响应ID" json:"ai_response_id"`
	BrandID        uuid.UUID `gorm:"type:uuid;not null;index:idx_reference_brand_id;comment:关联的品牌ID" json:"brand_id"`

	// Reference basic information
	Title       string        `gorm:"size:500;not null;comment:引用标题" json:"title"`
	URL         string        `gorm:"size:1000;not null;comment:引用URL" json:"url"`
	Domain      string        `gorm:"size:255;not null;index:idx_reference_domain;comment:引用域名" json:"domain"`
	Type        ReferenceType `gorm:"size:50;not null;index:idx_reference_type;comment:引用类型" json:"type"`
	Category    ReferenceCategory `gorm:"size:50;not null;index:idx_reference_category;comment:引用类别" json:"category"`
	Description string        `gorm:"type:text;comment:引用描述" json:"description"`

	// Ranking and positioning
	Ranking         int     `gorm:"not null;default:0;index:idx_reference_ranking;comment:引用在AI回答中的排名位置" json:"ranking"`
	Position        int     `gorm:"not null;default:0;comment:引用在文本中的位置" json:"position"`
	Prominence      float64 `gorm:"not null;default:0;comment:引用突出度(0-100)" json:"prominence"`
	RelevanceScore  float64 `gorm:"not null;default:0;comment:相关性得分(0-100)" json:"relevance_score"`

	// Quantity and frequency metrics
	MentionCount    int     `gorm:"not null;default:1;comment:在回答中被提及的次数" json:"mention_count"`
	CitationCount   int     `gorm:"not null;default:1;comment:被引用的次数" json:"citation_count"`
	Frequency       float64 `gorm:"not null;default:0;comment:引用频率" json:"frequency"`
	Weight          float64 `gorm:"not null;default:0;comment:引用权重" json:"weight"`

	// Sharing and engagement metrics
	ShareCount      int64   `gorm:"not null;default:0;comment:分享数量" json:"share_count"`
	ClickCount      int64   `gorm:"not null;default:0;comment:点击数量" json:"click_count"`
	ViewCount       int64   `gorm:"not null;default:0;comment:浏览数量" json:"view_count"`
	EngagementRate  float64 `gorm:"not null;default:0;comment:参与率" json:"engagement_rate"`

	// Quality and trust metrics
	AuthorityScore  float64 `gorm:"not null;default:0;comment:权威性得分(0-100)" json:"authority_score"`
	TrustScore      float64 `gorm:"not null;default:0;comment:信任度得分(0-100)" json:"trust_score"`
	QualityScore    float64 `gorm:"not null;default:0;comment:质量得分(0-100)" json:"quality_score"`
	FreshnessScore  float64 `gorm:"not null;default:0;comment:新鲜度得分(0-100)" json:"freshness_score"`

	// Content analysis
	ContentLength   int            `gorm:"not null;default:0;comment:内容长度" json:"content_length"`
	KeywordMatches  datatypes.JSON `gorm:"type:jsonb;comment:关键词匹配情况" json:"keyword_matches"`
	TopicRelevance  datatypes.JSON `gorm:"type:jsonb;comment:主题相关性分析" json:"topic_relevance"`
	SentimentScore  float64        `gorm:"comment:情感得分(-1到1)" json:"sentiment_score"`

	// Source metadata
	Author          string         `gorm:"size:200;comment:作者" json:"author"`
	Publisher       string         `gorm:"size:200;comment:发布者" json:"publisher"`
	PublishedDate   *time.Time     `gorm:"comment:发布日期" json:"published_date"`
	LastModified    *time.Time     `gorm:"comment:最后修改日期" json:"last_modified"`
	Language        string         `gorm:"size:10;comment:语言" json:"language"`
	Country         string         `gorm:"size:100;comment:国家" json:"country"`

	// Technical metadata
	ContentType     string         `gorm:"size:100;comment:内容类型" json:"content_type"`
	FileSize        int64          `gorm:"comment:文件大小(字节)" json:"file_size"`
	LoadTime        int            `gorm:"comment:加载时间(毫秒)" json:"load_time"`
	HTTPStatus      int            `gorm:"comment:HTTP状态码" json:"http_status"`
	IsAccessible    bool           `gorm:"default:true;comment:是否可访问" json:"is_accessible"`

	// Context information
	ContextBefore   string         `gorm:"type:text;comment:引用前的上下文" json:"context_before"`
	ContextAfter    string         `gorm:"type:text;comment:引用后的上下文" json:"context_after"`
	QuoteText       string         `gorm:"type:text;comment:被引用的具体文本" json:"quote_text"`
	
	// Additional data
	Metadata        datatypes.JSON `gorm:"type:jsonb;comment:额外元数据" json:"metadata"`
	Tags            datatypes.JSON `gorm:"type:jsonb;comment:标签列表" json:"tags"`

	// Geographic and platform context
	Region          string         `gorm:"size:100;index:idx_reference_region;comment:地理区域" json:"region"`
	Platform        string         `gorm:"size:100;index:idx_reference_platform;comment:AI平台" json:"platform"`

	// Timestamps
	CreatedAt       time.Time      `gorm:"autoCreateTime;comment:记录创建时间" json:"created_at"`
	UpdatedAt       time.Time      `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`
	DeletedAt       gorm.DeletedAt `gorm:"index:idx_reference_deleted_at;comment:软删除时间戳" json:"-"`

	// Relationships
	Brand      Brand             `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
	AISearch   AISearch          `gorm:"foreignKey:AISearchID" json:"ai_search,omitempty"`
	AIResponse AISearchResponse  `gorm:"foreignKey:AIResponseID" json:"ai_response,omitempty"`
}

// ReferenceAnalysis represents aggregated analysis of references
type ReferenceAnalysis struct {
	ID      uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:引用分析唯一标识符" json:"id"`
	BrandID uuid.UUID `gorm:"type:uuid;not null;index:idx_reference_analysis_brand_id;comment:关联的品牌ID" json:"brand_id"`

	// Analysis period
	StartDate time.Time `gorm:"not null;comment:分析开始日期" json:"start_date"`
	EndDate   time.Time `gorm:"not null;comment:分析结束日期" json:"end_date"`
	Period    string    `gorm:"size:20;not null;comment:分析周期" json:"period"`

	// Aggregated metrics
	TotalReferences     int     `gorm:"not null;default:0;comment:总引用数量" json:"total_references"`
	UniqueReferences    int     `gorm:"not null;default:0;comment:唯一引用数量" json:"unique_references"`
	AverageRanking      float64 `gorm:"not null;default:0;comment:平均排名" json:"average_ranking"`
	TopRankingCount     int     `gorm:"not null;default:0;comment:前三排名数量" json:"top_ranking_count"`
	TotalShares         int64   `gorm:"not null;default:0;comment:总分享数量" json:"total_shares"`
	TotalClicks         int64   `gorm:"not null;default:0;comment:总点击数量" json:"total_clicks"`

	// Domain analysis
	TopDomains          datatypes.JSON `gorm:"type:jsonb;comment:顶级域名分析" json:"top_domains"`
	DomainDistribution  datatypes.JSON `gorm:"type:jsonb;comment:域名分布" json:"domain_distribution"`
	NewDomains          datatypes.JSON `gorm:"type:jsonb;comment:新出现的域名" json:"new_domains"`

	// Category analysis
	CategoryDistribution datatypes.JSON `gorm:"type:jsonb;comment:类别分布" json:"category_distribution"`
	TypeDistribution     datatypes.JSON `gorm:"type:jsonb;comment:类型分布" json:"type_distribution"`

	// Quality metrics
	AverageAuthorityScore float64 `gorm:"not null;default:0;comment:平均权威性得分" json:"average_authority_score"`
	AverageTrustScore     float64 `gorm:"not null;default:0;comment:平均信任度得分" json:"average_trust_score"`
	AverageQualityScore   float64 `gorm:"not null;default:0;comment:平均质量得分" json:"average_quality_score"`

	// Trend analysis
	TrendData           datatypes.JSON `gorm:"type:jsonb;comment:趋势数据" json:"trend_data"`
	GrowthRate          float64        `gorm:"comment:增长率" json:"growth_rate"`
	
	// Geographic distribution
	RegionDistribution  datatypes.JSON `gorm:"type:jsonb;comment:地区分布" json:"region_distribution"`
	
	// Timestamps
	CreatedAt           time.Time      `gorm:"autoCreateTime;comment:记录创建时间" json:"created_at"`
	UpdatedAt           time.Time      `gorm:"autoUpdateTime;comment:记录最后更新时间" json:"updated_at"`
	DeletedAt           gorm.DeletedAt `gorm:"index:idx_reference_analysis_deleted_at;comment:软删除时间戳" json:"-"`

	// Relationships
	Brand Brand `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
}

// TableName methods for custom table names
func (Reference) TableName() string {
	return "references"
}

func (ReferenceAnalysis) TableName() string {
	return "reference_analyses"
}

// Helper methods for Reference
func (r *Reference) IsTopRanking() bool {
	return r.Ranking >= 1 && r.Ranking <= 3
}

func (r *Reference) IsHighQuality() bool {
	return r.QualityScore >= 80
}

func (r *Reference) IsHighAuthority() bool {
	return r.AuthorityScore >= 80
}

func (r *Reference) GetEngagementRate() float64 {
	if r.ViewCount == 0 {
		return 0
	}
	return float64(r.ClickCount+r.ShareCount) / float64(r.ViewCount) * 100
}

func (r *Reference) IsRecentlyPublished() bool {
	if r.PublishedDate == nil {
		return false
	}
	return time.Since(*r.PublishedDate).Hours() < 24*30 // 30 days
}

// Helper methods for ReferenceAnalysis
func (a *ReferenceAnalysis) GetTopRankingPercentage() float64 {
	if a.TotalReferences == 0 {
		return 0
	}
	return float64(a.TopRankingCount) / float64(a.TotalReferences) * 100
}

func (a *ReferenceAnalysis) GetAverageCTR() float64 {
	if a.TotalClicks == 0 || a.TotalReferences == 0 {
		return 0
	}
	return float64(a.TotalClicks) / float64(a.TotalReferences)
}

func (a *ReferenceAnalysis) GetDurationDays() int {
	return int(a.EndDate.Sub(a.StartDate).Hours() / 24)
}
