/*
 * @Description: 通知收件箱相关模型
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationType represents the type of notification
type NotificationType string

const (
	NotificationTypeSystem    NotificationType = "system"    // 系统通知
	NotificationTypePrompt    NotificationType = "prompt"    // 提示相关
	NotificationTypeBrand     NotificationType = "brand"     // 品牌相关
	NotificationTypeAnalytics NotificationType = "analytics" // 分析报告
	NotificationTypeAlert     NotificationType = "alert"     // 警告
	NotificationTypeUpdate    NotificationType = "update"    // 更新
)

// NotificationPriority represents the priority of notification
type NotificationPriority string

const (
	NotificationPriorityLow    NotificationPriority = "low"
	NotificationPriorityNormal NotificationPriority = "normal"
	NotificationPriorityHigh   NotificationPriority = "high"
	NotificationPriorityUrgent NotificationPriority = "urgent"
)

// Notification represents a notification in the user's inbox 首页-提示-弹窗
type Notification struct {
	// ID is the unique identifier for the notification
	ID uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:通知唯一标识符" json:"id"`

	// UserID is the foreign key reference to the user who receives this notification
	UserID uuid.UUID `gorm:"type:uuid;not null;index:idx_notification_user_id;comment:接收通知的用户ID" json:"user_id"`

	// Type categorizes the notification (system, prompt, brand, analytics, alert, update)
	Type NotificationType `gorm:"size:50;not null;index:idx_notification_type;comment:通知类型（system、prompt、brand、analytics、alert、update）" json:"type"`

	// Priority indicates the importance level of the notification (low, normal, high, urgent)
	Priority NotificationPriority `gorm:"size:50;default:normal;index:idx_notification_priority;comment:通知优先级（low、normal、high、urgent）" json:"priority"`

	// Title is the notification title/subject (required)
	Title string `gorm:"size:500;not null;comment:通知标题/主题，必填" json:"title"`

	// Content is the notification message content (required)
	Content string `gorm:"type:text;not null;comment:通知消息内容，必填" json:"content"`

	// Data contains additional structured data for the notification (JSON format)
	Data string `gorm:"type:jsonb;comment:通知的额外结构化数据（JSON格式）" json:"data"`

	// IsRead indicates whether the user has read this notification
	IsRead bool `gorm:"default:false;index:idx_notification_read;comment:用户是否已读此通知" json:"is_read"`

	// ReadAt is the timestamp when the notification was read (nullable)
	ReadAt *time.Time `gorm:"comment:通知被读取的时间戳，可为空" json:"read_at"`

	// ActionURL is an optional URL for notification actions (e.g., "View Details")
	ActionURL string `gorm:"size:500;comment:通知操作的可选URL（如查看详情）" json:"action_url"`

	// ExpiresAt is when the notification expires and should be auto-removed (nullable)
	ExpiresAt *time.Time `gorm:"index:idx_notification_expires;comment:通知过期时间，过期后自动删除，可为空" json:"expires_at"`

	// CreatedAt is the timestamp when the notification was created
	CreatedAt time.Time `gorm:"autoCreateTime;index:idx_notification_created_at;comment:通知创建时间" json:"created_at"`

	// UpdatedAt is the timestamp when the notification was last updated
	UpdatedAt time.Time `gorm:"autoUpdateTime;comment:通知最后更新时间" json:"updated_at"`

	// DeletedAt is the soft delete timestamp (hidden from JSON response)
	DeletedAt gorm.DeletedAt `gorm:"index:idx_notification_deleted_at" json:"-"`

	// Relationships
	// User is the associated user for this notification
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// NotificationSetting represents user notification preferences
type NotificationSetting struct {
	ID                     uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:通知设置唯一标识符" json:"id"`
	UserID                 uuid.UUID `gorm:"type:uuid;not null;uniqueIndex:idx_notification_setting_user_id;comment:关联的用户ID" json:"user_id"`
	EmailNotifications     bool      `gorm:"default:true;comment:是否启用邮件通知" json:"email_notifications"`
	PushNotifications      bool      `gorm:"default:true;comment:是否启用推送通知" json:"push_notifications"`
	SystemNotifications    bool      `gorm:"default:true;comment:是否启用系统通知" json:"system_notifications"`
	PromptNotifications    bool      `gorm:"default:true;comment:是否启用提示通知" json:"prompt_notifications"`
	BrandNotifications     bool      `gorm:"default:true;comment:是否启用品牌通知" json:"brand_notifications"`
	AnalyticsNotifications bool      `gorm:"default:true;comment:是否启用分析报告通知" json:"analytics_notifications"`
	AlertNotifications     bool      `gorm:"default:true;comment:是否启用警告通知" json:"alert_notifications"`
	UpdateNotifications    bool      `gorm:"default:true;comment:是否启用更新通知" json:"update_notifications"`
	CreatedAt              time.Time `gorm:"autoCreateTime;comment:设置创建时间" json:"created_at"`
	UpdatedAt              time.Time `gorm:"autoUpdateTime;comment:设置最后更新时间" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}
