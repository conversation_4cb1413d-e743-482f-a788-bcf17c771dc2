/*
 * @Description: User model for GEOK Center authentication
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// User represents a user in the GEOK Center system
type User struct {
	ID             uuid.UUID  `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:用户唯一标识符，使用UUID格式" json:"id"`
	Username       string     `gorm:"size:100;not null;uniqueIndex:idx_user_username;comment:用户名，必填且唯一" json:"username"`
	Email          string     `gorm:"size:255;not null;uniqueIndex:idx_user_email;comment:邮箱地址，必填且唯一" json:"email"`
	Password       string     `gorm:"size:255;comment:加密后的密码，不在JSON中返回" json:"-"`
	FirstName      string     `gorm:"size:100;comment:用户名字，可选" json:"first_name"`
	LastName       string     `gorm:"size:100;comment:用户姓氏，可选" json:"last_name"`
	Avatar         string     `gorm:"size:500;comment:用户头像URL，可选" json:"avatar"`
	Bio            string     `gorm:"size:500;comment:用户个人简介，可选" json:"bio"`
	Phone          string     `gorm:"size:20;comment:用户电话号码，可选" json:"phone"`
	Company        string     `gorm:"size:200;comment:用户所属公司，可选" json:"company"`
	Country        string     `gorm:"size:100;comment:用户所在国家，可选" json:"country"`
	Role           UserRole   `gorm:"size:50;default:user;index:idx_user_role;comment:用户角色，定义权限级别" json:"role"`
	Status         UserStatus `gorm:"size:50;default:active;index:idx_user_status;comment:用户账户状态" json:"status"`
	Origin         string     `gorm:"size:50;default:geok;index:idx_user_origin;comment:用户注册来源（geok、google等）" json:"origin"`
	EmailVerified  bool       `gorm:"default:false;index:idx_user_email_verified;comment:邮箱是否已验证" json:"email_verified"`
	GoogleEmail    string     `gorm:"size:255;comment:Google邮箱地址，用于Google OAuth认证" json:"google_email"`
	CurrentBrandID *string    `gorm:"size:36;index:idx_user_current_brand;comment:当前选择的品牌ID，不设置外键约束" json:"current_brand_id"`
	LastLogin      *time.Time `gorm:"index:idx_user_last_login;comment:最后登录时间，可为空" json:"last_login"`

	// 安全相关预留字段
	LoginProtection        bool `gorm:"default:false;comment:登录保护开关，预留字段" json:"login_protection"`
	PasswordChangeRequired bool `gorm:"default:false;comment:是否需要定期更改密码，预留字段" json:"password_change_required"`
	SecurityLock           bool `gorm:"default:false;comment:安全锁定状态，预留字段" json:"security_lock"`

	CreatedAt time.Time      `gorm:"autoCreateTime;comment:账户创建时间" json:"created_at"`
	UpdatedAt time.Time      `gorm:"autoUpdateTime;comment:账户最后更新时间" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index:idx_user_deleted_at;comment:软删除时间戳，不在JSON中返回" json:"-"`

	// Relationships
	Sessions []UserSession `gorm:"foreignKey:UserID" json:"sessions,omitempty"` // 用户的所有会话记录
}

// UserRole represents user roles in the system
type UserRole string

const (
	UserRoleAdmin      UserRole = "admin"      // 系统管理员
	UserRoleEnterprise UserRole = "enterprise" // 企业用户
	UserRoleUser       UserRole = "user"       // 普通用户
)

// UserStatus represents user status
type UserStatus string

const (
	UserStatusActive    UserStatus = "active"
	UserStatusInactive  UserStatus = "inactive"
	UserStatusSuspended UserStatus = "suspended"
	UserStatusPending   UserStatus = "pending"
)

// UserOrigin represents the origin/source of user registration
const (
	UserOriginGeok   = "geok"   // Default GEOK registration
	UserOriginGoogle = "google" // Google OAuth login
	UserOriginGithub = "github" // GitHub OAuth login (future)
	UserOriginWechat = "wechat" // WeChat login (future)
)

// UserSession represents user authentication sessions for tracking login sessions
type UserSession struct {
	ID        uuid.UUID `gorm:"type:uuid;default:uuid_generate_v4();primaryKey;comment:会话唯一标识符" json:"id"`
	UserID    uuid.UUID `gorm:"type:uuid;not null;index:idx_session_user_id;comment:关联的用户ID" json:"user_id"`
	Token     string    `gorm:"size:500;not null;uniqueIndex:idx_session_token;comment:会话令牌（JWT访问令牌）" json:"token"`
	ExpiresAt time.Time `gorm:"not null;index:idx_session_expires_at;comment:会话过期时间" json:"expires_at"`
	IPAddress string    `gorm:"size:45;comment:创建会话的IP地址" json:"ip_address"`
	UserAgent string    `gorm:"size:500;comment:浏览器/客户端用户代理字符串" json:"user_agent"`
	IsActive  bool      `gorm:"default:true;index:idx_session_active;comment:会话是否处于活跃状态" json:"is_active"`
	CreatedAt time.Time `gorm:"autoCreateTime;comment:会话创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime;comment:会话最后更新时间" json:"updated_at"`

	// Relationships
	User User `gorm:"foreignKey:UserID" json:"user,omitempty"` // 关联的用户信息
}

// GetFullName returns the user's full name
func (u *User) GetFullName() string {
	if u.FirstName != "" && u.LastName != "" {
		return u.FirstName + " " + u.LastName
	}
	if u.FirstName != "" {
		return u.FirstName
	}
	if u.LastName != "" {
		return u.LastName
	}
	return u.Username
}
