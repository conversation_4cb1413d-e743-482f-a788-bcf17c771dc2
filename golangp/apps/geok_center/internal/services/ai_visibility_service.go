/*
 * @Description: AI可见性指标计算服务 - 计算AI搜索问题的可见性指标
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package services

import (
	"encoding/json"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/common/logging"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AIVisibilityService handles AI visibility metrics calculation
type AIVisibilityService struct {
	db *gorm.DB
}

// NewAIVisibilityService creates a new AI visibility service
func NewAIVisibilityService(db *gorm.DB) *AIVisibilityService {
	return &AIVisibilityService{
		db: db,
	}
}

// CalculateVisibilityMetrics calculates visibility metrics for an AI search response
func (s *AIVisibilityService) CalculateVisibilityMetrics(responseID uuid.UUID) error {
	var response models.AISearchResponse
	if err := s.db.Preload("AISearch").Preload("AISearch.Brand").First(&response, "id = ?", responseID).Error; err != nil {
		return fmt.Errorf("failed to get AI search response: %w", err)
	}

	// 计算可见性指标
	visibilityData, err := s.calculateVisibilityData(&response)
	if err != nil {
		return fmt.Errorf("failed to calculate visibility data: %w", err)
	}

	// 计算关键词指标
	keywordData, err := s.calculateKeywordData(&response)
	if err != nil {
		return fmt.Errorf("failed to calculate keyword data: %w", err)
	}

	// 设置指标数据到响应中
	if err := response.SetVisibilityData(visibilityData); err != nil {
		return fmt.Errorf("failed to set visibility data: %w", err)
	}

	if err := response.SetKeywordMetrics(keywordData); err != nil {
		return fmt.Errorf("failed to set keyword metrics: %w", err)
	}

	// 计算综合得分
	if err := response.CalculateVisibilityScore(); err != nil {
		return fmt.Errorf("failed to calculate visibility score: %w", err)
	}

	// 保存到数据库
	if err := s.db.Save(&response).Error; err != nil {
		return fmt.Errorf("failed to save response with metrics: %w", err)
	}

	// 创建独立的可见性指标记录
	if err := s.createVisibilityMetricsRecord(&response, visibilityData, keywordData); err != nil {
		logging.Warning("Failed to create visibility metrics record: %v", err)
	}

	logging.Info("Successfully calculated visibility metrics for response %s", responseID)
	return nil
}

// calculateVisibilityData calculates visibility metrics data
func (s *AIVisibilityService) calculateVisibilityData(response *models.AISearchResponse) (*models.VisibilityMetricsData, error) {
	brand := response.AISearch.Brand
	responseText := response.Response

	data := &models.VisibilityMetricsData{
		ExtraMetrics: make(map[string]interface{}),
	}

	// 1. 计算AI中出现频率
	data.AIFrequency = s.calculateAIFrequency(brand, responseText)

	// 2. 计算品牌推荐率
	data.BrandRecommendRate = s.calculateBrandRecommendationRate(brand, responseText)

	// 3. 计算品牌搜索率
	data.BrandSearchRate = s.calculateBrandSearchRate(brand, responseText)

	// 4. 计算品牌在AI市场的首推率
	data.FirstChoiceRate = s.calculateFirstChoiceRate(brand, responseText, response.BrandPosition)

	// 5. 分析品牌提及位置
	data.BrandPosition = response.BrandPosition

	// 6. 分析品牌情感倾向
	data.BrandSentiment = response.BrandSentiment

	// 7. 分析竞争对手数量
	data.CompetitorCount = s.countCompetitors(responseText)

	// 8. 估算市场占有率
	data.MarketShare = s.estimateMarketShare(brand, responseText, data.CompetitorCount)

	return data, nil
}

// calculateKeywordData calculates keyword metrics data
func (s *AIVisibilityService) calculateKeywordData(response *models.AISearchResponse) (*models.KeywordMetricsData, error) {
	responseText := response.Response
	question := response.AISearch.Question

	data := &models.KeywordMetricsData{
		KeywordFrequency:  make(map[string]int),
		KeywordCategories: make(map[string][]string),
		ExtraKeywordData:  make(map[string]interface{}),
	}

	// 1. 提取和计算关键词频率
	data.KeywordFrequency = s.extractKeywordFrequency(responseText)

	// 2. 识别主要关键词
	data.MainKeywords = s.identifyMainKeywords(data.KeywordFrequency, 10)

	// 3. 计算关键词密度
	data.KeywordDensity = s.calculateKeywordDensity(responseText, data.MainKeywords)

	// 4. 计算语义相关性
	data.SemanticRelevance = s.calculateSemanticRelevance(question, responseText)

	// 5. 计算搜索意图匹配度
	data.IntentMatch = s.calculateIntentMatch(question, responseText)

	// 6. 识别长尾关键词
	data.LongTailKeywords = s.identifyLongTailKeywords(data.KeywordFrequency)

	// 7. 关键词分类
	data.KeywordCategories = s.classifyKeywords(data.MainKeywords)

	return data, nil
}

// calculateAIFrequency calculates how frequently the brand appears in AI responses
func (s *AIVisibilityService) calculateAIFrequency(brand models.Brand, responseText string) float64 {
	brandName := strings.ToLower(brand.Name)
	responseTextLower := strings.ToLower(responseText)

	// 计算品牌名称出现次数
	count := strings.Count(responseTextLower, brandName)

	// 计算相对频率 (每100字的出现次数)
	textLength := len(responseText)
	if textLength == 0 {
		return 0
	}

	frequency := float64(count) / float64(textLength) * 100

	// 标准化到0-100范围
	if frequency > 10 {
		frequency = 100
	} else {
		frequency = frequency * 10
	}

	return frequency
}

// calculateBrandRecommendationRate calculates brand recommendation rate
func (s *AIVisibilityService) calculateBrandRecommendationRate(brand models.Brand, responseText string) float64 {
	responseTextLower := strings.ToLower(responseText)
	brandNameLower := strings.ToLower(brand.Name)

	// 推荐相关的关键词
	recommendKeywords := []string{"推荐", "建议", "选择", "最好", "优秀", "首选", "值得", "recommend", "suggest", "best", "top"}

	score := 0.0
	for _, keyword := range recommendKeywords {
		if strings.Contains(responseTextLower, keyword) {
			// 检查推荐词附近是否有品牌名称
			if s.isNearBrand(responseTextLower, keyword, brandNameLower, 50) {
				score += 20
			}
		}
	}

	// 基础推荐分数
	if strings.Contains(responseTextLower, brandNameLower) {
		score += 30
	}

	// 限制在0-100范围内
	if score > 100 {
		score = 100
	}

	return score
}

// calculateBrandSearchRate calculates brand search rate
func (s *AIVisibilityService) calculateBrandSearchRate(brand models.Brand, responseText string) float64 {
	// 这里可以基于历史搜索数据计算，暂时使用简化算法
	brandMentioned := strings.Contains(strings.ToLower(responseText), strings.ToLower(brand.Name))

	if brandMentioned {
		// 基于品牌在响应中的位置和上下文计算搜索率
		baseRate := 50.0

		// 如果品牌被明确提及，增加分数
		if brandMentioned {
			baseRate += 30
		}

		// 根据响应长度调整（更详细的回答通常表示更高的搜索率）
		if len(responseText) > 500 {
			baseRate += 10
		}

		return baseRate
	}

	return 10.0 // 基础搜索率
}

// calculateFirstChoiceRate calculates first choice rate in AI market
func (s *AIVisibilityService) calculateFirstChoiceRate(brand models.Brand, responseText string, position int) float64 {
	if position == 1 {
		return 100.0
	} else if position == 2 {
		return 80.0
	} else if position == 3 {
		return 60.0
	} else if position > 0 && position <= 5 {
		return 40.0
	} else if strings.Contains(strings.ToLower(responseText), strings.ToLower(brand.Name)) {
		return 20.0
	}

	return 0.0
}

// countCompetitors counts the number of competitors mentioned in the response
func (s *AIVisibilityService) countCompetitors(responseText string) int {
	// 简化的竞争对手识别算法
	// 在实际应用中，这里应该有一个竞争对手数据库

	// 寻找可能的品牌名称模式
	brandPattern := regexp.MustCompile(`[A-Z][a-zA-Z]+(?:\s+[A-Z][a-zA-Z]+)*`)
	matches := brandPattern.FindAllString(responseText, -1)

	// 去重并过滤常见词汇
	uniqueBrands := make(map[string]bool)
	commonWords := map[string]bool{
		"The": true, "This": true, "That": true, "These": true, "Those": true,
		"When": true, "Where": true, "What": true, "How": true, "Why": true,
	}

	for _, match := range matches {
		if !commonWords[match] && len(match) > 2 {
			uniqueBrands[match] = true
		}
	}

	return len(uniqueBrands)
}

// estimateMarketShare estimates market share based on mention context
func (s *AIVisibilityService) estimateMarketShare(brand models.Brand, responseText string, competitorCount int) float64 {
	if competitorCount == 0 {
		return 100.0
	}

	// 基础市场份额估算
	baseShare := 100.0 / float64(competitorCount+1)

	// 根据品牌在响应中的重要性调整
	brandNameLower := strings.ToLower(brand.Name)
	responseTextLower := strings.ToLower(responseText)

	brandMentions := strings.Count(responseTextLower, brandNameLower)
	if brandMentions > 1 {
		baseShare *= 1.5
	}

	// 限制在合理范围内
	if baseShare > 80 {
		baseShare = 80
	}

	return baseShare
}

// extractKeywordFrequency extracts and counts keyword frequency
func (s *AIVisibilityService) extractKeywordFrequency(text string) map[string]int {
	frequency := make(map[string]int)

	// 转换为小写并分词
	words := strings.Fields(strings.ToLower(text))

	// 停用词列表
	stopWords := map[string]bool{
		"的": true, "是": true, "在": true, "有": true, "和": true, "与": true, "或": true,
		"但": true, "而": true, "也": true, "都": true, "很": true, "更": true, "最": true,
		"the": true, "is": true, "at": true, "which": true, "on": true, "and": true,
		"or": true, "but": true, "also": true, "all": true, "very": true, "more": true,
	}

	for _, word := range words {
		// 清理标点符号
		word = regexp.MustCompile(`[^\p{L}\p{N}]+`).ReplaceAllString(word, "")

		// 过滤停用词和短词
		if len(word) > 2 && !stopWords[word] {
			frequency[word]++
		}
	}

	return frequency
}

// identifyMainKeywords identifies main keywords based on frequency
func (s *AIVisibilityService) identifyMainKeywords(frequency map[string]int, limit int) []string {
	type keywordFreq struct {
		word string
		freq int
	}

	var keywords []keywordFreq
	for word, freq := range frequency {
		keywords = append(keywords, keywordFreq{word, freq})
	}

	// 按频率排序
	sort.Slice(keywords, func(i, j int) bool {
		return keywords[i].freq > keywords[j].freq
	})

	var result []string
	for i, kw := range keywords {
		if i >= limit {
			break
		}
		result = append(result, kw.word)
	}

	return result
}

// calculateKeywordDensity calculates keyword density
func (s *AIVisibilityService) calculateKeywordDensity(text string, keywords []string) float64 {
	if len(keywords) == 0 {
		return 0
	}

	totalWords := len(strings.Fields(text))
	if totalWords == 0 {
		return 0
	}

	keywordCount := 0
	textLower := strings.ToLower(text)

	for _, keyword := range keywords {
		keywordCount += strings.Count(textLower, strings.ToLower(keyword))
	}

	return float64(keywordCount) / float64(totalWords) * 100
}

// calculateSemanticRelevance calculates semantic relevance between question and response
func (s *AIVisibilityService) calculateSemanticRelevance(question, response string) float64 {
	// 简化的语义相关性计算
	questionWords := strings.Fields(strings.ToLower(question))
	responseWords := strings.Fields(strings.ToLower(response))

	questionWordSet := make(map[string]bool)
	for _, word := range questionWords {
		questionWordSet[word] = true
	}

	matchCount := 0
	for _, word := range responseWords {
		if questionWordSet[word] {
			matchCount++
		}
	}

	if len(responseWords) == 0 {
		return 0
	}

	relevance := float64(matchCount) / float64(len(responseWords)) * 100
	if relevance > 100 {
		relevance = 100
	}

	return relevance
}

// calculateIntentMatch calculates search intent match
func (s *AIVisibilityService) calculateIntentMatch(question, response string) float64 {
	// 识别问题意图
	questionLower := strings.ToLower(question)
	responseLower := strings.ToLower(response)

	// 意图关键词映射
	intentKeywords := map[string][]string{
		"informational": {"什么", "如何", "为什么", "怎么", "what", "how", "why"},
		"commercial":    {"购买", "价格", "比较", "推荐", "buy", "price", "compare", "recommend"},
		"navigational":  {"官网", "地址", "联系", "website", "contact", "address"},
	}

	// 检测问题意图
	detectedIntent := ""
	for intent, keywords := range intentKeywords {
		for _, keyword := range keywords {
			if strings.Contains(questionLower, keyword) {
				detectedIntent = intent
				break
			}
		}
		if detectedIntent != "" {
			break
		}
	}

	// 检查回答是否匹配意图
	if detectedIntent == "" {
		return 50.0 // 默认匹配度
	}

	matchScore := 50.0
	responseKeywords := intentKeywords[detectedIntent]
	for _, keyword := range responseKeywords {
		if strings.Contains(responseLower, keyword) {
			matchScore += 10
		}
	}

	if matchScore > 100 {
		matchScore = 100
	}

	return matchScore
}

// identifyLongTailKeywords identifies long-tail keywords
func (s *AIVisibilityService) identifyLongTailKeywords(frequency map[string]int) []string {
	var longTail []string

	for word, freq := range frequency {
		// 长尾关键词通常频率较低但词长较长
		if freq <= 2 && len(word) > 5 {
			longTail = append(longTail, word)
		}
	}

	return longTail
}

// classifyKeywords classifies keywords into categories
func (s *AIVisibilityService) classifyKeywords(keywords []string) map[string][]string {
	classification := map[string][]string{
		"品牌相关": {},
		"产品相关": {},
		"技术相关": {},
		"其他":   {},
	}

	// 简化的关键词分类
	brandKeywords := []string{"品牌", "公司", "企业", "brand", "company"}
	productKeywords := []string{"产品", "服务", "功能", "product", "service", "feature"}
	techKeywords := []string{"技术", "算法", "系统", "平台", "technology", "algorithm", "system", "platform"}

	for _, keyword := range keywords {
		keywordLower := strings.ToLower(keyword)
		classified := false

		for _, brandKw := range brandKeywords {
			if strings.Contains(keywordLower, brandKw) {
				classification["品牌相关"] = append(classification["品牌相关"], keyword)
				classified = true
				break
			}
		}

		if !classified {
			for _, productKw := range productKeywords {
				if strings.Contains(keywordLower, productKw) {
					classification["产品相关"] = append(classification["产品相关"], keyword)
					classified = true
					break
				}
			}
		}

		if !classified {
			for _, techKw := range techKeywords {
				if strings.Contains(keywordLower, techKw) {
					classification["技术相关"] = append(classification["技术相关"], keyword)
					classified = true
					break
				}
			}
		}

		if !classified {
			classification["其他"] = append(classification["其他"], keyword)
		}
	}

	return classification
}

// createVisibilityMetricsRecord creates a separate visibility metrics record
func (s *AIVisibilityService) createVisibilityMetricsRecord(response *models.AISearchResponse, visibilityData *models.VisibilityMetricsData, keywordData *models.KeywordMetricsData) error {
	metricsRecord := &models.AIVisibilityMetrics{
		ID:           uuid.New(),
		AISearchID:   response.AISearchID,
		AIResponseID: response.ID,
		BrandID:      response.AISearch.Brand.ID,
		CalculatedAt: time.Now(),
	}

	// 设置可见性数据
	if err := metricsRecord.SetVisibilityData(visibilityData); err != nil {
		return fmt.Errorf("failed to set visibility data: %w", err)
	}

	// 设置关键词数据
	if err := metricsRecord.SetKeywordData(keywordData); err != nil {
		return fmt.Errorf("failed to set keyword data: %w", err)
	}

	// 计算各项得分
	metricsRecord.FrequencyScore = visibilityData.AIFrequency
	metricsRecord.RecommendationScore = visibilityData.BrandRecommendRate
	metricsRecord.SearchRateScore = visibilityData.BrandSearchRate
	metricsRecord.FirstChoiceScore = visibilityData.FirstChoiceRate

	// 计算综合得分
	metricsRecord.CalculateOverallScore()

	// 保存到数据库
	if err := s.db.Create(metricsRecord).Error; err != nil {
		return fmt.Errorf("failed to create visibility metrics record: %w", err)
	}

	return nil
}

// isNearBrand checks if a keyword is near a brand name within specified distance
func (s *AIVisibilityService) isNearBrand(text, keyword, brandName string, maxDistance int) bool {
	keywordIndex := strings.Index(text, keyword)
	brandIndex := strings.Index(text, brandName)

	if keywordIndex == -1 || brandIndex == -1 {
		return false
	}

	distance := keywordIndex - brandIndex
	if distance < 0 {
		distance = -distance
	}

	return distance <= maxDistance
}

// CalculateBrandVisibilityAggregation calculates aggregated visibility metrics for a brand
func (s *AIVisibilityService) CalculateBrandVisibilityAggregation(brandID uuid.UUID, startDate, endDate time.Time) (*models.AIVisibilityAggregation, error) {
	var metrics []models.AIVisibilityMetrics

	if err := s.db.Where("brand_id = ? AND calculated_at BETWEEN ? AND ?", brandID, startDate, endDate).
		Find(&metrics).Error; err != nil {
		return nil, fmt.Errorf("failed to get visibility metrics: %w", err)
	}

	if len(metrics) == 0 {
		return nil, fmt.Errorf("no visibility metrics found for brand in specified date range")
	}

	aggregation := &models.AIVisibilityAggregation{
		ID:             uuid.New(),
		BrandID:        brandID,
		StartDate:      startDate,
		EndDate:        endDate,
		TotalQuestions: len(metrics),
	}

	// 计算平均得分
	var totalOverall, totalFreq, totalRec, totalSearch, totalFirst float64
	for _, metric := range metrics {
		totalOverall += metric.OverallScore
		totalFreq += metric.FrequencyScore
		totalRec += metric.RecommendationScore
		totalSearch += metric.SearchRateScore
		totalFirst += metric.FirstChoiceScore
	}

	count := float64(len(metrics))
	aggregation.AverageOverallScore = totalOverall / count
	aggregation.AverageFrequencyScore = totalFreq / count
	aggregation.AverageRecommendationScore = totalRec / count
	aggregation.AverageSearchRateScore = totalSearch / count
	aggregation.AverageFirstChoiceScore = totalFirst / count

	// 简化的趋势分析
	if aggregation.AverageOverallScore > 70 {
		aggregation.TrendDirection = "上升"
	} else if aggregation.AverageOverallScore < 40 {
		aggregation.TrendDirection = "下降"
	} else {
		aggregation.TrendDirection = "稳定"
	}

	// 保存聚合数据
	if err := s.db.Create(aggregation).Error; err != nil {
		return nil, fmt.Errorf("failed to create visibility aggregation: %w", err)
	}

	return aggregation, nil
}

// GetVisibilityMetricsByResponse gets visibility metrics for a specific response
func (s *AIVisibilityService) GetVisibilityMetricsByResponse(responseID uuid.UUID) (*models.AIVisibilityMetrics, error) {
	var metrics models.AIVisibilityMetrics
	if err := s.db.Where("ai_response_id = ?", responseID).First(&metrics).Error; err != nil {
		return nil, fmt.Errorf("failed to get visibility metrics: %w", err)
	}
	return &metrics, nil
}

// GetBrandVisibilityTrend gets visibility trend for a brand with associated AI search and response data
func (s *AIVisibilityService) GetBrandVisibilityTrend(brandID uuid.UUID, days int) ([]models.AIVisibilityMetrics, error) {
	startDate := time.Now().AddDate(0, 0, -days)

	var metrics []models.AIVisibilityMetrics
	if err := s.db.Preload("AISearch").Preload("AIResponse").Preload("Brand").
		Where("brand_id = ? AND calculated_at >= ?", brandID, startDate).
		Order("calculated_at ASC").
		Find(&metrics).Error; err != nil {
		return nil, fmt.Errorf("failed to get visibility trend: %w", err)
	}

	logging.Info("Retrieved %d visibility metrics for brand %s over %d days", len(metrics), brandID, days)
	return metrics, nil
}

// convertToInt converts interface{} to int, handling different number types
func (s *AIVisibilityService) convertToInt(value interface{}) int {
	switch v := value.(type) {
	case float64:
		return int(v)
	case int:
		return v
	case int64:
		return int(v)
	default:
		return 0
	}
}

// GetVisibilityMetricsStats gets visibility metrics statistics for the latest N days of data
func (s *AIVisibilityService) GetVisibilityMetricsStats(brandID *uuid.UUID, days int) (map[string]interface{}, error) {
	var metrics []models.AIVisibilityMetrics
	query := s.db.Order("calculated_at DESC")

	if brandID != nil {
		query = query.Where("brand_id = ?", *brandID)
	}

	// 获取最新的N天数据，而不是最近N天的数据
	query = query.Limit(days)

	if err := query.Find(&metrics).Error; err != nil {
		return nil, fmt.Errorf("failed to get visibility metrics: %w", err)
	}

	// 初始化聚合数据结构
	keywordFrequencyMap := make(map[string]int)
	brandRecommendMap := make(map[string]int)
	brandFirstChoiceMap := make(map[string]int)
	var brandSearchRateList []map[string]interface{}
	totalMetrics := len(metrics)

	for _, metric := range metrics {
		// 从JSON数据中提取关键词数据
		var rawKeywordData map[string]interface{}
		if err := json.Unmarshal(metric.KeywordData, &rawKeywordData); err != nil {
			logging.Warning("Failed to unmarshal keyword data for metric %s: %v", metric.ID, err)
			continue
		}

		// 1. 处理"在AI中出现频率"数据（累加）
		if aiFrequencyData, exists := rawKeywordData["在AI中出现频率"]; exists {
			if freqMap, ok := aiFrequencyData.(map[string]interface{}); ok {
				for keyword, freqInterface := range freqMap {
					if freq := s.convertToInt(freqInterface); freq > 0 {
						keywordFrequencyMap[keyword] += freq
					}
				}
			}
		}

		// 2. 处理"品牌推荐率"数据（累加）
		if brandRecommendData, exists := rawKeywordData["品牌推荐率"]; exists {
			if recommendMap, ok := brandRecommendData.(map[string]interface{}); ok {
				for brand, rateInterface := range recommendMap {
					if rate := s.convertToInt(rateInterface); rate > 0 {
						brandRecommendMap[brand] += rate
					}
				}
			}
		}

		// 3. 处理"品牌在AI市场的首推率"数据（累加）
		if brandFirstChoiceData, exists := rawKeywordData["品牌在AI市场的首推率"]; exists {
			if firstChoiceMap, ok := brandFirstChoiceData.(map[string]interface{}); ok {
				for brand, rateInterface := range firstChoiceMap {
					if rate := s.convertToInt(rateInterface); rate > 0 {
						brandFirstChoiceMap[brand] += rate
					}
				}
			}
		}

		// 4. 处理"品牌搜索率"数据（不累加，每天一个对象）
		if brandSearchData, exists := rawKeywordData["品牌搜索率"]; exists {
			if searchMap, ok := brandSearchData.(map[string]interface{}); ok {
				searchRateEntry := make(map[string]interface{})
				searchRateEntry["date"] = metric.CalculatedAt.Format("2006-01-02")
				searchRateEntry["data"] = searchMap
				brandSearchRateList = append(brandSearchRateList, searchRateEntry)
			}
		}
	}

	// 1. 处理关键词频率数据
	keywordFrequencyResult := s.processKeywordFrequency(keywordFrequencyMap)

	// 2. 处理品牌推荐率数据
	brandRecommendResult := s.processBrandData(brandRecommendMap, "品牌推荐率")

	// 3. 处理品牌首推率数据
	brandFirstChoiceResult := s.processBrandData(brandFirstChoiceMap, "品牌在AI市场的首推率")

	// 构建最终结果
	result := map[string]interface{}{
		"keyword_frequency":       keywordFrequencyResult,
		"brand_recommend_rate":    brandRecommendResult,
		"brand_first_choice_rate": brandFirstChoiceResult,
		"brand_search_rate":       brandSearchRateList,
	}

	logging.Info("Retrieved visibility metrics stats: %d keywords, %d brand recommend entries, %d first choice entries, %d search rate entries from %d metrics over %d days",
		len(keywordFrequencyResult), len(brandRecommendResult), len(brandFirstChoiceResult), len(brandSearchRateList), totalMetrics, days)
	return result, nil
}

// processKeywordFrequency processes keyword frequency data and returns formatted result
func (s *AIVisibilityService) processKeywordFrequency(keywordFrequencyMap map[string]int) []map[string]interface{} {
	// 转换为排序的切片格式
	type keywordStat struct {
		keyword   string
		frequency int
	}

	var keywordStats []keywordStat
	for keyword, freq := range keywordFrequencyMap {
		keywordStats = append(keywordStats, keywordStat{
			keyword:   keyword,
			frequency: freq,
		})
	}

	// 按频率降序排序
	sort.Slice(keywordStats, func(i, j int) bool {
		return keywordStats[i].frequency > keywordStats[j].frequency
	})

	// 转换为前端需要的格式，限制返回前20个
	var result []map[string]interface{}
	maxResults := 20
	if len(keywordStats) < maxResults {
		maxResults = len(keywordStats)
	}

	for i := 0; i < maxResults; i++ {
		stat := keywordStats[i]
		// 计算相对频率（标准化到100）
		relativeFreq := float64(stat.frequency)

		result = append(result, map[string]interface{}{
			"keyword":   stat.keyword,
			"frequency": int(relativeFreq),
		})
	}

	return result
}

// processBrandData processes brand data (recommend rate or first choice rate) and returns formatted result
func (s *AIVisibilityService) processBrandData(brandDataMap map[string]int, dataType string) []map[string]interface{} {
	// 转换为排序的切片格式
	type brandStat struct {
		brand string
		rate  int
	}

	var brandStats []brandStat
	for brand, rate := range brandDataMap {
		brandStats = append(brandStats, brandStat{
			brand: brand,
			rate:  rate,
		})
	}

	// 按比率降序排序
	sort.Slice(brandStats, func(i, j int) bool {
		return brandStats[i].rate > brandStats[j].rate
	})
	fmt.Println(brandStats)
	// 转换为前端需要的格式
	var result []map[string]interface{}

	// 根据不同的数据类型进行不同的处理
	switch dataType {
	case "品牌推荐率":
		// 品牌推荐率的特殊处理逻辑
		for _, stat := range brandStats {
			// 计算相对比率（标准化到100）
			relativeRate := float64(stat.rate)

			result = append(result, map[string]interface{}{
				"brand": stat.brand,
				"rate":  int(relativeRate),
			})
		}
	case "品牌在AI市场的首推率":
		// 品牌首推率的特殊处理逻辑
		for _, stat := range brandStats {
			// 计算相对比率（标准化到100）
			relativeRate := float64(stat.rate)

			result = append(result, map[string]interface{}{
				"brand": stat.brand,
				"rate":  int(relativeRate),
			})
		}
	default:
		// 默认处理逻辑
		for _, stat := range brandStats {
			relativeRate := float64(stat.rate)

			result = append(result, map[string]interface{}{
				"brand": stat.brand,
				"rate":  int(relativeRate),
			})
		}
	}

	return result
}
