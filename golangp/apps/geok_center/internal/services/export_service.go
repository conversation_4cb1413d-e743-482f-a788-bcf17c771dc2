/*
 * @Description: Export service for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package services

import (
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/pkg/types"

	"gorm.io/gorm"
)

// ExportService handles data export functionality
type ExportService struct {
	db      *gorm.DB
	tempDir string
}

// NewExportService creates a new export service
func NewExportService(db *gorm.DB) *ExportService {
	return &ExportService{
		db:      db,
		tempDir: "/tmp", // This should come from config
	}
}

// ExportDashboard exports dashboard data in specified format
func (s *ExportService) ExportDashboard(request types.ExportRequest) (*types.ExportResponse, error) {
	switch request.Format {
	case "csv":
		return s.exportDashboardCSV(request)
	case "json":
		return s.exportDashboardJSON(request)
	default:
		return nil, fmt.Errorf("unsupported export format: %s", request.Format)
	}
}

// ExportAnalytics exports analytics data in specified format
func (s *ExportService) ExportAnalytics(request types.ExportRequest) (*types.ExportResponse, error) {
	switch request.DataType {
	case "brands":
		return s.exportBrands(request)
	case "search_metrics":
		return s.exportSearchMetrics(request)
	case "ai_appearances":
		return s.exportAIAppearances(request)
	case "competitor_data":
		return s.exportCompetitorData(request)
	default:
		return nil, fmt.Errorf("unsupported data type: %s", request.DataType)
	}
}

// exportDashboardCSV exports dashboard data as CSV
func (s *ExportService) exportDashboardCSV(request types.ExportRequest) (*types.ExportResponse, error) {
	fileName := s.generateFileName("dashboard", "csv")
	filePath := filepath.Join(s.tempDir, fileName)

	file, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write headers
	headers := []string{"Metric", "Value", "Date"}
	if err := writer.Write(headers); err != nil {
		return nil, fmt.Errorf("failed to write headers: %w", err)
	}

	// Get dashboard data
	analyticsService := NewAnalyticsService(s.db)
	dashboardData, err := analyticsService.GetDashboardData(request.Filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get dashboard data: %w", err)
	}

	// Write brand summary data
	brandSummaryRows := [][]string{
		{"Total Brands", strconv.Itoa(dashboardData.BrandSummary.TotalBrands), time.Now().Format("2006-01-02")},
		{"Active Brands", strconv.Itoa(dashboardData.BrandSummary.ActiveBrands), time.Now().Format("2006-01-02")},
		{"Total Search Volume", strconv.FormatInt(dashboardData.BrandSummary.TotalSearchVolume, 10), time.Now().Format("2006-01-02")},
		{"Average Visibility", fmt.Sprintf("%.2f", dashboardData.BrandSummary.AverageVisibility), time.Now().Format("2006-01-02")},
	}

	for _, row := range brandSummaryRows {
		if err := writer.Write(row); err != nil {
			return nil, fmt.Errorf("failed to write row: %w", err)
		}
	}

	// Write search metrics data
	searchMetricsRows := [][]string{
		{"Total Searches", strconv.FormatInt(dashboardData.SearchMetrics.TotalSearches, 10), time.Now().Format("2006-01-02")},
		{"Search Rate", fmt.Sprintf("%.2f", dashboardData.SearchMetrics.SearchRate), time.Now().Format("2006-01-02")},
	}

	for _, row := range searchMetricsRows {
		if err := writer.Write(row); err != nil {
			return nil, fmt.Errorf("failed to write row: %w", err)
		}
	}

	// Get file info
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	return &types.ExportResponse{
		DownloadURL: fmt.Sprintf("/api/v1/exports/download/%s", fileName),
		FileName:    fileName,
		FileSize:    fileInfo.Size(),
		ExpiresAt:   time.Now().Add(24 * time.Hour),
	}, nil
}

// exportDashboardJSON exports dashboard data as JSON
func (s *ExportService) exportDashboardJSON(request types.ExportRequest) (*types.ExportResponse, error) {
	fileName := s.generateFileName("dashboard", "json")
	filePath := filepath.Join(s.tempDir, fileName)

	// Get dashboard data
	analyticsService := NewAnalyticsService(s.db)
	dashboardData, err := analyticsService.GetDashboardData(request.Filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get dashboard data: %w", err)
	}

	// Write JSON file
	file, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	// This would typically use json.Marshal and write to file
	// For now, we'll create a simple implementation
	jsonData := fmt.Sprintf(`{
		"brand_summary": {
			"total_brands": %d,
			"active_brands": %d,
			"total_search_volume": %d,
			"average_visibility": %.2f
		},
		"search_metrics": {
			"total_searches": %d,
			"search_rate": %.2f
		},
		"ai_appearance": {
			"total_appearances": %d,
			"appearance_rate": %.2f
		},
		"exported_at": "%s"
	}`,
		dashboardData.BrandSummary.TotalBrands,
		dashboardData.BrandSummary.ActiveBrands,
		dashboardData.BrandSummary.TotalSearchVolume,
		dashboardData.BrandSummary.AverageVisibility,
		dashboardData.SearchMetrics.TotalSearches,
		dashboardData.SearchMetrics.SearchRate,
		dashboardData.AIAppearance.TotalAppearances,
		dashboardData.AIAppearance.AppearanceRate,
		time.Now().Format(time.RFC3339),
	)

	if _, err := file.WriteString(jsonData); err != nil {
		return nil, fmt.Errorf("failed to write JSON data: %w", err)
	}

	// Get file info
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	return &types.ExportResponse{
		DownloadURL: fmt.Sprintf("/api/v1/exports/download/%s", fileName),
		FileName:    fileName,
		FileSize:    fileInfo.Size(),
		ExpiresAt:   time.Now().Add(24 * time.Hour),
	}, nil
}

// exportBrands exports brand data
func (s *ExportService) exportBrands(request types.ExportRequest) (*types.ExportResponse, error) {
	fileName := s.generateFileName("brands", request.Format)
	filePath := filepath.Join(s.tempDir, fileName)

	// Get brands data
	var brands []models.Brand
	query := s.db.Model(&models.Brand{})

	// Apply filters
	if request.Filters.StartDate != nil && request.Filters.EndDate != nil {
		query = query.Where("created_at BETWEEN ? AND ?", *request.Filters.StartDate, *request.Filters.EndDate)
	}

	if err := query.Find(&brands).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch brands: %w", err)
	}

	// Export based on format
	switch request.Format {
	case "csv":
		return s.exportBrandsCSV(brands, filePath, fileName)
	default:
		return nil, fmt.Errorf("unsupported format for brands export: %s", request.Format)
	}
}

// exportBrandsCSV exports brands as CSV
func (s *ExportService) exportBrandsCSV(brands []models.Brand, filePath, fileName string) (*types.ExportResponse, error) {
	file, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write headers
	headers := []string{"ID", "Name", "Domain", "Keywords", "Linked URL", "Asset Verified", "Status", "Created At"}
	if err := writer.Write(headers); err != nil {
		return nil, fmt.Errorf("failed to write headers: %w", err)
	}

	// Write data
	for _, brand := range brands {
		assetVerified := "No"
		if brand.IsAssetVerified {
			assetVerified = "Yes"
		}

		row := []string{
			brand.ID.String(),
			brand.Name,
			brand.Domain,
			brand.Keywords,
			brand.LinkedURL,
			assetVerified,
			string(brand.Status),
			brand.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		if err := writer.Write(row); err != nil {
			return nil, fmt.Errorf("failed to write row: %w", err)
		}
	}

	// Get file info
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	return &types.ExportResponse{
		DownloadURL: fmt.Sprintf("/api/v1/exports/download/%s", fileName),
		FileName:    fileName,
		FileSize:    fileInfo.Size(),
		ExpiresAt:   time.Now().Add(24 * time.Hour),
	}, nil
}

// Placeholder methods for other export types
func (s *ExportService) exportSearchMetrics(request types.ExportRequest) (*types.ExportResponse, error) {
	// Implementation would be similar to exportBrands but for search metrics
	return nil, fmt.Errorf("search metrics export not implemented yet")
}

func (s *ExportService) exportAIAppearances(request types.ExportRequest) (*types.ExportResponse, error) {
	// Implementation would be similar to exportBrands but for AI appearances
	return nil, fmt.Errorf("AI appearances export not implemented yet")
}

func (s *ExportService) exportCompetitorData(request types.ExportRequest) (*types.ExportResponse, error) {
	// Implementation would be similar to exportBrands but for competitor data
	return nil, fmt.Errorf("competitor data export not implemented yet")
}

// generateFileName generates a unique filename for exports
func (s *ExportService) generateFileName(dataType, format string) string {
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("%s_export_%s.%s", dataType, timestamp, format)
}
