/*
 * @Description: AI搜索响应服务 - 处理AI模型响应相关的业务逻辑
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package services

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/common/logging"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AISearchResponseService handles AI search response related operations
type AISearchResponseService struct {
	db *gorm.DB
}

// NewAISearchResponseService creates a new AI search response service instance
func NewAISearchResponseService(db *gorm.DB) *AISearchResponseService {
	return &AISearchResponseService{
		db: db,
	}
}

// CreateAISearchResponse creates a new AI search response
func (s *AISearchResponseService) CreateAISearchResponse(response *models.AISearchResponse) (*models.AISearchResponse, error) {
	if err := s.db.Create(response).Error; err != nil {
		logging.Error("Failed to create AI search response: %v", err)
		return nil, fmt.Erro<PERSON>("failed to create AI search response: %w", err)
	}

	logging.Info("AI search response created successfully: %s", response.ID)
	return response, nil
}

// GetAISearchResponseByID retrieves an AI search response by ID
func (s *AISearchResponseService) GetAISearchResponseByID(id uuid.UUID) (*models.AISearchResponse, error) {
	var response models.AISearchResponse
	if err := s.db.Preload("AISearch").Preload("AISearch.Brand").First(&response, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("AI search response not found")
		}
		logging.Error("Failed to get AI search response by ID: %v", err)
		return nil, fmt.Errorf("failed to get AI search response: %w", err)
	}

	return &response, nil
}

// GetResponsesBySearchID retrieves all responses for a specific AI search
func (s *AISearchResponseService) GetResponsesBySearchID(searchID uuid.UUID) ([]models.AISearchResponse, error) {
	var responses []models.AISearchResponse
	if err := s.db.Where("ai_search_id = ?", searchID).
		Order("created_at ASC").
		Find(&responses).Error; err != nil {
		logging.Error("Failed to get responses by search ID: %v", err)
		return nil, fmt.Errorf("failed to get responses: %w", err)
	}

	return responses, nil
}

// GetResponsesByModelType retrieves responses by AI model type
func (s *AISearchResponseService) GetResponsesByModelType(modelType models.AIModelType, limit, offset int) ([]models.AISearchResponse, int64, error) {
	var responses []models.AISearchResponse
	var total int64

	// Count total records
	if err := s.db.Model(&models.AISearchResponse{}).Where("model_type = ?", modelType).Count(&total).Error; err != nil {
		logging.Error("Failed to count responses by model type: %v", err)
		return nil, 0, fmt.Errorf("failed to count responses: %w", err)
	}

	// Get paginated results
	if err := s.db.Where("model_type = ?", modelType).
		Preload("AISearch").
		Preload("AISearch.Brand").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&responses).Error; err != nil {
		logging.Error("Failed to get responses by model type: %v", err)
		return nil, 0, fmt.Errorf("failed to get responses: %w", err)
	}

	return responses, total, nil
}

// GetResponsesWithBrandMention retrieves responses that mention the brand
func (s *AISearchResponseService) GetResponsesWithBrandMention(brandID uuid.UUID, limit, offset int) ([]models.AISearchResponse, int64, error) {
	var responses []models.AISearchResponse
	var total int64

	query := s.db.Model(&models.AISearchResponse{}).
		Joins("JOIN ai_searches ON ai_search_responses.ai_search_id = ai_searches.id").
		Where("ai_searches.brand_id = ? AND ai_search_responses.brand_mentioned = ?", brandID, true)

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		logging.Error("Failed to count responses with brand mention: %v", err)
		return nil, 0, fmt.Errorf("failed to count responses: %w", err)
	}

	// Get paginated results
	if err := s.db.Joins("JOIN ai_searches ON ai_search_responses.ai_search_id = ai_searches.id").
		Where("ai_searches.brand_id = ? AND ai_search_responses.brand_mentioned = ?", brandID, true).
		Preload("AISearch").
		Preload("AISearch.Brand").
		Order("ai_search_responses.brand_position ASC, ai_search_responses.created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&responses).Error; err != nil {
		logging.Error("Failed to get responses with brand mention: %v", err)
		return nil, 0, fmt.Errorf("failed to get responses: %w", err)
	}

	return responses, total, nil
}

// AnalyzeBrandMention analyzes if and how a brand is mentioned in the response
func (s *AISearchResponseService) AnalyzeBrandMention(response *models.AISearchResponse, brandName string) error {
	responseText := strings.ToLower(response.Response)
	brandNameLower := strings.ToLower(brandName)

	// Check if brand is mentioned
	if strings.Contains(responseText, brandNameLower) {
		response.BrandMentioned = true

		// Find position of brand mention (simplified)
		sentences := strings.Split(response.Response, ".")
		for i, sentence := range sentences {
			if strings.Contains(strings.ToLower(sentence), brandNameLower) {
				response.BrandPosition = i + 1
				response.BrandContext = strings.TrimSpace(sentence)
				break
			}
		}

		// Simple sentiment analysis (placeholder)
		response.BrandSentiment = s.analyzeSentiment(response.BrandContext)
	} else {
		response.BrandMentioned = false
		response.BrandPosition = 0
		response.BrandSentiment = "neutral"
	}

	// Update the response in database
	if err := s.db.Save(response).Error; err != nil {
		logging.Error("Failed to update response with brand analysis: %v", err)
		return fmt.Errorf("failed to update response: %w", err)
	}

	return nil
}

// analyzeSentiment performs simple sentiment analysis (placeholder implementation)
func (s *AISearchResponseService) analyzeSentiment(text string) string {
	text = strings.ToLower(text)

	positiveWords := []string{"好", "优秀", "推荐", "出色", "优质", "满意", "喜欢", "赞", "棒", "excellent", "good", "great", "recommend", "amazing"}
	negativeWords := []string{"差", "糟糕", "不好", "失望", "问题", "缺点", "不推荐", "bad", "poor", "terrible", "disappointing", "issue"}

	positiveCount := 0
	negativeCount := 0

	for _, word := range positiveWords {
		if strings.Contains(text, word) {
			positiveCount++
		}
	}

	for _, word := range negativeWords {
		if strings.Contains(text, word) {
			negativeCount++
		}
	}

	if positiveCount > negativeCount {
		return "positive"
	} else if negativeCount > positiveCount {
		return "negative"
	}
	return "neutral"
}

// ExtractCitations extracts citations from AI response (placeholder implementation)
func (s *AISearchResponseService) ExtractCitations(response *models.AISearchResponse) error {
	// This is a placeholder implementation
	// In a real implementation, you would parse the response for citations, URLs, references, etc.

	citations := []map[string]interface{}{}

	// Simple URL extraction
	responseText := response.Response
	if strings.Contains(responseText, "http") {
		// Extract URLs (simplified)
		words := strings.Fields(responseText)
		for _, word := range words {
			if strings.HasPrefix(word, "http") {
				citations = append(citations, map[string]interface{}{
					"type": "url",
					"url":  word,
				})
			}
		}
	}

	// Convert to JSON
	if len(citations) > 0 {
		citationsJSON, err := json.Marshal(citations)
		if err != nil {
			logging.Error("Failed to marshal citations: %v", err)
			return fmt.Errorf("failed to marshal citations: %w", err)
		}
		response.Citations = citationsJSON
	}

	// Update the response in database
	if err := s.db.Save(response).Error; err != nil {
		logging.Error("Failed to update response with citations: %v", err)
		return fmt.Errorf("failed to update response: %w", err)
	}

	return nil
}

// GetResponseStats returns statistics about AI search responses
func (s *AISearchResponseService) GetResponseStats(brandID *uuid.UUID) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	query := s.db.Model(&models.AISearchResponse{})
	if brandID != nil {
		query = query.Joins("JOIN ai_searches ON ai_search_responses.ai_search_id = ai_searches.id").
			Where("ai_searches.brand_id = ?", *brandID)
	}

	// Count by model type
	var modelCounts []struct {
		ModelType string
		Count     int64
	}
	if err := query.Select("model_type, COUNT(*) as count").Group("model_type").Scan(&modelCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get model counts: %w", err)
	}

	modelMap := make(map[string]int64)
	for _, mc := range modelCounts {
		modelMap[mc.ModelType] = mc.Count
	}
	stats["model_counts"] = modelMap

	// Brand mention statistics
	var brandMentionCount int64
	if err := query.Where("brand_mentioned = ?", true).Count(&brandMentionCount).Error; err != nil {
		return nil, fmt.Errorf("failed to get brand mention count: %w", err)
	}
	stats["brand_mentions"] = brandMentionCount

	// Sentiment distribution
	var sentimentCounts []struct {
		Sentiment string
		Count     int64
	}
	if err := query.Where("brand_mentioned = ?", true).
		Select("brand_sentiment, COUNT(*) as count").
		Group("brand_sentiment").
		Scan(&sentimentCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get sentiment counts: %w", err)
	}

	sentimentMap := make(map[string]int64)
	for _, sc := range sentimentCounts {
		sentimentMap[sc.Sentiment] = sc.Count
	}
	stats["sentiment_distribution"] = sentimentMap

	// Average response time
	var avgResponseTime float64
	if err := query.Select("AVG(response_time)").Scan(&avgResponseTime).Error; err != nil {
		return nil, fmt.Errorf("failed to get average response time: %w", err)
	}
	stats["avg_response_time_ms"] = avgResponseTime

	return stats, nil
}

// DeleteAISearchResponse soft deletes an AI search response
func (s *AISearchResponseService) DeleteAISearchResponse(id uuid.UUID) error {
	if err := s.db.Delete(&models.AISearchResponse{}, "id = ?", id).Error; err != nil {
		logging.Error("Failed to delete AI search response: %v", err)
		return fmt.Errorf("failed to delete AI search response: %w", err)
	}

	logging.Info("AI search response deleted successfully: %s", id)
	return nil
}

// CreateMockResponse creates a mock AI response for testing purposes
func (s *AISearchResponseService) CreateMockResponse(searchID uuid.UUID, modelType models.AIModelType, brandName string) (*models.AISearchResponse, error) {
	// Generate mock response content
	mockResponses := map[models.AIModelType]string{
		models.AIModelChatGPT:  fmt.Sprintf("%s是一个知名的品牌，在行业中具有良好的声誉。它提供高质量的产品和服务，深受用户喜爱。", brandName),
		models.AIModelClaude:   fmt.Sprintf("根据我的了解，%s是一家专业的公司，致力于为客户提供优质的解决方案。该品牌在市场上表现出色。", brandName),
		models.AIModelDeepseek: fmt.Sprintf("%s在技术创新方面表现突出，其产品具有很强的竞争力。用户对其服务质量普遍满意。", brandName),
	}

	responseText := mockResponses[modelType]
	if responseText == "" {
		responseText = fmt.Sprintf("%s是一个值得关注的品牌，在相关领域有一定的影响力。", brandName)
	}

	response := &models.AISearchResponse{
		ID:           uuid.New(),
		AISearchID:   searchID,
		ModelType:    modelType,
		ModelVersion: "mock-v1.0",
		Provider:     string(modelType),
		Response:     responseText,
		ResponseTime: 1500 + int(time.Now().UnixNano()%1000),           // Random response time 1500-2500ms
		TokensUsed:   len(responseText) * 2,                            // Rough token estimation
		Confidence:   0.85 + (float64(time.Now().UnixNano()%15) / 100), // Random confidence 0.85-1.0
		Relevance:    0.80 + (float64(time.Now().UnixNano()%20) / 100), // Random relevance 0.80-1.0
		Completeness: 0.90 + (float64(time.Now().UnixNano()%10) / 100), // Random completeness 0.90-1.0
		Status:       "success",
	}

	// Analyze brand mention
	if err := s.AnalyzeBrandMention(response, brandName); err != nil {
		logging.Error("Failed to analyze brand mention for mock response: %v", err)
	}

	// Create the response
	if _, err := s.CreateAISearchResponse(response); err != nil {
		return nil, err
	}

	return response, nil
}
