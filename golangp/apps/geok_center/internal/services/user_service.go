/*
 * @Description: User service for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package services

import (
	"errors"
	"fmt"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserService handles user-related business logic
type UserService struct {
	db *gorm.DB
}

// NewUserService creates a new user service
func NewUserService(db *gorm.DB) *UserService {
	return &UserService{
		db: db,
	}
}

// GetUserByID retrieves a user by ID
func (s *UserService) GetUserByID(id uuid.UUID) (*models.User, error) {
	var user models.User
	result := s.db.First(&user, "id = ?", id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", result.Error)
	}
	return &user, nil
}

// GetUserByEmail retrieves a user by email
func (s *UserService) GetUserByEmail(email string) (*models.User, error) {
	var user models.User
	result := s.db.First(&user, "email = ?", email)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", result.Error)
	}
	return &user, nil
}

// GetUserByUsername retrieves a user by username
func (s *UserService) GetUserByUsername(username string) (*models.User, error) {
	var user models.User
	result := s.db.First(&user, "username = ?", username)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", result.Error)
	}
	return &user, nil
}

// CreateUser creates a new user
func (s *UserService) CreateUser(user *models.User) error {
	// Check if email already exists
	var count int64
	s.db.Model(&models.User{}).Where("email = ?", user.Email).Count(&count)
	if count > 0 {
		return fmt.Errorf("email already exists")
	}

	// Check if username already exists
	s.db.Model(&models.User{}).Where("username = ?", user.Username).Count(&count)
	if count > 0 {
		return fmt.Errorf("username already exists")
	}

	// Create user
	result := s.db.Create(user)
	if result.Error != nil {
		return fmt.Errorf("failed to create user: %w", result.Error)
	}

	return nil
}

// UpdateUser updates an existing user with specific fields
func (s *UserService) UpdateUser(id uuid.UUID, updates map[string]interface{}) error {
	result := s.db.Model(&models.User{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update user: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

// ListUsers retrieves users with pagination and filtering
func (s *UserService) ListUsers(page, pageSize int, search, roleFilter string) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	query := s.db.Model(&models.User{})

	// Apply search filter
	if search != "" {
		query = query.Where("username ILIKE ? OR email ILIKE ? OR first_name ILIKE ? OR last_name ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply role filter
	if roleFilter != "" {
		query = query.Where("role = ?", roleFilter)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// Get users with pagination
	offset := (page - 1) * pageSize
	result := query.Order("created_at DESC").Limit(pageSize).Offset(offset).Find(&users)
	if result.Error != nil {
		return nil, 0, fmt.Errorf("failed to list users: %w", result.Error)
	}

	return users, total, nil
}

// DeleteUser deletes a user (soft delete)
func (s *UserService) DeleteUser(id uuid.UUID) error {
	result := s.db.Delete(&models.User{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete user: %w", result.Error)
	}
	return nil
}

// GetUserByToken retrieves a user by session token
func (s *UserService) GetUserByToken(token string) (*models.User, error) {
	var session models.UserSession
	result := s.db.Where("token = ? AND is_active = ? AND expires_at > ?", token, true, time.Now()).First(&session)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("invalid or expired token")
		}
		return nil, fmt.Errorf("failed to get session: %w", result.Error)
	}

	var user models.User
	result = s.db.First(&user, "id = ?", session.UserID)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to get user: %w", result.Error)
	}

	return &user, nil
}

// CreateSession creates a new user session
func (s *UserService) CreateSession(session *models.UserSession) (*models.UserSession, error) {
	result := s.db.Create(session)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to create session: %w", result.Error)
	}
	return session, nil
}

// DeactivateSession deactivates a user session
func (s *UserService) DeactivateSession(token string) error {
	result := s.db.Model(&models.UserSession{}).Where("token = ?", token).Update("is_active", false)
	if result.Error != nil {
		return fmt.Errorf("failed to deactivate session: %w", result.Error)
	}
	return nil
}

// DeactivateAllUserSessions deactivates all sessions for a user
func (s *UserService) DeactivateAllUserSessions(userID uuid.UUID) error {
	result := s.db.Model(&models.UserSession{}).Where("user_id = ?", userID).Update("is_active", false)
	if result.Error != nil {
		return fmt.Errorf("failed to deactivate sessions: %w", result.Error)
	}
	return nil
}

// GetDB returns the database connection for direct access
func (s *UserService) GetDB() *gorm.DB {
	return s.db
}
