/*
 * @Description: 提示内容服务 - 处理提示内容相关的业务逻辑
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package services

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/common/logging"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// PromptService handles prompt-related business logic
type PromptService struct {
	db *gorm.DB
}

// NewPromptService creates a new prompt service
func NewPromptService(db *gorm.DB) *PromptService {
	return &PromptService{
		db: db,
	}
}

// CreatePrompt creates a new prompt
func (s *PromptService) CreatePrompt(prompt *models.Prompt) (*models.Prompt, error) {
	// Update ranking tier based on ranking
	prompt.UpdateRankingTier()

	if err := s.db.Create(prompt).Error; err != nil {
		logging.Error("Failed to create prompt: %v", err)
		return nil, fmt.Errorf("failed to create prompt: %w", err)
	}

	logging.Info("Prompt created successfully: %s", prompt.ID)
	return prompt, nil
}

// GetPromptByID retrieves a prompt by ID
func (s *PromptService) GetPromptByID(id uuid.UUID) (*models.Prompt, error) {
	var prompt models.Prompt
	if err := s.db.Preload("Brand").Preload("Metrics").First(&prompt, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("prompt not found")
		}
		logging.Error("Failed to get prompt by ID: %v", err)
		return nil, fmt.Errorf("failed to get prompt: %w", err)
	}

	return &prompt, nil
}

// GetPromptsByBrandID retrieves prompts for a specific brand
func (s *PromptService) GetPromptsByBrandID(brandID uuid.UUID, status *models.PromptStatus, limit, offset int) ([]models.Prompt, int64, error) {
	var prompts []models.Prompt
	var total int64

	query := s.db.Model(&models.Prompt{}).Where("brand_id = ?", brandID)

	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		logging.Error("Failed to count prompts: %v", err)
		return nil, 0, fmt.Errorf("failed to count prompts: %w", err)
	}

	// Get paginated results
	if err := query.Preload("Brand").
		Order("ranking ASC, score DESC").
		Limit(limit).
		Offset(offset).
		Find(&prompts).Error; err != nil {
		logging.Error("Failed to get prompts by brand ID: %v", err)
		return nil, 0, fmt.Errorf("failed to get prompts: %w", err)
	}

	return prompts, total, nil
}

// GetTopPrompts retrieves top-ranking prompts
func (s *PromptService) GetTopPrompts(brandID *uuid.UUID, limit int) ([]models.Prompt, error) {
	query := s.db.Model(&models.Prompt{}).Where("status = ?", models.PromptStatusActive)

	if brandID != nil {
		query = query.Where("brand_id = ?", *brandID)
	}

	var prompts []models.Prompt
	if err := query.Preload("Brand").
		Order("ranking ASC, score DESC").
		Limit(limit).
		Find(&prompts).Error; err != nil {
		logging.Error("Failed to get top prompts: %v", err)
		return nil, fmt.Errorf("failed to get top prompts: %w", err)
	}

	return prompts, nil
}

// UpdatePrompt updates an existing prompt
func (s *PromptService) UpdatePrompt(id uuid.UUID, updates map[string]interface{}) (*models.Prompt, error) {
	// Update ranking tier if ranking is being updated
	if ranking, ok := updates["ranking"].(int); ok {
		var prompt models.Prompt
		prompt.Ranking = ranking
		prompt.UpdateRankingTier()
		updates["ranking_tier"] = prompt.RankingTier
	}

	updates["updated_at"] = time.Now()

	if err := s.db.Model(&models.Prompt{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		logging.Error("Failed to update prompt: %v", err)
		return nil, fmt.Errorf("failed to update prompt: %w", err)
	}

	// Return updated prompt
	return s.GetPromptByID(id)
}

// DeletePrompt soft deletes a prompt
func (s *PromptService) DeletePrompt(id uuid.UUID) error {
	if err := s.db.Delete(&models.Prompt{}, "id = ?", id).Error; err != nil {
		logging.Error("Failed to delete prompt: %v", err)
		return fmt.Errorf("failed to delete prompt: %w", err)
	}

	logging.Info("Prompt deleted successfully: %s", id)
	return nil
}

// GeneratePromptsFromAIResponses generates prompts based on AI search responses
func (s *PromptService) GeneratePromptsFromAIResponses(brandID uuid.UUID) error {
	// Get brand information
	var brand models.Brand
	if err := s.db.First(&brand, "id = ?", brandID).Error; err != nil {
		return fmt.Errorf("brand not found: %w", err)
	}

	// Get AI search responses for this brand
	var responses []models.AISearchResponse
	if err := s.db.Joins("JOIN ai_searches ON ai_search_responses.ai_search_id = ai_searches.id").
		Where("ai_searches.brand_id = ? AND ai_search_responses.brand_mentioned = ?", brandID, true).
		Find(&responses).Error; err != nil {
		return fmt.Errorf("failed to get AI responses: %w", err)
	}

	if len(responses) == 0 {
		logging.Info("No AI responses with brand mentions found for brand %s", brand.Name)
		return nil
	}

	// Generate prompts based on responses
	prompts := s.generatePromptContent(responses, brand)

	// Create prompt records
	for i, promptContent := range prompts {
		prompt := &models.Prompt{
			ID:         uuid.New(),
			BrandID:    brandID,
			AISearchID: uuid.New(), // 临时生成，实际应该关联真实的AI搜索
			Content:    promptContent.Content,
			Category:   "ai_generated",
			Score:      promptContent.Score,
			Ranking:    i + 1,
			ShareRate:  0, // Will be calculated later
			Status:     models.PromptStatusActive,
			Priority:   1,
			Region:     "global",
			Platform:   "ai_models",
			Language:   "zh",
		}

		// Set main keywords
		if len(promptContent.Keywords) > 0 {
			keywordsJSON, _ := json.Marshal(promptContent.Keywords)
			prompt.MainKeywords = keywordsJSON
		}

		// Set keyword frequency
		if len(promptContent.KeywordFreq) > 0 {
			freqJSON, _ := json.Marshal(promptContent.KeywordFreq)
			prompt.KeywordFrequency = freqJSON
		}

		prompt.UpdateRankingTier()

		if _, err := s.CreatePrompt(prompt); err != nil {
			logging.Error("Failed to create generated prompt: %v", err)
			continue
		}
	}

	logging.Info("Generated %d prompts for brand %s", len(prompts), brand.Name)
	return nil
}

// PromptContent represents generated prompt content
type PromptContent struct {
	Content     string
	Description string
	Score       float64
	Keywords    []string
	KeywordFreq map[string]int
}

// generatePromptContent generates prompt content from AI responses
func (s *PromptService) generatePromptContent(responses []models.AISearchResponse, brand models.Brand) []PromptContent {
	var prompts []PromptContent

	// Analyze responses to generate prompts
	for i, response := range responses {
		if i >= 10 { // Limit to 10 prompts
			break
		}

		// Extract key phrases from response
		content := s.extractKeyContent(response.Response, brand.Name)
		keywords := s.extractKeywords(response.Response, brand.Keywords)

		prompt := PromptContent{
			Content:     content,
			Description: fmt.Sprintf("基于AI模型%s的回答生成的提示内容", response.ModelType),
			Score:       s.calculatePromptScore(response),
			Keywords:    keywords,
			KeywordFreq: s.calculateKeywordFrequency(response.Response, keywords),
		}

		prompts = append(prompts, prompt)
	}

	return prompts
}

// extractKeyContent extracts key content from AI response
func (s *PromptService) extractKeyContent(response, brandName string) string {
	// Simple content extraction - in a real implementation, you'd use NLP
	sentences := strings.Split(response, "。")
	var keyContent []string

	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if len(sentence) > 10 && strings.Contains(sentence, brandName) {
			keyContent = append(keyContent, sentence)
		}
	}

	if len(keyContent) > 0 {
		return strings.Join(keyContent, "。") + "。"
	}

	// Fallback to first meaningful sentence
	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if len(sentence) > 20 {
			return sentence + "。"
		}
	}

	return response[:min(len(response), 100)] + "..."
}

// extractKeywords extracts keywords from response
func (s *PromptService) extractKeywords(response, brandKeywords string) []string {
	var keywords []string

	// Add brand keywords if they appear in response
	if brandKeywords != "" {
		brandKws := strings.Split(brandKeywords, ",")
		for _, kw := range brandKws {
			kw = strings.TrimSpace(kw)
			if kw != "" && strings.Contains(strings.ToLower(response), strings.ToLower(kw)) {
				keywords = append(keywords, kw)
			}
		}
	}

	// Add some common keywords (simplified)
	commonKeywords := []string{"产品", "服务", "质量", "用户", "体验", "推荐", "优势", "特点"}
	for _, kw := range commonKeywords {
		if strings.Contains(response, kw) {
			keywords = append(keywords, kw)
		}
	}

	return keywords
}

// calculateKeywordFrequency calculates keyword frequency
func (s *PromptService) calculateKeywordFrequency(response string, keywords []string) map[string]int {
	freq := make(map[string]int)
	responseLower := strings.ToLower(response)

	for _, keyword := range keywords {
		count := strings.Count(responseLower, strings.ToLower(keyword))
		if count > 0 {
			freq[keyword] = count
		}
	}

	return freq
}

// calculatePromptScore calculates prompt score based on AI response quality
func (s *PromptService) calculatePromptScore(response models.AISearchResponse) float64 {
	score := 50.0 // Base score

	// Add points for brand mention
	if response.BrandMentioned {
		score += 20
	}

	// Add points for positive sentiment
	if response.BrandSentiment == "positive" {
		score += 15
	} else if response.BrandSentiment == "neutral" {
		score += 5
	}

	// Add points for high position
	if response.BrandPosition == 1 {
		score += 15
	} else if response.BrandPosition <= 3 {
		score += 10
	} else if response.BrandPosition <= 5 {
		score += 5
	}

	// Add points for response quality
	score += response.Confidence * 10
	score += response.Relevance * 5

	if score > 100 {
		score = 100
	}

	return score
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
