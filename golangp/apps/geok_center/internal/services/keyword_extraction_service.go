/*
 * @Description: Keyword extraction service for GEO optimization based on AI search data
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package services

import (
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/common/database/postgres"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// KeywordExtractionService handles keyword extraction and analysis from AI search data
type KeywordExtractionService struct {
	db *gorm.DB
}

// NewKeywordExtractionService creates a new keyword extraction service
func NewKeywordExtractionService() *KeywordExtractionService {
	return &KeywordExtractionService{
		db: postgres.DB,
	}
}

// KeywordAnalysisResult represents the result of keyword analysis
type KeywordAnalysisResult struct {
	Keywords          map[string]int64    `json:"keywords"`           // 关键词及其频率
	KeywordCategories map[string][]string `json:"keyword_categories"` // 关键词分类
	SearchVolumes     map[string]int64    `json:"search_volumes"`     // 搜索量
	TrendingKeywords  []string            `json:"trending_keywords"`  // 趋势关键词
	SeasonalKeywords  []string            `json:"seasonal_keywords"`  // 季节性关键词
	LongTailKeywords  []string            `json:"long_tail_keywords"` // 长尾关键词
	KeywordDifficulty map[string]float64  `json:"keyword_difficulty"` // 关键词难度
	TotalQuestions    int64               `json:"total_questions"`    // 总问题数
	AnalyzedAt        time.Time           `json:"analyzed_at"`        // 分析时间
}

// ExtractKeywordsFromBrand extracts keywords from all AI searches related to a brand
func (s *KeywordExtractionService) ExtractKeywordsFromBrand(brandID uuid.UUID, region string) (*KeywordAnalysisResult, error) {
	// Get all AI searches for the brand
	var aiSearches []models.AISearch
	query := s.db.Where("brand_id = ?", brandID)

	if region != "" {
		query = query.Where("region = ?", region)
	}

	if err := query.Find(&aiSearches).Error; err != nil {
		return nil, fmt.Errorf("failed to get AI searches: %w", err)
	}

	if len(aiSearches) == 0 {
		return &KeywordAnalysisResult{
			Keywords:          make(map[string]int64),
			KeywordCategories: make(map[string][]string),
			SearchVolumes:     make(map[string]int64),
			TrendingKeywords:  []string{},
			SeasonalKeywords:  []string{},
			LongTailKeywords:  []string{},
			KeywordDifficulty: make(map[string]float64),
			TotalQuestions:    0,
			AnalyzedAt:        time.Now(),
		}, nil
	}

	// Extract keywords from questions
	keywordFreq := make(map[string]int64)
	questionTypes := make(map[string][]string)

	for _, search := range aiSearches {
		// Extract keywords from question
		keywords := s.extractKeywordsFromText(search.Question)

		// Count frequency
		for _, keyword := range keywords {
			keywordFreq[keyword]++
		}

		// Categorize by question type
		if search.QuestionType != "" {
			questionTypes[search.QuestionType] = append(questionTypes[search.QuestionType], keywords...)
		}

		// Also extract from existing keywords field if available
		if search.Keywords != "" {
			existingKeywords := strings.Split(search.Keywords, ",")
			for _, kw := range existingKeywords {
				kw = strings.TrimSpace(kw)
				if kw != "" {
					keywordFreq[kw]++
				}
			}
		}
	}

	// Analyze and categorize keywords
	result := &KeywordAnalysisResult{
		Keywords:          keywordFreq,
		KeywordCategories: s.categorizeKeywords(keywordFreq, questionTypes),
		SearchVolumes:     s.estimateSearchVolumes(keywordFreq),
		TrendingKeywords:  s.identifyTrendingKeywords(keywordFreq),
		SeasonalKeywords:  s.identifySeasonalKeywords(keywordFreq),
		LongTailKeywords:  s.identifyLongTailKeywords(keywordFreq),
		KeywordDifficulty: s.calculateKeywordDifficulty(keywordFreq),
		TotalQuestions:    int64(len(aiSearches)),
		AnalyzedAt:        time.Now(),
	}

	return result, nil
}

// extractKeywordsFromText extracts meaningful keywords from text
func (s *KeywordExtractionService) extractKeywordsFromText(text string) []string {
	// Convert to lowercase
	text = strings.ToLower(text)

	// Remove punctuation and special characters
	reg := regexp.MustCompile(`[^\p{L}\p{N}\s]+`)
	text = reg.ReplaceAllString(text, " ")

	// Split into words
	words := strings.Fields(text)

	// Filter out stop words and short words
	stopWords := s.getStopWords()
	var keywords []string

	for _, word := range words {
		word = strings.TrimSpace(word)
		if len(word) >= 2 && !s.isStopWord(word, stopWords) {
			keywords = append(keywords, word)
		}
	}

	// Extract phrases (2-3 word combinations)
	phrases := s.extractPhrases(words, stopWords)
	keywords = append(keywords, phrases...)

	return keywords
}

// getStopWords returns a list of Chinese and English stop words
func (s *KeywordExtractionService) getStopWords() map[string]bool {
	stopWords := map[string]bool{
		// Chinese stop words
		"的": true, "了": true, "在": true, "是": true, "我": true, "有": true, "和": true, "就": true,
		"不": true, "人": true, "都": true, "一": true, "一个": true, "上": true, "也": true, "很": true,
		"到": true, "说": true, "要": true, "去": true, "你": true, "会": true, "着": true, "没有": true,
		"看": true, "好": true, "自己": true, "这": true, "那": true, "什么": true, "怎么": true, "哪个": true,
		"如何": true, "为什么": true, "吗": true, "呢": true, "啊": true, "吧": true,

		// English stop words
		"the": true, "a": true, "an": true, "and": true, "or": true, "but": true, "in": true, "on": true,
		"at": true, "to": true, "for": true, "of": true, "with": true, "by": true, "is": true, "are": true,
		"was": true, "were": true, "be": true, "been": true, "have": true, "has": true, "had": true,
		"do": true, "does": true, "did": true, "will": true, "would": true, "could": true, "should": true,
		"may": true, "might": true, "must": true, "can": true, "this": true, "that": true, "these": true,
		"those": true, "i": true, "you": true, "he": true, "she": true, "it": true, "we": true, "they": true,
		"what": true, "which": true, "who": true, "when": true, "where": true, "why": true, "how": true,
	}
	return stopWords
}

// isStopWord checks if a word is a stop word
func (s *KeywordExtractionService) isStopWord(word string, stopWords map[string]bool) bool {
	return stopWords[word]
}

// extractPhrases extracts meaningful phrases from words
func (s *KeywordExtractionService) extractPhrases(words []string, stopWords map[string]bool) []string {
	var phrases []string

	// Extract 2-word phrases
	for i := 0; i < len(words)-1; i++ {
		if !s.isStopWord(words[i], stopWords) && !s.isStopWord(words[i+1], stopWords) {
			phrase := words[i] + " " + words[i+1]
			phrases = append(phrases, phrase)
		}
	}

	// Extract 3-word phrases
	for i := 0; i < len(words)-2; i++ {
		if !s.isStopWord(words[i], stopWords) && !s.isStopWord(words[i+2], stopWords) {
			phrase := words[i] + " " + words[i+1] + " " + words[i+2]
			phrases = append(phrases, phrase)
		}
	}

	return phrases
}

// categorizeKeywords categorizes keywords by type and context
func (s *KeywordExtractionService) categorizeKeywords(keywords map[string]int64, questionTypes map[string][]string) map[string][]string {
	categories := make(map[string][]string)

	// Product-related keywords
	productKeywords := []string{}
	// Brand-related keywords
	brandKeywords := []string{}
	// Technical keywords
	technicalKeywords := []string{}
	// Comparison keywords
	comparisonKeywords := []string{}
	// Quality keywords
	qualityKeywords := []string{}

	for keyword := range keywords {
		// Categorize based on content
		if s.isProductKeyword(keyword) {
			productKeywords = append(productKeywords, keyword)
		} else if s.isBrandKeyword(keyword) {
			brandKeywords = append(brandKeywords, keyword)
		} else if s.isTechnicalKeyword(keyword) {
			technicalKeywords = append(technicalKeywords, keyword)
		} else if s.isComparisonKeyword(keyword) {
			comparisonKeywords = append(comparisonKeywords, keyword)
		} else if s.isQualityKeyword(keyword) {
			qualityKeywords = append(qualityKeywords, keyword)
		}
	}

	categories["产品相关"] = productKeywords
	categories["品牌相关"] = brandKeywords
	categories["技术相关"] = technicalKeywords
	categories["对比相关"] = comparisonKeywords
	categories["质量相关"] = qualityKeywords

	return categories
}

// Helper functions for keyword categorization
func (s *KeywordExtractionService) isProductKeyword(keyword string) bool {
	productTerms := []string{"产品", "服务", "解决方案", "平台", "系统", "软件", "硬件", "设备", "工具"}
	for _, term := range productTerms {
		if strings.Contains(keyword, term) {
			return true
		}
	}
	return false
}

func (s *KeywordExtractionService) isBrandKeyword(keyword string) bool {
	brandTerms := []string{"品牌", "公司", "厂商", "制造商", "供应商", "企业"}
	for _, term := range brandTerms {
		if strings.Contains(keyword, term) {
			return true
		}
	}
	return false
}

func (s *KeywordExtractionService) isTechnicalKeyword(keyword string) bool {
	techTerms := []string{"技术", "算法", "架构", "框架", "协议", "标准", "规范", "接口", "api"}
	for _, term := range techTerms {
		if strings.Contains(keyword, term) {
			return true
		}
	}
	return false
}

func (s *KeywordExtractionService) isComparisonKeyword(keyword string) bool {
	compTerms := []string{"比较", "对比", "vs", "versus", "哪个", "更好", "优势", "劣势", "差异", "区别"}
	for _, term := range compTerms {
		if strings.Contains(keyword, term) {
			return true
		}
	}
	return false
}

func (s *KeywordExtractionService) isQualityKeyword(keyword string) bool {
	qualityTerms := []string{"质量", "性能", "效果", "体验", "满意", "评价", "口碑", "信誉", "可靠"}
	for _, term := range qualityTerms {
		if strings.Contains(keyword, term) {
			return true
		}
	}
	return false
}

// estimateSearchVolumes estimates search volumes based on keyword frequency
func (s *KeywordExtractionService) estimateSearchVolumes(keywords map[string]int64) map[string]int64 {
	volumes := make(map[string]int64)

	for keyword, freq := range keywords {
		// Simple estimation: frequency * multiplier based on keyword length
		multiplier := int64(100)
		if len(keyword) > 10 {
			multiplier = 50 // Long tail keywords have lower search volume
		} else if len(keyword) < 5 {
			multiplier = 200 // Short keywords have higher search volume
		}

		volumes[keyword] = freq * multiplier
	}

	return volumes
}

// identifyTrendingKeywords identifies trending keywords based on frequency
func (s *KeywordExtractionService) identifyTrendingKeywords(keywords map[string]int64) []string {
	type keywordFreq struct {
		keyword string
		freq    int64
	}

	var sorted []keywordFreq
	for keyword, freq := range keywords {
		sorted = append(sorted, keywordFreq{keyword, freq})
	}

	// Sort by frequency descending
	sort.Slice(sorted, func(i, j int) bool {
		return sorted[i].freq > sorted[j].freq
	})

	// Return top 10 trending keywords
	var trending []string
	limit := 10
	if len(sorted) < limit {
		limit = len(sorted)
	}

	for i := 0; i < limit; i++ {
		trending = append(trending, sorted[i].keyword)
	}

	return trending
}

// identifySeasonalKeywords identifies seasonal keywords (placeholder implementation)
func (s *KeywordExtractionService) identifySeasonalKeywords(keywords map[string]int64) []string {
	seasonal := []string{}
	seasonalTerms := []string{"春", "夏", "秋", "冬", "节日", "假期", "年末", "年初", "季度"}

	for keyword := range keywords {
		for _, term := range seasonalTerms {
			if strings.Contains(keyword, term) {
				seasonal = append(seasonal, keyword)
				break
			}
		}
	}

	return seasonal
}

// identifyLongTailKeywords identifies long tail keywords
func (s *KeywordExtractionService) identifyLongTailKeywords(keywords map[string]int64) []string {
	var longTail []string

	for keyword, freq := range keywords {
		// Long tail: length > 10 characters and frequency < 3
		if len(keyword) > 10 && freq < 3 {
			longTail = append(longTail, keyword)
		}
	}

	return longTail
}

// calculateKeywordDifficulty calculates keyword difficulty based on frequency and competition
func (s *KeywordExtractionService) calculateKeywordDifficulty(keywords map[string]int64) map[string]float64 {
	difficulty := make(map[string]float64)

	// Find max frequency for normalization
	var maxFreq int64 = 1
	for _, freq := range keywords {
		if freq > maxFreq {
			maxFreq = freq
		}
	}

	for keyword, freq := range keywords {
		// Difficulty based on frequency (higher frequency = higher difficulty)
		// and keyword length (shorter = more difficult)
		freqScore := float64(freq) / float64(maxFreq) * 50
		lengthScore := 50.0 / float64(len(keyword)) * 10

		if lengthScore > 50 {
			lengthScore = 50
		}

		difficulty[keyword] = freqScore + lengthScore

		// Cap at 100
		if difficulty[keyword] > 100 {
			difficulty[keyword] = 100
		}
	}

	return difficulty
}
