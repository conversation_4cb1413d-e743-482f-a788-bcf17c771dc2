/*
 * @Description: Blog service for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package services

import (
	"fmt"
	"html"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/common/logging"
)

// BlogService handles blog-related operations
type BlogService struct {
	db *gorm.DB
}

// NewBlogService creates a new blog service
func NewBlogService(db *gorm.DB) *BlogService {
	return &BlogService{db: db}
}

// CreateBlog creates a new blog
func (s *BlogService) CreateBlog(blog *models.Blog) error {
	// Generate slug if not provided
	if blog.Slug == "" {
		blog.Slug = generateSlug(blog.Title)
	}

	// Calculate word count and reading time
	blog.WordCount = calculateWordCount(blog.Content)
	blog.ReadingTime = calculateReadingTime(blog.WordCount)

	// Set published time if status is published
	if blog.Status == models.BlogStatusPublished && blog.PublishedAt == nil {
		now := time.Now()
		blog.PublishedAt = &now
	}

	if err := s.db.Create(blog).Error; err != nil {
		logging.Error("Failed to create blog: %v", err)
		return fmt.Errorf("failed to create blog: %w", err)
	}

	logging.Info("Created blog: %s (ID: %s)", blog.Title, blog.ID)
	return nil
}

// GetBlogByID retrieves a blog by ID
func (s *BlogService) GetBlogByID(id uuid.UUID) (*models.Blog, error) {
	var blog models.Blog
	if err := s.db.First(&blog, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("blog not found")
		}
		logging.Error("Failed to get blog by ID %s: %v", id, err)
		return nil, fmt.Errorf("failed to get blog: %w", err)
	}

	return &blog, nil
}

// GetBlogBySlug retrieves a blog by slug
func (s *BlogService) GetBlogBySlug(slug string) (*models.Blog, error) {
	var blog models.Blog
	if err := s.db.First(&blog, "slug = ?", slug).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("blog not found")
		}
		logging.Error("Failed to get blog by slug %s: %v", slug, err)
		return nil, fmt.Errorf("failed to get blog: %w", err)
	}

	return &blog, nil
}

// UpdateBlog updates an existing blog
func (s *BlogService) UpdateBlog(id uuid.UUID, updates map[string]interface{}) (*models.Blog, error) {
	var blog models.Blog
	if err := s.db.First(&blog, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("blog not found")
		}
		return nil, fmt.Errorf("failed to find blog: %w", err)
	}

	// Handle content updates
	if content, ok := updates["content"].(string); ok {
		updates["word_count"] = calculateWordCount(content)
		updates["reading_time"] = calculateReadingTime(calculateWordCount(content))
	}

	// Handle status changes
	if status, ok := updates["status"].(models.BlogStatus); ok {
		if status == models.BlogStatusPublished && blog.PublishedAt == nil {
			now := time.Now()
			updates["published_at"] = &now
		}
	}

	if err := s.db.Model(&blog).Updates(updates).Error; err != nil {
		logging.Error("Failed to update blog %s: %v", id, err)
		return nil, fmt.Errorf("failed to update blog: %w", err)
	}

	// Reload the updated blog
	if err := s.db.First(&blog, "id = ?", id).Error; err != nil {
		return nil, fmt.Errorf("failed to reload blog: %w", err)
	}

	logging.Info("Updated blog: %s (ID: %s)", blog.Title, blog.ID)
	return &blog, nil
}

// DeleteBlog deletes a blog
func (s *BlogService) DeleteBlog(id uuid.UUID) error {
	result := s.db.Delete(&models.Blog{}, "id = ?", id)
	if result.Error != nil {
		logging.Error("Failed to delete blog %s: %v", id, result.Error)
		return fmt.Errorf("failed to delete blog: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("blog not found")
	}

	logging.Info("Deleted blog: %s", id)
	return nil
}

// ListBlogs retrieves blogs with pagination and filtering
func (s *BlogService) ListBlogs(page, pageSize int, filters map[string]interface{}) ([]models.Blog, int64, error) {
	var blogs []models.Blog
	var total int64

	query := s.db.Model(&models.Blog{})

	// Apply filters
	if brandID, ok := filters["brand_id"].(uuid.UUID); ok {
		query = query.Where("brand_id = ?", brandID)
	}
	if status, ok := filters["status"].(string); ok && status != "" {
		query = query.Where("status = ?", status)
	}
	if category, ok := filters["category"].(string); ok && category != "" {
		query = query.Where("category = ?", category)
	}
	if search, ok := filters["search"].(string); ok && search != "" {
		query = query.Where("title ILIKE ? OR excerpt ILIKE ? OR content ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		logging.Error("Failed to count blogs: %v", err)
		return nil, 0, fmt.Errorf("failed to count blogs: %w", err)
	}

	// Apply pagination
	offset := (page - 1) * pageSize
	if err := query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&blogs).Error; err != nil {
		logging.Error("Failed to list blogs: %v", err)
		return nil, 0, fmt.Errorf("failed to list blogs: %w", err)
	}

	return blogs, total, nil
}

// IncrementViewCount increments the view count for a blog
func (s *BlogService) IncrementViewCount(id uuid.UUID) error {
	if err := s.db.Model(&models.Blog{}).Where("id = ?", id).UpdateColumn("view_count", gorm.Expr("view_count + 1")).Error; err != nil {
		logging.Error("Failed to increment view count for blog %s: %v", id, err)
		return fmt.Errorf("failed to increment view count: %w", err)
	}
	return nil
}

// Helper functions

// generateSlug generates a URL-friendly slug from title
func generateSlug(title string) string {
	// Convert to lowercase
	slug := strings.ToLower(title)

	// Replace spaces and special characters with hyphens
	// Use character class for ASCII letters, numbers, and Chinese characters
	reg := regexp.MustCompile(`[^a-z0-9\p{Han}]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	// Add timestamp to ensure uniqueness
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s-%d", slug, timestamp)
}

// calculateWordCount calculates word count from content
func calculateWordCount(content string) int {
	// Remove HTML tags and markdown syntax
	text := stripHTMLAndMarkdown(content)

	// Split by whitespace and count
	words := strings.Fields(text)
	return len(words)
}

// calculateReadingTime calculates reading time based on word count
func calculateReadingTime(wordCount int) int {
	// Assume average reading speed of 200 words per minute
	readingTime := wordCount / 200
	if readingTime < 1 {
		readingTime = 1
	}
	return readingTime
}

// stripHTMLAndMarkdown removes HTML tags and markdown syntax
func stripHTMLAndMarkdown(content string) string {
	// Remove HTML tags
	htmlReg := regexp.MustCompile(`<[^>]*>`)
	text := htmlReg.ReplaceAllString(content, "")

	// Remove markdown syntax
	markdownReg := regexp.MustCompile(`[#*_\[\]()~` + "`" + `]`)
	text = markdownReg.ReplaceAllString(text, "")

	// Unescape HTML entities
	text = html.UnescapeString(text)

	return strings.TrimSpace(text)
}
