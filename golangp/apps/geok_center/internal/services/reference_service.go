/*
 * @Description: 引用服务 - 处理AI回答中的引用信息相关的业务逻辑
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package services

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/common/logging"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ReferenceService handles reference related operations
type ReferenceService struct {
	db *gorm.DB
}

// NewReferenceService creates a new reference service instance
func NewReferenceService(db *gorm.DB) *ReferenceService {
	return &ReferenceService{
		db: db,
	}
}

// CreateReference creates a new reference record
func (s *ReferenceService) CreateReference(reference *models.Reference) (*models.Reference, error) {
	if err := s.db.Create(reference).Error; err != nil {
		logging.Error("Failed to create reference: %v", err)
		return nil, fmt.Errorf("failed to create reference: %w", err)
	}

	logging.Info("Reference created successfully: %s", reference.ID)
	return reference, nil
}

// GetReferenceByID retrieves a reference by ID
func (s *ReferenceService) GetReferenceByID(id uuid.UUID) (*models.Reference, error) {
	var reference models.Reference
	if err := s.db.Preload("Brand").Preload("AISearch").Preload("AIResponse").First(&reference, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("reference not found")
		}
		logging.Error("Failed to get reference by ID: %v", err)
		return nil, fmt.Errorf("failed to get reference: %w", err)
	}

	return &reference, nil
}

// GetReferencesByBrandID retrieves references for a specific brand
func (s *ReferenceService) GetReferencesByBrandID(brandID uuid.UUID, category *models.ReferenceCategory, limit, offset int) ([]models.Reference, int64, error) {
	var references []models.Reference
	var total int64

	query := s.db.Model(&models.Reference{}).Where("brand_id = ?", brandID)

	if category != nil {
		query = query.Where("category = ?", *category)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		logging.Error("Failed to count references: %v", err)
		return nil, 0, fmt.Errorf("failed to count references: %w", err)
	}

	// Get paginated results
	if err := query.Preload("Brand").
		Preload("AISearch").
		Preload("AIResponse").
		Order("ranking ASC, authority_score DESC").
		Limit(limit).
		Offset(offset).
		Find(&references).Error; err != nil {
		logging.Error("Failed to get references by brand ID: %v", err)
		return nil, 0, fmt.Errorf("failed to get references: %w", err)
	}

	return references, total, nil
}

// GetReferencesByDomain retrieves references by domain
func (s *ReferenceService) GetReferencesByDomain(domain string, limit, offset int) ([]models.Reference, int64, error) {
	var references []models.Reference
	var total int64

	// Count total records
	if err := s.db.Model(&models.Reference{}).Where("domain = ?", domain).Count(&total).Error; err != nil {
		logging.Error("Failed to count references by domain: %v", err)
		return nil, 0, fmt.Errorf("failed to count references: %w", err)
	}

	// Get paginated results
	if err := s.db.Where("domain = ?", domain).
		Preload("Brand").
		Preload("AISearch").
		Preload("AIResponse").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&references).Error; err != nil {
		logging.Error("Failed to get references by domain: %v", err)
		return nil, 0, fmt.Errorf("failed to get references: %w", err)
	}

	return references, total, nil
}

// GetTopReferences retrieves top-ranking references
func (s *ReferenceService) GetTopReferences(brandID *uuid.UUID, limit int) ([]models.Reference, error) {
	query := s.db.Model(&models.Reference{})

	if brandID != nil {
		query = query.Where("brand_id = ?", *brandID)
	}

	var references []models.Reference
	if err := query.Preload("Brand").
		Preload("AISearch").
		Preload("AIResponse").
		Order("ranking ASC, authority_score DESC").
		Limit(limit).
		Find(&references).Error; err != nil {
		logging.Error("Failed to get top references: %v", err)
		return nil, fmt.Errorf("failed to get top references: %w", err)
	}

	return references, nil
}

// ExtractReferencesFromAIResponse extracts references from AI search response
func (s *ReferenceService) ExtractReferencesFromAIResponse(responseID uuid.UUID) error {
	// Get AI search response
	var response models.AISearchResponse
	if err := s.db.Preload("AISearch").Preload("AISearch.Brand").First(&response, "id = ?", responseID).Error; err != nil {
		return fmt.Errorf("AI search response not found: %w", err)
	}

	// Extract references from response content
	references := s.extractReferencesFromText(response.Response)

	// Create reference records
	for i, ref := range references {
		reference := &models.Reference{
			ID:             uuid.New(),
			AISearchID:     response.AISearchID,
			AIResponseID:   response.ID,
			BrandID:        response.AISearch.BrandID,
			Title:          ref.Title,
			URL:            ref.URL,
			Domain:         ref.Domain,
			Type:           ref.Type,
			Category:       ref.Category,
			Ranking:        i + 1,
			Position:       ref.Position,
			MentionCount:   1,
			CitationCount:  1,
			AuthorityScore: s.calculateAuthorityScore(ref.Domain),
			TrustScore:     s.calculateTrustScore(ref.Domain),
			QualityScore:   s.calculateQualityScore(ref),
			Region:         "global",
			Platform:       string(response.ModelType),
		}

		if _, err := s.CreateReference(reference); err != nil {
			logging.Error("Failed to create extracted reference: %v", err)
			continue
		}
	}

	logging.Info("Extracted %d references from AI response %s", len(references), response.ID)
	return nil
}

// ReferenceData represents extracted reference data
type ReferenceData struct {
	Title    string
	URL      string
	Domain   string
	Type     models.ReferenceType
	Category models.ReferenceCategory
	Position int
}

// extractReferencesFromText extracts references from response text
func (s *ReferenceService) extractReferencesFromText(text string) []ReferenceData {
	var references []ReferenceData

	// Simple URL extraction (in a real implementation, you'd use more sophisticated parsing)
	words := strings.Fields(text)
	position := 0

	for i, word := range words {
		if strings.HasPrefix(word, "http") {
			parsedURL, err := url.Parse(word)
			if err != nil {
				continue
			}

			domain := parsedURL.Host
			if strings.HasPrefix(domain, "www.") {
				domain = domain[4:]
			}

			ref := ReferenceData{
				Title:    s.generateTitleFromURL(word),
				URL:      word,
				Domain:   domain,
				Type:     s.determineReferenceType(word),
				Category: s.determineReferenceCategory(domain),
				Position: i,
			}

			references = append(references, ref)
			position++
		}
	}

	// Extract implicit references (domain mentions without URLs)
	commonDomains := []string{"wikipedia.org", "baidu.com", "zhihu.com", "csdn.net", "github.com"}
	for _, domain := range commonDomains {
		if strings.Contains(strings.ToLower(text), domain) {
			ref := ReferenceData{
				Title:    s.generateTitleFromDomain(domain),
				URL:      "https://" + domain,
				Domain:   domain,
				Type:     models.ReferenceTypeWebsite,
				Category: s.determineReferenceCategory(domain),
				Position: position,
			}
			references = append(references, ref)
			position++
		}
	}

	return references
}

// generateTitleFromURL generates a title from URL
func (s *ReferenceService) generateTitleFromURL(urlStr string) string {
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return "未知链接"
	}

	domain := parsedURL.Host
	if strings.HasPrefix(domain, "www.") {
		domain = domain[4:]
	}

	return fmt.Sprintf("来自 %s 的引用", domain)
}

// generateTitleFromDomain generates a title from domain
func (s *ReferenceService) generateTitleFromDomain(domain string) string {
	return fmt.Sprintf("来自 %s 的引用", domain)
}

// determineReferenceType determines reference type from URL
func (s *ReferenceService) determineReferenceType(urlStr string) models.ReferenceType {
	urlLower := strings.ToLower(urlStr)

	if strings.Contains(urlLower, "youtube.com") || strings.Contains(urlLower, "bilibili.com") {
		return models.ReferenceTypeVideo
	}

	if strings.Contains(urlLower, ".pdf") {
		return models.ReferenceTypeDocument
	}

	if strings.Contains(urlLower, "api") {
		return models.ReferenceTypeAPI
	}

	return models.ReferenceTypeWebsite
}

// determineReferenceCategory determines reference category from domain
func (s *ReferenceService) determineReferenceCategory(domain string) models.ReferenceCategory {
	domainLower := strings.ToLower(domain)

	if strings.Contains(domainLower, "news") || strings.Contains(domainLower, "xinhua") {
		return models.ReferenceCategoryNews
	}

	if strings.Contains(domainLower, "edu") || strings.Contains(domainLower, "wikipedia") {
		return models.ReferenceCategoryEducation
	}

	if strings.Contains(domainLower, "github") || strings.Contains(domainLower, "csdn") {
		return models.ReferenceCategoryTechnology
	}

	if strings.Contains(domainLower, "gov") {
		return models.ReferenceCategoryGovernment
	}

	return models.ReferenceCategoryOther
}

// calculateAuthorityScore calculates authority score for a domain
func (s *ReferenceService) calculateAuthorityScore(domain string) float64 {
	// Simple authority scoring based on domain (in a real implementation, you'd use domain authority APIs)
	domainLower := strings.ToLower(domain)

	highAuthority := []string{"wikipedia.org", "gov", "edu", "nature.com", "science.org"}
	mediumAuthority := []string{"baidu.com", "zhihu.com", "csdn.net", "github.com"}

	for _, auth := range highAuthority {
		if strings.Contains(domainLower, auth) {
			return 90.0 + (float64(time.Now().UnixNano()%10) / 10) // 90-100
		}
	}

	for _, auth := range mediumAuthority {
		if strings.Contains(domainLower, auth) {
			return 70.0 + (float64(time.Now().UnixNano()%20) / 10) // 70-90
		}
	}

	return 50.0 + (float64(time.Now().UnixNano()%30) / 10) // 50-80
}

// calculateTrustScore calculates trust score for a domain
func (s *ReferenceService) calculateTrustScore(domain string) float64 {
	// Simple trust scoring (in a real implementation, you'd use trust metrics)
	return s.calculateAuthorityScore(domain) * 0.9 // Trust score is usually slightly lower than authority
}

// calculateQualityScore calculates quality score for a reference
func (s *ReferenceService) calculateQualityScore(ref ReferenceData) float64 {
	score := 50.0 // Base score

	// Add points for HTTPS
	if strings.HasPrefix(ref.URL, "https://") {
		score += 10
	}

	// Add points for known quality domains
	if strings.Contains(ref.Domain, "wikipedia") || strings.Contains(ref.Domain, "gov") {
		score += 20
	}

	// Add points for educational domains
	if strings.Contains(ref.Domain, "edu") {
		score += 15
	}

	if score > 100 {
		score = 100
	}

	return score
}

// DeleteReference soft deletes a reference
func (s *ReferenceService) DeleteReference(id uuid.UUID) error {
	if err := s.db.Delete(&models.Reference{}, "id = ?", id).Error; err != nil {
		logging.Error("Failed to delete reference: %v", err)
		return fmt.Errorf("failed to delete reference: %w", err)
	}

	logging.Info("Reference deleted successfully: %s", id)
	return nil
}

// GetReferenceStatsForBrand gets reference statistics for a brand with latest 7 days aggregation
func (s *ReferenceService) GetReferenceStatsForBrand(brandID uuid.UUID, days int) ([]map[string]interface{}, error) {
	// 获取最新的N天数据
	var references []models.Reference
	query := s.db.Where("brand_id = ?", brandID).
		Order("created_at DESC").
		Limit(days)

	if err := query.Find(&references).Error; err != nil {
		logging.Error("Failed to get references for stats: %v", err)
		return nil, fmt.Errorf("failed to get references: %w", err)
	}

	// 获取前N天到2N天的数据用于计算变化
	var previousReferences []models.Reference
	previousQuery := s.db.Where("brand_id = ?", brandID).
		Order("created_at DESC").
		Limit(days).
		Offset(days)

	if err := previousQuery.Find(&previousReferences).Error; err != nil {
		logging.Error("Failed to get previous references for comparison: %v", err)
		// 如果获取失败，继续处理，但变化值为0
	}

	// 按domain聚合数据
	domainStats := make(map[string]*DomainStats)

	// 处理最新N天的数据
	for _, ref := range references {
		if _, exists := domainStats[ref.Domain]; !exists {
			domainStats[ref.Domain] = &DomainStats{
				Domain:        ref.Domain,
				ShareCount:    0,
				CitationCount: 0,
			}
		}
		domainStats[ref.Domain].ShareCount += ref.ShareCount
		domainStats[ref.Domain].CitationCount += int64(ref.CitationCount)
	}

	// 处理前N天的数据用于计算变化
	previousDomainStats := make(map[string]*DomainStats)
	for _, ref := range previousReferences {
		if _, exists := previousDomainStats[ref.Domain]; !exists {
			previousDomainStats[ref.Domain] = &DomainStats{
				Domain:        ref.Domain,
				ShareCount:    0,
				CitationCount: 0,
			}
		}
		previousDomainStats[ref.Domain].ShareCount += ref.ShareCount
		previousDomainStats[ref.Domain].CitationCount += int64(ref.CitationCount)
	}

	// 构建结果
	var result []map[string]interface{}
	for domain, stats := range domainStats {
		// 计算变化值
		var shareCountChange int64 = 0
		var citationCountChange int64 = 0

		if prevStats, exists := previousDomainStats[domain]; exists {
			shareCountChange = stats.ShareCount - prevStats.ShareCount
			citationCountChange = stats.CitationCount - prevStats.CitationCount
		} else {
			// 如果前期没有数据，变化值就是当前值
			shareCountChange = stats.ShareCount
			citationCountChange = stats.CitationCount
		}

		result = append(result, map[string]interface{}{
			"domain":                domain,
			"share_count":           stats.ShareCount,
			"citation_count":        stats.CitationCount,
			"share_count_change":    shareCountChange,
			"citation_count_change": citationCountChange,
		})
	}

	logging.Info("Retrieved reference stats for brand %s: %d domains", brandID, len(result))
	return result, nil
}

// DomainStats represents aggregated statistics for a domain
type DomainStats struct {
	Domain        string
	ShareCount    int64
	CitationCount int64
}
