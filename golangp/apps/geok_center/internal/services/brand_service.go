/*
 * @Description: Brand service for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package services

import (
	"errors"
	"fmt"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/pkg/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BrandService handles brand-related business logic
type BrandService struct {
	db *gorm.DB
}

// NewBrandService creates a new brand service
func NewBrandService(db *gorm.DB) *BrandService {
	return &BrandService{db: db}
}

// GetBrands retrieves brands with pagination and filtering
func (s *BrandService) GetBrands(pagination types.PaginationParams, filters types.FilterParams) ([]models.Brand, *types.Meta, error) {
	var brands []models.Brand
	var total int64

	query := s.db.Model(&models.Brand{})

	// Apply filters
	if filters.UserID != "" {
		query = query.Where("user_id = ?", filters.UserID)
	}

	if filters.Region != "" {
		// Join with visibility metrics to filter by region
		query = query.Joins("JOIN visibility_metrics ON brands.id = visibility_metrics.brand_id").
			Where("visibility_metrics.region = ?", filters.Region)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to count brands: %w", err)
	}

	// Apply pagination
	offset := (pagination.Page - 1) * pagination.PageSize
	if err := query.Preload("User").Offset(offset).Limit(pagination.PageSize).Find(&brands).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to fetch brands: %w", err)
	}

	meta := &types.Meta{
		Page:       pagination.Page,
		PageSize:   pagination.PageSize,
		Total:      int(total),
		TotalPages: int((total + int64(pagination.PageSize) - 1) / int64(pagination.PageSize)),
	}

	return brands, meta, nil
}

// GetBrand retrieves a single brand by ID
func (s *BrandService) GetBrand(id uuid.UUID) (*models.Brand, error) {
	var brand models.Brand
	if err := s.db.Preload("User").First(&brand, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("brand not found")
		}
		return nil, fmt.Errorf("failed to fetch brand: %w", err)
	}
	return &brand, nil
}

// CreateBrand creates a new brand
func (s *BrandService) CreateBrand(brand *models.Brand) error {
	if err := s.db.Create(brand).Error; err != nil {
		return fmt.Errorf("failed to create brand: %w", err)
	}
	return nil
}

// UpdateBrand updates an existing brand
func (s *BrandService) UpdateBrand(id uuid.UUID, updates *models.Brand) error {
	var brand models.Brand
	if err := s.db.First(&brand, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("brand not found")
		}
		return fmt.Errorf("failed to fetch brand: %w", err)
	}

	if err := s.db.Model(&brand).Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update brand: %w", err)
	}
	return nil
}

// DeleteBrand soft deletes a brand
func (s *BrandService) DeleteBrand(id uuid.UUID) error {
	var brand models.Brand
	if err := s.db.First(&brand, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("brand not found")
		}
		return fmt.Errorf("failed to fetch brand: %w", err)
	}

	if err := s.db.Delete(&brand).Error; err != nil {
		return fmt.Errorf("failed to delete brand: %w", err)
	}
	return nil
}

// CreateBrandWithAISearches creates a new brand and automatically generates AI searches
func (s *BrandService) CreateBrandWithAISearches(brand *models.Brand) (*models.Brand, error) {
	// Create the brand first
	if err := s.db.Create(brand).Error; err != nil {
		return nil, fmt.Errorf("failed to create brand: %w", err)
	}

	// Generate AI search questions for the brand
	questions := models.GenerateBrandQuestions(*brand)

	// Create AI search records
	for _, question := range questions {
		aiSearch := &models.AISearch{
			ID:           uuid.New(),
			BrandID:      brand.ID,
			Question:     question,
			QuestionType: "brand_analysis",
			Keywords:     brand.Keywords,
			Status:       models.AISearchStatusPending,
			Priority:     1,
			Region:       "global",
			Language:     "zh",
		}

		if err := s.db.Create(aiSearch).Error; err != nil {
			// Log error but don't fail the brand creation
			fmt.Printf("Failed to create AI search for question: %s, error: %v\n", question, err)
		}
	}

	return brand, nil
}

// GetBrandStats retrieves brand statistics for dashboard
func (s *BrandService) GetBrandStats(brandID uuid.UUID) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Get AI search count
	var aiSearchCount int64
	if err := s.db.Model(&models.AISearch{}).Where("brand_id = ?", brandID).Count(&aiSearchCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count AI searches: %w", err)
	}
	stats["ai_search_count"] = aiSearchCount

	// Get AI visibility metrics count
	var aiVisibilityCount int64
	if err := s.db.Model(&models.AIVisibilityMetrics{}).Where("brand_id = ?", brandID).Count(&aiVisibilityCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count AI visibility metrics: %w", err)
	}
	stats["ai_visibility_metrics_count"] = aiVisibilityCount

	// Get prompts count
	var promptCount int64
	if err := s.db.Model(&models.Prompt{}).Where("brand_id = ?", brandID).Count(&promptCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count prompts: %w", err)
	}
	stats["prompt_count"] = promptCount

	// Get references count
	var referenceCount int64
	if err := s.db.Model(&models.Reference{}).Where("brand_id = ?", brandID).Count(&referenceCount).Error; err != nil {
		return nil, fmt.Errorf("failed to count references: %w", err)
	}
	stats["reference_count"] = referenceCount

	return stats, nil
}

// SearchBrands searches brands by name or description for a specific user
func (s *BrandService) SearchBrands(query string, userID string, pagination types.PaginationParams) ([]models.Brand, *types.Meta, error) {
	var brands []models.Brand
	var total int64

	dbQuery := s.db.Model(&models.Brand{}).Where("user_id = ? AND (name ILIKE ? OR keywords ILIKE ?)", userID, "%"+query+"%", "%"+query+"%")

	// Count total records
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to count brands: %w", err)
	}

	// Apply pagination
	offset := (pagination.Page - 1) * pagination.PageSize
	if err := dbQuery.Offset(offset).Limit(pagination.PageSize).Find(&brands).Error; err != nil {
		return nil, nil, fmt.Errorf("failed to search brands: %w", err)
	}

	meta := &types.Meta{
		Page:       pagination.Page,
		PageSize:   pagination.PageSize,
		Total:      int(total),
		TotalPages: int((total + int64(pagination.PageSize) - 1) / int64(pagination.PageSize)),
	}

	return brands, meta, nil
}
