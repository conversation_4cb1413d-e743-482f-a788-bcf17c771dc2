/*
 * @Description: Analytics service for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package services

import (
	"fmt"
	"time"

	"pointer/golangp/apps/geok_center/pkg/types"

	"gorm.io/gorm"
)

// AnalyticsService handles analytics-related business logic
type AnalyticsService struct {
	db *gorm.DB
}

// NewAnalyticsService creates a new analytics service
func NewAnalyticsService(db *gorm.DB) *AnalyticsService {
	return &AnalyticsService{db: db}
}

// GetDashboardData retrieves comprehensive dashboard data
func (s *AnalyticsService) GetDashboardData(filters types.FilterParams) (*types.DashboardData, error) {
	dashboardData := &types.DashboardData{
		LastUpdated: time.Now(),
	}

	// Get brand summary
	brandSummary, err := s.getBrandSummaryData(filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get brand summary: %w", err)
	}
	dashboardData.BrandSummary = *brandSummary

	// Get search metrics
	searchMetrics, err := s.getSearchMetricsData(filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get search metrics: %w", err)
	}
	dashboardData.SearchMetrics = *searchMetrics

	// Get AI appearance data
	aiAppearance, err := s.getAIAppearanceData(filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get AI appearance data: %w", err)
	}
	dashboardData.AIAppearance = *aiAppearance

	// Get competitor analysis
	competitorAnalysis, err := s.getCompetitorAnalysisData(filters)
	if err != nil {
		return nil, fmt.Errorf("failed to get competitor analysis: %w", err)
	}
	dashboardData.CompetitorAnalysis = *competitorAnalysis

	// Get recent activity
	recentActivity, err := s.getRecentActivity(10)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent activity: %w", err)
	}
	dashboardData.RecentActivity = recentActivity

	return dashboardData, nil
}

// getBrandSummaryData retrieves brand summary data
func (s *AnalyticsService) getBrandSummaryData(filters types.FilterParams) (*types.BrandSummaryData, error) {
	var result struct {
		TotalBrands       int     `json:"total_brands"`
		ActiveBrands      int     `json:"active_brands"`
		TotalSearchVolume int64   `json:"total_search_volume"`
		AverageVisibility float64 `json:"average_visibility"`
	}

	query := `
		SELECT 
			COUNT(DISTINCT b.id) as total_brands,
			COUNT(DISTINCT CASE WHEN b.status = 'active' THEN b.id END) as active_brands,
			COALESCE(SUM(sm.search_volume), 0) as total_search_volume,
			COALESCE(AVG(sm.visibility), 0) as average_visibility
		FROM brands b
		LEFT JOIN search_metrics sm ON b.id = sm.brand_id
		WHERE b.deleted_at IS NULL
	`

	args := []interface{}{}
	if filters.StartDate != nil && filters.EndDate != nil {
		query += " AND sm.date BETWEEN ? AND ?"
		args = append(args, *filters.StartDate, *filters.EndDate)
	}
	if filters.Region != "" {
		query += " AND sm.region = ?"
		args = append(args, filters.Region)
	}

	if err := s.db.Raw(query, args...).Scan(&result).Error; err != nil {
		return nil, err
	}

	// Calculate trend direction and change percentage
	// This would typically compare with previous period
	trendDirection := "stable"
	changePercentage := 0.0

	return &types.BrandSummaryData{
		TotalBrands:       result.TotalBrands,
		ActiveBrands:      result.ActiveBrands,
		TotalSearchVolume: result.TotalSearchVolume,
		AverageVisibility: result.AverageVisibility,
		TrendDirection:    trendDirection,
		ChangePercentage:  changePercentage,
	}, nil
}

// getSearchMetricsData retrieves search metrics data
func (s *AnalyticsService) getSearchMetricsData(filters types.FilterParams) (*types.SearchMetricsData, error) {
	var totalSearches int64
	var searchRate float64

	// Get total searches and search rate
	query := `
		SELECT 
			COALESCE(SUM(search_volume), 0) as total_searches,
			COALESCE(AVG(search_rate), 0) as search_rate
		FROM search_metrics sm
		WHERE 1=1
	`

	args := []interface{}{}
	if filters.StartDate != nil && filters.EndDate != nil {
		query += " AND sm.date BETWEEN ? AND ?"
		args = append(args, *filters.StartDate, *filters.EndDate)
	}
	if filters.Region != "" {
		query += " AND sm.region = ?"
		args = append(args, filters.Region)
	}

	var result struct {
		TotalSearches int64   `json:"total_searches"`
		SearchRate    float64 `json:"search_rate"`
	}

	if err := s.db.Raw(query, args...).Scan(&result).Error; err != nil {
		return nil, err
	}

	totalSearches = result.TotalSearches
	searchRate = result.SearchRate

	// Get top keywords (mock data for now)
	topKeywords := []types.KeywordMetric{
		{Keyword: "价格", Volume: 1500, Frequency: 0.72, Trend: "up"},
		{Keyword: "质量", Volume: 1200, Frequency: 0.68, Trend: "stable"},
		{Keyword: "性能", Volume: 1000, Frequency: 0.65, Trend: "up"},
		{Keyword: "性价比", Volume: 800, Frequency: 0.60, Trend: "down"},
		{Keyword: "品牌", Volume: 600, Frequency: 0.55, Trend: "stable"},
	}

	// Get regional data
	regionalData, err := s.getRegionalData(filters)
	if err != nil {
		return nil, err
	}

	// Get trend data
	trendData, err := s.getTrendData(filters)
	if err != nil {
		return nil, err
	}

	return &types.SearchMetricsData{
		TotalSearches: totalSearches,
		SearchRate:    searchRate,
		TopKeywords:   topKeywords,
		RegionalData:  regionalData,
		TrendData:     trendData,
	}, nil
}

// getAIAppearanceData retrieves AI appearance metrics
func (s *AnalyticsService) getAIAppearanceData(filters types.FilterParams) (*types.AIAppearanceData, error) {
	query := `
		SELECT
			COUNT(*) as total_appearances,
			COALESCE(AVG(frequency), 0) as appearance_rate
		FROM ai_appearances ai
		WHERE 1=1
	`

	args := []interface{}{}
	if filters.StartDate != nil && filters.EndDate != nil {
		query += " AND ai.date BETWEEN ? AND ?"
		args = append(args, *filters.StartDate, *filters.EndDate)
	}
	if filters.Region != "" {
		query += " AND ai.region = ?"
		args = append(args, filters.Region)
	}

	var result struct {
		TotalAppearances int64   `json:"total_appearances"`
		AppearanceRate   float64 `json:"appearance_rate"`
	}

	if err := s.db.Raw(query, args...).Scan(&result).Error; err != nil {
		return nil, err
	}

	// Get platform metrics
	platformMetrics, err := s.getPlatformMetrics(filters)
	if err != nil {
		return nil, err
	}

	// Get sentiment analysis
	sentimentData := types.SentimentData{
		Positive: 0.45,
		Neutral:  0.35,
		Negative: 0.20,
	}

	// Get frequency data
	frequencyData, err := s.getFrequencyData(filters)
	if err != nil {
		return nil, err
	}

	return &types.AIAppearanceData{
		TotalAppearances:  result.TotalAppearances,
		AppearanceRate:    result.AppearanceRate,
		TopPlatforms:      platformMetrics,
		SentimentAnalysis: sentimentData,
		FrequencyData:     frequencyData,
	}, nil
}

// getCompetitorAnalysisData retrieves competitor analysis data
func (s *AnalyticsService) getCompetitorAnalysisData(filters types.FilterParams) (*types.CompetitorAnalysisData, error) {
	// Get brand distribution data (from Figma design)
	brandDistribution := []types.BrandDistributionItem{
		{Brand: "Intel", Percentage: 72.5, Color: "#3EE9BR"},
		{Brand: "AMD", Percentage: 15.7, Color: "#XCCJG5"},
		{Brand: "Apple", Percentage: 9.3, Color: "#WDUJBX"},
		{Brand: "Qualcomm", Percentage: 1.3, Color: "#JWFSEV"},
		{Brand: "ARM Holdings", Percentage: 1.2, Color: "#ZDAOYT"},
	}

	// Mock competitor metrics
	topCompetitors := []types.CompetitorMetric{
		{Name: "Intel", MarketShare: 72.5, SearchShare: 68.2, AIShare: 75.1, Trend: "stable"},
		{Name: "AMD", MarketShare: 15.7, SearchShare: 18.3, AIShare: 12.4, Trend: "up"},
		{Name: "Apple", MarketShare: 9.3, SearchShare: 10.1, AIShare: 8.7, Trend: "up"},
		{Name: "Qualcomm", MarketShare: 1.3, SearchShare: 1.8, AIShare: 2.1, Trend: "down"},
		{Name: "ARM Holdings", MarketShare: 1.2, SearchShare: 1.6, AIShare: 1.7, Trend: "stable"},
	}

	return &types.CompetitorAnalysisData{
		MarketPosition:    1, // Assuming Intel is #1
		MarketShare:       72.5,
		TopCompetitors:    topCompetitors,
		BrandDistribution: brandDistribution,
	}, nil
}

// Helper methods for analytics service

// getRegionalData retrieves regional metrics
func (s *AnalyticsService) getRegionalData(filters types.FilterParams) ([]types.RegionalMetric, error) {
	var regionalData []types.RegionalMetric

	query := `
		SELECT
			region,
			SUM(search_volume) as volume,
			(SUM(search_volume) * 100.0 / (SELECT SUM(search_volume) FROM search_metrics WHERE 1=1
	`

	args := []interface{}{}
	whereClause := ""

	if filters.StartDate != nil && filters.EndDate != nil {
		whereClause += " AND date BETWEEN ? AND ?"
		args = append(args, *filters.StartDate, *filters.EndDate)
	}

	query += whereClause + ")) as percentage FROM search_metrics WHERE 1=1" + whereClause + " GROUP BY region ORDER BY volume DESC"
	args = append(args, args...) // Duplicate args for subquery

	if err := s.db.Raw(query, args...).Scan(&regionalData).Error; err != nil {
		return nil, err
	}

	return regionalData, nil
}

// getTrendData retrieves trend data points
func (s *AnalyticsService) getTrendData(filters types.FilterParams) ([]types.TrendDataPoint, error) {
	var trendData []types.TrendDataPoint

	query := `
		SELECT
			DATE(date) as date,
			AVG(search_rate) as value
		FROM search_metrics
		WHERE 1=1
	`

	args := []interface{}{}
	if filters.StartDate != nil && filters.EndDate != nil {
		query += " AND date BETWEEN ? AND ?"
		args = append(args, *filters.StartDate, *filters.EndDate)
	}
	if filters.Region != "" {
		query += " AND region = ?"
		args = append(args, filters.Region)
	}

	query += " GROUP BY DATE(date) ORDER BY date"

	if err := s.db.Raw(query, args...).Scan(&trendData).Error; err != nil {
		return nil, err
	}

	return trendData, nil
}

// getPlatformMetrics retrieves platform metrics for AI appearances
func (s *AnalyticsService) getPlatformMetrics(filters types.FilterParams) ([]types.PlatformMetric, error) {
	var platformMetrics []types.PlatformMetric

	query := `
		SELECT
			ai_provider as platform,
			COUNT(*) as appearances,
			(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ai_appearances WHERE 1=1
	`

	args := []interface{}{}
	whereClause := ""

	if filters.StartDate != nil && filters.EndDate != nil {
		whereClause += " AND date BETWEEN ? AND ?"
		args = append(args, *filters.StartDate, *filters.EndDate)
	}
	if filters.Region != "" {
		whereClause += " AND region = ?"
		args = append(args, filters.Region)
	}

	query += whereClause + ")) as percentage FROM ai_appearances WHERE 1=1" + whereClause + " GROUP BY ai_provider ORDER BY appearances DESC"
	args = append(args, args...) // Duplicate args for subquery

	if err := s.db.Raw(query, args...).Scan(&platformMetrics).Error; err != nil {
		return nil, err
	}

	return platformMetrics, nil
}

// getFrequencyData retrieves frequency data over time
func (s *AnalyticsService) getFrequencyData(filters types.FilterParams) ([]types.FrequencyDataPoint, error) {
	var frequencyData []types.FrequencyDataPoint

	query := `
		SELECT
			DATE(date) as date,
			AVG(frequency) as frequency,
			ai_provider as platform
		FROM ai_appearances
		WHERE 1=1
	`

	args := []interface{}{}
	if filters.StartDate != nil && filters.EndDate != nil {
		query += " AND date BETWEEN ? AND ?"
		args = append(args, *filters.StartDate, *filters.EndDate)
	}
	if filters.Region != "" {
		query += " AND region = ?"
		args = append(args, filters.Region)
	}

	query += " GROUP BY DATE(date), ai_provider ORDER BY date, platform"

	if err := s.db.Raw(query, args...).Scan(&frequencyData).Error; err != nil {
		return nil, err
	}

	return frequencyData, nil
}

// getRecentActivity retrieves recent activity items
func (s *AnalyticsService) getRecentActivity(limit int) ([]types.ActivityItem, error) {
	var activities []types.ActivityItem

	// This would typically aggregate from multiple tables
	// For now, we'll create mock data based on recent changes
	activities = []types.ActivityItem{
		{
			ID:          "1",
			Type:        "brand_update",
			Description: "Intel brand metrics updated",
			Timestamp:   time.Now().Add(-1 * time.Hour),
		},
		{
			ID:          "2",
			Type:        "ai_appearance",
			Description: "New AI appearance detected for AMD",
			Timestamp:   time.Now().Add(-2 * time.Hour),
		},
		{
			ID:          "3",
			Type:        "search_spike",
			Description: "Search volume spike detected for Apple",
			Timestamp:   time.Now().Add(-3 * time.Hour),
		},
	}

	return activities, nil
}
