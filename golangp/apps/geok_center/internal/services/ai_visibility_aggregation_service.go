/*
 * @Description: AI可见性指标聚合服务 - 处理品牌级别的可见性指标聚合和分析
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package services

import (
	"encoding/json"
	"fmt"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/common/logging"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AIVisibilityAggregationService handles aggregation of AI visibility metrics
type AIVisibilityAggregationService struct {
	db *gorm.DB
}

// NewAIVisibilityAggregationService creates a new aggregation service
func NewAIVisibilityAggregationService(db *gorm.DB) *AIVisibilityAggregationService {
	return &AIVisibilityAggregationService{
		db: db,
	}
}

// AggregatedVisibilityData represents aggregated visibility data
type AggregatedVisibilityData struct {
	TotalQuestions        int                    `json:"总问题数量"`
	AverageAIFrequency    float64                `json:"平均AI中出现频率"`
	AverageBrandRecommend float64                `json:"平均品牌推荐率"`
	AverageBrandSearch    float64                `json:"平均品牌搜索率"`
	AverageFirstChoice    float64                `json:"平均首推率"`
	TopKeywords           []string               `json:"热门关键词"`
	KeywordFrequencyTotal map[string]int         `json:"关键词总频率"`
	SentimentDistribution map[string]int         `json:"情感分布"`
	CompetitorAnalysis    map[string]interface{} `json:"竞争对手分析"`
	TrendData             []TrendPoint           `json:"趋势数据"`
}

// AggregatedKeywordData represents aggregated keyword data
type AggregatedKeywordData struct {
	TotalKeywords            int                 `json:"总关键词数量"`
	UniqueKeywords           int                 `json:"唯一关键词数量"`
	AverageKeywordDensity    float64             `json:"平均关键词密度"`
	AverageSemanticRelevance float64             `json:"平均语义相关性"`
	AverageIntentMatch       float64             `json:"平均意图匹配度"`
	KeywordCategories        map[string][]string `json:"关键词分类汇总"`
	LongTailKeywords         []string            `json:"长尾关键词汇总"`
}

// TrendPoint represents a point in trend analysis
type TrendPoint struct {
	Date  time.Time `json:"日期"`
	Score float64   `json:"得分"`
	Count int       `json:"数量"`
}

// CreateBrandAggregation creates aggregated metrics for a brand
func (s *AIVisibilityAggregationService) CreateBrandAggregation(brandID uuid.UUID, startDate, endDate time.Time) (*models.AIVisibilityAggregation, error) {
	// 获取指定时间范围内的所有可见性指标
	var metrics []models.AIVisibilityMetrics
	if err := s.db.Where("brand_id = ? AND calculated_at BETWEEN ? AND ?", brandID, startDate, endDate).
		Find(&metrics).Error; err != nil {
		return nil, fmt.Errorf("failed to get visibility metrics: %w", err)
	}

	if len(metrics) == 0 {
		return nil, fmt.Errorf("no visibility metrics found for brand in specified date range")
	}

	// 计算聚合数据
	aggregatedVisibility, err := s.aggregateVisibilityData(metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate visibility data: %w", err)
	}

	aggregatedKeywords, err := s.aggregateKeywordData(metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to aggregate keyword data: %w", err)
	}

	// 创建聚合记录
	aggregation := &models.AIVisibilityAggregation{
		ID:             uuid.New(),
		BrandID:        brandID,
		StartDate:      startDate,
		EndDate:        endDate,
		TotalQuestions: len(metrics),
	}

	// 计算平均得分
	s.calculateAverageScores(aggregation, metrics)

	// 设置聚合数据
	if err := s.setAggregatedData(aggregation, aggregatedVisibility, aggregatedKeywords); err != nil {
		return nil, fmt.Errorf("failed to set aggregated data: %w", err)
	}

	// 计算趋势方向
	s.calculateTrendDirection(aggregation, metrics)

	// 保存到数据库
	if err := s.db.Create(aggregation).Error; err != nil {
		return nil, fmt.Errorf("failed to create aggregation: %w", err)
	}

	logging.Info("Created brand aggregation for brand %s with %d metrics", brandID, len(metrics))
	return aggregation, nil
}

// aggregateVisibilityData aggregates visibility data from multiple metrics
func (s *AIVisibilityAggregationService) aggregateVisibilityData(metrics []models.AIVisibilityMetrics) (*AggregatedVisibilityData, error) {
	data := &AggregatedVisibilityData{
		TotalQuestions:        len(metrics),
		KeywordFrequencyTotal: make(map[string]int),
		SentimentDistribution: make(map[string]int),
		CompetitorAnalysis:    make(map[string]interface{}),
		TrendData:             make([]TrendPoint, 0),
	}

	var totalAIFreq, totalBrandRec, totalBrandSearch, totalFirstChoice float64
	allKeywords := make(map[string]int)

	for _, metric := range metrics {
		// 累加得分
		totalAIFreq += metric.FrequencyScore
		totalBrandRec += metric.RecommendationScore
		totalBrandSearch += metric.SearchRateScore
		totalFirstChoice += metric.FirstChoiceScore

		// 解析可见性数据
		visibilityData, err := metric.GetVisibilityData()
		if err != nil {
			logging.Warning("Failed to parse visibility data for metric %s: %v", metric.ID, err)
			continue
		}

		// 聚合情感分布
		if visibilityData.BrandSentiment != "" {
			data.SentimentDistribution[visibilityData.BrandSentiment]++
		}

		// 解析关键词数据
		keywordData, err := metric.GetKeywordData()
		if err != nil {
			logging.Warning("Failed to parse keyword data for metric %s: %v", metric.ID, err)
			continue
		}

		// 聚合关键词频率
		for keyword, freq := range keywordData.KeywordFrequency {
			allKeywords[keyword] += freq
			data.KeywordFrequencyTotal[keyword] += freq
		}

		// 添加趋势点
		data.TrendData = append(data.TrendData, TrendPoint{
			Date:  metric.CalculatedAt,
			Score: metric.OverallScore,
			Count: 1,
		})
	}

	// 计算平均值
	count := float64(len(metrics))
	data.AverageAIFrequency = totalAIFreq / count
	data.AverageBrandRecommend = totalBrandRec / count
	data.AverageBrandSearch = totalBrandSearch / count
	data.AverageFirstChoice = totalFirstChoice / count

	// 提取热门关键词
	data.TopKeywords = s.extractTopKeywords(allKeywords, 20)

	// 竞争对手分析
	data.CompetitorAnalysis["average_competitor_count"] = s.calculateAverageCompetitorCount(metrics)

	return data, nil
}

// aggregateKeywordData aggregates keyword data from multiple metrics
func (s *AIVisibilityAggregationService) aggregateKeywordData(metrics []models.AIVisibilityMetrics) (*AggregatedKeywordData, error) {
	data := &AggregatedKeywordData{
		KeywordCategories: make(map[string][]string),
		LongTailKeywords:  make([]string, 0),
	}

	var totalDensity, totalSemanticRelevance, totalIntentMatch float64
	allKeywords := make(map[string]bool)
	allLongTail := make(map[string]bool)
	categoryKeywords := make(map[string]map[string]bool)

	validMetricsCount := 0

	for _, metric := range metrics {
		keywordData, err := metric.GetKeywordData()
		if err != nil {
			logging.Warning("Failed to parse keyword data for metric %s: %v", metric.ID, err)
			continue
		}

		validMetricsCount++

		// 累加数值指标
		totalDensity += keywordData.KeywordDensity
		totalSemanticRelevance += keywordData.SemanticRelevance
		totalIntentMatch += keywordData.IntentMatch

		// 收集所有关键词
		for keyword := range keywordData.KeywordFrequency {
			allKeywords[keyword] = true
		}

		// 收集长尾关键词
		for _, keyword := range keywordData.LongTailKeywords {
			allLongTail[keyword] = true
		}

		// 聚合关键词分类
		for category, keywords := range keywordData.KeywordCategories {
			if categoryKeywords[category] == nil {
				categoryKeywords[category] = make(map[string]bool)
			}
			for _, keyword := range keywords {
				categoryKeywords[category][keyword] = true
			}
		}
	}

	if validMetricsCount > 0 {
		// 计算平均值
		count := float64(validMetricsCount)
		data.AverageKeywordDensity = totalDensity / count
		data.AverageSemanticRelevance = totalSemanticRelevance / count
		data.AverageIntentMatch = totalIntentMatch / count
	}

	// 设置统计数据
	data.TotalKeywords = len(allKeywords)
	data.UniqueKeywords = len(allKeywords)

	// 转换分类数据
	for category, keywordSet := range categoryKeywords {
		keywords := make([]string, 0, len(keywordSet))
		for keyword := range keywordSet {
			keywords = append(keywords, keyword)
		}
		data.KeywordCategories[category] = keywords
	}

	// 转换长尾关键词
	for keyword := range allLongTail {
		data.LongTailKeywords = append(data.LongTailKeywords, keyword)
	}

	return data, nil
}

// calculateAverageScores calculates average scores for the aggregation
func (s *AIVisibilityAggregationService) calculateAverageScores(aggregation *models.AIVisibilityAggregation, metrics []models.AIVisibilityMetrics) {
	var totalOverall, totalFreq, totalRec, totalSearch, totalFirst float64

	for _, metric := range metrics {
		totalOverall += metric.OverallScore
		totalFreq += metric.FrequencyScore
		totalRec += metric.RecommendationScore
		totalSearch += metric.SearchRateScore
		totalFirst += metric.FirstChoiceScore
	}

	count := float64(len(metrics))
	aggregation.AverageOverallScore = totalOverall / count
	aggregation.AverageFrequencyScore = totalFreq / count
	aggregation.AverageRecommendationScore = totalRec / count
	aggregation.AverageSearchRateScore = totalSearch / count
	aggregation.AverageFirstChoiceScore = totalFirst / count
}

// setAggregatedData sets the aggregated data in JSON format
func (s *AIVisibilityAggregationService) setAggregatedData(aggregation *models.AIVisibilityAggregation, visibilityData *AggregatedVisibilityData, keywordData *AggregatedKeywordData) error {
	// 设置可见性聚合数据
	visibilityJSON, err := json.Marshal(visibilityData)
	if err != nil {
		return fmt.Errorf("failed to marshal visibility data: %w", err)
	}
	aggregation.AggregatedVisibilityData = visibilityJSON

	// 设置关键词聚合数据
	keywordJSON, err := json.Marshal(keywordData)
	if err != nil {
		return fmt.Errorf("failed to marshal keyword data: %w", err)
	}
	aggregation.AggregatedKeywordData = keywordJSON

	return nil
}

// calculateTrendDirection calculates the trend direction based on metrics
func (s *AIVisibilityAggregationService) calculateTrendDirection(aggregation *models.AIVisibilityAggregation, metrics []models.AIVisibilityMetrics) {
	if len(metrics) < 2 {
		aggregation.TrendDirection = "稳定"
		aggregation.ChangePercentage = 0
		return
	}

	// 简化的趋势计算：比较前半部分和后半部分的平均得分
	mid := len(metrics) / 2

	var firstHalfSum, secondHalfSum float64
	for i := 0; i < mid; i++ {
		firstHalfSum += metrics[i].OverallScore
	}
	for i := mid; i < len(metrics); i++ {
		secondHalfSum += metrics[i].OverallScore
	}

	firstHalfAvg := firstHalfSum / float64(mid)
	secondHalfAvg := secondHalfSum / float64(len(metrics)-mid)

	changePercentage := ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100
	aggregation.ChangePercentage = changePercentage

	if changePercentage > 5 {
		aggregation.TrendDirection = "上升"
	} else if changePercentage < -5 {
		aggregation.TrendDirection = "下降"
	} else {
		aggregation.TrendDirection = "稳定"
	}
}

// extractTopKeywords extracts top keywords by frequency
func (s *AIVisibilityAggregationService) extractTopKeywords(keywords map[string]int, limit int) []string {
	type keywordFreq struct {
		keyword string
		freq    int
	}

	var keywordList []keywordFreq
	for keyword, freq := range keywords {
		keywordList = append(keywordList, keywordFreq{keyword, freq})
	}

	// 简单排序（冒泡排序）
	for i := 0; i < len(keywordList)-1; i++ {
		for j := 0; j < len(keywordList)-i-1; j++ {
			if keywordList[j].freq < keywordList[j+1].freq {
				keywordList[j], keywordList[j+1] = keywordList[j+1], keywordList[j]
			}
		}
	}

	var result []string
	for i, kw := range keywordList {
		if i >= limit {
			break
		}
		result = append(result, kw.keyword)
	}

	return result
}

// calculateAverageCompetitorCount calculates average competitor count
func (s *AIVisibilityAggregationService) calculateAverageCompetitorCount(metrics []models.AIVisibilityMetrics) float64 {
	var total float64
	validCount := 0

	for _, metric := range metrics {
		visibilityData, err := metric.GetVisibilityData()
		if err != nil {
			continue
		}
		total += float64(visibilityData.CompetitorCount)
		validCount++
	}

	if validCount == 0 {
		return 0
	}

	return total / float64(validCount)
}

// GetBrandAggregationHistory gets historical aggregation data for a brand
func (s *AIVisibilityAggregationService) GetBrandAggregationHistory(brandID uuid.UUID, months int) ([]models.AIVisibilityAggregation, error) {
	startDate := time.Now().AddDate(0, -months, 0)

	var aggregations []models.AIVisibilityAggregation
	if err := s.db.Where("brand_id = ? AND created_at >= ?", brandID, startDate).
		Order("created_at ASC").
		Find(&aggregations).Error; err != nil {
		return nil, fmt.Errorf("failed to get aggregation history: %w", err)
	}

	return aggregations, nil
}

// CompareBrandAggregations compares aggregations between two brands
func (s *AIVisibilityAggregationService) CompareBrandAggregations(brandID1, brandID2 uuid.UUID, startDate, endDate time.Time) (map[string]interface{}, error) {
	// 获取两个品牌的聚合数据
	var agg1, agg2 models.AIVisibilityAggregation

	if err := s.db.Where("brand_id = ? AND start_date >= ? AND end_date <= ?", brandID1, startDate, endDate).
		Order("created_at DESC").First(&agg1).Error; err != nil {
		return nil, fmt.Errorf("failed to get aggregation for brand 1: %w", err)
	}

	if err := s.db.Where("brand_id = ? AND start_date >= ? AND end_date <= ?", brandID2, startDate, endDate).
		Order("created_at DESC").First(&agg2).Error; err != nil {
		return nil, fmt.Errorf("failed to get aggregation for brand 2: %w", err)
	}

	// 计算比较结果
	comparison := map[string]interface{}{
		"brand_1_id":                brandID1,
		"brand_2_id":                brandID2,
		"comparison_date":           time.Now(),
		"overall_score_diff":        agg1.AverageOverallScore - agg2.AverageOverallScore,
		"frequency_score_diff":      agg1.AverageFrequencyScore - agg2.AverageFrequencyScore,
		"recommendation_score_diff": agg1.AverageRecommendationScore - agg2.AverageRecommendationScore,
		"search_rate_score_diff":    agg1.AverageSearchRateScore - agg2.AverageSearchRateScore,
		"first_choice_score_diff":   agg1.AverageFirstChoiceScore - agg2.AverageFirstChoiceScore,
		"questions_count_diff":      agg1.TotalQuestions - agg2.TotalQuestions,
		"brand_1_aggregation":       agg1,
		"brand_2_aggregation":       agg2,
	}

	// 确定优势品牌
	if agg1.AverageOverallScore > agg2.AverageOverallScore {
		comparison["leading_brand"] = brandID1
		comparison["advantage_percentage"] = ((agg1.AverageOverallScore - agg2.AverageOverallScore) / agg2.AverageOverallScore) * 100
	} else {
		comparison["leading_brand"] = brandID2
		comparison["advantage_percentage"] = ((agg2.AverageOverallScore - agg1.AverageOverallScore) / agg1.AverageOverallScore) * 100
	}

	return comparison, nil
}

// GenerateVisibilityReport generates a comprehensive visibility report for a brand
func (s *AIVisibilityAggregationService) GenerateVisibilityReport(brandID uuid.UUID, startDate, endDate time.Time) (map[string]interface{}, error) {
	// 获取聚合数据
	aggregation, err := s.CreateBrandAggregation(brandID, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to create aggregation: %w", err)
	}

	// 获取历史趋势
	history, err := s.GetBrandAggregationHistory(brandID, 6) // 6个月历史
	if err != nil {
		logging.Warning("Failed to get aggregation history: %v", err)
		history = []models.AIVisibilityAggregation{}
	}

	// 解析聚合数据
	var visibilityData AggregatedVisibilityData
	if aggregation.AggregatedVisibilityData != nil {
		if err := json.Unmarshal(aggregation.AggregatedVisibilityData, &visibilityData); err != nil {
			logging.Warning("Failed to unmarshal visibility data: %v", err)
		}
	}

	var keywordData AggregatedKeywordData
	if aggregation.AggregatedKeywordData != nil {
		if err := json.Unmarshal(aggregation.AggregatedKeywordData, &keywordData); err != nil {
			logging.Warning("Failed to unmarshal keyword data: %v", err)
		}
	}

	// 生成报告
	report := map[string]interface{}{
		"brand_id": brandID,
		"report_period": map[string]interface{}{
			"start_date": startDate,
			"end_date":   endDate,
		},
		"generated_at": time.Now(),
		"summary": map[string]interface{}{
			"total_questions":   aggregation.TotalQuestions,
			"overall_score":     aggregation.AverageOverallScore,
			"visibility_level":  s.getVisibilityLevel(aggregation.AverageOverallScore),
			"trend_direction":   aggregation.TrendDirection,
			"change_percentage": aggregation.ChangePercentage,
		},
		"detailed_scores": map[string]interface{}{
			"frequency_score":      aggregation.AverageFrequencyScore,
			"recommendation_score": aggregation.AverageRecommendationScore,
			"search_rate_score":    aggregation.AverageSearchRateScore,
			"first_choice_score":   aggregation.AverageFirstChoiceScore,
		},
		"visibility_data":  visibilityData,
		"keyword_data":     keywordData,
		"historical_trend": history,
		"recommendations":  s.generateRecommendations(aggregation, &visibilityData, &keywordData),
	}

	return report, nil
}

// getVisibilityLevel returns visibility level based on score
func (s *AIVisibilityAggregationService) getVisibilityLevel(score float64) string {
	if score >= 80 {
		return "优秀"
	} else if score >= 60 {
		return "良好"
	} else if score >= 40 {
		return "一般"
	} else {
		return "需要改进"
	}
}

// generateRecommendations generates recommendations based on aggregated data
func (s *AIVisibilityAggregationService) generateRecommendations(aggregation *models.AIVisibilityAggregation, visibilityData *AggregatedVisibilityData, keywordData *AggregatedKeywordData) []string {
	var recommendations []string

	// 基于整体得分的建议
	if aggregation.AverageOverallScore < 40 {
		recommendations = append(recommendations, "整体可见性较低，建议加强品牌推广和内容优化")
	}

	// 基于频率得分的建议
	if aggregation.AverageFrequencyScore < 50 {
		recommendations = append(recommendations, "AI中出现频率较低，建议增加相关内容的发布和优化")
	}

	// 基于推荐率的建议
	if aggregation.AverageRecommendationScore < 50 {
		recommendations = append(recommendations, "品牌推荐率有待提升，建议改善产品质量和用户体验")
	}

	// 基于关键词密度的建议
	if keywordData.AverageKeywordDensity < 2 {
		recommendations = append(recommendations, "关键词密度较低，建议在内容中增加相关关键词")
	}

	// 基于趋势的建议
	if aggregation.TrendDirection == "下降" {
		recommendations = append(recommendations, "可见性呈下降趋势，需要立即采取改进措施")
	}

	// 基于竞争对手分析的建议
	if avgCompetitors, ok := visibilityData.CompetitorAnalysis["average_competitor_count"].(float64); ok && avgCompetitors > 5 {
		recommendations = append(recommendations, "竞争激烈，建议制定差异化策略突出品牌优势")
	}

	if len(recommendations) == 0 {
		recommendations = append(recommendations, "当前表现良好，建议继续保持并寻找进一步优化的机会")
	}

	return recommendations
}
