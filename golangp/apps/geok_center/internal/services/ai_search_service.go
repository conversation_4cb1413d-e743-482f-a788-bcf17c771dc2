/*
 * @Description: AI搜索服务 - 处理AI搜索相关的业务逻辑
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package services

import (
	"fmt"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/common/logging"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AISearchService handles AI search related operations
type AISearchService struct {
	db *gorm.DB
}

// NewAISearchService creates a new AI search service instance
func NewAISearchService(db *gorm.DB) *AISearchService {
	return &AISearchService{
		db: db,
	}
}

// CreateAISearch creates a new AI search record
func (s *AISearchService) CreateAISearch(search *models.AISearch) (*models.AISearch, error) {
	if err := s.db.Create(search).Error; err != nil {
		logging.Error("Failed to create AI search: %v", err)
		return nil, fmt.Errorf("failed to create AI search: %w", err)
	}

	logging.Info("AI search created successfully: %s", search.ID)
	return search, nil
}

// GetAISearchByID retrieves an AI search by ID
func (s *AISearchService) GetAISearchByID(id uuid.UUID) (*models.AISearch, error) {
	var search models.AISearch
	if err := s.db.Preload("Brand").Preload("Responses").First(&search, "id = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("AI search not found")
		}
		logging.Error("Failed to get AI search by ID: %v", err)
		return nil, fmt.Errorf("failed to get AI search: %w", err)
	}

	return &search, nil
}

// GetAISearchesByBrandID retrieves AI searches for a specific brand
func (s *AISearchService) GetAISearchesByBrandID(brandID uuid.UUID, limit, offset int) ([]models.AISearch, int64, error) {
	var searches []models.AISearch
	var total int64

	// Count total records
	if err := s.db.Model(&models.AISearch{}).Where("brand_id = ?", brandID).Count(&total).Error; err != nil {
		logging.Error("Failed to count AI searches: %v", err)
		return nil, 0, fmt.Errorf("failed to count AI searches: %w", err)
	}

	// Get paginated results
	if err := s.db.Where("brand_id = ?", brandID).
		Preload("Brand").
		Preload("Responses").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&searches).Error; err != nil {
		logging.Error("Failed to get AI searches by brand ID: %v", err)
		return nil, 0, fmt.Errorf("failed to get AI searches: %w", err)
	}

	return searches, total, nil
}

// GetAISearchesByStatus retrieves AI searches by status
func (s *AISearchService) GetAISearchesByStatus(status models.AISearchStatus, limit, offset int) ([]models.AISearch, int64, error) {
	var searches []models.AISearch
	var total int64

	// Count total records
	if err := s.db.Model(&models.AISearch{}).Where("status = ?", status).Count(&total).Error; err != nil {
		logging.Error("Failed to count AI searches by status: %v", err)
		return nil, 0, fmt.Errorf("failed to count AI searches: %w", err)
	}

	// Get paginated results
	if err := s.db.Where("status = ?", status).
		Preload("Brand").
		Preload("Responses").
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&searches).Error; err != nil {
		logging.Error("Failed to get AI searches by status: %v", err)
		return nil, 0, fmt.Errorf("failed to get AI searches: %w", err)
	}

	return searches, total, nil
}

// UpdateAISearchStatus updates the status of an AI search
func (s *AISearchService) UpdateAISearchStatus(id uuid.UUID, status models.AISearchStatus) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	// Set timestamps based on status
	switch status {
	case models.AISearchStatusProcessing:
		now := time.Now()
		updates["started_at"] = &now
	case models.AISearchStatusCompleted:
		now := time.Now()
		updates["completed_at"] = &now
	case models.AISearchStatusFailed:
		now := time.Now()
		updates["completed_at"] = &now
	}

	if err := s.db.Model(&models.AISearch{}).Where("id = ?", id).Updates(updates).Error; err != nil {
		logging.Error("Failed to update AI search status: %v", err)
		return fmt.Errorf("failed to update AI search status: %w", err)
	}

	logging.Info("AI search status updated: %s -> %s", id, status)
	return nil
}

// DeleteAISearch soft deletes an AI search
func (s *AISearchService) DeleteAISearch(id uuid.UUID) error {
	if err := s.db.Delete(&models.AISearch{}, "id = ?", id).Error; err != nil {
		logging.Error("Failed to delete AI search: %v", err)
		return fmt.Errorf("failed to delete AI search: %w", err)
	}

	logging.Info("AI search deleted successfully: %s", id)
	return nil
}

// CreateBrandAISearches creates AI searches for a brand based on generated questions
func (s *AISearchService) CreateBrandAISearches(brandID uuid.UUID, batchID *uuid.UUID) ([]models.AISearch, error) {
	// Get brand information
	var brand models.Brand
	if err := s.db.First(&brand, "id = ?", brandID).Error; err != nil {
		return nil, fmt.Errorf("brand not found: %w", err)
	}

	// Generate questions for the brand
	questions := models.GenerateBrandQuestions(brand)

	var searches []models.AISearch
	for _, question := range questions {
		search := models.AISearch{
			ID:           uuid.New(),
			BrandID:      brandID,
			BatchID:      batchID,
			Question:     question,
			QuestionType: "brand_analysis",
			Keywords:     brand.Keywords,
			Status:       models.AISearchStatusPending,
			Priority:     1,
			Region:       "global",
			Language:     "zh",
		}

		if err := s.db.Create(&search).Error; err != nil {
			logging.Error("Failed to create AI search for question: %s, error: %v", question, err)
			continue
		}

		searches = append(searches, search)
	}

	logging.Info("Created %d AI searches for brand %s", len(searches), brand.Name)
	return searches, nil
}

// ProcessPendingSearches processes pending AI searches
func (s *AISearchService) ProcessPendingSearches(limit int) error {
	var searches []models.AISearch
	if err := s.db.Where("status = ?", models.AISearchStatusPending).
		Order("priority DESC, created_at ASC").
		Limit(limit).
		Find(&searches).Error; err != nil {
		return fmt.Errorf("failed to get pending searches: %w", err)
	}

	for _, search := range searches {
		// Update status to processing
		if err := s.UpdateAISearchStatus(search.ID, models.AISearchStatusProcessing); err != nil {
			logging.Error("Failed to update search status to processing: %v", err)
			continue
		}

		// Process the search (placeholder for actual AI interaction)
		if err := models.CreateAISearchFunction(&search); err != nil {
			logging.Error("Failed to process AI search: %v", err)
			s.UpdateAISearchStatus(search.ID, models.AISearchStatusFailed)
			continue
		}

		// Update status to completed
		if err := s.UpdateAISearchStatus(search.ID, models.AISearchStatusCompleted); err != nil {
			logging.Error("Failed to update search status to completed: %v", err)
		}
	}

	logging.Info("Processed %d pending AI searches", len(searches))
	return nil
}

// GetAISearchStats returns statistics about AI searches
func (s *AISearchService) GetAISearchStats(brandID *uuid.UUID) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	query := s.db.Model(&models.AISearch{})
	if brandID != nil {
		query = query.Where("brand_id = ?", *brandID)
	}

	// Count by status
	var statusCounts []struct {
		Status string
		Count  int64
	}
	if err := query.Select("status, COUNT(*) as count").Group("status").Scan(&statusCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to get status counts: %w", err)
	}

	statusMap := make(map[string]int64)
	for _, sc := range statusCounts {
		statusMap[sc.Status] = sc.Count
	}
	stats["status_counts"] = statusMap

	// Total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to get total count: %w", err)
	}
	stats["total"] = total

	// Recent activity (last 24 hours)
	var recentCount int64
	if err := query.Where("created_at >= ?", time.Now().AddDate(0, 0, -1)).Count(&recentCount).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent count: %w", err)
	}
	stats["recent_24h"] = recentCount

	return stats, nil
}
