load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "services",
    srcs = [
        "ai_search_response_service.go",
        "ai_search_service.go",
        "ai_visibility_aggregation_service.go",
        "ai_visibility_service.go",
        "analytics_service.go",
        "blog_service.go",
        "brand_service.go",
        "export_service.go",
        "geo_optimization_service.go",
        "keyword_extraction_service.go",
        "notification_service.go",
        "prompt_service.go",
        "reference_service.go",
        "user_service.go",
    ],
    importpath = "pointer/golangp/apps/geok_center/internal/services",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/geok_center/internal/config",
        "//golangp/apps/geok_center/internal/models",
        "//golangp/apps/geok_center/pkg/services/manager",
        "//golangp/apps/geok_center/pkg/types",
        "//golangp/common/database/postgres",
        "//golangp/common/logging:logger",
        "//golangp/common/utils",
        "@com_github_google_uuid//:uuid",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
    ],
)
