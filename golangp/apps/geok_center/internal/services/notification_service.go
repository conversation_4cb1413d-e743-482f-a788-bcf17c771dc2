/*
 * @Description: 通知服务
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// NotificationService handles notification-related business logic
type NotificationService struct {
	db *gorm.DB
}

// NewNotificationService creates a new notification service
func NewNotificationService(db *gorm.DB) *NotificationService {
	return &NotificationService{
		db: db,
	}
}

// CreateNotification creates a new notification
func (s *NotificationService) CreateNotification(notification *models.Notification) (*models.Notification, error) {
	result := s.db.Create(notification)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to create notification: %w", result.Error)
	}
	return notification, nil
}

// GetNotificationByID retrieves a notification by ID
func (s *NotificationService) GetNotificationByID(id uuid.UUID) (*models.Notification, error) {
	var notification models.Notification
	result := s.db.First(&notification, "id = ?", id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("notification not found")
		}
		return nil, fmt.Errorf("failed to get notification: %w", result.Error)
	}
	return &notification, nil
}

// GetNotificationsByUserID retrieves notifications by user ID
func (s *NotificationService) GetNotificationsByUserID(userID uuid.UUID, limit, offset int) ([]models.Notification, int64, error) {
	var notifications []models.Notification
	var total int64

	query := s.db.Model(&models.Notification{}).Where("user_id = ?", userID)

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count notifications: %w", err)
	}

	// Get notifications with pagination
	result := query.Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&notifications)

	if result.Error != nil {
		return nil, 0, fmt.Errorf("failed to get notifications: %w", result.Error)
	}

	return notifications, total, nil
}

// GetUnreadNotificationCount retrieves the count of unread notifications for a user
func (s *NotificationService) GetUnreadNotificationCount(userID uuid.UUID) (int64, error) {
	var count int64
	result := s.db.Model(&models.Notification{}).
		Where("user_id = ? AND is_read = ?", userID, false).
		Count(&count)

	if result.Error != nil {
		return 0, fmt.Errorf("failed to count unread notifications: %w", result.Error)
	}

	return count, nil
}

// MarkNotificationAsRead marks a notification as read
func (s *NotificationService) MarkNotificationAsRead(id uuid.UUID) error {
	now := time.Now()
	result := s.db.Model(&models.Notification{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_read": true,
			"read_at": &now,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to mark notification as read: %w", result.Error)
	}

	return nil
}

// MarkAllNotificationsAsRead marks all notifications for a user as read
func (s *NotificationService) MarkAllNotificationsAsRead(userID uuid.UUID) error {
	now := time.Now()
	result := s.db.Model(&models.Notification{}).
		Where("user_id = ? AND is_read = ?", userID, false).
		Updates(map[string]interface{}{
			"is_read": true,
			"read_at": &now,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to mark all notifications as read: %w", result.Error)
	}

	return nil
}

// DeleteNotification deletes a notification (soft delete)
func (s *NotificationService) DeleteNotification(id uuid.UUID) error {
	result := s.db.Delete(&models.Notification{}, "id = ?", id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete notification: %w", result.Error)
	}
	return nil
}

// DeleteAllNotifications deletes all notifications for a user (soft delete)
func (s *NotificationService) DeleteAllNotifications(userID uuid.UUID) error {
	result := s.db.Where("user_id = ?", userID).Delete(&models.Notification{})
	if result.Error != nil {
		return fmt.Errorf("failed to delete all notifications: %w", result.Error)
	}
	return nil
}

// GetNotificationSettings retrieves notification settings for a user
func (s *NotificationService) GetNotificationSettings(userID uuid.UUID) (*models.NotificationSetting, error) {
	var settings models.NotificationSetting
	result := s.db.Where("user_id = ?", userID).First(&settings)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// Create default settings if not found
			settings = models.NotificationSetting{
				ID:                     uuid.New(),
				UserID:                 userID,
				EmailNotifications:     true,
				PushNotifications:      true,
				SystemNotifications:    true,
				PromptNotifications:    true,
				BrandNotifications:     true,
				AnalyticsNotifications: true,
				AlertNotifications:     true,
				UpdateNotifications:    true,
			}
			if err := s.db.Create(&settings).Error; err != nil {
				return nil, fmt.Errorf("failed to create default notification settings: %w", err)
			}
			return &settings, nil
		}
		return nil, fmt.Errorf("failed to get notification settings: %w", result.Error)
	}
	return &settings, nil
}

// UpdateNotificationSettings updates notification settings for a user
func (s *NotificationService) UpdateNotificationSettings(settings *models.NotificationSetting) (*models.NotificationSetting, error) {
	result := s.db.Save(settings)
	if result.Error != nil {
		return nil, fmt.Errorf("failed to update notification settings: %w", result.Error)
	}
	return settings, nil
}

// CreateSystemNotification creates a system notification for a user
func (s *NotificationService) CreateSystemNotification(userID uuid.UUID, title, content string, priority models.NotificationPriority, data map[string]interface{}, actionURL string) (*models.Notification, error) {
	// Check if user has enabled system notifications
	settings, err := s.GetNotificationSettings(userID)
	if err != nil {
		return nil, err
	}

	if !settings.SystemNotifications {
		return nil, nil // User has disabled system notifications
	}

	// Convert data to JSON
	var dataJSON string
	if data != nil {
		dataBytes, err := json.Marshal(data)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal notification data: %w", err)
		}
		dataJSON = string(dataBytes)
	}

	notification := &models.Notification{
		ID:        uuid.New(),
		UserID:    userID,
		Type:      models.NotificationTypeSystem,
		Priority:  priority,
		Title:     title,
		Content:   content,
		Data:      dataJSON,
		IsRead:    false,
		ActionURL: actionURL,
	}

	return s.CreateNotification(notification)
}
