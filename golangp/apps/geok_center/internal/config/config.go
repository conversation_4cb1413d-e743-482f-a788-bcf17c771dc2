/*
 * @Description: Configuration management for GEOK Center using Viper
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package config

import (
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/viper"
)

// Config holds all configuration for the application
type Config struct {
	Environment string `mapstructure:"ENVIRONMENT"`
	LogPath     string `mapstructure:"LOG_PATH"`

	// Server Configuration
	ServerPort         string `mapstructure:"SERVER_PORT"`
	ServerReadTimeout  int    `mapstructure:"SERVER_READ_TIMEOUT"`
	ServerWriteTimeout int    `mapstructure:"SERVER_WRITE_TIMEOUT"`

	// Database Configuration
	DBHost     string `mapstructure:"DB_HOST"`
	DBPort     string `mapstructure:"DB_PORT"`
	DBUsername string `mapstructure:"DB_USERNAME"`
	DBPassword string `mapstructure:"DB_PASSWORD"`
	DBName     string `mapstructure:"DB_NAME"`
	DBSSLMode  string `mapstructure:"DB_SSL_MODE"`

	// JWT Configuration
	JWTSecret      string `mapstructure:"JWT_SECRET"`
	JWTExpiryHours int    `mapstructure:"JWT_EXPIRY_HOURS"`

	// Redis Configuration
	RedisHost     string `mapstructure:"REDIS_HOST"`
	RedisPort     string `mapstructure:"REDIS_PORT"`
	RedisPassword string `mapstructure:"REDIS_PASSWORD"`
	RedisDB       int    `mapstructure:"REDIS_DB"`

	// Export Configuration
	ExportMaxRecords int    `mapstructure:"EXPORT_MAX_RECORDS"`
	ExportTempDir    string `mapstructure:"EXPORT_TEMP_DIR"`

	// External API Configuration
	AIServiceURL string `mapstructure:"AI_SERVICE_URL"`
	APITimeout   int    `mapstructure:"API_TIMEOUT"`

	// Security Configuration
	EnableHTTPS             bool   `mapstructure:"ENABLE_HTTPS"`
	SSLCertPath             string `mapstructure:"SSL_CERT_PATH"`
	SSLKeyPath              string `mapstructure:"SSL_KEY_PATH"`
	TrustedProxies          string `mapstructure:"TRUSTED_PROXIES"`
	CORSAllowedOrigins      string `mapstructure:"CORS_ALLOWED_ORIGINS"`
	RateLimitRequestsPerMin int    `mapstructure:"RATE_LIMIT_REQUESTS_PER_MINUTE"`
	MaxUploadSize           int64  `mapstructure:"MAX_UPLOAD_SIZE"`

	// Google OAuth Configuration
	GoogleClientID     string `mapstructure:"GOOGLE_CLIENT_ID"`
	GoogleClientSecret string `mapstructure:"GOOGLE_CLIENT_SECRET"`
	GoogleRedirectURI  string `mapstructure:"GOOGLE_REDIRECT_URI"`

	// Cookie Configuration
	Domain             string `mapstructure:"DOMAIN"`
	AccessTokenMaxAge  int    `mapstructure:"ACCESS_TOKEN_MAX_AGE"`
	RefreshTokenMaxAge int    `mapstructure:"REFRESH_TOKEN_MAX_AGE"`

	// JWT Token Configuration
	AccessTokenPrivateKey  string `mapstructure:"ACCESS_TOKEN_PRIVATE_KEY"`
	AccessTokenPublicKey   string `mapstructure:"ACCESS_TOKEN_PUBLIC_KEY"`
	RefreshTokenPrivateKey string `mapstructure:"REFRESH_TOKEN_PRIVATE_KEY"`
	RefreshTokenPublicKey  string `mapstructure:"REFRESH_TOKEN_PUBLIC_KEY"`

	// Storage Configuration (S3-compatible)
	StorageProvider    string `mapstructure:"STORAGE_PROVIDER"`      // Storage provider name
	StorageEndpoint    string `mapstructure:"STORAGE_ENDPOINT"`      // S3-compatible endpoint
	StorageAccessKeyID string `mapstructure:"STORAGE_ACCESS_KEY_ID"` // Access Key ID
	StorageSecretKey   string `mapstructure:"STORAGE_SECRET_KEY"`    // Secret Access Key
	StorageUseSSL      bool   `mapstructure:"STORAGE_USE_SSL"`       // Use HTTPS
	StorageRegion      string `mapstructure:"STORAGE_REGION"`        // Storage region
	StorageBucket      string `mapstructure:"STORAGE_BUCKET"`        // Storage bucket name
	StorageBaseURL     string `mapstructure:"STORAGE_BASE_URL"`      // Base URL for accessing files
	AllowedImageExts   string `mapstructure:"ALLOWED_IMAGE_EXTS"`    // Allowed image extensions

	// Email Configuration
	SMTPHost     string `mapstructure:"SMTP_HOST"`
	SMTPPort     int    `mapstructure:"SMTP_PORT"`
	SMTPUsername string `mapstructure:"SMTP_USERNAME"`
	SMTPPassword string `mapstructure:"SMTP_PASSWORD"`
	SMTPFrom     string `mapstructure:"SMTP_FROM"`

	// Feature Flags
	EnableExport         bool `mapstructure:"ENABLE_EXPORT"`
	EnableAIAnalysis     bool `mapstructure:"ENABLE_AI_ANALYSIS"`
	EnableRealTimeUpdate bool `mapstructure:"ENABLE_REAL_TIME_UPDATES"`
	EnableMetrics        bool `mapstructure:"ENABLE_METRICS"`
	EnableSwaggerUI      bool `mapstructure:"ENABLE_SWAGGER_UI"`
	EnableSMS            bool `mapstructure:"ENABLE_SMS"`
}

func init() {
	var envPath string
	if sr := os.Getenv("TEST_SRCDIR"); sr != "" {
		ws := os.Getenv("TEST_WORKSPACE")
		if ws == "" {
			ws = filepath.Base(sr)
		}
		envPath = filepath.Join(sr, ws, "golangp", "apps", "geok_center", "app.env")
	} else {
		envPath = filepath.Join("golangp", "apps", "geok_center", "app.env")
	}

	// 如果指定路径不存在，尝试使用同级目录下的 app.env
	if _, err := os.Stat(envPath); os.IsNotExist(err) {
		envPath = "app.env"
	}

	viper.SetConfigFile(envPath)
	viper.SetConfigType("env")
	viper.AutomaticEnv()

	// 设置环境变量前缀和分隔符
	viper.SetEnvPrefix("")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	if err := viper.ReadInConfig(); err != nil {
		log.Printf("Warning: Error reading config file (%s): %v", envPath, err)
		log.Println("Using environment variables and defaults...")
	}
}

func Load() *Config {
	var cfg Config
	if err := viper.Unmarshal(&cfg); err != nil {
		log.Printf("Error unmarshaling config: %v", err)
	}

	// Set default values for optional fields
	if cfg.Environment == "" {
		cfg.Environment = "development"
	}
	if cfg.LogPath == "" {
		cfg.LogPath = "/tmp/geok_center"
	}
	if cfg.ServerPort == "" {
		cfg.ServerPort = "8080"
	}
	if cfg.ServerReadTimeout == 0 {
		cfg.ServerReadTimeout = 30
	}
	if cfg.ServerWriteTimeout == 0 {
		cfg.ServerWriteTimeout = 30
	}
	if cfg.DBHost == "" {
		cfg.DBHost = "localhost"
	}
	if cfg.DBPort == "" {
		cfg.DBPort = "5432"
	}
	if cfg.DBUsername == "" {
		cfg.DBUsername = "postgres"
	}
	if cfg.DBPassword == "" {
		cfg.DBPassword = "password"
	}
	if cfg.DBName == "" {
		cfg.DBName = "geok_center"
	}
	if cfg.DBSSLMode == "" {
		cfg.DBSSLMode = "disable"
	}
	if cfg.JWTSecret == "" {
		cfg.JWTSecret = "your-secret-key"
	}
	if cfg.JWTExpiryHours == 0 {
		cfg.JWTExpiryHours = 24
	}
	if cfg.RedisHost == "" {
		cfg.RedisHost = "localhost"
	}
	if cfg.RedisPort == "" {
		cfg.RedisPort = "6379"
	}
	if cfg.ExportMaxRecords == 0 {
		cfg.ExportMaxRecords = 10000
	}
	if cfg.ExportTempDir == "" {
		cfg.ExportTempDir = "/tmp"
	}
	if cfg.APITimeout == 0 {
		cfg.APITimeout = 30
	}
	if cfg.CORSAllowedOrigins == "" {
		cfg.CORSAllowedOrigins = "*"
	}
	if cfg.RateLimitRequestsPerMin == 0 {
		cfg.RateLimitRequestsPerMin = 100
	}
	if cfg.MaxUploadSize == 0 {
		cfg.MaxUploadSize = 10485760 // 10MB
	}

	// Set default storage configuration
	if cfg.StorageProvider == "" {
		cfg.StorageProvider = "minio"
	}
	if cfg.StorageEndpoint == "" {
		cfg.StorageEndpoint = "localhost:9000"
	}
	if cfg.StorageRegion == "" {
		cfg.StorageRegion = "us-east-1"
	}
	if cfg.StorageBucket == "" {
		cfg.StorageBucket = "geok-center"
	}
	if cfg.StorageBaseURL == "" {
		cfg.StorageBaseURL = "http://localhost:9000"
	}
	if cfg.AllowedImageExts == "" {
		cfg.AllowedImageExts = "jpg,jpeg,png,webp"
	}

	// Set default email configuration
	if cfg.SMTPPort == 0 {
		cfg.SMTPPort = 587
	}
	if cfg.SMTPFrom == "" {
		cfg.SMTPFrom = "<EMAIL>"
	}

	return &cfg
}
