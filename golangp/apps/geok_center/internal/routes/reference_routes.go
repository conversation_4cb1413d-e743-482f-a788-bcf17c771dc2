/*
 * @Description: 引用相关路由配置
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/handlers"
	"pointer/golangp/apps/geok_center/internal/middleware"
	"pointer/golangp/apps/geok_center/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupReferenceRoutes configures reference related routes
func SetupReferenceRoutes(router *gin.RouterGroup, db *gorm.DB) {
	// Initialize services
	referenceService := services.NewReferenceService(db)

	// Initialize handlers
	referenceHandler := handlers.NewReferenceHandler(referenceService)

	// Reference routes
	referenceGroup := router.Group("/references")
	referenceGroup.Use(middleware.DeserializeUser()) // Require authentication
	{
		// Basic operations
		referenceGroup.GET("/:id", referenceHandler.GetReference)       // Get reference by ID
		referenceGroup.DELETE("/:id", referenceHandler.DeleteReference) // Delete reference

		// Query operations
		referenceGroup.GET("/top", referenceHandler.GetTopReferences)                 // Get top references
		referenceGroup.GET("/domain/:domain", referenceHandler.GetReferencesByDomain) // Get references by domain

		// Extraction operations
		referenceGroup.POST("/extract/:responseId", referenceHandler.ExtractReferencesFromResponse) // Extract references from AI response
	}

	// Brand-specific reference routes
	brandReferenceGroup := router.Group("/brands/:id/references")
	brandReferenceGroup.Use(middleware.DeserializeUser())
	{
		// Query operations
		brandReferenceGroup.GET("", referenceHandler.GetReferencesByBrand)            // Get references for brand
		brandReferenceGroup.GET("/dashboard", referenceHandler.GetReferenceDashboard) // Get reference dashboard
		brandReferenceGroup.GET("/analytics", referenceHandler.GetReferenceAnalytics) // Get reference analytics
	}

	// Reference analytics routes
	analyticsGroup := router.Group("/reference-analytics")
	analyticsGroup.Use(middleware.DeserializeUser())
	{
		// Domain analysis
		analyticsGroup.GET("/domain-analysis", func(c *gin.Context) {
			// Analyze domain distribution and authority
			c.JSON(200, gin.H{"message": "Domain analysis retrieved"})
		})

		// Category analysis
		analyticsGroup.GET("/category-analysis", func(c *gin.Context) {
			// Analyze reference categories
			c.JSON(200, gin.H{"message": "Category analysis retrieved"})
		})

		// Authority analysis
		analyticsGroup.GET("/authority-analysis", func(c *gin.Context) {
			// Analyze reference authority scores
			c.JSON(200, gin.H{"message": "Authority analysis retrieved"})
		})

		// Trend analysis
		analyticsGroup.GET("/brands/:id/trends", func(c *gin.Context) {
			// Analyze reference trends over time
			c.JSON(200, gin.H{"message": "Reference trends retrieved"})
		})

		// Competitive analysis
		analyticsGroup.GET("/brands/:id/competitive", func(c *gin.Context) {
			// Compare references with competitors
			c.JSON(200, gin.H{"message": "Competitive reference analysis retrieved"})
		})
	}

	// Reference info routes
	infoGroup := router.Group("/reference-info")
	{
		infoGroup.GET("/types", func(c *gin.Context) {
			// Return available reference types
			c.JSON(200, gin.H{
				"types": []string{
					"website",
					"article",
					"document",
					"video",
					"image",
					"database",
					"api",
					"other",
				},
			})
		})

		infoGroup.GET("/categories", func(c *gin.Context) {
			// Return available reference categories
			c.JSON(200, gin.H{
				"categories": []string{
					"news",
					"education",
					"technology",
					"business",
					"health",
					"science",
					"entertainment",
					"sports",
					"government",
					"other",
				},
			})
		})

		infoGroup.GET("/quality-metrics", func(c *gin.Context) {
			// Return quality metric definitions
			c.JSON(200, gin.H{
				"metrics": map[string]string{
					"authority_score": "Domain authority and credibility (0-100)",
					"trust_score":     "Trustworthiness of the source (0-100)",
					"quality_score":   "Overall content quality (0-100)",
					"freshness_score": "Content recency and relevance (0-100)",
					"relevance_score": "Relevance to the query (0-100)",
				},
			})
		})
	}
}
