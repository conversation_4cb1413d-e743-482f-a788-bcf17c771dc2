load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "routes",
    srcs = glob(["**/*.go"]),
    importpath = "pointer/golangp/apps/geok_center/internal/routes",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/geok_center/internal/handlers",
        "//golangp/apps/geok_center/internal/middleware",
        "//golangp/apps/geok_center/internal/services",
        "//golangp/apps/geok_center/pkg/response",
        "//golangp/common/database/postgres",
        "@com_github_gin_gonic_gin//:gin",
        "@io_gorm_gorm//:gorm",
    ],
)
