/*
 * @Description: AI搜索相关路由配置
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/handlers"
	"pointer/golangp/apps/geok_center/internal/middleware"
	"pointer/golangp/apps/geok_center/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupAISearchRoutes configures AI search related routes
func SetupAISearchRoutes(router *gin.RouterGroup, db *gorm.DB) {
	// Initialize services
	aiSearchService := services.NewAISearchService(db)
	aiSearchResponseService := services.NewAISearchResponseService(db)
	aiVisibilityService := services.NewAIVisibilityService(db)
	aiAggregationService := services.NewAIVisibilityAggregationService(db)

	// Initialize handlers
	aiSearchHandler := handlers.NewAISearchHandler(aiSearchService, aiSearchResponseService)
	aiVisibilityHandler := handlers.NewAIVisibilityHandler(aiVisibilityService, aiAggregationService)

	// AI Search routes
	aiSearchGroup := router.Group("/ai-searches")
	aiSearchGroup.Use(middleware.DeserializeUser()) // Require authentication
	{
		// Basic CRUD operations
		aiSearchGroup.POST("", aiSearchHandler.CreateAISearch)                 // Create AI search
		aiSearchGroup.GET("/:id", aiSearchHandler.GetAISearch)                 // Get AI search by ID
		aiSearchGroup.PUT("/:id/status", aiSearchHandler.UpdateAISearchStatus) // Update AI search status
		aiSearchGroup.DELETE("/:id", aiSearchHandler.DeleteAISearch)           // Delete AI search

		// Query operations
		aiSearchGroup.GET("/status/:status", aiSearchHandler.GetAISearchesByStatus) // Get AI searches by status
		aiSearchGroup.GET("/stats", aiSearchHandler.GetAISearchStats)               // Get AI search statistics

		// Processing operations
		aiSearchGroup.POST("/process-pending", aiSearchHandler.ProcessPendingSearches) // Process pending searches
	}

	// Brand-specific AI search routes
	brandAISearchGroup := router.Group("/brands/:id/ai-searches")
	brandAISearchGroup.Use(middleware.DeserializeUser())
	{
		brandAISearchGroup.GET("", aiSearchHandler.GetAISearchesByBrand)   // Get AI searches for brand
		brandAISearchGroup.POST("", aiSearchHandler.CreateBrandAISearches) // Create AI searches for brand
	}

	// AI Search Response routes
	aiResponseGroup := router.Group("/ai-responses")
	aiResponseGroup.Use(middleware.DeserializeUser())
	{
		// Response operations
		aiResponseGroup.GET("/:id", func(c *gin.Context) {
			// Get response by ID - implementation needed
			c.JSON(200, gin.H{"message": "Get AI response by ID"})
		})
		aiResponseGroup.GET("/search/:searchId", func(c *gin.Context) {
			// Get responses by search ID - implementation needed
			c.JSON(200, gin.H{"message": "Get responses by search ID"})
		})
		aiResponseGroup.GET("/model/:modelType", func(c *gin.Context) {
			// Get responses by model type - implementation needed
			c.JSON(200, gin.H{"message": "Get responses by model type"})
		})
		aiResponseGroup.DELETE("/:id", func(c *gin.Context) {
			// Delete response - implementation needed
			c.JSON(200, gin.H{"message": "Delete AI response"})
		})

		// Analysis operations
		aiResponseGroup.POST("/:responseId/analyze-brand", func(c *gin.Context) {
			// This would be implemented to analyze brand mentions in response
			// For now, return success
			c.JSON(200, gin.H{"message": "Brand analysis completed"})
		})
		aiResponseGroup.POST("/:responseId/extract-citations", func(c *gin.Context) {
			// This would be implemented to extract citations from response
			// For now, return success
			c.JSON(200, gin.H{"message": "Citations extracted"})
		})

		// Statistics
		aiResponseGroup.GET("/stats", func(c *gin.Context) {
			// Get response statistics
			// Implementation would call aiSearchResponseService.GetResponseStats
			c.JSON(200, gin.H{"message": "Response stats retrieved"})
		})
	}

	// Brand-specific AI response routes
	brandResponseGroup := router.Group("/brands/:id/ai-responses")
	brandResponseGroup.Use(middleware.DeserializeUser())
	{
		brandResponseGroup.GET("/mentions", func(c *gin.Context) {
			// Get responses with brand mentions
			// Implementation would call aiSearchResponseService.GetResponsesWithBrandMention
			c.JSON(200, gin.H{"message": "Brand mentions retrieved"})
		})
	}

	// AI Visibility Metrics routes
	visibilityGroup := router.Group("/ai-visibility")
	visibilityGroup.Use(middleware.DeserializeUser())
	{
		// Response-specific visibility metrics
		visibilityGroup.POST("/responses/:responseId/calculate", aiVisibilityHandler.CalculateVisibilityMetrics)
		visibilityGroup.GET("/responses/:responseId/metrics", aiVisibilityHandler.GetVisibilityMetrics)

		// Batch operations
		visibilityGroup.POST("/batch-calculate", aiVisibilityHandler.BatchCalculateVisibilityMetrics)

		// Statistics
		visibilityGroup.GET("/stats", aiVisibilityHandler.GetVisibilityMetricsStats)
	}

	// Brand-specific AI visibility routes
	brandVisibilityGroup := router.Group("/brands/:id/ai-visibility")
	brandVisibilityGroup.Use(middleware.DeserializeUser())
	{
		// Trend analysis
		brandVisibilityGroup.GET("/trend", aiVisibilityHandler.GetBrandVisibilityTrend)

		// Aggregation
		brandVisibilityGroup.POST("/aggregation", aiVisibilityHandler.CalculateBrandAggregation)
		brandVisibilityGroup.GET("/aggregation/history", aiVisibilityHandler.GetBrandAggregationHistory)

		// Reports
		brandVisibilityGroup.GET("/report", aiVisibilityHandler.GenerateVisibilityReport)
	}

	// Brand comparison routes
	comparisonGroup := router.Group("/ai-visibility/compare")
	comparisonGroup.Use(middleware.DeserializeUser())
	{
		comparisonGroup.GET("/brands/:id1/:id2", aiVisibilityHandler.CompareBrandVisibility)
	}
}
