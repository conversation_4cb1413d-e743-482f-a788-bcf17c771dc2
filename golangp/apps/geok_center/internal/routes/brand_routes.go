/*
 * @Description: Brand routes for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/handlers"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/common/database/postgres"

	"github.com/gin-gonic/gin"
)

// SetupBrandRoutes configures brand-related routes
func SetupBrandRoutes(api *gin.RouterGroup) {
	// Initialize brand service and handler
	brandService := services.NewBrandService(postgres.DB)
	brandHandler := handlers.NewBrandHandler(brandService)

	brands := api.Group("/brands")
	{
		brands.GET("", brandHandler.GetBrands)
		brands.GET("/search", brandHandler.SearchBrands)
		brands.GET("/:id", brandHandler.GetBrand)
		brands.POST("", brandHandler.CreateBrand)
		brands.PUT("/:id", brandHandler.UpdateBrand)
		brands.DELETE("/:id", brandHandler.DeleteBrand)

		// New brand statistics and operations
		brands.GET("/:id/stats", func(c *gin.Context) {
			// Get brand statistics (AI searches, visibility metrics, prompts, references)
			c.JSON(200, gin.H{"message": "Brand stats retrieved"})
		})

		brands.POST("/:id/create-with-ai", func(c *gin.Context) {
			// Create brand with automatic AI search generation
			c.JSON(200, gin.H{"message": "Brand created with AI searches"})
		})
	}
}
