/*
 * @Description: Analytics routes for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/handlers"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/common/database/postgres"

	"github.com/gin-gonic/gin"
)

// SetupAnalyticsRoutes configures analytics-related routes
func SetupAnalyticsRoutes(api *gin.RouterGroup) {
	// Initialize analytics service and handler
	analyticsService := services.NewAnalyticsService(postgres.DB)
	analyticsHandler := handlers.NewAnalyticsHandler(analyticsService)

	analytics := api.Group("/analytics")
	{
		analytics.GET("/dashboard", analyticsHandler.GetDashboardData)
		analytics.GET("/summary", analyticsHandler.GetMetricsSummary)
		analytics.GET("/brand-distribution", analyticsHandler.GetBrandDistribution)
		analytics.GET("/search-trends", analyticsHandler.GetSearchTrends)
		analytics.GET("/ai-appearance", analyticsHandler.GetAIAppearanceMetrics)
		analytics.GET("/competitor-analysis", analyticsHandler.GetCompetitorAnalysis)
		analytics.GET("/activity", analyticsHandler.GetRecentActivity)
	}
}
