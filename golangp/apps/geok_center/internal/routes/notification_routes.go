/*
 * @Description: 通知系统路由
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/handlers"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/common/database/postgres"

	"github.com/gin-gonic/gin"
)

// SetupNotificationRoutes configures notification-related routes
func SetupNotificationRoutes(api *gin.RouterGroup) {
	// Initialize services and handlers
	notificationService := services.NewNotificationService(postgres.DB)
	notificationHandler := handlers.NewNotificationHandler(notificationService)

	// Notification routes
	notifications := api.Group("/notifications")
	{
		// CRUD operations
		notifications.GET("", notificationHandler.GetNotifications)
		notifications.GET("/:id", notificationHandler.GetNotification)
		notifications.DELETE("/:id", notificationHandler.DeleteNotification)
		notifications.DELETE("", notificationHandler.DeleteAllNotifications)

		// Read status operations
		notifications.PUT("/:id/read", notificationHandler.MarkNotificationAsRead)
		notifications.PUT("/read-all", notificationHandler.MarkAllNotificationsAsRead)

		// Settings
		notifications.GET("/settings", notificationHandler.GetNotificationSettings)
		notifications.PUT("/settings", notificationHandler.UpdateNotificationSettings)
	}

}
