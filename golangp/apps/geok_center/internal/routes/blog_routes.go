/*
 * @Description: Blog routes for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package routes

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"pointer/golangp/apps/geok_center/internal/handlers"
	"pointer/golangp/apps/geok_center/internal/middleware"
	"pointer/golangp/apps/geok_center/internal/services"
)

// SetupBlogRoutes sets up blog-related routes
func SetupBlogRoutes(router *gin.RouterGroup, db *gorm.DB) {
	// Initialize services
	blogService := services.NewBlogService(db)

	// Initialize handlers
	blogHandler := handlers.NewBlogHandler(blogService)

	// Blog routes
	blogRoutes := router.Group("/blogs")
	{
		// Public routes (no authentication required)
		blogRoutes.GET("", blogHandler.ListBlogs)   // GET /api/v1/blogs
		blogRoutes.GET("/:id", blogHandler.GetBlog) // GET /api/v1/blogs/:id

		// Protected routes (authentication required)
		protected := blogRoutes.Group("")
		protected.Use(middleware.DeserializeUser())
		{
			protected.POST("", blogHandler.CreateBlog)       // POST /api/v1/blogs
			protected.PUT("/:id", blogHandler.UpdateBlog)    // PUT /api/v1/blogs/:id
			protected.DELETE("/:id", blogHandler.DeleteBlog) // DELETE /api/v1/blogs/:id
		}
	}
}
