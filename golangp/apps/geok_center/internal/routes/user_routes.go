/*
 * @Description: User management routes for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/handlers"
	"pointer/golangp/apps/geok_center/internal/middleware"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/common/database/postgres"

	"github.com/gin-gonic/gin"
)

// SetupUserRoutes configures user management routes
func SetupUserRoutes(api *gin.RouterGroup) {
	// Initialize user service and handler
	userService := services.NewUserService(postgres.DB)
	userHandler := handlers.NewUserHandler(userService)

	// User management routes (admin only) - 使用统一权限控制
	users := api.Group("/users")
	users.Use(middleware.AdminMiddleware()) // All user management routes require admin privileges
	{
		users.POST("", userHandler.CreateUser)       // Create new user (including enterprise users)
		users.GET("", userHandler.ListUsers)         // List all users with pagination
		users.GET("/:id", userHandler.GetUser)       // Get user by ID
		users.PUT("/:id", userHandler.UpdateUser)    // Update user information
		users.DELETE("/:id", userHandler.DeleteUser) // Delete user
	}

}
