/*
 * @Description: Export routes for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/handlers"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/common/database/postgres"

	"github.com/gin-gonic/gin"
)

// SetupExportRoutes configures export-related routes
func SetupExportRoutes(api *gin.RouterGroup) {
	// Initialize export service and handler
	exportService := services.NewExportService(postgres.DB)
	exportHandler := handlers.NewExportHandler(exportService)

	exports := api.Group("/exports")
	{
		exports.POST("/dashboard", exportHandler.ExportDashboard)
		exports.POST("/analytics", exportHandler.ExportAnalytics)
		exports.GET("/download/:filename", exportHandler.DownloadFile)
		exports.GET("/history", exportHandler.GetExportHistory)
		exports.GET("/status/:id", exportHandler.GetExportStatus)
	}
}
