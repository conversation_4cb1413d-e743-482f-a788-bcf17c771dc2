/*
 * @Description: Authentication routes for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/handlers"
	"pointer/golangp/common/database/postgres"

	"github.com/gin-gonic/gin"
)

// SetupAuthRoutes configures authentication-related routes
func SetupAuthRoutes(api *gin.RouterGroup, googleClientID string, googleClientSecret string) {
	// Initialize user service and auth handler
	authHandler := handlers.NewAuthHandler(postgres.DB, googleClientID, googleClientSecret)

	auth := api.Group("/auth")
	{
		// 统一登录接口 - 支持普通登录和Google登录
		auth.POST("/login", authHandler.HandleLogin)

		// 注册接口
		auth.POST("/register", authHandler.HandleRegister)
	}
}

// SetupProtectedAuthRoutes configures protected authentication routes that require authorization
func SetupProtectedAuthRoutes(api *gin.RouterGroup, googleClient<PERSON> string, googleClientSecret string) {
	// Initialize user service and auth handler
	authHandler := handlers.NewAuthHandler(postgres.DB, googleClientID, googleClientSecret)

	auth := api.Group("/auth")
	{
		// 登出 (需要认证)
		auth.POST("/logout", authHandler.HandleLogout)

		// 获取当前用户信息 (需要认证)
		auth.GET("/me", authHandler.GetCurrentUser)

		// 刷新token (需要认证)
		auth.POST("/refresh", authHandler.RefreshToken)

		auth.PUT("/profile", authHandler.UpdateProfile) // PUT /api/v1/profile

		// Avatar management routes
		auth.PUT("/avatar", authHandler.UploadAvatar) // PUT /api/v1/profile/avatar

		// Password management routes
		auth.PUT("/password", authHandler.ChangePassword) // PUT /api/v1/profile/password

		// Email management routes
		auth.PUT("/email", authHandler.RequestEmailChange) // PUT /api/v1/profile/email - Direct email change

		// User profile routes (authenticated users only) - 使用统一权限控制

		auth.PUT("/current-brand", authHandler.UpdateCurrentBrand) // Update current brand
		auth.GET("/current-brand", authHandler.GetCurrentBrand)    // Get current brand

	}
}
