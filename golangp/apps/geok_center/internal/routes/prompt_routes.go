/*
 * @Description: 重构后的提示内容路由
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/handlers"
	"pointer/golangp/apps/geok_center/internal/middleware"
	"pointer/golangp/apps/geok_center/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupPromptRoutes configures prompt-related routes
func SetupPromptRoutes(router *gin.RouterGroup, db *gorm.DB) {
	// Initialize services
	promptService := services.NewPromptService(db)

	// Initialize handlers
	promptHandler := handlers.NewPromptHandler(promptService)

	// Prompt routes
	promptGroup := router.Group("/prompts")
	promptGroup.Use(middleware.DeserializeUser()) // Require authentication
	{
		// Basic CRUD operations
		promptGroup.POST("", promptHandler.CreatePrompt)       // Create prompt
		promptGroup.GET("/:id", promptHandler.GetPrompt)       // Get prompt by ID
		promptGroup.PUT("/:id", promptHandler.UpdatePrompt)    // Update prompt
		promptGroup.DELETE("/:id", promptHandler.DeletePrompt) // Delete prompt

		// Query operations
		promptGroup.GET("/top", promptHandler.GetTopPrompts) // Get top prompts
	}

	// Brand-specific prompt routes
	brandPromptGroup := router.Group("/brands/:id/prompts")
	brandPromptGroup.Use(middleware.DeserializeUser())
	{
		// Query operations
		brandPromptGroup.GET("", promptHandler.GetPromptsByBrand)            // Get prompts for brand
		brandPromptGroup.GET("/dashboard", promptHandler.GetPromptDashboard) // Get prompt dashboard

		// Generation operations
		brandPromptGroup.POST("/generate", promptHandler.GeneratePromptsFromAI) // Generate prompts from AI responses
	}

	// Prompt analytics routes
	analyticsGroup := router.Group("/prompt-analytics")
	analyticsGroup.Use(middleware.DeserializeUser())
	{
		// Performance analysis
		analyticsGroup.GET("/brands/:id/performance", func(c *gin.Context) {
			// Analyze prompt performance metrics
			c.JSON(200, gin.H{"message": "Prompt performance analysis retrieved"})
		})

		// Keyword analysis
		analyticsGroup.GET("/brands/:id/keywords", func(c *gin.Context) {
			// Analyze prompt keywords and frequency
			c.JSON(200, gin.H{"message": "Prompt keyword analysis retrieved"})
		})

		// Ranking analysis
		analyticsGroup.GET("/brands/:id/rankings", func(c *gin.Context) {
			// Analyze prompt rankings and tiers
			c.JSON(200, gin.H{"message": "Prompt ranking analysis retrieved"})
		})

		// Engagement analysis
		analyticsGroup.GET("/brands/:id/engagement", func(c *gin.Context) {
			// Analyze prompt engagement metrics
			c.JSON(200, gin.H{"message": "Prompt engagement analysis retrieved"})
		})
	}

	// Prompt info routes
	infoGroup := router.Group("/prompt-info")
	{
		infoGroup.GET("/statuses", func(c *gin.Context) {
			// Return available prompt statuses
			c.JSON(200, gin.H{
				"statuses": []string{
					"active",
					"inactive",
					"pending",
					"archived",
				},
			})
		})

		infoGroup.GET("/ranking-tiers", func(c *gin.Context) {
			// Return ranking tier definitions
			c.JSON(200, gin.H{
				"tiers": map[string]string{
					"top":    "Top ranking (1-3)",
					"high":   "High ranking (4-10)",
					"medium": "Medium ranking (11-20)",
					"low":    "Low ranking (21+)",
				},
			})
		})

		infoGroup.GET("/categories", func(c *gin.Context) {
			// Return available categories
			c.JSON(200, gin.H{
				"categories": []string{
					"ai_generated",
					"analysis",
					"strategy",
					"optimization",
					"general",
				},
			})
		})
	}
}
