/*
 * @Description: GEO optimization routes for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/handlers"
	"pointer/golangp/apps/geok_center/internal/middleware"
	"pointer/golangp/apps/geok_center/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupGEOOptimizationRoutes sets up GEO optimization related routes
func SetupGEOOptimizationRoutes(router *gin.RouterGroup, db *gorm.DB) {
	// Initialize services
	geoOptimizationService := services.NewGEOOptimizationService(db)

	// Initialize handlers
	geoHandler := handlers.NewGEOOptimizationHandler(geoOptimizationService)

	// Brand-specific GEO optimization routes
	brandGeoGroup := router.Group("/brands/:id/geo-optimizations")
	brandGeoGroup.Use(middleware.DeserializeUser())
	{
		brandGeoGroup.GET("", geoHandler.GetGEOOptimizationsByBrand)                 // Get GEO optimizations for brand
		brandGeoGroup.GET("/analysis", geoHandler.AnalyzeGEOPerformance)             // Analyze GEO performance
		brandGeoGroup.GET("/recommendations", geoHandler.GenerateGEORecommendations) // Generate recommendations
	}

	// Brand-specific GEO database routes
	brandGeoDBGroup := router.Group("/brands/:id/geo-databases")
	brandGeoDBGroup.Use(middleware.DeserializeUser())
	{
		brandGeoDBGroup.GET("", geoHandler.GetGEODatabasesByBrand) // Get GEO databases for brand
	}
}
