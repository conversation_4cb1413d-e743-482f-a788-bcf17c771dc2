/*
 * @Description: Main routes configuration for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/middleware"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SetupRoutes configures all routes for the application
func SetupRoutes(router *gin.Engine, db *gorm.DB, googleClientID string, googleClientSecret string) {
	// Add global middleware
	router.Use(middleware.CORS())
	router.Use(middleware.ErrorHandler())
	router.Use(middleware.Security())
	router.Use(middleware.RequestID())
	router.Use(gin.Recovery())

	// Setup health check routes
	SetupHealthRoutes(router)

	// API routes group
	api := router.Group("/api/v1")
	{
		// Setup API health routes
		SetupAPIHealthRoutes(api)

		// Setup authentication routes (no auth required)
		SetupAuthRoutes(api, googleClientID, googleClientSecret)

		// Protected routes group (requires authentication)
		protected := api.Group("")
		protected.Use(middleware.DeserializeUser())
		{
			// Setup protected authentication routes
			SetupProtectedAuthRoutes(protected, googleClientID, googleClientSecret)

			// Setup legacy feature routes (保持向后兼容)
			SetupBrandRoutes(protected)
			SetupAnalyticsRoutes(protected)
			SetupExportRoutes(protected)
			SetupNotificationRoutes(protected)
			SetupUserRoutes(protected)

			// Setup new business flow routes (新业务流程路由)
			SetupAISearchRoutes(protected, db)        // AI搜索路由 (包含AI可见性指标)
			SetupPromptRoutes(protected, db)          // 重构后的提示路由
			SetupReferenceRoutes(protected, db)       // 引用路由
			SetupGEOOptimizationRoutes(protected, db) // GEO优化路由
		}

		// Setup blog routes (部分公开，部分需要认证)
		SetupBlogRoutes(api, db) // 博客路由

		// Setup upload routes (independent of business logic, no authentication required)
		SetupUploadRoutes(api)
	}
}
