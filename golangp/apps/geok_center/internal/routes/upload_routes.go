/*
 * @Description: Upload routes for GEOK Center file upload management
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package routes

import (
	"pointer/golangp/apps/geok_center/internal/handlers"

	"github.com/gin-gonic/gin"
)

// SetupUploadRoutes configures file upload routes
func SetupUploadRoutes(api *gin.RouterGroup) {
	// Initialize upload handler
	uploadHandler := handlers.NewUploadHandler()

	// Upload routes (independent of business logic)
	upload := api.Group("/upload")
	{
		upload.POST("/file", uploadHandler.UploadFile)
	}
}
