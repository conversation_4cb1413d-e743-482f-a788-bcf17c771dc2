/*
 * @Description: Health check routes for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package routes

import (
	"pointer/golangp/apps/geok_center/pkg/response"
	"time"

	"github.com/gin-gonic/gin"
)

// SetupHealthRoutes configures health check routes
func SetupHealthRoutes(router *gin.Engine) {
	// Simple health endpoint
	router.GET("/health", func(c *gin.Context) {
		response.OK(c, "hello geok", nil)
	})

	// Ping endpoint
	router.GET("/ping", func(c *gin.Context) {
		response.OK(c, "pong", map[string]interface{}{
			"time": time.Now().Unix(),
		})
	})
}

// SetupAPIHealthRoutes configures API health check routes
func SetupAPIHealthRoutes(api *gin.RouterGroup) {
	// Detailed health check
	api.GET("/health", func(c *gin.Context) {
		response.OK(c, "hello geok", map[string]interface{}{
			"status":    "ok",
			"service":   "geok_center",
			"version":   "1.0.0",
			"timestamp": time.Now().Unix(),
		})
	})

	// API status endpoint
	api.GET("/status", func(c *gin.Context) {
		response.OK(c, "", map[string]interface{}{
			"api":       "online",
			"database":  "connected", // TODO: Add actual database health check
			"redis":     "connected", // TODO: Add actual Redis health check
			"timestamp": time.Now().Unix(),
		})
	})
}
