load("@rules_go//go:def.bzl", "go_library")

go_library(
    name = "handlers",
    srcs = [
        "ai_search_handler.go",
        "ai_visibility_handler.go",
        "analytics_handler.go",
        "auth_handler.go",
        "blog_handler.go",
        "brand_handler.go",
        "export_handler.go",
        "geo_optimization_handler.go",
        "notification_handler.go",
        "prompt_handler.go",
        "reference_handler.go",
        "upload_handler.go",
        "user_handler.go",
    ],
    importpath = "pointer/golangp/apps/geok_center/internal/handlers",
    visibility = ["//visibility:public"],
    deps = [
        "//golangp/apps/geok_center/internal/config",
        "//golangp/apps/geok_center/internal/models",
        "//golangp/apps/geok_center/internal/services",
        "//golangp/apps/geok_center/pkg/response",
        "//golangp/apps/geok_center/pkg/services/manager",
        "//golangp/apps/geok_center/pkg/types",
        "//golangp/common/auth/googleauth",
        "//golangp/common/database/postgres",
        "//golangp/common/logging:logger",
        "//golangp/common/utils",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_google_uuid//:uuid",
        "@io_gorm_datatypes//:datatypes",
        "@io_gorm_gorm//:gorm",
        "@org_golang_x_crypto//bcrypt",
    ],
)
