/*
 * @Description: Export handler for GEOK Center API
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package handlers

import (
	"os"
	"path/filepath"

	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/apps/geok_center/pkg/response"
	"pointer/golangp/apps/geok_center/pkg/types"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
)

// ExportHandler handles export-related HTTP requests
type ExportHandler struct {
	exportService *services.ExportService
	logger        *logging.Logger
}

// NewExportHandler creates a new export handler
func NewExportHandler(exportService *services.ExportService) *ExportHandler {
	return &ExportHandler{
		exportService: exportService,
		logger:        logging.GetLogger("export_handler"),
	}
}

// ExportDashboard handles POST /exports/dashboard
func (h *ExportHandler) ExportDashboard(c *gin.Context) {
	var request types.ExportRequest
	if err := c.ShouldBindJ<PERSON>(&request); err != nil {
		response.BadRequest(c, "Invalid request data")
		return
	}

	// Validate required fields
	if request.Format == "" {
		response.BadRequest(c, "Export format is required")
		return
	}

	// Validate format
	if request.Format != "csv" && request.Format != "json" && request.Format != "excel" {
		response.BadRequest(c, "Unsupported export format. Supported formats: csv, json, excel")
		return
	}

	// Set data type for dashboard export
	request.DataType = "dashboard"

	// Export dashboard data
	exportResponse, err := h.exportService.ExportDashboard(request)
	if err != nil {
		response.InternalServerError(c, "Failed to export dashboard data: "+err.Error())
		return
	}

	response.OK(c, "Dashboard data exported successfully", exportResponse)
}

// ExportAnalytics handles POST /exports/analytics
func (h *ExportHandler) ExportAnalytics(c *gin.Context) {
	var request types.ExportRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		response.BadRequest(c, "Invalid request data")
		return
	}

	// Validate required fields
	if request.Format == "" {
		response.BadRequest(c, "Export format is required")
		return
	}

	if request.DataType == "" {
		response.BadRequest(c, "Data type is required")
		return
	}

	// Validate format
	if request.Format != "csv" && request.Format != "json" && request.Format != "excel" {
		response.BadRequest(c, "Unsupported export format. Supported formats: csv, json, excel")
		return
	}

	// Validate data type
	validDataTypes := []string{"brands", "search_metrics", "ai_appearances", "competitor_data"}
	isValidDataType := false
	for _, validType := range validDataTypes {
		if request.DataType == validType {
			isValidDataType = true
			break
		}
	}

	if !isValidDataType {
		response.BadRequest(c, "Invalid data type. Supported types: brands, search_metrics, ai_appearances, competitor_data")
		return
	}

	// Export analytics data
	exportResponse, err := h.exportService.ExportAnalytics(request)
	if err != nil {
		response.InternalServerError(c, "Failed to export analytics data: "+err.Error())
		return
	}

	response.OK(c, "Analytics data exported successfully", exportResponse)
}

// DownloadFile handles GET /exports/download/:filename
func (h *ExportHandler) DownloadFile(c *gin.Context) {
	filename := c.Param("filename")
	if filename == "" {
		response.BadRequest(c, "Filename is required")
		return
	}

	// Security check: prevent directory traversal
	if filepath.Base(filename) != filename {
		response.BadRequest(c, "Invalid filename")
		return
	}

	// Construct file path (this should use the same temp directory as export service)
	filePath := filepath.Join("/tmp", filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		response.NotFound(c, "File not found or has expired")
		return
	}

	// Set appropriate headers for file download
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "application/octet-stream")

	// Serve the file
	c.File(filePath)
}

// GetExportHistory handles GET /exports/history
func (h *ExportHandler) GetExportHistory(c *gin.Context) {
	// This would typically fetch export history from database
	// For now, return mock data
	history := []map[string]interface{}{
		{
			"id":           "1",
			"filename":     "dashboard_export_20250715_120000.csv",
			"data_type":    "dashboard",
			"format":       "csv",
			"status":       "completed",
			"created_at":   "2025-07-15T12:00:00Z",
			"expires_at":   "2025-07-16T12:00:00Z",
			"file_size":    1024,
			"download_url": "/api/v1/exports/download/dashboard_export_20250715_120000.csv",
		},
		{
			"id":           "2",
			"filename":     "brands_export_20250715_110000.csv",
			"data_type":    "brands",
			"format":       "csv",
			"status":       "completed",
			"created_at":   "2025-07-15T11:00:00Z",
			"expires_at":   "2025-07-16T11:00:00Z",
			"file_size":    2048,
			"download_url": "/api/v1/exports/download/brands_export_20250715_110000.csv",
		},
	}

	response.OK(c, "", history)
}

// GetExportStatus handles GET /exports/status/:id
func (h *ExportHandler) GetExportStatus(c *gin.Context) {
	exportID := c.Param("id")
	if exportID == "" {
		response.BadRequest(c, "Export ID is required")
		return
	}

	// This would typically fetch export status from database
	// For now, return mock data
	status := map[string]interface{}{
		"id":         exportID,
		"status":     "completed",
		"progress":   100,
		"message":    "Export completed successfully",
		"created_at": "2025-07-15T12:00:00Z",
		"updated_at": "2025-07-15T12:01:00Z",
	}

	response.OK(c, "", status)
}
