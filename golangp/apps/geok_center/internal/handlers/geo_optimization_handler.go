/*
 * @Description: GEO optimization handler for GEOK Center API
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package handlers

import (
	"net/http"
	"strconv"
	"time"

	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/apps/geok_center/pkg/response"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/datatypes"
)

// GEOOptimizationHandler handles GEO optimization related HTTP requests
type GEOOptimizationHandler struct {
	geoService *services.GEOOptimizationService
	logger     *logging.Logger
}

// NewGEOOptimizationHandler creates a new GEO optimization handler
func NewGEOOptimizationHandler(geoService *services.GEOOptimizationService) *GEOOptimizationHandler {
	return &GEOOptimizationHandler{
		geoService: geoService,
		logger:     logging.GetLogger("geo_optimization_handler"),
	}
}

// CreateGEOOptimizationRequest represents the request for creating GEO optimization
type CreateGEOOptimizationRequest struct {
	BrandID            uuid.UUID      `json:"brand_id" binding:"required"`
	Region             string         `json:"region" binding:"required"`
	Country            string         `json:"country" binding:"required"`
	City               string         `json:"city"`
	VisibilityScore    float64        `json:"visibility_score"`
	MarketPenetration  float64        `json:"market_penetration"`
	SearchVolume       int64          `json:"search_volume"`
	CompetitionLevel   string         `json:"competition_level"`
	OpportunityScore   float64        `json:"opportunity_score"`
	KeyMetrics         datatypes.JSON `json:"key_metrics"`
	TrendingKeywords   datatypes.JSON `json:"trending_keywords"`
	RecommendedActions datatypes.JSON `json:"recommended_actions"`
	Priority           int            `json:"priority"`
}

// CreateGEODatabaseRequest represents the request for creating GEO database entry
type CreateGEODatabaseRequest struct {
	BrandID           uuid.UUID      `json:"brand_id" binding:"required"`
	GeoOptimizationID uuid.UUID      `json:"geo_optimization_id" binding:"required"`
	AISearchID        *uuid.UUID     `json:"ai_search_id,omitempty"`
	ContentType       string         `json:"content_type" binding:"required"`
	Title             string         `json:"title" binding:"required"`
	Content           string         `json:"content" binding:"required"`
	Region            string         `json:"region" binding:"required"`
	Country           string         `json:"country" binding:"required"`
	Language          string         `json:"language"`
	SearchQuery       string         `json:"search_query"`
	SearchResults     datatypes.JSON `json:"search_results"`
	KeywordData       datatypes.JSON `json:"keyword_data"`
	CompetitorData    datatypes.JSON `json:"competitor_data"`
	RelevanceScore    float64        `json:"relevance_score"`
	QualityScore      float64        `json:"quality_score"`
	ConfidenceLevel   float64        `json:"confidence_level"`
	Source            string         `json:"source"`
	Metadata          datatypes.JSON `json:"metadata"`
	Tags              datatypes.JSON `json:"tags"`
}

// GetGEOOptimizationsByBrand retrieves GEO optimizations for a brand
func (h *GEOOptimizationHandler) GetGEOOptimizationsByBrand(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	geos, total, err := h.geoService.GetGEOOptimizationsByBrandID(brandID, pageSize, offset)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to get GEO optimizations", err.Error())
		return
	}

	result := map[string]interface{}{
		"data":        geos,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
	}

	response.Success(c, http.StatusOK, "GEO optimizations retrieved successfully", result)
}

// AnalyzeGEOPerformance analyzes GEO performance for a brand
func (h *GEOOptimizationHandler) AnalyzeGEOPerformance(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	analysis, err := h.geoService.AnalyzeGEOPerformance(brandID)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to analyze GEO performance", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "GEO performance analysis completed", analysis)
}

// GenerateGEORecommendations generates optimization recommendations for a brand
func (h *GEOOptimizationHandler) GenerateGEORecommendations(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	recommendations, err := h.geoService.GenerateGEORecommendations(brandID)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to generate recommendations", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "GEO recommendations generated successfully", recommendations)
}

// GetGEODatabasesByBrand retrieves GEO database statistics for a brand (latest N days aggregation)
func (h *GEOOptimizationHandler) GetGEODatabasesByBrand(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// Parse days parameter (default to 7)
	daysStr := c.DefaultQuery("days", "7")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid days parameter", "Days must be between 1 and 365")
		return
	}

	// Get GEO database statistics
	geoStats, err := h.geoService.GetGEODatabaseStatsForBrand(brandID, days)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve GEO database statistics", err.Error())
		return
	}

	result := map[string]interface{}{
		"data":      geoStats,
		"days":      days,
		"brand_id":  brandID,
		"timestamp": time.Now(),
	}

	response.Success(c, http.StatusOK, "GEO database statistics retrieved successfully", result)
}
