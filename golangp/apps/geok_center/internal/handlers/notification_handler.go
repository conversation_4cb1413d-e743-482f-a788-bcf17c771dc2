/*
 * @Description: 通知处理器
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package handlers

import (
	"net/http"
	"strconv"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/apps/geok_center/pkg/response"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// NotificationHandler handles notification-related requests
type NotificationHandler struct {
	notificationService *services.NotificationService
	logger              *logging.Logger
}

// NewNotificationHandler creates a new notification handler
func NewNotificationHandler(notificationService *services.NotificationService) *NotificationHandler {
	return &NotificationHandler{
		notificationService: notificationService,
		logger:              logging.GetLogger("notification_handler"),
	}
}

// GetNotifications retrieves notifications for the authenticated user
func (h *NotificationHandler) GetNotifications(c *gin.Context) {
	// Get user from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "20")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page <= 0 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize <= 0 || pageSize > 100 {
		pageSize = 20
	}

	// Convert to limit/offset for service layer
	limit := pageSize
	offset := (page - 1) * pageSize

	notifications, total, err := h.notificationService.GetNotificationsByUserID(userID.(uuid.UUID), limit, offset)
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to get notifications", err.Error())
		return
	}

	// Get unread count
	unreadCount, err := h.notificationService.GetUnreadNotificationCount(userID.(uuid.UUID))
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to get unread count", err.Error())
		return
	}

	response.OK(c, "", map[string]interface{}{
		"notifications": notifications,
		"total":         total,
		"unread_count":  unreadCount,
		"limit":         limit,
		"offset":        offset,
	})
}

// GetNotification retrieves a notification by ID
func (h *NotificationHandler) GetNotification(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "Invalid notification ID")
		return
	}

	notification, err := h.notificationService.GetNotificationByID(id)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusNotFound, "Notification not found", err.Error())
		return
	}

	response.OK(c, "", map[string]interface{}{
		"notification": notification,
	})
}

// MarkNotificationAsRead marks a notification as read
func (h *NotificationHandler) MarkNotificationAsRead(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "Invalid notification ID")
		return
	}

	if err := h.notificationService.MarkNotificationAsRead(id); err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to mark notification as read", err.Error())
		return
	}

	response.OK(c, "Notification marked as read", nil)
}

// MarkAllNotificationsAsRead marks all notifications as read
func (h *NotificationHandler) MarkAllNotificationsAsRead(c *gin.Context) {
	// Get user from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	if err := h.notificationService.MarkAllNotificationsAsRead(userID.(uuid.UUID)); err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to mark all notifications as read", err.Error())
		return
	}

	response.OK(c, "All notifications marked as read", nil)
}

// DeleteNotification deletes a notification
func (h *NotificationHandler) DeleteNotification(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.BadRequest(c, "Invalid notification ID")
		return
	}

	if err := h.notificationService.DeleteNotification(id); err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to delete notification", err.Error())
		return
	}

	response.OK(c, "Notification deleted", nil)
}

// DeleteAllNotifications deletes all notifications
func (h *NotificationHandler) DeleteAllNotifications(c *gin.Context) {
	// Get user from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	if err := h.notificationService.DeleteAllNotifications(userID.(uuid.UUID)); err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to delete all notifications", err.Error())
		return
	}

	response.OK(c, "All notifications deleted", nil)
}

// GetNotificationSettings retrieves notification settings
func (h *NotificationHandler) GetNotificationSettings(c *gin.Context) {
	// Get user from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	settings, err := h.notificationService.GetNotificationSettings(userID.(uuid.UUID))
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to get notification settings", err.Error())
		return
	}

	response.OK(c, "", map[string]interface{}{
		"settings": settings,
	})
}

// UpdateNotificationSettings updates notification settings
func (h *NotificationHandler) UpdateNotificationSettings(c *gin.Context) {
	// Get user from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	var req models.NotificationSetting
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetails(c, "Invalid request", err.Error())
		return
	}

	// Get existing settings
	settings, err := h.notificationService.GetNotificationSettings(userID.(uuid.UUID))
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to get notification settings", err.Error())
		return
	}

	// Update fields
	settings.EmailNotifications = req.EmailNotifications
	settings.PushNotifications = req.PushNotifications
	settings.SystemNotifications = req.SystemNotifications
	settings.PromptNotifications = req.PromptNotifications
	settings.BrandNotifications = req.BrandNotifications
	settings.AnalyticsNotifications = req.AnalyticsNotifications
	settings.AlertNotifications = req.AlertNotifications
	settings.UpdateNotifications = req.UpdateNotifications

	updatedSettings, err := h.notificationService.UpdateNotificationSettings(settings)
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to update notification settings", err.Error())
		return
	}

	response.OK(c, "Notification settings updated", map[string]interface{}{
		"settings": updatedSettings,
	})
}

