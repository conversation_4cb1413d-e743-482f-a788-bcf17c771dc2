/*
 * @Description: Authentication handlers for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package handlers

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"pointer/golangp/apps/geok_center/internal/config"
	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/pkg/response"
	"pointer/golangp/common/auth/googleauth"
	"pointer/golangp/common/logging"
	"pointer/golangp/common/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	db            *gorm.DB
	googleService *googleauth.Service
	logger        *logging.Logger
}

// NewAuthHandler creates a new authentication handler
func NewAuthHandler(db *gorm.DB, googleClientID string, googleClientSecret string) *AuthHandler {
	logger := logging.GetLogger("auth_handler")

	// Log Google OAuth configuration (mask sensitive data)
	maskedClientID := googleClientID
	if len(googleClientID) > 10 {
		maskedClientID = googleClientID[:10] + "..."
	}
	maskedClientSecret := "***"
	if googleClientSecret != "" {
		maskedClientSecret = "***configured***"
	}

	logger.Info("🔧 Initializing Google OAuth - ClientID: %s, ClientSecret: %s", maskedClientID, maskedClientSecret)

	googleConfig := googleauth.NewConfig(googleClientID, googleClientSecret)
	googleService := googleauth.NewService(googleConfig)

	return &AuthHandler{
		db:            db,
		googleService: googleService,
		logger:        logger,
	}
}

// LoginRequest represents the unified login request
type LoginRequest struct {
	// For normal login
	Email    string `json:"email"`
	Password string `json:"password"`

	// For Google login with ID Token (frontend flow)
	IDToken string `json:"id_token"`

	// For Google login with authorization code (server flow)
	AuthCode    string `json:"auth_code"`
	RedirectURI string `json:"redirect_uri"`
}

// RegisterRequest represents the registration request
type RegisterRequest struct {
	Username  string `json:"username" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=6"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

// LoginResponse represents the response for successful login
type LoginResponse struct {
	User         *models.User `json:"user"`
	AccessToken  string       `json:"access_token"`
	RefreshToken string       `json:"refresh_token"`
	ExpiresAt    time.Time    `json:"expires_at"`
	IsNewUser    bool         `json:"is_new_user"`
	LoginTime    time.Time    `json:"login_time"`
	Message      string       `json:"message"`
}

// HandleLogin handles unified login (normal or Google)
func (h *AuthHandler) HandleLogin(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	h.logger.Info("🔐 HandleLogin started - Method: %s, Path: %s, ClientIP: %s, UserAgent: %s",
		c.Request.Method, c.Request.URL.Path, clientIP, userAgent)

	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Login request binding failed - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequestWithDetails(c, "Invalid request", err.Error())
		return
	}

	h.logger.Info("📝 Login request received - Email: %s, HasIDToken: %v, HasAuthCode: %v, ClientIP: %s",
		req.Email, req.IDToken != "", req.AuthCode != "", clientIP)

	// Check if this is a Google login or normal login
	if req.IDToken != "" {
		// Google login with ID Token (frontend flow)
		h.logger.Info("🔄 Processing Google ID token login - Email: %s, ClientIP: %s", req.Email, clientIP)
		h.handleGoogleLogin(c, req.IDToken)
		h.logger.Info("✅ HandleLogin completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	} else if req.AuthCode != "" && req.RedirectURI != "" {
		// Google login with authorization code (server flow)
		h.logger.Info("🔄 Processing Google auth code login - Email: %s, ClientIP: %s", req.Email, clientIP)
		h.handleGoogleAuthCode(c, req.AuthCode, req.RedirectURI)
		h.logger.Info("✅ HandleLogin completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	} else if req.Email != "" && req.Password != "" {
		// Normal login
		h.logger.Info("🔄 Processing normal email/password login - Email: %s, ClientIP: %s", req.Email, clientIP)
		h.handleNormalLogin(c, req.Email, req.Password)
		h.logger.Info("✅ HandleLogin completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	} else {
		h.logger.Warning("❌ Invalid login request - Missing required fields, ClientIP: %s", clientIP)
		response.BadRequest(c, "Either ID token, auth code with redirect URI, or email/password must be provided")
		h.logger.Info("❌ HandleLogin failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}
}

// HandleRegister handles user registration
func (h *AuthHandler) HandleRegister(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	h.logger.Info("📝 HandleRegister started - Method: %s, Path: %s, ClientIP: %s, UserAgent: %s",
		c.Request.Method, c.Request.URL.Path, clientIP, userAgent)

	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("❌ Register request binding failed - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequestWithDetails(c, "Invalid request", err.Error())
		h.logger.Info("❌ HandleRegister failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	h.logger.Info("📝 Register request received - Email: %s, ClientIP: %s", req.Email, clientIP)

	// Check if email already exists
	h.logger.Debug("🔍 Checking if email exists - Email: %s, ClientIP: %s", req.Email, clientIP)
	var existingUser models.User
	if err := h.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		h.logger.Warning("❌ Registration failed - Email already exists: %s, ClientIP: %s", req.Email, clientIP)
		response.Conflict(c, "Email already registered")
		h.logger.Info("❌ HandleRegister failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	// Check if username already exists
	h.logger.Debug("🔍 Checking if username exists - Username: %s, ClientIP: %s", req.Username, clientIP)
	if err := h.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		h.logger.Warning("❌ Registration failed - Username already taken: %s, ClientIP: %s", req.Username, clientIP)
		response.Conflict(c, "Username already taken")
		h.logger.Info("❌ HandleRegister failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	// Hash password
	h.logger.Debug("🔐 Hashing password for user: %s, ClientIP: %s", req.Email, clientIP)
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		h.logger.Error("❌ Password hashing failed - Email: %s, Error: %v, ClientIP: %s", req.Email, err, clientIP)
		response.InternalServerErrorWithDetails(c, "Failed to process password", err.Error())
		h.logger.Info("❌ HandleRegister failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	// Create new user
	userID := uuid.New()
	h.logger.Info("👤 Creating new user - UserID: %s, Email: %s, Username: %s, ClientIP: %s",
		userID, req.Email, req.Username, clientIP)

	newUser := &models.User{
		ID:        userID,
		Username:  req.Username,
		Email:     req.Email,
		Password:  hashedPassword,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Role:      models.UserRoleUser,
		Status:    models.UserStatusActive,
		Origin:    "geok",
	}

	// Set current time as last login
	now := time.Now()
	newUser.LastLogin = &now

	h.logger.Debug("💾 Saving user to database - UserID: %s, ClientIP: %s", userID, clientIP)
	if err := h.db.Create(newUser).Error; err != nil {
		h.logger.Error("❌ Failed to create user - UserID: %s, Email: %s, Error: %v, ClientIP: %s",
			userID, req.Email, err, clientIP)
		response.InternalServerErrorWithDetails(c, "Failed to create user", err.Error())
		h.logger.Info("❌ HandleRegister failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}
	h.logger.Info("✅ User created successfully - UserID: %s, Email: %s, ClientIP: %s",
		newUser.ID, newUser.Email, clientIP)

	// Generate tokens
	h.logger.Debug("🔑 Generating tokens - UserID: %s, ClientIP: %s", newUser.ID, clientIP)
	accessToken, refreshToken, accessExpiresAt, _, err := h.generateTokens(newUser)
	if err != nil {
		h.logger.Error("❌ Failed to generate tokens - UserID: %s, Error: %v, ClientIP: %s",
			newUser.ID, err, clientIP)
		response.InternalServerErrorWithDetails(c, "Failed to generate tokens", err.Error())
		h.logger.Info("❌ HandleRegister failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	// Create session record
	sessionID := uuid.New()
	h.logger.Debug("💾 Creating session record - SessionID: %s, UserID: %s, ClientIP: %s",
		sessionID, newUser.ID, clientIP)

	session := &models.UserSession{
		ID:        sessionID,
		UserID:    newUser.ID,
		Token:     accessToken, // Store access token in session
		ExpiresAt: accessExpiresAt,
		IPAddress: c.ClientIP(),
		UserAgent: c.GetHeader("User-Agent"),
		IsActive:  true,
	}

	if err := h.db.Create(session).Error; err != nil {
		h.logger.Error("❌ Failed to create session - SessionID: %s, UserID: %s, Error: %v, ClientIP: %s",
			sessionID, newUser.ID, err, clientIP)
		response.InternalServerErrorWithDetails(c, "Failed to create session", err.Error())
		h.logger.Info("❌ HandleRegister failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	h.logger.Info("✅ User registration completed successfully - UserID: %s, Email: %s, SessionID: %s, ClientIP: %s",
		newUser.ID, newUser.Email, sessionID, clientIP)

	response.Created(c, "Registration successful", LoginResponse{
		User:         newUser,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    accessExpiresAt,
		IsNewUser:    true,
		LoginTime:    time.Now(),
		Message:      "Registration successful",
	})

	h.logger.Info("✅ HandleRegister completed successfully - Duration: %v, UserID: %s, ClientIP: %s",
		time.Since(startTime), newUser.ID, clientIP)
}

// handleGoogleAuthCode handles Google OAuth login with authorization code
func (h *AuthHandler) handleGoogleAuthCode(c *gin.Context, authCode string, redirectURI string) {
	ctx := context.Background()

	// Mask auth code for logging
	maskedCode := authCode
	if len(authCode) > 10 {
		maskedCode = authCode[:10] + "..."
	}
	h.logger.Info("🔄 Exchanging Google auth code - Code: %s, RedirectURI: %s", maskedCode, redirectURI)

	// Exchange authorization code for ID token
	idToken, err := h.googleService.ExchangeAuthCode(ctx, authCode, redirectURI)
	if err != nil {
		h.logger.Error("❌ Failed to exchange auth code: %v", err)
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Failed to exchange authorization code", err.Error())
		return
	}

	h.logger.Info("✅ Successfully exchanged auth code for ID token")

	// Now that we have the ID token, proceed with normal Google login flow
	h.handleGoogleLogin(c, idToken)
}

// handleGoogleLogin handles Google OAuth login with ID token
func (h *AuthHandler) handleGoogleLogin(c *gin.Context, idToken string) {
	ctx := context.Background()

	// Verify Google ID Token
	googleUser, err := h.googleService.VerifyIDToken(ctx, idToken)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Invalid Google ID token", err.Error())
		return
	}

	// Check if user exists by google_email first, then by email
	var existingUser models.User
	err = h.db.Where("google_email = ?", googleUser.Email).First(&existingUser).Error
	isNewUser := false

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// User doesn't exist with google_email, create new user
		newUser := &models.User{
			ID:          uuid.New(),
			Username:    generateUsernameFromEmail(googleUser.Email),
			Email:       googleUser.Email,
			GoogleEmail: googleUser.Email,
			FirstName:   googleUser.GivenName,
			LastName:    googleUser.FamilyName,
			Role:        models.UserRoleUser,
			Status:      models.UserStatusActive,
			Origin:      "google",
			LastLogin:   &time.Time{},
		}

		// Set current time as last login
		now := time.Now()
		newUser.LastLogin = &now

		if err := h.db.Create(newUser).Error; err != nil {
			response.InternalServerErrorWithDetails(c, "Failed to create user", err.Error())
			return
		}

		existingUser = *newUser
		isNewUser = true
	} else if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to get user", err.Error())
		return
	} else {
		// User exists, update last login
		now := time.Now()
		existingUser.LastLogin = &now

		// Update user info from Google if needed
		if existingUser.Origin == "google" {
			existingUser.FirstName = googleUser.GivenName
			existingUser.LastName = googleUser.FamilyName
		}

		if err := h.db.Save(&existingUser).Error; err != nil {
			response.InternalServerErrorWithDetails(c, "Failed to update user", err.Error())
			return
		}
	}

	// Generate tokens
	accessToken, refreshToken, accessExpiresAt, _, err := h.generateTokens(&existingUser)
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to generate tokens", err.Error())
		return
	}

	// Create session record
	session := &models.UserSession{
		ID:        uuid.New(),
		UserID:    existingUser.ID,
		Token:     accessToken, // Store access token in session
		ExpiresAt: accessExpiresAt,
		IPAddress: c.ClientIP(),
		UserAgent: c.GetHeader("User-Agent"),
		IsActive:  true,
	}

	if err := h.db.Create(session).Error; err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to create session", err.Error())
		return
	}

	// Set HTTP cookies
	cfg := config.Load()
	c.SetCookie("access_token", accessToken, cfg.AccessTokenMaxAge*60, "/", cfg.Domain, false, true)
	c.SetCookie("refresh_token", refreshToken, cfg.RefreshTokenMaxAge*60, "/", cfg.Domain, false, true)

	// Return success response
	message := "Login successful"
	if isNewUser {
		message = "Account created and login successful"
	}

	response.OK(c, message, LoginResponse{
		User:         &existingUser,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    accessExpiresAt,
		IsNewUser:    isNewUser,
		LoginTime:    time.Now(),
		Message:      message,
	})
}

// handleNormalLogin handles normal login with email and password
func (h *AuthHandler) handleNormalLogin(c *gin.Context, email, password string) {
	// Get user by email
	var user models.User
	if err := h.db.Where("email = ?", email).First(&user).Error; err != nil {
		h.logger.Warning("❌ User not found - Email: %s, ClientIP: %s", email, c.ClientIP())
		response.Unauthorized(c, "User not found")
		return
	}

	// Verify password
	if err := utils.VerifyPassword(user.Password, password); err != nil {
		h.logger.Warning("❌ Password verification failed - Email: %s, ClientIP: %s", email, c.ClientIP())
		response.Unauthorized(c, "Password incorrect")
		return
	}

	// Update last login
	now := time.Now()
	user.LastLogin = &now
	if err := h.db.Save(&user).Error; err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to update user", err.Error())
		return
	}

	// Generate tokens
	accessToken, refreshToken, accessExpiresAt, _, err := h.generateTokens(&user)
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to generate tokens", err.Error())
		return
	}

	// Create session record
	session := &models.UserSession{
		ID:        uuid.New(),
		UserID:    user.ID,
		Token:     accessToken, // Store access token in session
		ExpiresAt: accessExpiresAt,
		IPAddress: c.ClientIP(),
		UserAgent: c.GetHeader("User-Agent"),
		IsActive:  true,
	}

	if err := h.db.Create(session).Error; err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to create session", err.Error())
		return
	}

	// Set HTTP cookies
	cfg := config.Load()
	c.SetCookie("access_token", accessToken, cfg.AccessTokenMaxAge*60, "/", cfg.Domain, false, true)
	c.SetCookie("refresh_token", refreshToken, cfg.RefreshTokenMaxAge*60, "/", cfg.Domain, false, true)

	response.OK(c, "Login successful", LoginResponse{
		User:         &user,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    accessExpiresAt,
		IsNewUser:    false,
		LoginTime:    time.Now(),
		Message:      "Login successful",
	})
}

// generateUsernameFromEmail generates a username from email address
func generateUsernameFromEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) > 0 {
		return parts[0]
	}
	return "user_" + uuid.New().String()[:8]
}

// generateAccessToken generates a JWT access token for the user
func (h *AuthHandler) generateAccessToken(user *models.User) (string, time.Time, error) {
	cfg := config.Load()

	// Set token expiry
	expiresAt := time.Now().Add(time.Duration(cfg.AccessTokenMaxAge) * time.Minute)
	ttl := time.Duration(cfg.AccessTokenMaxAge) * time.Minute

	// Create token payload
	payload := map[string]interface{}{
		"user_id": user.ID.String(),
		"email":   user.Email,
		"role":    string(user.Role),
		"type":    "access",
	}

	// Generate JWT token using common utils
	token, err := utils.CreateToken(ttl, payload, cfg.AccessTokenPrivateKey)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to create access token: %w", err)
	}

	return token, expiresAt, nil
}

// generateRefreshToken generates a JWT refresh token for the user
func (h *AuthHandler) generateRefreshToken(user *models.User) (string, time.Time, error) {
	cfg := config.Load()

	// Set token expiry
	expiresAt := time.Now().Add(time.Duration(cfg.RefreshTokenMaxAge) * time.Minute)
	ttl := time.Duration(cfg.RefreshTokenMaxAge) * time.Minute

	// Create token payload
	payload := map[string]interface{}{
		"user_id": user.ID.String(),
		"email":   user.Email,
		"role":    string(user.Role),
		"type":    "refresh",
	}

	// Generate JWT token using common utils
	token, err := utils.CreateToken(ttl, payload, cfg.RefreshTokenPrivateKey)
	if err != nil {
		return "", time.Time{}, fmt.Errorf("failed to create refresh token: %w", err)
	}

	return token, expiresAt, nil
}

// generateTokens generates both access and refresh tokens for the user
func (h *AuthHandler) generateTokens(user *models.User) (string, string, time.Time, time.Time, error) {
	accessToken, accessExpiresAt, err := h.generateAccessToken(user)
	if err != nil {
		return "", "", time.Time{}, time.Time{}, err
	}

	refreshToken, refreshExpiresAt, err := h.generateRefreshToken(user)
	if err != nil {
		return "", "", time.Time{}, time.Time{}, err
	}

	return accessToken, refreshToken, accessExpiresAt, refreshExpiresAt, nil
}

// HandleLogout handles user logout
func (h *AuthHandler) HandleLogout(c *gin.Context) {
	// Get user from middleware context
	currentUser, exists := c.Get("currentUser")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	user, ok := currentUser.(models.User)
	if !ok {
		response.InternalServerError(c, "Invalid user context")
		return
	}

	// Get the access token from Authorization header to deactivate the session
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		token := strings.TrimPrefix(authHeader, "Bearer ")
		// Deactivate session
		if err := h.db.Model(&models.UserSession{}).Where("token = ?", token).Update("is_active", false).Error; err != nil {
			h.logger.Warning("Failed to deactivate session for user %s: %v", user.ID, err)
			// Continue with logout even if session deactivation fails
		}
	}

	// Clear HTTP cookies
	cfg := config.Load()
	c.SetCookie("access_token", "", -1, "/", cfg.Domain, false, true)
	c.SetCookie("refresh_token", "", -1, "/", cfg.Domain, false, true)

	h.logger.Info("✅ User logged out successfully - UserID: %s, Email: %s", user.ID, user.Email)
	response.OK(c, "Logout successful", nil)
}

// GetCurrentUser retrieves the current authenticated user
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	h.logger.Info("👤 GetCurrentUser started - Method: %s, Path: %s, ClientIP: %s, UserAgent: %s",
		c.Request.Method, c.Request.URL.Path, clientIP, userAgent)

	// Get user from middleware context
	currentUser, exists := c.Get("currentUser")
	if !exists {
		h.logger.Warning("❌ User not found in context - ClientIP: %s", clientIP)
		response.Unauthorized(c, "User not authenticated")
		h.logger.Info("❌ GetCurrentUser failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	user, ok := currentUser.(models.User)
	if !ok {
		h.logger.Error("❌ Invalid user context type - ClientIP: %s", clientIP)
		response.InternalServerError(c, "Invalid user context")
		h.logger.Info("❌ GetCurrentUser failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	h.logger.Info("✅ User retrieved from context - UserID: %s, Email: %s, ClientIP: %s",
		user.ID, user.Email, clientIP)

	response.OK(c, "", map[string]interface{}{
		"user": user,
	})

	h.logger.Info("✅ GetCurrentUser completed - Duration: %v, UserID: %s, ClientIP: %s",
		time.Since(startTime), user.ID, clientIP)
}

// RefreshToken refreshes an authentication token using refresh token
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// Get refresh token from cookie
	refreshToken, err := c.Cookie("refresh_token")
	if err != nil || refreshToken == "" {
		response.Unauthorized(c, "Refresh token not found")
		return
	}

	// Validate refresh token
	cfg := config.Load()
	payload, err := utils.ValidateToken(refreshToken, cfg.RefreshTokenPublicKey)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Invalid or expired refresh token", err.Error())
		return
	}

	// Extract user information from token payload
	payloadMap, ok := payload.(map[string]interface{})
	if !ok {
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Invalid token payload", nil)
		return
	}

	userID, ok := payloadMap["user_id"].(string)
	if !ok {
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Invalid user ID in token", nil)
		return
	}

	// Parse user ID to UUID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusUnauthorized, "Invalid user ID format", err.Error())
		return
	}

	// Get user from database
	var user models.User
	if err := h.db.First(&user, "id = ?", userUUID).Error; err != nil {
		response.ErrorWithDetails(c, http.StatusUnauthorized, "User not found", err.Error())
		return
	}

	// Generate new tokens
	newAccessToken, newRefreshToken, accessExpiresAt, _, err := h.generateTokens(&user)
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to generate new tokens", err.Error())
		return
	}

	// Note: We don't deactivate sessions for refresh token flow
	// The old refresh token will naturally expire

	// Create new session
	session := &models.UserSession{
		ID:        uuid.New(),
		UserID:    user.ID,
		Token:     newAccessToken, // Store new access token in session
		ExpiresAt: accessExpiresAt,
		IPAddress: c.ClientIP(),
		UserAgent: c.GetHeader("User-Agent"),
		IsActive:  true,
	}

	if err := h.db.Create(session).Error; err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to create new session", err.Error())
		return
	}

	// Set HTTP cookies with new tokens
	cfg = config.Load()
	c.SetCookie("access_token", newAccessToken, cfg.AccessTokenMaxAge*60, "/", cfg.Domain, false, true)
	c.SetCookie("refresh_token", newRefreshToken, cfg.RefreshTokenMaxAge*60, "/", cfg.Domain, false, true)

	response.OK(c, "Token refreshed successfully", map[string]interface{}{
		"access_token":  newAccessToken,
		"refresh_token": newRefreshToken,
		"expires_at":    accessExpiresAt,
	})
}

// UpdateProfile updates user's basic profile information
func (h *AuthHandler) UpdateProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warning("UpdateProfile called without user_id in context")
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		h.logger.Error("Invalid user_id type in context: %T", userID)
		response.BadRequest(c, "Invalid user ID")
		return
	}

	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		h.logger.Warning("Invalid update profile request - UserID: %s, Error: %v", userUUID, err)
		response.BadRequestWithDetails(c, "Invalid request data", err.Error())
		return
	}

	// Get current user
	var user models.User
	if err := h.db.First(&user, "id = ?", userUUID).Error; err != nil {
		h.logger.Error("Failed to get user - UserID: %s, Error: %v", userUUID, err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c, "User not found")
			return
		}
		response.InternalServerError(c, "Failed to retrieve user")
		return
	}

	// Validate username uniqueness if being updated
	if newUsername, exists := updates["username"]; exists {
		var existingUser models.User
		if err := h.db.Where("username = ? AND id != ?", newUsername, userUUID).First(&existingUser).Error; err == nil {
			h.logger.Warning("Username already exists - UserID: %s, Username: %v", userUUID, newUsername)
			response.BadRequest(c, "Username already exists")
			return
		}
	}

	// Update allowed fields
	allowedFields := []string{
		"username", "first_name", "last_name", "bio",
		"phone", "company", "country",
		"login_protection", "password_change_required", "security_lock",
	}
	updateData := make(map[string]interface{})
	for _, field := range allowedFields {
		if value, exists := updates[field]; exists {
			updateData[field] = value
		}
	}

	if len(updateData) == 0 {
		h.logger.Info("No valid fields to update - UserID: %s", userUUID)
		response.OK(c, "Profile updated successfully", &user)
		return
	}

	if err := h.db.Model(&user).Updates(updateData).Error; err != nil {
		h.logger.Error("Failed to update profile - UserID: %s, Error: %v", userUUID, err)
		response.InternalServerError(c, "Failed to update profile")
		return
	}

	// Reload user to get updated data
	if err := h.db.First(&user, "id = ?", userUUID).Error; err != nil {
		h.logger.Error("Failed to reload user - UserID: %s, Error: %v", userUUID, err)
		response.InternalServerError(c, "Failed to retrieve updated profile")
		return
	}

	h.logger.Info("Profile updated successfully - UserID: %s", userUUID)
	response.OK(c, "Profile updated successfully", &user)
}

// ChangePassword handles password change requests
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warning("ChangePassword called without user_id in context")
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		h.logger.Error("Invalid user_id type in context: %T", userID)
		response.BadRequest(c, "Invalid user ID")
		return
	}

	var req struct {
		CurrentPassword string `json:"current_password" binding:"required"`
		NewPassword     string `json:"new_password" binding:"required"`
		ConfirmPassword string `json:"confirm_password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("Invalid change password request - UserID: %s, Error: %v", userUUID, err)
		response.BadRequestWithDetails(c, "Invalid request data", err.Error())
		return
	}

	// Validate password confirmation
	if req.NewPassword != req.ConfirmPassword {
		h.logger.Warning("Password confirmation mismatch - UserID: %s", userUUID)
		response.BadRequest(c, "New password and confirm password do not match")
		return
	}

	// Get current user
	var user models.User
	if err := h.db.First(&user, "id = ?", userUUID).Error; err != nil {
		h.logger.Error("Failed to get user - UserID: %s, Error: %v", userUUID, err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c, "User not found")
			return
		}
		response.InternalServerError(c, "Failed to retrieve user")
		return
	}

	// Verify current password
	if err := utils.VerifyPassword(user.Password, req.CurrentPassword); err != nil {
		h.logger.Warning("Password change failed - invalid current password - UserID: %s", userUUID)
		response.BadRequest(c, "Current password is incorrect")
		return
	}

	// Validate new password strength
	// if err := h.ValidatePasswordStrength(req.NewPassword); err != nil {
	// 	h.logger.Warning("Password validation failed - UserID: %s, Error: %v", userUUID, err)
	// 	response.BadRequest(c, err.Error())
	// 	return
	// }

	// Hash new password
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		h.logger.Error("Failed to hash password - UserID: %s, Error: %v", userUUID, err)
		response.InternalServerError(c, "Failed to change password")
		return
	}

	// Update password
	if err := h.db.Model(&user).Update("password", hashedPassword).Error; err != nil {
		h.logger.Error("Failed to update password - UserID: %s, Error: %v", userUUID, err)
		response.InternalServerError(c, "Failed to change password")
		return
	}

	h.logger.Info("Password changed successfully - UserID: %s", userUUID)
	response.OK(c, "Password changed successfully", nil)
}

// validatePasswordStrength validates password strength requirements
func (h *AuthHandler) ValidatePasswordStrength(password string) error {
	if len(password) < 8 {
		return errors.New("password must be at least 8 characters long")
	}

	hasUpper := false
	hasLower := false
	hasDigit := false
	hasSpecial := false

	for _, char := range password {
		switch {
		case char >= 'A' && char <= 'Z':
			hasUpper = true
		case char >= 'a' && char <= 'z':
			hasLower = true
		case char >= '0' && char <= '9':
			hasDigit = true
		case strings.ContainsRune("!@#$%^&*()_+-=[]{}|;:,.<>?", char):
			hasSpecial = true
		}
	}

	if !hasUpper || !hasLower || !hasDigit || !hasSpecial {
		return errors.New("password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character")
	}

	return nil
}

// UploadAvatar handles avatar URL update requests
func (h *AuthHandler) UploadAvatar(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warning("UploadAvatar called without user_id in context")
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		h.logger.Error("Invalid user_id type in context: %T", userID)
		response.BadRequest(c, "Invalid user ID")
		return
	}

	// Parse JSON request body for avatar URL
	var req struct {
		AvatarURL string `json:"avatar_url" binding:"required,url"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("Invalid avatar URL request - UserID: %s, Error: %v", userUUID, err)
		response.BadRequestWithDetails(c, "Invalid request data", err.Error())
		return
	}

	// Get current user
	var user models.User
	if err := h.db.First(&user, "id = ?", userUUID).Error; err != nil {
		h.logger.Error("Failed to get user - UserID: %s, Error: %v", userUUID, err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c, "User not found")
			return
		}
		response.InternalServerError(c, "Failed to retrieve user")
		return
	}

	// Update user avatar URL
	if err := h.db.Model(&user).Update("avatar", req.AvatarURL).Error; err != nil {
		h.logger.Error("Failed to update user avatar - UserID: %s, Error: %v", userUUID, err)
		response.InternalServerError(c, "Failed to update avatar")
		return
	}

	h.logger.Info("Avatar updated successfully - UserID: %s, URL: %s", userUUID, req.AvatarURL)
	response.OK(c, "Avatar updated successfully", map[string]interface{}{
		"avatar_url": req.AvatarURL,
	})
}

// RequestEmailChange handles direct email change requests for authenticated users
func (h *AuthHandler) RequestEmailChange(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		h.logger.Warning("RequestEmailChange called without user_id in context")
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		h.logger.Error("Invalid user_id type in context: %T", userID)
		response.BadRequest(c, "Invalid user ID")
		return
	}

	var req struct {
		NewEmail string `json:"new_email" binding:"required,email"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("Invalid email change request - UserID: %s, Error: %v", userUUID, err)
		response.BadRequestWithDetails(c, "Invalid request data", err.Error())
		return
	}

	// Validate email format
	if !h.isValidEmail(req.NewEmail) {
		h.logger.Warning("Invalid email format - UserID: %s, Email: %s", userUUID, req.NewEmail)
		response.BadRequest(c, "Invalid email format")
		return
	}

	// Check if email is already in use
	var existingUser models.User
	if err := h.db.Where("email = ? AND id != ?", req.NewEmail, userUUID).First(&existingUser).Error; err == nil {
		h.logger.Warning("Email already in use - UserID: %s, Email: %s", userUUID, req.NewEmail)
		response.BadRequest(c, "Email address is already in use")
		return
	}

	// Get current user
	var user models.User
	if err := h.db.First(&user, "id = ?", userUUID).Error; err != nil {
		h.logger.Error("Failed to get user - UserID: %s, Error: %v", userUUID, err)
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c, "User not found")
			return
		}
		response.InternalServerError(c, "Failed to retrieve user")
		return
	}

	// Directly update the email address (no verification required)
	updates := map[string]interface{}{
		"email":          req.NewEmail,
		"email_verified": true,
	}

	if err := h.db.Model(&models.User{}).Where("id = ?", userUUID).Updates(updates).Error; err != nil {
		h.logger.Error("Failed to update email - UserID: %s, Error: %v", userUUID, err)
		response.InternalServerError(c, "Failed to update email")
		return
	}

	h.logger.Info("Email updated successfully - UserID: %s, NewEmail: %s", userUUID, req.NewEmail)
	response.OK(c, "Email updated successfully", gin.H{
		"new_email": req.NewEmail,
	})
}

// isValidEmail validates email format using regex
func (h *AuthHandler) isValidEmail(email string) bool {
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	re := regexp.MustCompile(emailRegex)
	return re.MatchString(email)
}

// UpdateCurrentBrandRequest represents a request to update current brand
type UpdateCurrentBrandRequest struct {
	BrandID *string `json:"brand_id"` // 可以为null来清除当前品牌
}

// UpdateCurrentBrand updates the current brand for the authenticated user
func (h *AuthHandler) UpdateCurrentBrand(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userID, ok := userIDInterface.(uuid.UUID)
	if !ok {
		response.InternalServerError(c, "Invalid user ID format")
		return
	}

	var req UpdateCurrentBrandRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetails(c, "Invalid request format", err.Error())
		return
	}

	// Validate brand ID format if provided
	if req.BrandID != nil && *req.BrandID != "" {
		if _, err := uuid.Parse(*req.BrandID); err != nil {
			response.BadRequest(c, "Invalid brand ID format")
			return
		}
	}

	// Update current brand
	if err := h.updateCurrentBrand(userID, req.BrandID); err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to update current brand", err.Error())
		return
	}

	response.OK(c, "Current brand updated successfully", map[string]interface{}{
		"brand_id": req.BrandID,
	})
}

// GetCurrentBrand gets the current brand for the authenticated user
func (h *AuthHandler) GetCurrentBrand(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userID, ok := userIDInterface.(uuid.UUID)
	if !ok {
		response.InternalServerError(c, "Invalid user ID format")
		return
	}

	// Get current brand
	brandID, err := h.getCurrentBrand(userID)
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to get current brand", err.Error())
		return
	}

	response.OK(c, "Current brand get successfully", map[string]interface{}{
		"brand_id": brandID,
	})
}

// updateCurrentBrand updates the current brand for a user
func (h *AuthHandler) updateCurrentBrand(userID uuid.UUID, brandID *string) error {
	// Validate that the user exists
	var user models.User
	if err := h.db.First(&user, "id = ?", userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to find user: %w", err)
	}

	// Update the current brand ID
	result := h.db.Model(&user).Update("current_brand_id", brandID)
	if result.Error != nil {
		return fmt.Errorf("failed to update current brand: %w", result.Error)
	}

	return nil
}

// getCurrentBrand retrieves the current brand ID for a user
func (h *AuthHandler) getCurrentBrand(userID uuid.UUID) (*string, error) {
	var user models.User
	if err := h.db.Select("current_brand_id").First(&user, "id = ?", userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user.CurrentBrandID, nil
}
