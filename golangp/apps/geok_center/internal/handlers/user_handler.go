/*
 * @Description: User management handler for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
package handlers

import (
	"strconv"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/apps/geok_center/pkg/response"
	"pointer/golangp/common/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserHandler handles user management operations
type UserHandler struct {
	userService *services.UserService
}

// NewUserHandler creates a new user handler
func NewUserHandler(userService *services.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// CreateUserRequest represents a request to create a new user
type CreateUserRequest struct {
	Username  string `json:"username" binding:"required,min=3,max=50"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=6"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Role      string `json:"role" binding:"required,oneof=user enterprise"`
}

// CreateUser creates a new user (admin only, can create enterprise users)
func (h *UserHandler) CreateUser(c *gin.Context) {
	// 权限检查已通过路由中间件处理，这里不需要重复检查
	// Check if user is admin
	// role, exists := c.Get("role")
	// if !exists || role.(string) != "admin" {
	// 	response.Forbidden(c, "Admin access required")
	// 	return
	// }

	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetails(c, "Invalid request data", err.Error())
		return
	}

	// Check if user already exists
	existingUser, err := h.userService.GetUserByEmail(req.Email)
	if err == nil && existingUser != nil {
		response.Conflict(c, "User with this email already exists")
		return
	}

	// Check if username already exists
	existingUser, err = h.userService.GetUserByUsername(req.Username)
	if err == nil && existingUser != nil {
		response.Conflict(c, "User with this username already exists")
		return
	}

	// Hash password
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to hash password", err.Error())
		return
	}

	// Create user
	user := &models.User{
		ID:        uuid.New(),
		Username:  req.Username,
		Email:     req.Email,
		Password:  hashedPassword,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Role:      models.UserRole(req.Role),
		Status:    models.UserStatusActive,
		Origin:    "geok",
	}

	if err := h.userService.CreateUser(user); err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to create user", err.Error())
		return
	}

	response.OK(c, "User created successfully", map[string]interface{}{
		"user": user,
	})
}

// ListUsers lists all users with pagination (admin only)
func (h *UserHandler) ListUsers(c *gin.Context) {
	// 权限检查已通过路由中间件处理，这里不需要重复检查
	// Check if user is admin
	role, exists := c.Get("role")
	if !exists || role.(string) != "admin" {
		response.Forbidden(c, "Admin access required")
		return
	}

	// Parse query parameters
	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("page_size", "10")
	search := c.Query("search")
	roleFilter := c.Query("role")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	users, total, err := h.userService.ListUsers(page, pageSize, search, roleFilter)
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to list users", err.Error())
		return
	}

	response.OK(c, "Users retrieved successfully", map[string]interface{}{
		"users":     users,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// GetUser gets a user by ID (admin only)
func (h *UserHandler) GetUser(c *gin.Context) {
	// Check if user is admin
	role, exists := c.Get("role")
	if !exists || role.(string) != "admin" {
		response.Forbidden(c, "Admin access required")
		return
	}

	userIDStr := c.Param("id")
	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequestWithDetails(c, "Invalid user ID", err.Error())
		return
	}

	user, err := h.userService.GetUserByID(userUUID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "User not found")
			return
		}
		response.InternalServerErrorWithDetails(c, "Failed to get user", err.Error())
		return
	}

	response.OK(c, "User retrieved successfully", map[string]interface{}{
		"user": user,
	})
}

// UpdateUserRequest represents a request to update user information
type UpdateUserRequest struct {
	FirstName *string `json:"first_name"`
	LastName  *string `json:"last_name"`
	Role      *string `json:"role" binding:"omitempty,oneof=user enterprise admin"`
	Status    *string `json:"status" binding:"omitempty,oneof=active inactive suspended"`
}

// UpdateUser updates user information (admin only)
func (h *UserHandler) UpdateUser(c *gin.Context) {
	// Check if user is admin
	role, exists := c.Get("role")
	if !exists || role.(string) != "admin" {
		response.Forbidden(c, "Admin access required")
		return
	}

	userIDStr := c.Param("id")
	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequestWithDetails(c, "Invalid user ID", err.Error())
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetails(c, "Invalid request data", err.Error())
		return
	}

	// Get existing user
	user, err := h.userService.GetUserByID(userUUID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "User not found")
			return
		}
		response.InternalServerErrorWithDetails(c, "Failed to get user", err.Error())
		return
	}

	// Update fields
	updateData := make(map[string]interface{})
	if req.FirstName != nil {
		updateData["first_name"] = *req.FirstName
	}
	if req.LastName != nil {
		updateData["last_name"] = *req.LastName
	}
	if req.Role != nil {
		updateData["role"] = *req.Role
	}
	if req.Status != nil {
		updateData["status"] = *req.Status
	}

	if len(updateData) == 0 {
		response.OK(c, "No changes to update", map[string]interface{}{
			"user": user,
		})
		return
	}

	if err := h.userService.UpdateUser(userUUID, updateData); err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to update user", err.Error())
		return
	}

	// Get updated user
	updatedUser, err := h.userService.GetUserByID(userUUID)
	if err != nil {
		response.InternalServerErrorWithDetails(c, "Failed to get updated user", err.Error())
		return
	}

	response.OK(c, "User updated successfully", map[string]interface{}{
		"user": updatedUser,
	})
}

// DeleteUser deletes a user (admin only)
func (h *UserHandler) DeleteUser(c *gin.Context) {
	// Check if user is admin
	role, exists := c.Get("role")
	if !exists || role.(string) != "admin" {
		response.Forbidden(c, "Admin access required")
		return
	}

	userIDStr := c.Param("id")
	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		response.BadRequestWithDetails(c, "Invalid user ID", err.Error())
		return
	}

	// Prevent admin from deleting themselves
	currentUserID, _ := c.Get("user_id")
	if currentUserID.(uuid.UUID) == userUUID {
		response.BadRequest(c, "Cannot delete your own account")
		return
	}

	if err := h.userService.DeleteUser(userUUID); err != nil {
		if err == gorm.ErrRecordNotFound {
			response.NotFound(c, "User not found")
			return
		}
		response.InternalServerErrorWithDetails(c, "Failed to delete user", err.Error())
		return
	}

	response.OK(c, "User deleted successfully", nil)
}

