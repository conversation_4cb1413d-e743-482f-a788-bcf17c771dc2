/*
 * @Description: AI搜索处理器 - 处理AI搜索相关的HTTP请求
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package handlers

import (
	"net/http"
	"strconv"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/apps/geok_center/pkg/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AISearchHandler handles AI search related HTTP requests
type AISearchHandler struct {
	aiSearchService         *services.AISearchService
	aiSearchResponseService *services.AISearchResponseService
}

// NewAISearchHandler creates a new AI search handler
func NewAISearchHandler(aiSearchService *services.AISearchService, aiSearchResponseService *services.AISearchResponseService) *AISearchHandler {
	return &AISearchHandler{
		aiSearchService:         aiSearchService,
		aiSearchResponseService: aiSearchResponseService,
	}
}

// CreateAISearch creates a new AI search
func (h *AISearchHandler) CreateAISearch(c *gin.Context) {
	var req struct {
		BrandID      uuid.UUID `json:"brand_id" binding:"required"`
		Question     string    `json:"question" binding:"required"`
		QuestionType string    `json:"question_type"`
		Keywords     string    `json:"keywords"`
		Priority     int       `json:"priority"`
		Region       string    `json:"region"`
		Language     string    `json:"language"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", err.Error())
		return
	}

	aiSearch := &models.AISearch{
		ID:           uuid.New(),
		BrandID:      req.BrandID,
		Question:     req.Question,
		QuestionType: req.QuestionType,
		Keywords:     req.Keywords,
		Status:       models.AISearchStatusPending,
		Priority:     req.Priority,
		Region:       req.Region,
		Language:     req.Language,
	}

	if req.Priority == 0 {
		aiSearch.Priority = 1
	}
	if req.Region == "" {
		aiSearch.Region = "global"
	}
	if req.Language == "" {
		aiSearch.Language = "zh"
	}

	createdSearch, err := h.aiSearchService.CreateAISearch(aiSearch)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to create AI search", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "AI search created successfully", createdSearch)
}

// GetAISearch retrieves an AI search by ID
func (h *AISearchHandler) GetAISearch(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid AI search ID", err.Error())
		return
	}

	aiSearch, err := h.aiSearchService.GetAISearchByID(id)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusNotFound, "AI search not found", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "AI search retrieved successfully", aiSearch)
}

// GetAISearchesByBrand retrieves AI searches for a specific brand
func (h *AISearchHandler) GetAISearchesByBrand(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	limit := pageSize
	offset := (page - 1) * pageSize

	searches, total, err := h.aiSearchService.GetAISearchesByBrandID(brandID, limit, offset)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve AI searches", err.Error())
		return
	}

	result := map[string]interface{}{
		"searches": searches,
		"pagination": map[string]interface{}{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}

	response.Success(c, http.StatusOK, "AI searches retrieved successfully", result)
}

// GetAISearchesByStatus retrieves AI searches by status
func (h *AISearchHandler) GetAISearchesByStatus(c *gin.Context) {
	statusStr := c.Param("status")
	status := models.AISearchStatus(statusStr)

	// Validate status
	validStatuses := []models.AISearchStatus{
		models.AISearchStatusPending,
		models.AISearchStatusProcessing,
		models.AISearchStatusCompleted,
		models.AISearchStatusFailed,
	}

	isValid := false
	for _, validStatus := range validStatuses {
		if status == validStatus {
			isValid = true
			break
		}
	}

	if !isValid {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid status", "Status must be one of: pending, processing, completed, failed")
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	limit := pageSize
	offset := (page - 1) * pageSize

	searches, total, err := h.aiSearchService.GetAISearchesByStatus(status, limit, offset)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve AI searches", err.Error())
		return
	}

	result := map[string]interface{}{
		"searches": searches,
		"pagination": map[string]interface{}{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}

	response.Success(c, http.StatusOK, "AI searches retrieved successfully", result)
}

// UpdateAISearchStatus updates the status of an AI search
func (h *AISearchHandler) UpdateAISearchStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid AI search ID", err.Error())
		return
	}

	var req struct {
		Status models.AISearchStatus `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", err.Error())
		return
	}

	if err := h.aiSearchService.UpdateAISearchStatus(id, req.Status); err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to update AI search status", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "AI search status updated successfully", nil)
}

// DeleteAISearch deletes an AI search
func (h *AISearchHandler) DeleteAISearch(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid AI search ID", err.Error())
		return
	}

	if err := h.aiSearchService.DeleteAISearch(id); err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to delete AI search", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "AI search deleted successfully", nil)
}

// CreateBrandAISearches creates AI searches for a brand
func (h *AISearchHandler) CreateBrandAISearches(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	searches, err := h.aiSearchService.CreateBrandAISearches(brandID, nil)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to create brand AI searches", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "Brand AI searches created successfully", map[string]interface{}{
		"searches": searches,
		"count":    len(searches),
	})
}

// ProcessPendingSearches processes pending AI searches
func (h *AISearchHandler) ProcessPendingSearches(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if limit < 1 || limit > 100 {
		limit = 10
	}

	if err := h.aiSearchService.ProcessPendingSearches(limit); err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to process pending searches", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "Pending searches processed successfully", nil)
}

// GetAISearchStats returns AI search statistics
func (h *AISearchHandler) GetAISearchStats(c *gin.Context) {
	var brandID *uuid.UUID
	if brandIDStr := c.Query("brand_id"); brandIDStr != "" {
		if id, err := uuid.Parse(brandIDStr); err == nil {
			brandID = &id
		}
	}

	stats, err := h.aiSearchService.GetAISearchStats(brandID)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to get AI search stats", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "AI search stats retrieved successfully", stats)
}
