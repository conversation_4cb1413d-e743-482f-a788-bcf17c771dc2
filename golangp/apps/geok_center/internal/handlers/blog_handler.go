/*
 * @Description: Blog handlers for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/datatypes"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/apps/geok_center/pkg/response"
	"pointer/golangp/common/logging"
)

// BlogHandler handles blog-related requests
type BlogHandler struct {
	blogService *services.BlogService
}

// NewBlogHandler creates a new blog handler
func NewBlogHandler(blogService *services.BlogService) *BlogHandler {
	return &BlogHandler{
		blogService: blogService,
	}
}

// CreateBlogRequest represents the request for creating a blog
type CreateBlogRequest struct {
	BrandID         string         `json:"brand_id" binding:"required"`
	Title           string         `json:"title" binding:"required"`
	Slug            string         `json:"slug"`
	Excerpt         string         `json:"excerpt"`
	Content         string         `json:"content" binding:"required"`
	Author          datatypes.JSON `json:"author"`
	Category        string         `json:"category" binding:"required"`
	Tags            datatypes.JSON `json:"tags"`
	Status          string         `json:"status"`
	FeaturedImage   string         `json:"featured_image"`
	MetaTitle       string         `json:"meta_title"`
	MetaDescription string         `json:"meta_description"`
	MetaKeywords    datatypes.JSON `json:"meta_keywords"`
}

// UpdateBlogRequest represents the request for updating a blog
type UpdateBlogRequest struct {
	BrandID         *string         `json:"brand_id"`
	Title           *string         `json:"title"`
	Slug            *string         `json:"slug"`
	Excerpt         *string         `json:"excerpt"`
	Content         *string         `json:"content"`
	Author          *datatypes.JSON `json:"author"`
	Category        *string         `json:"category"`
	Tags            *datatypes.JSON `json:"tags"`
	Status          *string         `json:"status"`
	FeaturedImage   *string         `json:"featured_image"`
	MetaTitle       *string         `json:"meta_title"`
	MetaDescription *string         `json:"meta_description"`
	MetaKeywords    *datatypes.JSON `json:"meta_keywords"`
}

// CreateBlog handles the creation of a new blog
// @Summary Create a new blog
// @Description Create a new blog with markdown content
// @Tags blogs
// @Accept json
// @Produce json
// @Param blog body CreateBlogRequest true "Blog information"
// @Success 201 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/blogs [post]
func (h *BlogHandler) CreateBlog(c *gin.Context) {
	var req CreateBlogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logging.Error("Invalid request: %v", err)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// Parse BrandID
	brandID, err := uuid.Parse(req.BrandID)
	if err != nil {
		logging.Error("Invalid brand ID: %v", err)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// Convert status string to BlogStatus
	var status models.BlogStatus
	if req.Status != "" {
		status = models.BlogStatus(req.Status)
	} else {
		status = models.BlogStatusDraft
	}

	blog := &models.Blog{
		BrandID:         brandID,
		Title:           req.Title,
		Slug:            req.Slug,
		Excerpt:         req.Excerpt,
		Content:         req.Content,
		Author:          req.Author,
		Category:        req.Category,
		Tags:            req.Tags,
		Status:          status,
		FeaturedImage:   req.FeaturedImage,
		MetaTitle:       req.MetaTitle,
		MetaDescription: req.MetaDescription,
		MetaKeywords:    req.MetaKeywords,
	}

	if err := h.blogService.CreateBlog(blog); err != nil {
		logging.Error("Failed to create blog: %v", err)
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to create blog", err.Error())
		return
	}

	response.Success(c, http.StatusCreated, "Blog created successfully", gin.H{
		"blog": blog,
	})
}

// GetBlog handles the retrieval of a blog by ID or slug
// @Summary Get a blog
// @Description Get a blog by ID or slug
// @Tags blogs
// @Produce json
// @Param id path string true "Blog ID or slug"
// @Success 200 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/blogs/{id} [get]
func (h *BlogHandler) GetBlog(c *gin.Context) {
	id := c.Param("id")

	var blog *models.Blog
	var err error

	// Try to parse as UUID first
	if uuid, err := uuid.Parse(id); err == nil {
		blog, err = h.blogService.GetBlogByID(uuid)
	} else {
		// If not a valid UUID, try as slug
		blog, err = h.blogService.GetBlogBySlug(id)
	}

	if err != nil {
		if err.Error() == "blog not found" {
			response.Error(c, http.StatusNotFound, "Blog not found")
		} else {
			logging.Error("Failed to get blog: %v", err)
			response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to get blog", err.Error())
		}
		return
	}

	// Increment view count
	if blog != nil {
		go h.blogService.IncrementViewCount(blog.ID)
	}

	response.Success(c, http.StatusOK, "Blog retrieved successfully", gin.H{
		"blog": blog,
	})
}

// UpdateBlog handles the update of a blog
// @Summary Update a blog
// @Description Update a blog by ID
// @Tags blogs
// @Accept json
// @Produce json
// @Param id path string true "Blog ID"
// @Param blog body UpdateBlogRequest true "Blog information"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/blogs/{id} [put]
func (h *BlogHandler) UpdateBlog(c *gin.Context) {
	id := c.Param("id")
	blogID, err := uuid.Parse(id)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog ID", err.Error())
		return
	}

	var req UpdateBlogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logging.Error("Invalid request: %v", err)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request", err.Error())
		return
	}

	// Build updates map
	updates := make(map[string]interface{})
	if req.BrandID != nil {
		brandID, err := uuid.Parse(*req.BrandID)
		if err != nil {
			logging.Error("Invalid brand ID: %v", err)
			response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
			return
		}
		updates["brand_id"] = brandID
	}
	if req.Title != nil {
		updates["title"] = *req.Title
	}
	if req.Slug != nil {
		updates["slug"] = *req.Slug
	}
	if req.Excerpt != nil {
		updates["excerpt"] = *req.Excerpt
	}
	if req.Content != nil {
		updates["content"] = *req.Content
	}
	if req.Author != nil {
		updates["author"] = *req.Author
	}
	if req.Category != nil {
		updates["category"] = *req.Category
	}
	if req.Tags != nil {
		updates["tags"] = *req.Tags
	}
	if req.Status != nil {
		updates["status"] = models.BlogStatus(*req.Status)
	}
	if req.FeaturedImage != nil {
		updates["featured_image"] = *req.FeaturedImage
	}
	if req.MetaTitle != nil {
		updates["meta_title"] = *req.MetaTitle
	}
	if req.MetaDescription != nil {
		updates["meta_description"] = *req.MetaDescription
	}
	if req.MetaKeywords != nil {
		updates["meta_keywords"] = *req.MetaKeywords
	}

	blog, err := h.blogService.UpdateBlog(blogID, updates)
	if err != nil {
		if err.Error() == "blog not found" {
			response.Error(c, http.StatusNotFound, "Blog not found")
		} else {
			logging.Error("Failed to update blog: %v", err)
			response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to update blog", err.Error())
		}
		return
	}

	response.Success(c, http.StatusOK, "Blog updated successfully", gin.H{
		"blog": blog,
	})
}

// DeleteBlog handles the deletion of a blog
// @Summary Delete a blog
// @Description Delete a blog by ID
// @Tags blogs
// @Produce json
// @Param id path string true "Blog ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 404 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/blogs/{id} [delete]
func (h *BlogHandler) DeleteBlog(c *gin.Context) {
	id := c.Param("id")
	blogID, err := uuid.Parse(id)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid blog ID", err.Error())
		return
	}

	if err := h.blogService.DeleteBlog(blogID); err != nil {
		if err.Error() == "blog not found" {
			response.Error(c, http.StatusNotFound, "Blog not found")
		} else {
			logging.Error("Failed to delete blog: %v", err)
			response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to delete blog", err.Error())
		}
		return
	}

	response.Success(c, http.StatusOK, "Blog deleted successfully", nil)
}

// ListBlogs handles the listing of blogs with pagination and filtering
// @Summary List blogs
// @Description List blogs with pagination and filtering
// @Tags blogs
// @Produce json
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 10)"
// @Param status query string false "Filter by status"
// @Param category query string false "Filter by category"
// @Param search query string false "Search in title, excerpt, and content"
// @Success 200 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/blogs [get]
func (h *BlogHandler) ListBlogs(c *gin.Context) {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Parse filters
	filters := make(map[string]interface{})
	if brandIDStr := c.Query("brand_id"); brandIDStr != "" {
		brandID, err := uuid.Parse(brandIDStr)
		if err != nil {
			logging.Error("Invalid brand ID: %v", err)
			response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
			return
		}
		filters["brand_id"] = brandID
	}
	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if category := c.Query("category"); category != "" {
		filters["category"] = category
	}
	if search := c.Query("search"); search != "" {
		filters["search"] = search
	}

	blogs, total, err := h.blogService.ListBlogs(page, pageSize, filters)
	if err != nil {
		logging.Error("Failed to list blogs: %v", err)
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to list blogs", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "Blogs retrieved successfully", gin.H{
		"blogs": blogs,
		"pagination": gin.H{
			"current_page": page,
			"page_size":    pageSize,
			"total_items":  total,
			"total_pages":  (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// ListBlogsByBrand handles the listing of blogs for a specific brand
// @Summary List blogs by brand
// @Description List blogs for a specific brand with pagination and filtering
// @Tags blogs
// @Produce json
// @Param brand_id path string true "Brand ID"
// @Param page query int false "Page number (default: 1)"
// @Param page_size query int false "Page size (default: 10)"
// @Param status query string false "Filter by status"
// @Param category query string false "Filter by category"
// @Param search query string false "Search in title, excerpt, and content"
// @Success 200 {object} response.Response
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /api/v1/brands/{brand_id}/blogs [get]
func (h *BlogHandler) ListBlogsByBrand(c *gin.Context) {
	// Parse brand ID
	brandIDStr := c.Param("brand_id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		logging.Error("Invalid brand ID: %v", err)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Parse filters
	filters := make(map[string]interface{})
	filters["brand_id"] = brandID // Always filter by brand_id

	if status := c.Query("status"); status != "" {
		filters["status"] = status
	}
	if category := c.Query("category"); category != "" {
		filters["category"] = category
	}
	if search := c.Query("search"); search != "" {
		filters["search"] = search
	}

	blogs, total, err := h.blogService.ListBlogs(page, pageSize, filters)
	if err != nil {
		logging.Error("Failed to list blogs for brand %s: %v", brandID, err)
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to list blogs", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "Blogs retrieved successfully", gin.H{
		"blogs":    blogs,
		"brand_id": brandID,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}
