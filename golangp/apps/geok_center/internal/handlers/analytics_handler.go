/*
 * @Description: Analytics handler for GEOK Center API
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package handlers

import (
	"strconv"
	"time"

	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/apps/geok_center/pkg/response"
	"pointer/golangp/apps/geok_center/pkg/types"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
)

// AnalyticsHandler handles analytics-related HTTP requests
type AnalyticsHandler struct {
	analyticsService *services.AnalyticsService
	logger           *logging.Logger
}

// NewAnalyticsHandler creates a new analytics handler
func NewAnalyticsHandler(analyticsService *services.AnalyticsService) *AnalyticsHandler {
	return &AnalyticsHandler{
		analyticsService: analyticsService,
		logger:           logging.GetLogger("analytics_handler"),
	}
}

// GetDashboardData handles GET /analytics/dashboard
func (h *AnalyticsHandler) GetDashboardData(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()
	userAgent := c.Get<PERSON>eader("User-Agent")

	h.logger.Info("📊 GetDashboardData started - Method: %s, Path: %s, ClientIP: %s, UserAgent: %s",
		c.Request.Method, c.Request.URL.Path, clientIP, userAgent)

	// Parse filter parameters
	var filters types.FilterParams
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.Error("❌ Invalid filter parameters - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid filter parameters")
		h.logger.Info("❌ GetDashboardData failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	h.logger.Debug("🔍 Filter parameters parsed - StartDate: %v, EndDate: %v, ClientIP: %s",
		filters.StartDate, filters.EndDate, clientIP)

	// Set default date range if not provided (last 30 days)
	if filters.StartDate == nil || filters.EndDate == nil {
		now := time.Now()
		endDate := now
		startDate := now.AddDate(0, 0, -30)
		filters.StartDate = &startDate
		filters.EndDate = &endDate
		h.logger.Info("📅 Using default date range - StartDate: %v, EndDate: %v, ClientIP: %s",
			startDate, endDate, clientIP)
	}

	h.logger.Debug("💾 Fetching dashboard data from service - ClientIP: %s", clientIP)
	dashboardData, err := h.analyticsService.GetDashboardData(filters)
	if err != nil {
		h.logger.Error("❌ Failed to fetch dashboard data - Error: %v, ClientIP: %s", err, clientIP)
		response.InternalServerError(c, "Failed to fetch dashboard data")
		h.logger.Info("❌ GetDashboardData failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	h.logger.Info("✅ Dashboard data fetched successfully - ClientIP: %s", clientIP)
	response.OK(c, "", dashboardData)
	h.logger.Info("✅ GetDashboardData completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
}

// GetBrandDistribution handles GET /analytics/brand-distribution
func (h *AnalyticsHandler) GetBrandDistribution(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	h.logger.Info("📊 GetBrandDistribution started - Method: %s, Path: %s, ClientIP: %s",
		c.Request.Method, c.Request.URL.Path, clientIP)

	// Parse filter parameters
	var filters types.FilterParams
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.Error("❌ Invalid filter parameters - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid filter parameters")
		h.logger.Info("❌ GetBrandDistribution failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	h.logger.Debug("💾 Fetching brand distribution data - ClientIP: %s", clientIP)
	// Get dashboard data and extract brand distribution
	dashboardData, err := h.analyticsService.GetDashboardData(filters)
	if err != nil {
		h.logger.Error("❌ Failed to fetch brand distribution data - Error: %v, ClientIP: %s", err, clientIP)
		response.InternalServerError(c, "Failed to fetch brand distribution data")
		h.logger.Info("❌ GetBrandDistribution failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	h.logger.Info("✅ Brand distribution data fetched successfully - ClientIP: %s", clientIP)
	response.OK(c, "", dashboardData.CompetitorAnalysis.BrandDistribution)
	h.logger.Info("✅ GetBrandDistribution completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
}

// GetSearchTrends handles GET /analytics/search-trends
func (h *AnalyticsHandler) GetSearchTrends(c *gin.Context) {
	startTime := time.Now()
	clientIP := c.ClientIP()

	h.logger.Info("📈 GetSearchTrends started - Method: %s, Path: %s, ClientIP: %s",
		c.Request.Method, c.Request.URL.Path, clientIP)

	// Parse filter parameters
	var filters types.FilterParams
	if err := c.ShouldBindQuery(&filters); err != nil {
		h.logger.Error("❌ Invalid filter parameters - Error: %v, ClientIP: %s", err, clientIP)
		response.BadRequest(c, "Invalid filter parameters")
		h.logger.Info("❌ GetSearchTrends failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	// Set default date range if not provided (last 7 days)
	if filters.StartDate == nil || filters.EndDate == nil {
		now := time.Now()
		endDate := now
		startDate := now.AddDate(0, 0, -7)
		filters.StartDate = &startDate
		filters.EndDate = &endDate
	}

	// Get dashboard data and extract search trends
	h.logger.Debug("💾 Fetching search trends data - ClientIP: %s", clientIP)
	dashboardData, err := h.analyticsService.GetDashboardData(filters)
	if err != nil {
		h.logger.Error("❌ Failed to fetch search trends data - Error: %v, ClientIP: %s", err, clientIP)
		response.InternalServerError(c, "Failed to fetch search trends data")
		h.logger.Info("❌ GetSearchTrends failed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
		return
	}

	responseData := map[string]interface{}{
		"trend_data":     dashboardData.SearchMetrics.TrendData,
		"regional_data":  dashboardData.SearchMetrics.RegionalData,
		"top_keywords":   dashboardData.SearchMetrics.TopKeywords,
		"total_searches": dashboardData.SearchMetrics.TotalSearches,
		"search_rate":    dashboardData.SearchMetrics.SearchRate,
	}

	h.logger.Info("✅ Search trends data fetched successfully - ClientIP: %s", clientIP)
	response.OK(c, "", responseData)
	h.logger.Info("✅ GetSearchTrends completed - Duration: %v, ClientIP: %s", time.Since(startTime), clientIP)
}

// GetAIAppearanceMetrics handles GET /analytics/ai-appearance
func (h *AnalyticsHandler) GetAIAppearanceMetrics(c *gin.Context) {
	// Parse filter parameters
	var filters types.FilterParams
	if err := c.ShouldBindQuery(&filters); err != nil {
		response.BadRequest(c, "Invalid filter parameters")
		return
	}

	// Set default date range if not provided (last 30 days)
	if filters.StartDate == nil || filters.EndDate == nil {
		now := time.Now()
		endDate := now
		startDate := now.AddDate(0, 0, -30)
		filters.StartDate = &startDate
		filters.EndDate = &endDate
	}

	// Get dashboard data and extract AI appearance metrics
	dashboardData, err := h.analyticsService.GetDashboardData(filters)
	if err != nil {
		response.InternalServerError(c, "Failed to fetch AI appearance metrics")
		return
	}

	response.OK(c, "", dashboardData.AIAppearance)
}

// GetCompetitorAnalysis handles GET /analytics/competitor-analysis
func (h *AnalyticsHandler) GetCompetitorAnalysis(c *gin.Context) {
	// Parse filter parameters
	var filters types.FilterParams
	if err := c.ShouldBindQuery(&filters); err != nil {
		response.BadRequest(c, "Invalid filter parameters")
		return
	}

	// Get dashboard data and extract competitor analysis
	dashboardData, err := h.analyticsService.GetDashboardData(filters)
	if err != nil {
		response.InternalServerError(c, "Failed to fetch competitor analysis data")
		return
	}

	response.OK(c, "", dashboardData.CompetitorAnalysis)
}

// GetMetricsSummary handles GET /analytics/summary
func (h *AnalyticsHandler) GetMetricsSummary(c *gin.Context) {
	// Parse filter parameters
	var filters types.FilterParams
	if err := c.ShouldBindQuery(&filters); err != nil {
		response.BadRequest(c, "Invalid filter parameters")
		return
	}

	// Get dashboard data
	dashboardData, err := h.analyticsService.GetDashboardData(filters)
	if err != nil {
		response.InternalServerError(c, "Failed to fetch metrics summary")
		return
	}

	// Create summary response
	summary := map[string]interface{}{
		"brand_summary": dashboardData.BrandSummary,
		"search_summary": map[string]interface{}{
			"total_searches": dashboardData.SearchMetrics.TotalSearches,
			"search_rate":    dashboardData.SearchMetrics.SearchRate,
		},
		"ai_summary": map[string]interface{}{
			"total_appearances": dashboardData.AIAppearance.TotalAppearances,
			"appearance_rate":   dashboardData.AIAppearance.AppearanceRate,
		},
		"competitor_summary": map[string]interface{}{
			"market_position": dashboardData.CompetitorAnalysis.MarketPosition,
			"market_share":    dashboardData.CompetitorAnalysis.MarketShare,
		},
		"last_updated": dashboardData.LastUpdated,
	}

	response.OK(c, "", summary)
}

// GetRecentActivity handles GET /analytics/activity
func (h *AnalyticsHandler) GetRecentActivity(c *gin.Context) {
	// Parse limit parameter
	limit := 10
	if limitParam := c.Query("limit"); limitParam != "" {
		if l, err := strconv.Atoi(limitParam); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Get dashboard data and extract recent activity
	filters := types.FilterParams{} // No filters for recent activity
	dashboardData, err := h.analyticsService.GetDashboardData(filters)
	if err != nil {
		response.InternalServerError(c, "Failed to fetch recent activity")
		return
	}

	// Limit the results
	activities := dashboardData.RecentActivity
	if len(activities) > limit {
		activities = activities[:limit]
	}

	response.OK(c, "", activities)
}
