/*
 * @Description: AI可见性指标处理器 - 处理AI可见性指标相关的HTTP请求
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package handlers

import (
	"net/http"
	"strconv"
	"time"

	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/apps/geok_center/pkg/response"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AIVisibilityHandler handles AI visibility metrics related HTTP requests
type AIVisibilityHandler struct {
	visibilityService  *services.AIVisibilityService
	aggregationService *services.AIVisibilityAggregationService
	logger             *logging.Logger
}

// NewAIVisibilityHandler creates a new AI visibility handler
func NewAIVisibilityHandler(visibilityService *services.AIVisibilityService, aggregationService *services.AIVisibilityAggregationService) *AIVisibilityHandler {
	return &AIVisibilityHandler{
		visibilityService:  visibilityService,
		aggregationService: aggregationService,
		logger:             logging.GetLogger("ai_visibility_handler"),
	}
}

// CalculateVisibilityMetrics calculates visibility metrics for an AI search response
func (h *AIVisibilityHandler) CalculateVisibilityMetrics(c *gin.Context) {
	responseIDStr := c.Param("responseId")
	responseID, err := uuid.Parse(responseIDStr)
	if err != nil {
		h.logger.Warning("Invalid response ID format: %s", responseIDStr)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid response ID", err.Error())
		return
	}

	h.logger.Info("Calculating visibility metrics for response: %s", responseID)

	if err := h.visibilityService.CalculateVisibilityMetrics(responseID); err != nil {
		h.logger.Error("Failed to calculate visibility metrics for response %s: %v", responseID, err)
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to calculate visibility metrics", err.Error())
		return
	}

	h.logger.Info("Successfully calculated visibility metrics for response: %s", responseID)
	response.Success(c, http.StatusOK, "Visibility metrics calculated successfully", nil)
}

// GetVisibilityMetrics gets visibility metrics for a specific response
func (h *AIVisibilityHandler) GetVisibilityMetrics(c *gin.Context) {
	responseIDStr := c.Param("responseId")
	responseID, err := uuid.Parse(responseIDStr)
	if err != nil {
		h.logger.Warning("Invalid response ID format: %s", responseIDStr)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid response ID", err.Error())
		return
	}

	h.logger.Info("Getting visibility metrics for response: %s", responseID)

	metrics, err := h.visibilityService.GetVisibilityMetricsByResponse(responseID)
	if err != nil {
		h.logger.Error("Failed to get visibility metrics for response %s: %v", responseID, err)
		response.ErrorWithDetails(c, http.StatusNotFound, "Visibility metrics not found", err.Error())
		return
	}

	h.logger.Info("Successfully retrieved visibility metrics for response: %s", responseID)
	response.Success(c, http.StatusOK, "Visibility metrics retrieved successfully", metrics)
}

// GetBrandVisibilityTrend gets visibility trend for a brand
func (h *AIVisibilityHandler) GetBrandVisibilityTrend(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		h.logger.Warning("Invalid brand ID format: %s", brandIDStr)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		h.logger.Warning("Invalid days parameter: %s", daysStr)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid days parameter", "Days must be between 1 and 365")
		return
	}

	h.logger.Info("Getting visibility trend for brand %s over %d days", brandID, days)

	trend, err := h.visibilityService.GetBrandVisibilityTrend(brandID, days)
	if err != nil {
		h.logger.Error("Failed to get visibility trend for brand %s: %v", brandID, err)
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to get visibility trend", err.Error())
		return
	}

	// 构建包含完整关联信息的响应数据
	trendData := make([]map[string]interface{}, len(trend))
	for i, metric := range trend {
		trendData[i] = map[string]interface{}{
			"id":                   metric.ID,
			"ai_search_id":         metric.AISearchID,
			"ai_response_id":       metric.AIResponseID,
			"brand_id":             metric.BrandID,
			"visibility_data":      metric.VisibilityData,
			"keyword_data":         metric.KeywordData,
			"overall_score":        metric.OverallScore,
			"frequency_score":      metric.FrequencyScore,
			"recommendation_score": metric.RecommendationScore,
			"search_rate_score":    metric.SearchRateScore,
			"first_choice_score":   metric.FirstChoiceScore,
			"calculated_at":        metric.CalculatedAt,
			"created_at":           metric.CreatedAt,
			"updated_at":           metric.UpdatedAt,
		}

		// 如果预加载了关联数据，也包含进来
		if metric.AISearch.ID != uuid.Nil {
			trendData[i]["ai_search"] = map[string]interface{}{
				"id":            metric.AISearch.ID,
				"question":      metric.AISearch.Question,
				"question_type": metric.AISearch.QuestionType,
				"keywords":      metric.AISearch.Keywords,
				"status":        metric.AISearch.Status,
				"region":        metric.AISearch.Region,
				"language":      metric.AISearch.Language,
				"created_at":    metric.AISearch.CreatedAt,
			}
		}

		if metric.AIResponse.ID != uuid.Nil {
			trendData[i]["ai_response"] = map[string]interface{}{
				"id":              metric.AIResponse.ID,
				"response":        metric.AIResponse.Response,
				"brand_position":  metric.AIResponse.BrandPosition,
				"brand_sentiment": metric.AIResponse.BrandSentiment,
				"confidence":      metric.AIResponse.Confidence,
				"relevance":       metric.AIResponse.Relevance,
			}
		}

		if metric.Brand.ID != uuid.Nil {
			trendData[i]["brand"] = map[string]interface{}{
				"id":          metric.Brand.ID,
				"name":        metric.Brand.Name,
				"domain":      metric.Brand.Domain,
				"keywords":    metric.Brand.Keywords,
				"description": metric.Brand.Description,
			}
		}
	}

	h.logger.Info("Successfully retrieved visibility trend for brand %s (%d records with full associations)", brandID, len(trend))
	response.Success(c, http.StatusOK, "Visibility trend retrieved successfully", map[string]interface{}{
		"brand_id": brandID,
		"days":     days,
		"trend":    trendData,
		"count":    len(trend),
		"summary": map[string]interface{}{
			"total_metrics":    len(trend),
			"date_range_start": time.Now().AddDate(0, 0, -days).Format("2006-01-02"),
			"date_range_end":   time.Now().Format("2006-01-02"),
		},
	})
}

// CalculateBrandAggregation calculates aggregated visibility metrics for a brand
func (h *AIVisibilityHandler) CalculateBrandAggregation(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		h.logger.Warning("Invalid brand ID format: %s", brandIDStr)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// 解析日期参数
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			h.logger.Warning("Invalid start_date format: %s", startDateStr)
			response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid start_date format", "Use YYYY-MM-DD format")
			return
		}
	} else {
		// 默认为30天前
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			h.logger.Warning("Invalid end_date format: %s", endDateStr)
			response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid end_date format", "Use YYYY-MM-DD format")
			return
		}
	} else {
		// 默认为今天
		endDate = time.Now()
	}

	h.logger.Info("Calculating brand aggregation for brand %s from %s to %s", brandID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))

	aggregation, err := h.visibilityService.CalculateBrandVisibilityAggregation(brandID, startDate, endDate)
	if err != nil {
		h.logger.Error("Failed to calculate brand aggregation for brand %s: %v", brandID, err)
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to calculate brand aggregation", err.Error())
		return
	}

	h.logger.Info("Successfully calculated brand aggregation for brand %s", brandID)
	response.Success(c, http.StatusOK, "Brand aggregation calculated successfully", aggregation)
}

// BatchCalculateVisibilityMetrics calculates visibility metrics for multiple responses
func (h *AIVisibilityHandler) BatchCalculateVisibilityMetrics(c *gin.Context) {
	var req struct {
		ResponseIDs []string `json:"response_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warning("Invalid request body: %v", err)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request body", err.Error())
		return
	}

	if len(req.ResponseIDs) == 0 {
		h.logger.Warning("Empty response IDs list")
		response.ErrorWithDetails(c, http.StatusBadRequest, "Empty response IDs list", "At least one response ID is required")
		return
	}

	if len(req.ResponseIDs) > 100 {
		h.logger.Warning("Too many response IDs: %d", len(req.ResponseIDs))
		response.ErrorWithDetails(c, http.StatusBadRequest, "Too many response IDs", "Maximum 100 response IDs allowed")
		return
	}

	h.logger.Info("Batch calculating visibility metrics for %d responses", len(req.ResponseIDs))

	results := make(map[string]interface{})
	successCount := 0
	errorCount := 0

	for _, responseIDStr := range req.ResponseIDs {
		responseID, err := uuid.Parse(responseIDStr)
		if err != nil {
			h.logger.Warning("Invalid response ID format: %s", responseIDStr)
			results[responseIDStr] = map[string]interface{}{
				"success": false,
				"error":   "Invalid UUID format",
			}
			errorCount++
			continue
		}

		if err := h.visibilityService.CalculateVisibilityMetrics(responseID); err != nil {
			h.logger.Error("Failed to calculate visibility metrics for response %s: %v", responseID, err)
			results[responseIDStr] = map[string]interface{}{
				"success": false,
				"error":   err.Error(),
			}
			errorCount++
		} else {
			results[responseIDStr] = map[string]interface{}{
				"success": true,
			}
			successCount++
		}
	}

	h.logger.Info("Batch calculation completed: %d success, %d errors", successCount, errorCount)
	response.Success(c, http.StatusOK, "Batch calculation completed", map[string]interface{}{
		"total":         len(req.ResponseIDs),
		"success_count": successCount,
		"error_count":   errorCount,
		"results":       results,
	})
}

// GetVisibilityMetricsStats gets statistics about visibility metrics
func (h *AIVisibilityHandler) GetVisibilityMetricsStats(c *gin.Context) {
	brandIDStr := c.Query("brand_id")
	var brandID *uuid.UUID

	if brandIDStr != "" {
		parsed, err := uuid.Parse(brandIDStr)
		if err != nil {
			h.logger.Warning("Invalid brand ID format: %s", brandIDStr)
			response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
			return
		}
		brandID = &parsed
	}

	// 获取天数参数，默认为7天
	daysStr := c.DefaultQuery("days", "7")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		h.logger.Warning("Invalid days parameter: %s", daysStr)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid days parameter", "Days must be between 1 and 365")
		return
	}

	h.logger.Info("Getting visibility metrics statistics for %d days", days)

	// 获取可见性指标统计数据
	visibilityStats, err := h.visibilityService.GetVisibilityMetricsStats(brandID, days)
	if err != nil {
		h.logger.Error("Failed to get visibility metrics stats: %v", err)
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to get visibility metrics statistics", err.Error())
		return
	}

	// 构建响应数据
	stats := map[string]interface{}{
		"data":      visibilityStats,
		"days":      days,
		"brand_id":  brandID,
		"timestamp": time.Now(),
	}

	h.logger.Info("Successfully retrieved visibility metrics statistics")
	response.Success(c, http.StatusOK, "Statistics retrieved successfully", stats)
}

// GenerateVisibilityReport generates a comprehensive visibility report for a brand
func (h *AIVisibilityHandler) GenerateVisibilityReport(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		h.logger.Warning("Invalid brand ID format: %s", brandIDStr)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// 解析日期参数
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			h.logger.Warning("Invalid start_date format: %s", startDateStr)
			response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid start_date format", "Use YYYY-MM-DD format")
			return
		}
	} else {
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			h.logger.Warning("Invalid end_date format: %s", endDateStr)
			response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid end_date format", "Use YYYY-MM-DD format")
			return
		}
	} else {
		endDate = time.Now()
	}

	h.logger.Info("Generating visibility report for brand %s from %s to %s", brandID, startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))

	report, err := h.aggregationService.GenerateVisibilityReport(brandID, startDate, endDate)
	if err != nil {
		h.logger.Error("Failed to generate visibility report for brand %s: %v", brandID, err)
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to generate visibility report", err.Error())
		return
	}

	h.logger.Info("Successfully generated visibility report for brand %s", brandID)
	response.Success(c, http.StatusOK, "Visibility report generated successfully", report)
}

// CompareBrandVisibility compares visibility metrics between two brands
func (h *AIVisibilityHandler) CompareBrandVisibility(c *gin.Context) {
	brandID1Str := c.Param("id1")
	brandID2Str := c.Param("id2")

	brandID1, err := uuid.Parse(brandID1Str)
	if err != nil {
		h.logger.Warning("Invalid brand ID 1 format: %s", brandID1Str)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID 1", err.Error())
		return
	}

	brandID2, err := uuid.Parse(brandID2Str)
	if err != nil {
		h.logger.Warning("Invalid brand ID 2 format: %s", brandID2Str)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID 2", err.Error())
		return
	}

	// 解析日期参数
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var startDate, endDate time.Time
	if startDateStr != "" {
		startDate, err = time.Parse("2006-01-02", startDateStr)
		if err != nil {
			h.logger.Warning("Invalid start_date format: %s", startDateStr)
			response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid start_date format", "Use YYYY-MM-DD format")
			return
		}
	} else {
		startDate = time.Now().AddDate(0, 0, -30)
	}

	if endDateStr != "" {
		endDate, err = time.Parse("2006-01-02", endDateStr)
		if err != nil {
			h.logger.Warning("Invalid end_date format: %s", endDateStr)
			response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid end_date format", "Use YYYY-MM-DD format")
			return
		}
	} else {
		endDate = time.Now()
	}

	h.logger.Info("Comparing visibility between brands %s and %s", brandID1, brandID2)

	comparison, err := h.aggregationService.CompareBrandAggregations(brandID1, brandID2, startDate, endDate)
	if err != nil {
		h.logger.Error("Failed to compare brand visibility: %v", err)
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to compare brand visibility", err.Error())
		return
	}

	h.logger.Info("Successfully compared visibility between brands %s and %s", brandID1, brandID2)
	response.Success(c, http.StatusOK, "Brand visibility comparison completed", comparison)
}

// GetBrandAggregationHistory gets historical aggregation data for a brand
func (h *AIVisibilityHandler) GetBrandAggregationHistory(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		h.logger.Warning("Invalid brand ID format: %s", brandIDStr)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	monthsStr := c.DefaultQuery("months", "6")
	months, err := strconv.Atoi(monthsStr)
	if err != nil || months < 1 || months > 24 {
		h.logger.Warning("Invalid months parameter: %s", monthsStr)
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid months parameter", "Months must be between 1 and 24")
		return
	}

	h.logger.Info("Getting aggregation history for brand %s over %d months", brandID, months)

	history, err := h.aggregationService.GetBrandAggregationHistory(brandID, months)
	if err != nil {
		h.logger.Error("Failed to get aggregation history for brand %s: %v", brandID, err)
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to get aggregation history", err.Error())
		return
	}

	h.logger.Info("Successfully retrieved aggregation history for brand %s (%d records)", brandID, len(history))
	response.Success(c, http.StatusOK, "Aggregation history retrieved successfully", map[string]interface{}{
		"brand_id": brandID,
		"months":   months,
		"history":  history,
		"count":    len(history),
	})
}
