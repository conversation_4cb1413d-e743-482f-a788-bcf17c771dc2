/*
 * @Description: Upload handler for GEOK Center file upload management
 * @Author: AI Assistant
 * @Date: 2025-07-16
 */
package handlers

import (
	"pointer/golangp/apps/geok_center/pkg/response"
	"pointer/golangp/apps/geok_center/pkg/services/manager"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
)

// UploadHandler handles file upload operations
type UploadHandler struct {
	logger *logging.Logger
}

// NewUploadHandler creates a new upload handler instance
func NewUploadHandler() *UploadHandler {
	logger := logging.GetLogger("upload_handler")
	return &UploadHandler{
		logger: logger,
	}
}

// UploadFile handles general file upload requests
func (h *UploadHandler) UploadFile(c *gin.Context) {
	// Get category from query parameter, default to "general"
	category := c.DefaultQuery("category", "general")

	// Get uploaded file
	file, err := c.FormFile("file")
	if err != nil {
		h.logger.Warning("No file provided, Error: %v", err)
		response.BadRequest(c, "No file provided")
		return
	}

	h.logger.Info("File upload request - Category: %s, Filename: %s, Size: %d",
		category, file.Filename, file.Size)

	// Get global upload service and upload file
	uploadService := manager.GetUploadService()
	fileURL, err := uploadService.UploadFromMultipart(file, category)
	if err != nil {
		h.logger.Error("Failed to upload file - Error: %v", err)
		response.BadRequest(c, err.Error())
		return
	}

	h.logger.Info("File uploaded successfully - URL: %s", fileURL)
	response.OK(c, "File uploaded successfully", map[string]interface{}{
		"file_url": fileURL,
		"category": category,
	})
}
