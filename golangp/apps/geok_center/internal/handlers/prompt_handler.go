/*
 * @Description: 提示内容处理器 - 处理提示内容相关的HTTP请求
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package handlers

import (
	"net/http"
	"strconv"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/apps/geok_center/pkg/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// PromptHandler handles prompt-related requests
type PromptHandler struct {
	promptService *services.PromptService
}

// NewPromptHandler creates a new prompt handler
func NewPromptHandler(promptService *services.PromptService) *PromptHandler {
	return &PromptHandler{
		promptService: promptService,
	}
}

// CreatePromptRequest represents the request for creating a prompt
type CreatePromptRequest struct {
	BrandID    uuid.UUID `json:"brand_id" binding:"required"`
	AISearchID uuid.UUID `json:"ai_search_id" binding:"required"`
	Content    string    `json:"content" binding:"required"`
	Category   string    `json:"category"`
	Priority   int       `json:"priority"`
	Region     string    `json:"region"`
	Platform   string    `json:"platform"`
	Language   string    `json:"language"`
}

// UpdatePromptRequest represents the request for updating a prompt
type UpdatePromptRequest struct {
	AISearchID *uuid.UUID           `json:"ai_search_id"`
	Content    *string              `json:"content"`
	Category   *string              `json:"category"`
	Score      *float64             `json:"score"`
	Ranking    *int                 `json:"ranking"`
	ShareRate  *float64             `json:"share_rate"`
	Priority   *int                 `json:"priority"`
	Status     *models.PromptStatus `json:"status"`
}

// CreatePrompt creates a new prompt
func (h *PromptHandler) CreatePrompt(c *gin.Context) {
	var req CreatePromptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", err.Error())
		return
	}

	prompt := &models.Prompt{
		ID:         uuid.New(),
		BrandID:    req.BrandID,
		AISearchID: req.AISearchID,
		Content:    req.Content,
		Category:   req.Category,
		Score:      0, // Will be calculated
		Ranking:    0, // Will be assigned
		Status:     models.PromptStatusActive,
		Priority:   req.Priority,
		Region:     req.Region,
		Platform:   req.Platform,
		Language:   req.Language,
	}

	// Set defaults
	if prompt.Priority == 0 {
		prompt.Priority = 1
	}
	if prompt.Region == "" {
		prompt.Region = "global"
	}
	if prompt.Platform == "" {
		prompt.Platform = "ai_models"
	}
	if prompt.Language == "" {
		prompt.Language = "zh"
	}
	if prompt.Category == "" {
		prompt.Category = "general"
	}

	createdPrompt, err := h.promptService.CreatePrompt(prompt)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to create prompt", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "Prompt created successfully", createdPrompt)
}

// GetPrompt retrieves a prompt by ID
func (h *PromptHandler) GetPrompt(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid prompt ID", err.Error())
		return
	}

	prompt, err := h.promptService.GetPromptByID(id)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusNotFound, "Prompt not found", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "Prompt retrieved successfully", prompt)
}

// GetPromptsByBrand retrieves prompts for a specific brand
func (h *PromptHandler) GetPromptsByBrand(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// Parse query parameters
	var status *models.PromptStatus
	if statusStr := c.Query("status"); statusStr != "" {
		s := models.PromptStatus(statusStr)
		status = &s
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	limit := pageSize
	offset := (page - 1) * pageSize

	prompts, total, err := h.promptService.GetPromptsByBrandID(brandID, status, limit, offset)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve prompts", err.Error())
		return
	}

	result := map[string]interface{}{
		"prompts": prompts,
		"pagination": map[string]interface{}{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}

	response.Success(c, http.StatusOK, "Prompts retrieved successfully", result)
}

// GetTopPrompts retrieves top-ranking prompts
func (h *PromptHandler) GetTopPrompts(c *gin.Context) {
	var brandID *uuid.UUID
	if brandIDStr := c.Query("brand_id"); brandIDStr != "" {
		if id, err := uuid.Parse(brandIDStr); err == nil {
			brandID = &id
		}
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if limit < 1 || limit > 100 {
		limit = 10
	}

	prompts, err := h.promptService.GetTopPrompts(brandID, limit)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve top prompts", err.Error())
		return
	}

	result := map[string]interface{}{
		"prompts":  prompts,
		"count":    len(prompts),
		"brand_id": brandID,
	}

	response.Success(c, http.StatusOK, "Top prompts retrieved successfully", result)
}

// UpdatePrompt updates an existing prompt
func (h *PromptHandler) UpdatePrompt(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid prompt ID", err.Error())
		return
	}

	var req UpdatePromptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", err.Error())
		return
	}

	// Build updates map
	updates := make(map[string]interface{})
	if req.AISearchID != nil {
		updates["ai_search_id"] = *req.AISearchID
	}
	if req.Content != nil {
		updates["content"] = *req.Content
	}
	if req.Category != nil {
		updates["category"] = *req.Category
	}
	if req.Score != nil {
		updates["score"] = *req.Score
	}
	if req.Ranking != nil {
		updates["ranking"] = *req.Ranking
	}
	if req.ShareRate != nil {
		updates["share_rate"] = *req.ShareRate
	}
	if req.Priority != nil {
		updates["priority"] = *req.Priority
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	updatedPrompt, err := h.promptService.UpdatePrompt(id, updates)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to update prompt", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "Prompt updated successfully", updatedPrompt)
}

// DeletePrompt deletes a prompt
func (h *PromptHandler) DeletePrompt(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid prompt ID", err.Error())
		return
	}

	if err := h.promptService.DeletePrompt(id); err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to delete prompt", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "Prompt deleted successfully", nil)
}

// GeneratePromptsFromAI generates prompts based on AI search responses
func (h *PromptHandler) GeneratePromptsFromAI(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	if err := h.promptService.GeneratePromptsFromAIResponses(brandID); err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to generate prompts from AI responses", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "Prompts generated from AI responses successfully", nil)
}

// GetPromptDashboard returns dashboard data for prompts
func (h *PromptHandler) GetPromptDashboard(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// Get top prompts
	topPrompts, err := h.promptService.GetTopPrompts(&brandID, 10)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve top prompts", err.Error())
		return
	}

	// Get all prompts for statistics
	allPrompts, _, err := h.promptService.GetPromptsByBrandID(brandID, nil, 1000, 0)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve prompts", err.Error())
		return
	}

	// Calculate statistics
	totalPrompts := len(allPrompts)
	activePrompts := 0
	topRankingPrompts := 0
	var totalScore float64
	var totalClicks int64

	for _, prompt := range allPrompts {
		if prompt.IsActive() {
			activePrompts++
		}
		if prompt.IsTopRanking() {
			topRankingPrompts++
		}
		totalScore += prompt.Score
		totalClicks += prompt.ClickCount
	}

	var averageScore float64
	if totalPrompts > 0 {
		averageScore = totalScore / float64(totalPrompts)
	}

	dashboard := map[string]interface{}{
		"brand_id":            brandID,
		"total_prompts":       totalPrompts,
		"active_prompts":      activePrompts,
		"top_ranking_prompts": topRankingPrompts,
		"average_score":       averageScore,
		"total_clicks":        totalClicks,
		"top_prompts":         topPrompts,
		"statistics": map[string]interface{}{
			"active_rate":      float64(activePrompts) / float64(max(totalPrompts, 1)) * 100,
			"top_ranking_rate": float64(topRankingPrompts) / float64(max(totalPrompts, 1)) * 100,
		},
	}

	response.Success(c, http.StatusOK, "Prompt dashboard retrieved successfully", dashboard)
}

// max returns the maximum of two integers
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
