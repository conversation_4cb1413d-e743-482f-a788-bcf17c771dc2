/*
 * @Description: Brand handler for GEOK Center API
 * @Author: AI Assistant
 * @Date: 2025-07-15
 */
package handlers

import (
	"encoding/json"
	"fmt"
	"strconv"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/apps/geok_center/pkg/response"
	"pointer/golangp/apps/geok_center/pkg/types"
	"pointer/golangp/common/logging"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// BrandHandler handles brand-related HTTP requests
type BrandHandler struct {
	brandService *services.BrandService
	logger       *logging.Logger
}

// NewBrandHandler creates a new brand handler
func <PERSON>BrandHandler(brandService *services.BrandService) *BrandHandler {
	return &BrandHandler{
		brandService: brandService,
		logger:       logging.GetLogger("brand_handler"),
	}
}

// GetBrands handles GET /brands
func (h *BrandHandler) GetBrands(c *gin.Context) {
	h.logger.Info("🏷️ GetBrands called - Method: %s, Path: %s", c.Request.Method, c.Request.URL.Path)

	// Parse pagination parameters
	pagination := types.PaginationParams{
		Page:     1,
		PageSize: 10,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			pagination.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			pagination.PageSize = ps
		}
	}

	// Get current user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		response.BadRequest(c, "Invalid user ID type in context")
		return
	}

	// Parse filter parameters
	var filters types.FilterParams
	if err := c.ShouldBindQuery(&filters); err != nil {
		response.BadRequest(c, "Invalid filter parameters")
		return
	}

	// TODO: 品牌需要过滤用户
	fmt.Println("filters: ", userUUID)
	// Add user ID filter to ensure users only see their own brands
	// filters.UserID = userUUID.String()

	// Get brands from service
	brands, meta, err := h.brandService.GetBrands(pagination, filters)
	if err != nil {
		response.InternalServerError(c, "Failed to fetch brands")
		return
	}

	response.OKWithMeta(c, "", brands, &response.Meta{
		Page:       meta.Page,
		PageSize:   meta.PageSize,
		Total:      meta.Total,
		TotalPages: meta.TotalPages,
	})
}

// GetBrand handles GET /brands/:id
func (h *BrandHandler) GetBrand(c *gin.Context) {
	idParam := c.Param("id")
	brandID, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(c, "Invalid brand ID")
		return
	}

	// Get current user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		response.BadRequest(c, "Invalid user ID type in context")
		return
	}

	brand, err := h.brandService.GetBrand(brandID)
	if err != nil {
		if err.Error() == "brand not found" {
			response.NotFound(c, "Brand not found")
			return
		}
		response.InternalServerError(c, "Failed to fetch brand")
		return
	}

	// Check if the brand belongs to the current user
	if brand.UserID != userUUID {
		response.Forbidden(c, "You don't have permission to access this brand")
		return
	}

	response.OK(c, "", brand)
}

// CreateBrand handles POST /brands
func (h *BrandHandler) CreateBrand(c *gin.Context) {
	var brand models.Brand
	if err := c.ShouldBindJSON(&brand); err != nil {
		response.BadRequest(c, "Invalid request data")
		return
	}

	// Validate required fields
	if brand.Name == "" {
		response.BadRequest(c, "Brand name is required")
		return
	}

	// Get current user ID from context (set by authentication middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	// Convert user_id to UUID
	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		response.BadRequest(c, "Invalid user ID type in context")
		return
	}

	// Set the user ID for the brand
	brand.UserID = userUUID

	// Validate suggestions field if provided (should be a valid JSON array)
	if brand.Suggestions != nil {
		var suggestions []interface{}
		if err := json.Unmarshal(brand.Suggestions, &suggestions); err != nil {
			response.BadRequest(c, "Invalid suggestions format - must be a JSON array")
			return
		}
	}

	if err := h.brandService.CreateBrand(&brand); err != nil {
		response.InternalServerError(c, "Failed to create brand")
		return
	}

	response.Created(c, "Brand created successfully", brand)
}

// UpdateBrand handles PUT /brands/:id
func (h *BrandHandler) UpdateBrand(c *gin.Context) {
	idParam := c.Param("id")
	brandID, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(c, "Invalid brand ID")
		return
	}

	// Get current user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		response.BadRequest(c, "Invalid user ID type in context")
		return
	}

	// Check if the brand belongs to the current user
	existingBrand, err := h.brandService.GetBrand(brandID)
	if err != nil {
		if err.Error() == "brand not found" {
			response.NotFound(c, "Brand not found")
			return
		}
		response.InternalServerError(c, "Failed to fetch brand")
		return
	}

	if existingBrand.UserID != userUUID {
		response.Forbidden(c, "You don't have permission to update this brand")
		return
	}

	var updates models.Brand
	if err := c.ShouldBindJSON(&updates); err != nil {
		response.BadRequest(c, "Invalid request data")
		return
	}

	// Validate suggestions field if provided
	if updates.Suggestions != nil {
		var suggestions []interface{}
		if err := json.Unmarshal(updates.Suggestions, &suggestions); err != nil {
			response.BadRequest(c, "Invalid suggestions format - must be a JSON array")
			return
		}
	}

	if err := h.brandService.UpdateBrand(brandID, &updates); err != nil {
		if err.Error() == "brand not found" {
			response.NotFound(c, "Brand not found")
			return
		}
		response.InternalServerError(c, "Failed to update brand")
		return
	}

	response.OK(c, "Brand updated successfully", nil)
}

// DeleteBrand handles DELETE /brands/:id
func (h *BrandHandler) DeleteBrand(c *gin.Context) {
	idParam := c.Param("id")
	brandID, err := uuid.Parse(idParam)
	if err != nil {
		response.BadRequest(c, "Invalid brand ID")
		return
	}

	// Get current user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		response.BadRequest(c, "Invalid user ID type in context")
		return
	}

	// Check if the brand belongs to the current user before deleting
	existingBrand, err := h.brandService.GetBrand(brandID)
	if err != nil {
		if err.Error() == "brand not found" {
			response.NotFound(c, "Brand not found")
			return
		}
		response.InternalServerError(c, "Failed to fetch brand")
		return
	}

	if existingBrand.UserID != userUUID {
		response.Forbidden(c, "You don't have permission to delete this brand")
		return
	}

	if err := h.brandService.DeleteBrand(brandID); err != nil {
		if err.Error() == "brand not found" {
			response.NotFound(c, "Brand not found")
			return
		}
		response.InternalServerError(c, "Failed to delete brand")
		return
	}

	response.OK(c, "Brand deleted successfully", nil)
}

// SearchBrands handles GET /brands/search
func (h *BrandHandler) SearchBrands(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		response.BadRequest(c, "Search query is required")
		return
	}

	// Parse pagination parameters
	pagination := types.PaginationParams{
		Page:     1,
		PageSize: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			pagination.Page = p
		}
	}

	if pageSize := c.Query("page_size"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil && ps > 0 && ps <= 100 {
			pagination.PageSize = ps
		}
	}

	// Get current user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		response.Unauthorized(c, "User not authenticated")
		return
	}

	userUUID, ok := userID.(uuid.UUID)
	if !ok {
		response.BadRequest(c, "Invalid user ID type in context")
		return
	}

	brands, meta, err := h.brandService.SearchBrands(query, userUUID.String(), pagination)
	if err != nil {
		response.InternalServerError(c, "Failed to search brands")
		return
	}

	response.OKWithMeta(c, "", brands, &response.Meta{
		Page:       meta.Page,
		PageSize:   meta.PageSize,
		Total:      meta.Total,
		TotalPages: meta.TotalPages,
	})
}
