/*
 * @Description: 引用处理器 - 处理引用相关的HTTP请求
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package handlers

import (
	"net/http"
	"strconv"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/internal/services"
	"pointer/golangp/apps/geok_center/pkg/response"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ReferenceHandler handles reference related HTTP requests
type ReferenceHandler struct {
	referenceService *services.ReferenceService
}

// NewReferenceHandler creates a new reference handler
func NewReferenceHandler(referenceService *services.ReferenceService) *ReferenceHandler {
	return &ReferenceHandler{
		referenceService: referenceService,
	}
}

// GetReference retrieves a reference by ID
func (h *ReferenceHandler) GetReference(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid reference ID", err.Error())
		return
	}

	reference, err := h.referenceService.GetReferenceByID(id)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusNotFound, "Reference not found", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "Reference retrieved successfully", reference)
}

// GetReferencesByBrand retrieves reference statistics for a specific brand (latest 7 days aggregation)
func (h *ReferenceHandler) GetReferencesByBrand(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// Parse days parameter (default to 7)
	daysStr := c.DefaultQuery("days", "7")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 || days > 365 {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid days parameter", "Days must be between 1 and 365")
		return
	}

	// Get reference statistics
	referenceStats, err := h.referenceService.GetReferenceStatsForBrand(brandID, days)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve reference statistics", err.Error())
		return
	}

	result := map[string]interface{}{
		"references": referenceStats,
		"days":       days,
		"brand_id":   brandID,
		"timestamp":  time.Now(),
	}

	response.Success(c, http.StatusOK, "Reference statistics retrieved successfully", result)
}

// GetReferencesByDomain retrieves references by domain
func (h *ReferenceHandler) GetReferencesByDomain(c *gin.Context) {
	domain := c.Param("domain")
	if domain == "" {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Domain is required", "")
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	limit := pageSize
	offset := (page - 1) * pageSize

	references, total, err := h.referenceService.GetReferencesByDomain(domain, limit, offset)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve references", err.Error())
		return
	}

	result := map[string]interface{}{
		"references": references,
		"domain":     domain,
		"pagination": map[string]interface{}{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}

	response.Success(c, http.StatusOK, "References retrieved successfully", result)
}

// GetTopReferences retrieves top-ranking references
func (h *ReferenceHandler) GetTopReferences(c *gin.Context) {
	var brandID *uuid.UUID
	if brandIDStr := c.Query("brand_id"); brandIDStr != "" {
		if id, err := uuid.Parse(brandIDStr); err == nil {
			brandID = &id
		}
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if limit < 1 || limit > 100 {
		limit = 10
	}

	references, err := h.referenceService.GetTopReferences(brandID, limit)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve top references", err.Error())
		return
	}

	result := map[string]interface{}{
		"references": references,
		"count":      len(references),
		"brand_id":   brandID,
	}

	response.Success(c, http.StatusOK, "Top references retrieved successfully", result)
}

// ExtractReferencesFromResponse extracts references from AI search response
func (h *ReferenceHandler) ExtractReferencesFromResponse(c *gin.Context) {
	responseIDStr := c.Param("responseId")
	responseID, err := uuid.Parse(responseIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid response ID", err.Error())
		return
	}

	if err := h.referenceService.ExtractReferencesFromAIResponse(responseID); err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to extract references", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "References extracted successfully", nil)
}

// DeleteReference deletes a reference
func (h *ReferenceHandler) DeleteReference(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid reference ID", err.Error())
		return
	}

	if err := h.referenceService.DeleteReference(id); err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to delete reference", err.Error())
		return
	}

	response.Success(c, http.StatusOK, "Reference deleted successfully", nil)
}

// GetReferenceDashboard returns dashboard data for references
func (h *ReferenceHandler) GetReferenceDashboard(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// Get top references
	topReferences, err := h.referenceService.GetTopReferences(&brandID, 10)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve top references", err.Error())
		return
	}

	// Get references by category
	categories := []models.ReferenceCategory{
		models.ReferenceCategoryNews,
		models.ReferenceCategoryEducation,
		models.ReferenceCategoryTechnology,
		models.ReferenceCategoryBusiness,
		models.ReferenceCategoryGovernment,
	}

	categoryStats := make(map[string]int)
	for _, category := range categories {
		_, total, err := h.referenceService.GetReferencesByBrandID(brandID, &category, 1, 0)
		if err == nil {
			categoryStats[string(category)] = int(total)
		}
	}

	// Get domain distribution
	domainStats := make(map[string]int)
	allRefs, _, err := h.referenceService.GetReferencesByBrandID(brandID, nil, 100, 0)
	if err == nil {
		for _, ref := range allRefs {
			domainStats[ref.Domain]++
		}
	}

	// Calculate summary statistics
	totalReferences := len(allRefs)
	topRankingCount := 0
	highAuthorityCount := 0

	for _, ref := range allRefs {
		if ref.IsTopRanking() {
			topRankingCount++
		}
		if ref.IsHighAuthority() {
			highAuthorityCount++
		}
	}

	dashboard := map[string]interface{}{
		"brand_id":             brandID,
		"total_references":     totalReferences,
		"top_ranking_count":    topRankingCount,
		"high_authority_count": highAuthorityCount,
		"top_references":       topReferences,
		"category_stats":       categoryStats,
		"domain_stats":         domainStats,
		"summary": map[string]interface{}{
			"total":               totalReferences,
			"top_ranking_rate":    float64(topRankingCount) / float64(max(totalReferences, 1)) * 100,
			"high_authority_rate": float64(highAuthorityCount) / float64(max(totalReferences, 1)) * 100,
		},
	}

	response.Success(c, http.StatusOK, "Reference dashboard retrieved successfully", dashboard)
}

// GetReferenceAnalytics returns analytics data for references
func (h *ReferenceHandler) GetReferenceAnalytics(c *gin.Context) {
	brandIDStr := c.Param("id")
	brandID, err := uuid.Parse(brandIDStr)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid brand ID", err.Error())
		return
	}

	// Get all references for the brand
	references, _, err := h.referenceService.GetReferencesByBrandID(brandID, nil, 1000, 0)
	if err != nil {
		response.ErrorWithDetails(c, http.StatusInternalServerError, "Failed to retrieve references", err.Error())
		return
	}

	// Analyze references
	analytics := map[string]interface{}{
		"brand_id":         brandID,
		"total_references": len(references),
	}

	// Type distribution
	typeStats := make(map[string]int)
	categoryStats := make(map[string]int)
	domainStats := make(map[string]int)
	rankingStats := make(map[string]int)

	var totalAuthorityScore float64
	var totalTrustScore float64
	var totalQualityScore float64

	for _, ref := range references {
		typeStats[string(ref.Type)]++
		categoryStats[string(ref.Category)]++
		domainStats[ref.Domain]++

		if ref.Ranking <= 3 {
			rankingStats["top_3"]++
		} else if ref.Ranking <= 10 {
			rankingStats["top_10"]++
		} else {
			rankingStats["others"]++
		}

		totalAuthorityScore += ref.AuthorityScore
		totalTrustScore += ref.TrustScore
		totalQualityScore += ref.QualityScore
	}

	if len(references) > 0 {
		analytics["average_authority_score"] = totalAuthorityScore / float64(len(references))
		analytics["average_trust_score"] = totalTrustScore / float64(len(references))
		analytics["average_quality_score"] = totalQualityScore / float64(len(references))
	}

	analytics["type_distribution"] = typeStats
	analytics["category_distribution"] = categoryStats
	analytics["domain_distribution"] = domainStats
	analytics["ranking_distribution"] = rankingStats

	response.Success(c, http.StatusOK, "Reference analytics retrieved successfully", analytics)
}
