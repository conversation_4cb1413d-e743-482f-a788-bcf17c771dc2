-- Add suggestions field to brands table
-- This field stores an optional JSON array of suggestions/prompts for the brand

-- Add suggestions field to brands table
-- This field stores an optional JSON array of suggestions/prompts for the brand

-- Step 1: Add the column
ALTER TABLE brands
ADD COLUMN suggestions JSONB DEFAULT NULL;

-- Step 2: Add comment to the column
COMMENT ON COLUMN brands.suggestions IS '品牌提示建议，可选的JSON数组';

-- Step 3: Add index for better performance when querying suggestions
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_brands_suggestions
ON brands USING GIN (suggestions)
WHERE suggestions IS NOT NULL;