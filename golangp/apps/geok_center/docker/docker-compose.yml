services:
  geok:
    image: geok_center:v1.0
    container_name: geok_container
    ports:
      - "0.0.0.0:8012:8012"
    depends_on:
      - mysql
      - redis
    volumes:
      - ./app.env:/app/app.env
      - ./geok:/tmp/geok/
    networks:
      - geok-network

  geok_web:
    image: geok_web:v1.0
    container_name: geok_web_container
    restart: always
    ports:
      - "0.0.0.0:3000:80"
    environment:
      NEXT_PUBLIC_API_URL: http://118.178.242.142:3001
    networks:
      - geok-network

  mysql:
    image: mysql:8.0
    container_name: mysql_container
    ports:
      - "3307:3306"
    environment:
      MYSQL_ROOT_PASSWORD: geok
      MYSQL_DATABASE: geok
      MYSQL_USER: geok
      MYSQL_PASSWORD: geok
    volumes:
      - ./mysql-data:/var/lib/mysql
    networks:
      - geok-network

  redis:
    image: redis:7.0-alpine
    container_name: redis_container
    ports:
      - "6379:6379"
    command: redis-server --requirepass geok --appendonly yes
    volumes:
      - ./redis-data:/data
    networks:
      - geok-network

networks:
  geok-network:
    driver: bridge
