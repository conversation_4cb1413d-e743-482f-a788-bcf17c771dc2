services:
  redis:
    image: redis:7.0-alpine
    ports:
      - "6389:6379"
    command: redis-server --requirepass geok --appendonly yes
    volumes:
      - ./redis-data:/data
    restart: unless-stopped

  postgres:
    image: postgres:14
    container_name: postgres_container
    environment:
      POSTGRES_USER: geok
      POSTGRES_PASSWORD: geok
      POSTGRES_DB: geok
    restart: always
    ports:
      - "0.0.0.0:5439:5432"
    volumes:
      - ./docker-data/postgres_data:/var/lib/postgresql/data
