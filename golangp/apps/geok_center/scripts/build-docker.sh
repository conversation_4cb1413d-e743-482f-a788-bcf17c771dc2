#!/bin/bash
###
 # @Description: Build Docker image for GEOK Center
 # @Author: Devin
 # @Date: 2025-07-26 11:07:37
###
set -e  # 如果有任何命令失败，则脚本退出

# =============================================================================
# 配置区域 - 只需要修改这里的版本号
# =============================================================================
VERSION_TAG="v1.0-beta"
IMAGE_NAME="geok_center"
FULL_IMAGE_TAG="${IMAGE_NAME}:${VERSION_TAG}"

# 保存当前路径
WORK_DIR=$(pwd)

# 预先提示 sudo 密码（只输入一次）
if ! docker info > /dev/null 2>&1; then
  echo "🔐 Docker 需要 sudo 权限，请输入密码..."
  sudo -v
fi

# 判断是否需要 sudo 执行 docker
DOCKER_CMD="docker"
if ! docker info > /dev/null 2>&1; then
  DOCKER_CMD="sudo docker"
fi

# Step 1: Bazel 构建
echo "🛠️  Building with Bazel..."
bazel build //golangp/apps/geok_center:geok_center

# Step 2: 复制构建产物到源码目录中（Dockerfile 所在处）
echo "📦 Copying binaries and Dockerfile..."
cp bazel-bin/golangp/apps/geok_center/geok_center_/geok_center golangp/apps/geok_center/

# Step 3: 进入目录构建 Docker 镜像
cd golangp/apps/geok_center
echo "🐳 Building Docker image '$FULL_IMAGE_TAG' in $(pwd)..."
$DOCKER_CMD build -t "$FULL_IMAGE_TAG" .

# Step 4: 清理构建产物
echo "🧹 Cleaning up build artifacts..."
rm -f geok_center

# Step 5: 回到原始工作目录
cd "$WORK_DIR"

# Step 6: 保存镜像为压缩包
OUTPUT_PATH="$HOME/docker_images/${IMAGE_NAME}_${VERSION_TAG}.tar.gz"
echo "📤 Saving Docker image '$FULL_IMAGE_TAG' to $OUTPUT_PATH ..."
mkdir -p "$(dirname "$OUTPUT_PATH")"
$DOCKER_CMD save "$FULL_IMAGE_TAG" | gzip | sudo tee "$OUTPUT_PATH" > /dev/null


echo "✅ Done. Docker image '$FULL_IMAGE_TAG' built and saved to $OUTPUT_PATH"