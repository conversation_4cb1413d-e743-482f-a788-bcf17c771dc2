#!/bin/bash

# Test script for updated GetVisibilityMetricsStats API endpoint
# This script tests the new functionality with three metrics

set -e

# Configuration
BASE_URL="http://localhost:8080/api/v1"
ENDPOINT="/ai-visibility/stats"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to test API endpoint
test_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    print_status $YELLOW "Testing: $description"
    print_status $YELLOW "URL: $url"
    
    # Make the request and capture response
    response=$(curl -s -w "\n%{http_code}" "$url" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json")
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    response_body=$(echo "$response" | head -n -1)
    
    # Check status code
    if [ "$http_code" -eq "$expected_status" ]; then
        print_status $GREEN "✓ Status: $http_code (Expected: $expected_status)"
    else
        print_status $RED "✗ Status: $http_code (Expected: $expected_status)"
        echo "Response: $response_body"
        return 1
    fi
    
    # Pretty print JSON response if it's valid JSON
    if echo "$response_body" | jq . >/dev/null 2>&1; then
        print_status $GREEN "✓ Valid JSON response"
        
        # Check for expected data structure
        if echo "$response_body" | jq -e '.data.keyword_frequency' >/dev/null 2>&1; then
            print_status $GREEN "✓ Contains keyword_frequency data"
        else
            print_status $YELLOW "⚠ Missing keyword_frequency data"
        fi
        
        if echo "$response_body" | jq -e '.data.brand_recommend_rate' >/dev/null 2>&1; then
            print_status $GREEN "✓ Contains brand_recommend_rate data"
        else
            print_status $YELLOW "⚠ Missing brand_recommend_rate data"
        fi
        
        if echo "$response_body" | jq -e '.data.brand_first_choice_rate' >/dev/null 2>&1; then
            print_status $GREEN "✓ Contains brand_first_choice_rate data"
        else
            print_status $YELLOW "⚠ Missing brand_first_choice_rate data"
        fi
        
        if echo "$response_body" | jq -e '.data.brand_search_rate' >/dev/null 2>&1; then
            print_status $GREEN "✓ Contains brand_search_rate data"
        else
            print_status $YELLOW "⚠ Missing brand_search_rate data"
        fi
        
        echo "$response_body" | jq .
    else
        print_status $RED "✗ Invalid JSON response"
        echo "Raw response: $response_body"
        return 1
    fi
    
    echo ""
    return 0
}

# Main test function
main() {
    print_status $YELLOW "=== Testing Updated GetVisibilityMetricsStats API ==="
    echo ""
    
    # Test 1: Basic stats request (default 7 days)
    test_endpoint "${BASE_URL}${ENDPOINT}" "Get visibility metrics stats (default 7 days)"
    
    # Test 2: Stats with specific days parameter
    test_endpoint "${BASE_URL}${ENDPOINT}?days=30" "Get visibility metrics stats (30 days)"
    
    # Test 3: Stats with brand_id parameter
    test_endpoint "${BASE_URL}${ENDPOINT}?brand_id=550e8400-e29b-41d4-a716-************" "Get visibility metrics stats for specific brand"
    
    # Test 4: Stats with both parameters
    test_endpoint "${BASE_URL}${ENDPOINT}?days=14&brand_id=550e8400-e29b-41d4-a716-************" "Get visibility metrics stats (14 days, specific brand)"
    
    # Test 5: Invalid days parameter (should return 400)
    test_endpoint "${BASE_URL}${ENDPOINT}?days=invalid" "Invalid days parameter" 400
    
    # Test 6: Invalid brand_id parameter (should return 400)
    test_endpoint "${BASE_URL}${ENDPOINT}?brand_id=invalid-uuid" "Invalid brand_id parameter" 400
    
    print_status $GREEN "=== All tests completed ==="
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_status $RED "Error: jq is required for JSON parsing. Please install jq."
    exit 1
fi

# Check if curl is installed
if ! command -v curl &> /dev/null; then
    print_status $RED "Error: curl is required for API testing. Please install curl."
    exit 1
fi

# Run tests
main
