#!/bin/bash

# GEOK Center Application Startup Script
# This script sets up the environment and starts the GEOK Center application

set -e

echo "🚀 Starting GEOK Center Application..."

# Check if app.env exists
if [ ! -f "app.env" ]; then
    echo "⚠️  app.env file not found. Creating from example..."
    if [ -f "app.env.example" ]; then
        cp app.env.example app.env
        echo "✅ Created app.env from example. Please edit it with your configuration."
        echo "📝 Edit app.env file and run this script again."
        exit 1
    else
        echo "❌ app.env.example not found. Please create app.env manually."
        exit 1
    fi
fi

# Load environment variables from app.env
if [ -f "app.env" ]; then
    echo "📄 Loading environment variables from app.env..."
    export $(grep -v '^#' app.env | xargs)
fi

# Check if database is accessible
echo "🔍 Checking database connection..."
if command -v psql &> /dev/null; then
    if ! PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_NAME -c '\q' 2>/dev/null; then
        echo "❌ Cannot connect to database. Please check your database configuration."
        echo "   Host: $DB_HOST:$DB_PORT"
        echo "   Database: $DB_NAME"
        echo "   User: $DB_USERNAME"
        exit 1
    fi
    echo "✅ Database connection successful"
else
    echo "⚠️  psql not found. Skipping database connection check."
fi

# Run database migration
echo "🗄️  Running database migration..."
if command -v bazel &> /dev/null; then
    bazel run //golangp/apps/geok_center:geok_center -- migrate --seed
else
    go run cmd/*.go migrate --seed
fi

if [ $? -eq 0 ]; then
    echo "✅ Database migration completed"
else
    echo "❌ Database migration failed"
    exit 1
fi

# Start the application
echo "🎯 Starting GEOK Center server..."
if command -v bazel &> /dev/null; then
    echo "🔨 Using Bazel to build and run..."
    bazel run //golangp/apps/geok_center:geok_center -- server
else
    echo "🐹 Using Go to run..."
    go run cmd/*.go server
fi
