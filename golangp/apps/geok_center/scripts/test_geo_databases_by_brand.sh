#!/bin/bash

# Test script for updated GetGEODatabasesByBrand API endpoint
# This script tests the new GEO database statistics functionality

set -e

# Configuration
BASE_URL="http://localhost:8080/api/v1"
ENDPOINT="/brands/{brand_id}/geo-databases"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to test API endpoint
test_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    print_status $YELLOW "Testing: $description"
    print_status $YELLOW "URL: $url"
    
    # Make the request and capture response
    response=$(curl -s -w "\n%{http_code}" "$url" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json")
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    response_body=$(echo "$response" | head -n -1)
    
    # Check status code
    if [ "$http_code" -eq "$expected_status" ]; then
        print_status $GREEN "✓ Status: $http_code (Expected: $expected_status)"
    else
        print_status $RED "✗ Status: $http_code (Expected: $expected_status)"
        echo "Response: $response_body"
        return 1
    fi
    
    # Pretty print JSON response if it's valid JSON
    if echo "$response_body" | jq . >/dev/null 2>&1; then
        print_status $GREEN "✓ Valid JSON response"
        
        # Check for expected data structure
        if echo "$response_body" | jq -e '.data.data' >/dev/null 2>&1; then
            print_status $GREEN "✓ Contains data array"
            
            # Check if data is an array
            if echo "$response_body" | jq -e '.data.data | type == "array"' >/dev/null 2>&1; then
                print_status $GREEN "✓ Data is an array"
                
                # Check for expected fields in each entry
                if echo "$response_body" | jq -e '.data.data[0].title' >/dev/null 2>&1; then
                    print_status $GREEN "✓ Contains title field"
                fi
                
                if echo "$response_body" | jq -e '.data.data[0].keywords' >/dev/null 2>&1; then
                    print_status $GREEN "✓ Contains keywords field"
                    
                    # Check if keywords is an array
                    if echo "$response_body" | jq -e '.data.data[0].keywords | type == "array"' >/dev/null 2>&1; then
                        print_status $GREEN "✓ Keywords is an array"
                    fi
                fi
                
                if echo "$response_body" | jq -e '.data.data[0].search_count' >/dev/null 2>&1; then
                    print_status $GREEN "✓ Contains search_count field"
                fi
                
                if echo "$response_body" | jq -e '.data.data[0].search_count_change' >/dev/null 2>&1; then
                    print_status $GREEN "✓ Contains search_count_change field"
                fi
                
                if echo "$response_body" | jq -e '.data.data[0].mention_rate' >/dev/null 2>&1; then
                    print_status $GREEN "✓ Contains mention_rate field"
                fi
                
                if echo "$response_body" | jq -e '.data.data[0].mention_rate_change' >/dev/null 2>&1; then
                    print_status $GREEN "✓ Contains mention_rate_change field"
                fi
            else
                print_status $YELLOW "⚠ Data is not an array"
            fi
        else
            print_status $YELLOW "⚠ Missing data array"
        fi
        
        if echo "$response_body" | jq -e '.data.days' >/dev/null 2>&1; then
            print_status $GREEN "✓ Contains days parameter"
        else
            print_status $YELLOW "⚠ Missing days parameter"
        fi
        
        if echo "$response_body" | jq -e '.data.brand_id' >/dev/null 2>&1; then
            print_status $GREEN "✓ Contains brand_id parameter"
        else
            print_status $YELLOW "⚠ Missing brand_id parameter"
        fi
        
        echo "$response_body" | jq .
    else
        print_status $RED "✗ Invalid JSON response"
        echo "Raw response: $response_body"
        return 1
    fi
    
    echo ""
    return 0
}

# Main test function
main() {
    print_status $YELLOW "=== Testing Updated GetGEODatabasesByBrand API ==="
    echo ""
    
    # Use a sample brand ID (you may need to adjust this)
    BRAND_ID="4fc86ecb-8e0e-476b-8826-bf4dc95fce0d"
    
    # Test 1: Basic request (default 7 days)
    test_endpoint "${BASE_URL}/brands/${BRAND_ID}/geo-databases" "Get GEO database stats (default 7 days)"
    
    # Test 2: Request with specific days parameter
    test_endpoint "${BASE_URL}/brands/${BRAND_ID}/geo-databases?days=14" "Get GEO database stats (14 days)"
    
    # Test 3: Request with maximum days
    test_endpoint "${BASE_URL}/brands/${BRAND_ID}/geo-databases?days=30" "Get GEO database stats (30 days)"
    
    # Test 4: Invalid brand ID (should return 400)
    test_endpoint "${BASE_URL}/brands/invalid-uuid/geo-databases" "Invalid brand ID" 400
    
    # Test 5: Invalid days parameter (should return 400)
    test_endpoint "${BASE_URL}/brands/${BRAND_ID}/geo-databases?days=invalid" "Invalid days parameter" 400
    
    # Test 6: Days out of range (should return 400)
    test_endpoint "${BASE_URL}/brands/${BRAND_ID}/geo-databases?days=500" "Days out of range (500)" 400
    
    print_status $GREEN "=== All tests completed ==="
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    print_status $RED "Error: jq is required for JSON parsing. Please install jq."
    exit 1
fi

# Check if curl is installed
if ! command -v curl &> /dev/null; then
    print_status $RED "Error: curl is required for API testing. Please install curl."
    exit 1
fi

# Run tests
main
