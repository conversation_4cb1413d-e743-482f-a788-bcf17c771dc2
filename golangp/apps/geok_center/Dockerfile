# 使用 ubuntu 作为基础镜像
FROM ubuntu:22.04

# 设置非交互模式
ENV DEBIAN_FRONTEND=noninteractive

# 安装必要的系统依赖
RUN apt-get update && \
    apt-get install -y ca-certificates tzdata && \
    rm -rf /var/lib/apt/lists/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用目录
WORKDIR /app

# 复制二进制文件并设置权限
COPY geok_center .
RUN ls -la /app && \
    chmod +x /app/geok_center

# 复制配置文件到正确的位置
COPY app.env /app/app.env

# 暴露端口
EXPOSE 8012

# 设置环境变量
ENV ENVIRONMENT=production

# 运行应用
CMD ["/app/geok_center", "server"]
