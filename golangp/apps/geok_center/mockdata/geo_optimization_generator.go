/*
 * @Description: GEO optimization mock data generator based on AI search data
 * @Author: AI Assistant
 * @Date: 2025-07-21
 */
package mockdata

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/apps/geok_center/internal/services"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// GEOOptimizationGenerator generates mock GEO optimization data based on AI search data
type GEOOptimizationGenerator struct {
	db                       *gorm.DB
	keywordExtractionService *services.KeywordExtractionService
}

// NewGEOOptimizationGenerator creates a new GEO optimization generator
func NewGEOOptimizationGenerator(db *gorm.DB) *GEOOptimizationGenerator {
	return &GEOOptimizationGenerator{
		db:                       db,
		keywordExtractionService: services.NewKeywordExtractionService(),
	}
}

// GenerateGEOOptimizationsForBrand generates GEO optimization records for a brand based on AI search data
func (g *GEOOptimizationGenerator) GenerateGEOOptimizationsForBrand(brandID uuid.UUID) error {
	// Get AI searches for this brand
	var aiSearches []models.AISearch
	if err := g.db.Where("brand_id = ?", brandID).Find(&aiSearches).Error; err != nil {
		return fmt.Errorf("failed to get AI searches for brand %s: %w", brandID, err)
	}

	if len(aiSearches) == 0 {
		fmt.Printf("No AI searches found for brand %s, skipping GEO optimization generation\n", brandID)
		return nil
	}

	// Group AI searches by region
	regionSearches := make(map[string][]models.AISearch)
	for _, search := range aiSearches {
		region := search.Region
		if region == "" {
			region = g.getRandomRegion()
		}
		regionSearches[region] = append(regionSearches[region], search)
	}

	// Generate GEO optimization for each region
	for region, searches := range regionSearches {
		if err := g.generateGEOOptimizationForRegion(brandID, region, searches); err != nil {
			fmt.Printf("Failed to generate GEO optimization for brand %s region %s: %v\n", brandID, region, err)
			continue
		}
	}

	return nil
}

// generateGEOOptimizationForRegion generates a GEO optimization record for a specific region
func (g *GEOOptimizationGenerator) generateGEOOptimizationForRegion(brandID uuid.UUID, region string, aiSearches []models.AISearch) error {
	// Extract keywords from AI search data
	keywordAnalysis, err := g.keywordExtractionService.ExtractKeywordsFromBrand(brandID, region)
	if err != nil {
		return fmt.Errorf("failed to extract keywords: %w", err)
	}

	// Generate mock data based on AI search analysis
	geoOptimization := &models.GEOOptimization{
		ID:                  uuid.New(),
		BrandID:             brandID,
		Region:              region,
		Country:             g.getCountryFromRegion(region),
		Status:              models.GEOOptimizationStatusActive,
		Priority:            rand.Intn(5) + 1,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
		LastAnalyzedAt:      time.Now(),
		DataVersion:         1,
		SourceAISearchCount: len(aiSearches),
	}

	// Generate brand overview
	brandOverview := g.generateBrandOverview(aiSearches)
	if err := geoOptimization.SetBrandOverview(brandOverview); err != nil {
		return fmt.Errorf("failed to set brand overview: %w", err)
	}

	// Generate advantages
	advantages := g.generateAdvantages(aiSearches)
	if err := geoOptimization.SetAdvantages(advantages); err != nil {
		return fmt.Errorf("failed to set advantages: %w", err)
	}

	// Generate weaknesses
	weaknesses := g.generateWeaknesses(aiSearches)
	if err := geoOptimization.SetWeaknesses(weaknesses); err != nil {
		return fmt.Errorf("failed to set weaknesses: %w", err)
	}

	// Set other JSON fields
	expertiseAreas := g.generateExpertiseAreas(keywordAnalysis)
	expertiseJSON, _ := json.Marshal(expertiseAreas)
	geoOptimization.MainExpertiseAreas = expertiseJSON

	commonAssociations := g.generateCommonAssociations(keywordAnalysis)
	associationsJSON, _ := json.Marshal(commonAssociations)
	geoOptimization.CommonAssociations = associationsJSON

	contentStrategy := g.generateContentStrategy(region, keywordAnalysis)
	strategyJSON, _ := json.Marshal(contentStrategy)
	geoOptimization.ContentStrategyRecommendations = strategyJSON

	competitorAnalysis := g.generateCompetitorAnalysis()
	competitorJSON, _ := json.Marshal(competitorAnalysis)
	geoOptimization.CompetitorAnalysis = competitorJSON

	referencesData := g.generateReferencesData()
	referencesJSON, _ := json.Marshal(referencesData)
	geoOptimization.ReferencesData = referencesJSON

	statistics := g.generateStatistics(aiSearches, keywordAnalysis)
	statisticsJSON, _ := json.Marshal(statistics)
	geoOptimization.Statistics = statisticsJSON

	keywordsJSON, _ := json.Marshal(keywordAnalysis)
	geoOptimization.ExtractedKeywords = keywordsJSON

	// Calculate performance metrics
	geoOptimization.VisibilityScore = g.calculateMockVisibilityScore(len(aiSearches))
	geoOptimization.MarketPenetration = g.calculateMockMarketPenetration(region)
	geoOptimization.SearchVolume = g.calculateMockSearchVolume(keywordAnalysis)
	geoOptimization.MentionFrequency = int64(len(aiSearches))
	geoOptimization.SentimentScore = g.calculateMockSentimentScore(aiSearches)
	geoOptimization.CompetitionLevel = g.determineMockCompetitionLevel(region)
	geoOptimization.OpportunityScore = g.calculateMockOpportunityScore(geoOptimization)

	// Save to database
	if err := g.db.Create(geoOptimization).Error; err != nil {
		return fmt.Errorf("failed to create GEO optimization: %w", err)
	}

	// Create GEO database entries for this optimization
	if err := g.createGEODatabaseEntries(brandID, *geoOptimization); err != nil {
		fmt.Printf("Failed to create GEO database entries for brand %s region %s: %v\n", brandID, region, err)
		// Don't return error, just log it
	}

	fmt.Printf("Generated GEO optimization for brand %s in region %s\n", brandID, region)
	return nil
}

// Helper methods for generating mock data

// generateBrandOverview generates mock brand overview data
func (g *GEOOptimizationGenerator) generateBrandOverview(aiSearches []models.AISearch) *models.BrandOverviewData {
	return &models.BrandOverviewData{
		Positioning:    "技术领先的创新品牌",
		CoreValues:     []string{"创新", "质量", "可靠性", "用户体验"},
		TargetAudience: "技术专业人士和企业用户",
		MarketSegment:  "高端技术产品市场",
		Description:    fmt.Sprintf("基于%d个AI搜索问题分析得出的品牌概述", len(aiSearches)),
	}
}

// generateAdvantages generates mock advantages data
func (g *GEOOptimizationGenerator) generateAdvantages(aiSearches []models.AISearch) *models.AdvantagesData {
	return &models.AdvantagesData{
		TechnicalAdvantages: []string{"先进的技术架构", "高性能处理能力", "稳定的系统表现"},
		MarketAdvantages:    []string{"强大的品牌认知度", "广泛的市场覆盖", "良好的客户口碑"},
		ProductAdvantages:   []string{"丰富的产品线", "优秀的产品设计", "持续的产品创新"},
		ServiceAdvantages:   []string{"专业的技术支持", "快速的响应时间", "全面的售后服务"},
		OverallStrengths:    []string{"行业领导地位", "技术创新能力", "客户满意度高"},
	}
}

// generateWeaknesses generates mock weaknesses data
func (g *GEOOptimizationGenerator) generateWeaknesses(aiSearches []models.AISearch) *models.WeaknessesData {
	return &models.WeaknessesData{
		TechnicalWeaknesses: []string{"某些新技术采用较慢", "部分系统兼容性问题"},
		MarketWeaknesses:    []string{"在某些细分市场渗透不足", "价格竞争力有待提升"},
		ProductWeaknesses:   []string{"产品线复杂度较高", "学习曲线较陡"},
		ServiceWeaknesses:   []string{"部分地区服务覆盖不足", "多语言支持有限"},
		ImprovementAreas:    []string{"用户界面优化", "成本控制", "市场教育"},
	}
}

// generateExpertiseAreas generates mock expertise areas
func (g *GEOOptimizationGenerator) generateExpertiseAreas(keywordAnalysis *services.KeywordAnalysisResult) *models.ExpertiseAreasData {
	primaryAreas := []string{"云计算", "人工智能", "大数据分析", "网络安全"}
	if len(keywordAnalysis.TrendingKeywords) > 0 {
		primaryAreas = append(primaryAreas, keywordAnalysis.TrendingKeywords[:min(3, len(keywordAnalysis.TrendingKeywords))]...)
	}

	return &models.ExpertiseAreasData{
		PrimaryAreas:   primaryAreas,
		SecondaryAreas: []string{"物联网", "区块链", "边缘计算", "机器学习"},
		EmergingAreas:  []string{"量子计算", "增强现实", "自动驾驶", "数字孪生"},
		Specialties:    []string{"企业级解决方案", "高可用性系统", "数据安全", "性能优化"},
	}
}

// generateCommonAssociations generates mock common associations
func (g *GEOOptimizationGenerator) generateCommonAssociations(keywordAnalysis *services.KeywordAnalysisResult) *models.CommonAssociationsData {
	return &models.CommonAssociationsData{
		PositiveAssociations: []string{"可靠", "高效", "创新", "专业", "领先"},
		NeutralAssociations:  []string{"技术", "解决方案", "平台", "系统", "服务"},
		NegativeAssociations: []string{"复杂", "昂贵", "学习难度"},
		FrequentPairs:        []string{"技术领先", "性能优异", "服务专业", "解决方案完整"},
		ContextualWords:      []string{"企业级", "云原生", "智能化", "数字化转型"},
	}
}

// generateContentStrategy generates mock content strategy
func (g *GEOOptimizationGenerator) generateContentStrategy(region string, keywordAnalysis *services.KeywordAnalysisResult) *models.ContentStrategyData {
	strategy := &models.ContentStrategyData{
		RecommendedTopics:  []string{"技术趋势分析", "最佳实践分享", "案例研究", "产品更新"},
		ContentTypes:       []string{"技术白皮书", "视频教程", "在线研讨会", "博客文章"},
		TargetKeywords:     keywordAnalysis.TrendingKeywords,
		PublishingChannels: []string{"官方网站", "技术社区", "社交媒体", "行业媒体"},
		OptimalTiming:      []string{"工作日上午9-11点", "技术会议期间", "产品发布前后"},
		LocalizationTips:   g.getRegionSpecificTips(region),
	}

	return strategy
}

// generateCompetitorAnalysis generates mock competitor analysis
func (g *GEOOptimizationGenerator) generateCompetitorAnalysis() *models.CompetitorAnalysisData {
	return &models.CompetitorAnalysisData{
		MainCompetitors: []string{"竞争对手A", "竞争对手B", "竞争对手C"},
		CompetitorStrengths: map[string][]string{
			"竞争对手A": {"价格优势", "市场份额大"},
			"竞争对手B": {"技术创新", "用户体验好"},
			"竞争对手C": {"生态系统完整", "合作伙伴多"},
		},
		CompetitorWeaknesses: map[string][]string{
			"竞争对手A": {"技术相对落后", "服务质量不稳定"},
			"竞争对手B": {"价格较高", "市场覆盖有限"},
			"竞争对手C": {"产品复杂度高", "学习成本大"},
		},
		MarketPositioning: map[string]string{
			"竞争对手A": "成本领导者",
			"竞争对手B": "技术创新者",
			"竞争对手C": "生态系统构建者",
		},
		CompetitiveAdvantage: []string{"技术领先性", "服务质量", "客户关系", "品牌影响力"},
	}
}

// generateReferencesData generates mock references data
func (g *GEOOptimizationGenerator) generateReferencesData() *models.ReferencesData {
	return &models.ReferencesData{
		SourceDomains: []string{"tech.example.com", "industry.example.com", "news.example.com"},
		AuthorityScores: map[string]float64{
			"tech.example.com":     0.85,
			"industry.example.com": 0.78,
			"news.example.com":     0.72,
		},
		CitationCount: rand.Intn(100) + 50,
		QualityMetrics: map[string]float64{
			"accuracy":     0.88,
			"completeness": 0.82,
			"timeliness":   0.79,
		},
		TrustScores: map[string]float64{
			"overall":    0.83,
			"technical":  0.87,
			"commercial": 0.79,
		},
	}
}

// generateStatistics generates mock statistics
func (g *GEOOptimizationGenerator) generateStatistics(aiSearches []models.AISearch, keywordAnalysis *services.KeywordAnalysisResult) *models.StatisticsData {
	totalSearches := int64(len(aiSearches))
	positiveMentions := totalSearches * 60 / 100 // 60% positive
	negativeMentions := totalSearches * 15 / 100 // 15% negative
	neutralMentions := totalSearches - positiveMentions - negativeMentions

	return &models.StatisticsData{
		TotalSearches:    totalSearches,
		TotalMentions:    totalSearches * 2, // Assume 2 mentions per search on average
		PositiveMentions: positiveMentions,
		NegativeMentions: negativeMentions,
		NeutralMentions:  neutralMentions,
		AveragePosition:  float64(rand.Intn(3) + 1), // Position 1-3
		TopQuestions:     g.extractTopQuestions(aiSearches),
		ResponseDistribution: map[string]int64{
			"技术问题": totalSearches * 40 / 100,
			"产品咨询": totalSearches * 30 / 100,
			"价格比较": totalSearches * 20 / 100,
			"其他":   totalSearches * 10 / 100,
		},
		TimeSeriesData: map[string]int64{
			"2024-01": totalSearches * 20 / 100,
			"2024-02": totalSearches * 25 / 100,
			"2024-03": totalSearches * 30 / 100,
			"2024-04": totalSearches * 25 / 100,
		},
	}
}

// Helper utility methods

// getRandomRegion returns a random region
func (g *GEOOptimizationGenerator) getRandomRegion() string {
	regions := []string{"北美", "欧洲", "亚太", "南美", "非洲", "中东"}
	return regions[rand.Intn(len(regions))]
}

// getCountryFromRegion maps region to country
func (g *GEOOptimizationGenerator) getCountryFromRegion(region string) string {
	regionMap := map[string]string{
		"北美": "美国", "欧洲": "德国", "亚太": "中国",
		"南美": "巴西", "非洲": "南非", "中东": "阿联酋",
	}
	if country, exists := regionMap[region]; exists {
		return country
	}
	return "未知"
}

// getRegionSpecificTips returns region-specific localization tips
func (g *GEOOptimizationGenerator) getRegionSpecificTips(region string) []string {
	switch region {
	case "北美":
		return []string{"强调技术创新和ROI", "注重数据驱动的决策", "突出企业级安全性"}
	case "欧洲":
		return []string{"重视GDPR合规性", "强调可持续发展", "注重本地化支持"}
	case "亚太":
		return []string{"关注性价比和实用性", "重视移动端体验", "适应快速变化的市场"}
	default:
		return []string{"根据当地文化调整内容", "注重本地语言支持", "考虑时区差异"}
	}
}

// extractTopQuestions extracts top questions from AI searches
func (g *GEOOptimizationGenerator) extractTopQuestions(aiSearches []models.AISearch) []string {
	questions := make([]string, 0, min(10, len(aiSearches)))
	for i, search := range aiSearches {
		if i >= 10 {
			break
		}
		questions = append(questions, search.Question)
	}
	return questions
}

// Performance calculation methods for mock data

// calculateMockVisibilityScore calculates mock visibility score
func (g *GEOOptimizationGenerator) calculateMockVisibilityScore(searchCount int) float64 {
	baseScore := float64(searchCount) * 1.5
	if baseScore > 95 {
		baseScore = 95
	}
	// Add some randomness
	return baseScore + rand.Float64()*10 - 5
}

// calculateMockMarketPenetration calculates mock market penetration
func (g *GEOOptimizationGenerator) calculateMockMarketPenetration(region string) float64 {
	// Different regions have different base penetration
	basePenetration := map[string]float64{
		"北美": 75.0, "欧洲": 68.0, "亚太": 82.0,
		"南美": 45.0, "非洲": 35.0, "中东": 55.0,
	}

	base := basePenetration[region]
	if base == 0 {
		base = 50.0
	}

	// Add randomness
	return base + rand.Float64()*20 - 10
}

// calculateMockSearchVolume calculates mock search volume
func (g *GEOOptimizationGenerator) calculateMockSearchVolume(keywordAnalysis *services.KeywordAnalysisResult) int64 {
	total := int64(0)
	for _, volume := range keywordAnalysis.SearchVolumes {
		total += volume
	}

	if total == 0 {
		// Generate random volume if no data
		return int64(rand.Intn(10000) + 1000)
	}

	return total
}

// calculateMockSentimentScore calculates mock sentiment score
func (g *GEOOptimizationGenerator) calculateMockSentimentScore(aiSearches []models.AISearch) float64 {
	// Simple sentiment analysis based on question content
	positive := 0
	negative := 0

	positiveWords := []string{"好", "优秀", "推荐", "值得", "满意"}
	negativeWords := []string{"差", "问题", "不好", "失望", "糟糕"}

	for _, search := range aiSearches {
		for _, word := range positiveWords {
			if contains(search.Question, word) {
				positive++
				break
			}
		}
		for _, word := range negativeWords {
			if contains(search.Question, word) {
				negative++
				break
			}
		}
	}

	if positive+negative == 0 {
		return 0.1 // Slightly positive default
	}

	return float64(positive-negative) / float64(positive+negative)
}

// determineMockCompetitionLevel determines mock competition level
func (g *GEOOptimizationGenerator) determineMockCompetitionLevel(region string) string {
	// Different regions have different competition levels
	competitionMap := map[string]string{
		"北美": models.CompetitionLevelVeryHigh,
		"欧洲": models.CompetitionLevelHigh,
		"亚太": models.CompetitionLevelVeryHigh,
		"南美": models.CompetitionLevelMedium,
		"非洲": models.CompetitionLevelLow,
		"中东": models.CompetitionLevelMedium,
	}

	if level, exists := competitionMap[region]; exists {
		return level
	}

	return models.CompetitionLevelMedium
}

// calculateMockOpportunityScore calculates mock opportunity score
func (g *GEOOptimizationGenerator) calculateMockOpportunityScore(geo *models.GEOOptimization) float64 {
	// Weighted calculation
	visibilityWeight := 0.3
	marketWeight := 0.3
	sentimentWeight := 0.2
	competitionWeight := 0.2

	// Normalize sentiment score
	normalizedSentiment := (geo.SentimentScore + 1) * 5

	// Competition level to score
	competitionScore := 10.0
	switch geo.CompetitionLevel {
	case models.CompetitionLevelLow:
		competitionScore = 8.0
	case models.CompetitionLevelMedium:
		competitionScore = 6.0
	case models.CompetitionLevelHigh:
		competitionScore = 4.0
	case models.CompetitionLevelVeryHigh:
		competitionScore = 2.0
	}

	score := (geo.VisibilityScore/10)*visibilityWeight +
		(geo.MarketPenetration/10)*marketWeight +
		normalizedSentiment*sentimentWeight +
		competitionScore*competitionWeight

	return score
}

// Utility functions

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr || len(substr) == 0 ||
			(len(s) > len(substr) && (s[:len(substr)] == substr ||
				s[len(s)-len(substr):] == substr ||
				containsSubstring(s, substr))))
}

// containsSubstring checks if string contains substring
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// min returns the minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// createGEODatabaseEntries creates GEO database entries for a GEO optimization
func (g *GEOOptimizationGenerator) createGEODatabaseEntries(brandID uuid.UUID, geo models.GEOOptimization) error {
	// Get brand information
	var brand models.Brand
	if err := g.db.First(&brand, "id = ?", brandID).Error; err != nil {
		return fmt.Errorf("failed to get brand: %w", err)
	}

	// Sample GEO database entries for different content types
	geoDBEntries := []models.GEODatabase{
		{
			ID:                uuid.New(),
			BrandID:           brandID,
			GeoOptimizationID: geo.ID,
			ContentType:       models.GEOContentTypeSearchResult,
			Title:             fmt.Sprintf("%s在%s的搜索结果分析", brand.Name, geo.Region),
			Content:           fmt.Sprintf("基于AI搜索的%s品牌在%s地区的表现分析，可见性评分为%.1f", brand.Name, geo.Region, geo.VisibilityScore),
			Region:            geo.Region,
			Country:           geo.Country,
			Language:          "zh",
			SearchCount:       3000,
			MentionRate:       0.05,
		},
		{
			ID:                uuid.New(),
			BrandID:           brandID,
			GeoOptimizationID: geo.ID,
			ContentType:       models.GEOContentTypeKeywordAnalysis,
			Title:             fmt.Sprintf("%s关键词分析 - %s", brand.Name, geo.Region),
			Content:           fmt.Sprintf("AI模型对%s品牌在%s地区相关问题的关键词分析，情感评分为%.2f", brand.Name, geo.Region, geo.SentimentScore),
			Region:            geo.Region,
			Country:           geo.Country,
			Language:          "zh",
			SearchCount:       8000,
			MentionRate:       0.5,
		},
		{
			ID:                uuid.New(),
			BrandID:           brandID,
			GeoOptimizationID: geo.ID,
			ContentType:       models.GEOContentTypeMarketInsight,
			Title:             fmt.Sprintf("%s市场洞察报告 - %s", brand.Name, geo.Region),
			Content:           fmt.Sprintf("%s品牌在%s地区的市场渗透率为%.1f%%，竞争水平为%s", brand.Name, geo.Region, geo.MarketPenetration, geo.CompetitionLevel),
			Region:            geo.Region,
			Country:           geo.Country,
			Language:          "zh",
			SearchCount:       81000,
			MentionRate:       0.15,
		},
	}

	// Save all entries to database
	for _, entry := range geoDBEntries {
		if err := g.db.Create(&entry).Error; err != nil {
			fmt.Printf("Failed to create GEO database entry: %v\n", err)
			continue
		}
	}

	fmt.Printf("Created %d GEO database entries for brand %s in region %s\n", len(geoDBEntries), brand.Name, geo.Region)
	return nil
}
