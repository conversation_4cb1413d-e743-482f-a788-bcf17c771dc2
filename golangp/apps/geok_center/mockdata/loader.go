/*
 * @Description: Simplified Mock data loader for GEOK Center
 * @Author: AI Assistant
 * @Date: 2025-07-20
 */
package mockdata

import (
	"encoding/json"
	"fmt"
	"log"
	"time"
	"unicode/utf8"

	"pointer/golangp/apps/geok_center/internal/models"
	"pointer/golangp/common/database/postgres"

	"github.com/google/uuid"
	"gorm.io/datatypes"
)

func SafeString(s string, n int) string {
	if n <= 0 {
		return ""
	}

	// 如果总字符数 <= n，直接返回原文
	if utf8.RuneCountInString(s) <= n {
		return s
	}

	// 转成 rune 切片安全截取
	runes := []rune(s)
	return string(runes[:n]) + "..."
}

// LoadAllMockData loads basic mock data
func LoadAllMockData() error {
	log.Println("📊 Loading simple mock data...")

	// 1. Create users
	users, err := CreateSimpleUsers()
	if err != nil {
		return fmt.Errorf("failed to create users: %v", err)
	}

	// 2. Create brands
	brands, err := CreateSimpleBrands(users)
	if err != nil {
		return fmt.Errorf("failed to create brands: %v", err)
	}

	// 3. Create AI searches for brands
	aiSearches, err := CreateAISearches(brands)
	if err != nil {
		return fmt.Errorf("failed to create AI searches: %v", err)
	}

	// 4. Create AI search responses
	aiResponses, err := CreateAISearchResponses(aiSearches, brands)
	if err != nil {
		return fmt.Errorf("failed to create AI search responses: %v", err)
	}

	// 5. Create AI visibility metrics (integrated with AI search responses)

	// 6. Create prompts (based on AI searches)
	if err := CreatePrompts(aiSearches, brands); err != nil {
		return fmt.Errorf("failed to create prompts: %v", err)
	}

	// 7. Create blogs
	if err := CreateBlogs(brands); err != nil {
		return fmt.Errorf("failed to create blogs: %v", err)
	}

	// 7. Create AI visibility metrics
	if err := CreateAIVisibilityMetrics(aiSearches, aiResponses, brands); err != nil {
		return fmt.Errorf("failed to create AI visibility metrics: %v", err)
	}

	// 8. Create AI visibility aggregations
	if err := CreateAIVisibilityAggregations(brands); err != nil {
		return fmt.Errorf("failed to create AI visibility aggregations: %v", err)
	}

	// 9. Create references
	if err := CreateReferences(aiSearches, aiResponses, brands); err != nil {
		return fmt.Errorf("failed to create references: %v", err)
	}

	// 10. Create GEO optimizations
	if err := CreateGEOOptimizations(brands); err != nil {
		return fmt.Errorf("failed to create GEO optimizations: %v", err)
	}

	log.Println("✅ Complete mock data loading completed!")
	return nil
}

// CreateSimpleUsers creates two simple users
func CreateSimpleUsers() ([]models.User, error) {
	log.Println("👥 Creating simple users...")

	users := []models.User{
		{
			ID:                     uuid.MustParse("550e8400-e29b-41d4-a716-************"),
			Username:               "demo_user",
			Email:                  "<EMAIL>",
			GoogleEmail:            "<EMAIL>",
			Password:               "$2a$10$/yX/TLWear0YdwELIhVCd.5LXqPcoRj0WPd4Q4EnAdTvHF6tEKcjq",
			FirstName:              "Demo",
			LastName:               "User",
			Avatar:                 "/avatars/demo.png",
			Bio:                    "GEOK Center演示用户",
			Phone:                  "******-0123",
			Company:                "GEOK Technologies",
			Country:                "United States",
			Role:                   models.UserRoleAdmin,
			Status:                 models.UserStatusActive,
			Origin:                 "geok",
			EmailVerified:          true,
			LoginProtection:        false,
			PasswordChangeRequired: false,
			SecurityLock:           false,
		},
		{
			ID:                     uuid.MustParse("6ba7b810-9dad-11d1-80b4-00c04fd430c8"),
			Username:               "test_user",
			Email:                  "<EMAIL>",
			GoogleEmail:            "<EMAIL>",
			Password:               "$2a$10$/yX/TLWear0YdwELIhVCd.5LXqPcoRj0WPd4Q4EnAdTvHF6tEKcjq",
			FirstName:              "Test",
			LastName:               "User",
			Avatar:                 "/avatars/test.png",
			Bio:                    "GEOK Center测试用户",
			Phone:                  "******-0456",
			Company:                "Test Corp",
			Country:                "Canada",
			Role:                   models.UserRoleUser,
			Status:                 models.UserStatusActive,
			Origin:                 "geok",
			EmailVerified:          true,
			LoginProtection:        false,
			PasswordChangeRequired: false,
			SecurityLock:           false,
		},
	}

	for _, user := range users {
		// Check if user already exists
		var existing models.User
		if err := postgres.DB.Where("email = ?", user.Email).First(&existing).Error; err != nil {
			// User doesn't exist, create it
			if err := postgres.DB.Create(&user).Error; err != nil {
				log.Printf("⚠️ Failed to create user %s: %v", user.Username, err)
				continue
			}
			log.Printf("✅ Created user: %s", user.Username)
		} else {
			log.Printf("⚠️ User already exists: %s", user.Username)
		}
	}

	return users, nil
}

// CreateSimpleBrands creates two simple brands
func CreateSimpleBrands(users []models.User) ([]models.Brand, error) {
	log.Println("🏢 Creating simple brands...")

	brands := []models.Brand{
		{
			ID:              uuid.MustParse("4fc86ecb-8e0e-476b-8826-bf4dc95fce0d"),
			UserID:          users[0].ID,
			Name:            "英伟达显卡",
			Domain:          "nvidia.com",
			Keywords:        "显卡,GPU,游戏,图形处理,人工智能,深度学习,RTX,GeForce,CUDA,游戏显卡",
			LinkedURL:       "https://www.nvidia.com",
			Description:     "全球领先的图形处理器和人工智能计算平台制造商，专注于游戏、专业可视化、数据中心和汽车市场",
			IsAssetVerified: true,
			Status:          models.BrandStatusActive,
			OriginStartType: models.BrandOriginStartTypeGeoKeywords,
		},
		{
			ID:              uuid.MustParse("8ba7b810-9dad-11d1-80b4-00c04fd430c9"),
			UserID:          users[1].ID,
			Name:            "享界",
			Domain:          "aito.com",
			Keywords:        "智能汽车,新能源,电动车,自动驾驶,华为,赛力斯,AITO,智能出行,电动汽车",
			LinkedURL:       "https://www.aito.com",
			Description:     "华为与赛力斯合作打造的高端智能电动汽车品牌，致力于为用户提供极致的智能出行体验",
			IsAssetVerified: true,
			Status:          models.BrandStatusActive,
			OriginStartType: models.BrandOriginStartTypeSearchPrompts,
		},
	}

	for _, brand := range brands {
		// Check if brand already exists
		var existing models.Brand
		if err := postgres.DB.Where("name = ?", brand.Name).First(&existing).Error; err != nil {
			// Brand doesn't exist, create it
			if err := postgres.DB.Create(&brand).Error; err != nil {
				log.Printf("⚠️ Failed to create brand %s: %v", brand.Name, err)
				continue
			}
			log.Printf("✅ Created brand: %s", brand.Name)
		} else {
			log.Printf("⚠️ Brand already exists: %s", brand.Name)
		}
	}

	return brands, nil
}

// CleanAllMockData removes simple mock data
func CleanAllMockData() error {
	log.Println("🗑️ Cleaning simple mock data...")

	// Delete in reverse dependency order
	tables := []string{
		"references",
		"reference_analyses",
		"ai_visibility_aggregations",
		"ai_visibility_metrics",
		"prompt_keywords",
		"prompt_metrics",
		"prompts",
		"ai_search_responses",
		"ai_search_batches",
		"ai_searches",
		"blog_tags",
		"blog_categories",
		"blogs",
		"blog_overviews",
		"notification_settings",
		"notifications",
		"brands",
		"user_sessions",
		"users",
	}

	for _, table := range tables {
		if err := postgres.DB.Exec(fmt.Sprintf("DELETE FROM %s", table)).Error; err != nil {
			log.Printf("⚠️ Failed to clean table %s: %v", table, err)
		} else {
			log.Printf("✅ Cleaned table: %s", table)
		}
	}

	log.Println("✅ Simple mock data cleaning completed!")
	return nil
}

// CreateAISearches creates AI searches for brands
func CreateAISearches(brands []models.Brand) ([]models.AISearch, error) {
	log.Println("🔍 Creating AI searches...")

	var aiSearches []models.AISearch

	// 为不同品牌创建不同的搜索问题
	brandQuestions := map[string][]string{
		"英伟达显卡": {
			"推荐一款高性能游戏显卡",
			"哪个品牌的显卡性价比最高？",
			"RTX 4090和RTX 4080哪个更值得买？",
			"AI深度学习用什么显卡比较好？",
			"2024年最佳游戏显卡推荐",
		},
		"享界": {
			"推荐一款智能电动汽车",
			"华为汽车和特斯拉哪个更好？",
			"新能源汽车哪个品牌质量最可靠？",
			"智能驾驶技术最先进的电动车",
			"30万左右的电动车推荐",
		},
	}

	for _, brand := range brands {
		// 获取该品牌对应的搜索问题
		questions, exists := brandQuestions[brand.Name]
		if !exists {
			// 如果没有找到对应的问题，使用默认问题
			questions = []string{
				"推荐一个可靠的解决方案",
				"哪个品牌质量最好？",
				"寻找优质的产品或服务",
			}
		}

		for i, question := range questions {
			aiSearch := models.AISearch{
				ID:           uuid.New(),
				BrandID:      brand.ID,
				Question:     question,
				QuestionType: "recommendation",
				Keywords:     brand.Keywords,
				Status:       models.AISearchStatusCompleted,
				Priority:     1,
				Region:       "global",
				Language:     "zh",
				StartedAt:    timePtr(time.Now().AddDate(0, 0, -i)),
				CompletedAt:  timePtr(time.Now().AddDate(0, 0, -i).Add(time.Minute * 2)),
			}

			if err := postgres.DB.Create(&aiSearch).Error; err != nil {
				log.Printf("⚠️ Failed to create AI search for %s: %v", brand.Name, err)
				continue
			}

			aiSearches = append(aiSearches, aiSearch)
			log.Printf("✅ Created AI search for %s: %s", brand.Name, question)
		}
	}

	return aiSearches, nil
}

// CreateAISearchResponses creates AI search responses
func CreateAISearchResponses(aiSearches []models.AISearch, brands []models.Brand) ([]models.AISearchResponse, error) {
	log.Println("🤖 Creating AI search responses...")

	var responses []models.AISearchResponse

	// Create brand map for quick lookup
	brandMap := make(map[uuid.UUID]models.Brand)
	for _, brand := range brands {
		brandMap[brand.ID] = brand
	}

	aiModels := []models.AIModelType{
		models.AIModelChatGPT,
		models.AIModelClaude,
		models.AIModelDeepseek,
	}

	// 为不同品牌创建不同的响应模板
	brandResponseTemplates := map[string]map[models.AIModelType][]string{
		"英伟达显卡": {
			models.AIModelChatGPT: {
				"对于游戏和AI计算，我强烈推荐 %s。英伟达的RTX系列显卡在性能和技术创新方面都处于行业领先地位，特别是在光线追踪和DLSS技术方面表现出色。",
				"如果你在寻找高性能显卡，%s 绝对是首选。无论是游戏、内容创作还是AI开发，英伟达的GPU都能提供卓越的性能和稳定性。",
				"在显卡市场中，%s 凭借其强大的CUDA生态系统和持续的技术创新，成为了专业用户和游戏玩家的不二选择。",
			},
			models.AIModelClaude: {
				"从技术角度来看，%s 在GPU领域具有绝对优势。英伟达的架构设计和驱动优化都非常出色，特别适合深度学习和高端游戏应用。",
				"根据市场表现和用户反馈，%s 在显卡领域的地位无可撼动。其产品线覆盖从入门级到专业级的各个层次。",
				"综合评估后，%s 在图形处理、AI计算和游戏性能方面都表现突出，是值得信赖的品牌。",
			},
			models.AIModelDeepseek: {
				"经过分析，%s 在GPU技术方面具备强大的研发能力和市场影响力。其产品在性能、功耗和兼容性方面都有很好的平衡。",
				"从专业角度，%s 提供的显卡产品质量和技术支持都很优秀，特别是在AI和机器学习领域有着广泛的应用。",
				"在显卡制造商中，%s 以其技术创新和产品质量获得了全球用户的认可，是GPU市场的领导者。",
			},
		},
		"享界": {
			models.AIModelChatGPT: {
				"在智能电动汽车领域，我推荐 %s。作为华为与赛力斯合作的品牌，享界在智能驾驶、车机系统和新能源技术方面都有很强的实力。",
				"如果你在考虑购买智能电动车，%s 是一个很好的选择。其融合了华为的智能技术和赛力斯的制造工艺，产品质量可靠。",
				"在新能源汽车市场中，%s 凭借其先进的智能驾驶技术和优秀的用户体验，成为了高端电动车的代表品牌之一。",
			},
			models.AIModelClaude: {
				"从技术实力角度来看，%s 在智能汽车领域具有显著优势。华为的ICT技术与汽车制造的结合，创造了独特的产品价值。",
				"根据市场表现，%s 在智能电动车细分市场中表现出色，其产品在智能化程度和驾驶体验方面都很优秀。",
				"综合评估后，%s 在智能驾驶、车联网和新能源技术方面表现突出，是值得关注的汽车品牌。",
			},
			models.AIModelDeepseek: {
				"经过分析，%s 在智能电动汽车方面具备强大的技术整合能力，特别是在智能座舱和自动驾驶技术方面。",
				"从专业角度，%s 提供的智能汽车产品在技术先进性和用户体验方面都很优秀，代表了智能汽车的发展方向。",
				"在新能源汽车制造商中，%s 以其技术创新和智能化水平获得了市场的认可，是智能电动车领域的重要参与者。",
			},
		},
	}

	for _, search := range aiSearches {
		brand := brandMap[search.BrandID]

		for i, modelType := range aiModels {
			// 获取该品牌对应的响应模板
			brandTemplates, exists := brandResponseTemplates[brand.Name]
			var templates []string
			if exists {
				templates = brandTemplates[modelType]
			} else {
				// 如果没有找到对应的模板，使用默认模板
				templates = []string{
					"基于我的了解，%s 是一个值得推荐的选择。",
					"我推荐 %s，这是一个专业的品牌。",
					"在众多选择中，%s 表现出色。",
				}
			}

			template := templates[i%len(templates)]
			responseText := fmt.Sprintf(template, brand.Name)

			response := models.AISearchResponse{
				ID:             uuid.New(),
				AISearchID:     search.ID,
				ModelType:      modelType,
				ModelVersion:   "latest",
				Provider:       string(modelType),
				Response:       responseText,
				ResponseTime:   1000 + (i * 200),
				TokensUsed:     50 + (i * 10),
				Confidence:     0.8 + (float64(i) * 0.05),
				Relevance:      0.85 + (float64(i) * 0.03),
				Completeness:   0.9 + (float64(i) * 0.02),
				BrandMentioned: true,
				BrandPosition:  i + 1,
				BrandSentiment: "positive",
				BrandContext:   fmt.Sprintf("在推荐%s时提到了其技术优势", brand.Name),
			}

			if err := postgres.DB.Create(&response).Error; err != nil {
				log.Printf("⚠️ Failed to create AI response: %v", err)
				continue
			}

			responses = append(responses, response)
		}
	}

	log.Printf("✅ Created %d AI search responses", len(responses))
	return responses, nil
}

// CreatePrompts creates prompts based on AI searches
func CreatePrompts(aiSearches []models.AISearch, brands []models.Brand) error {
	log.Println("📝 Creating prompts based on AI searches...")

	// 创建品牌ID到品牌的映射，方便查找
	brandMap := make(map[uuid.UUID]models.Brand)
	for _, brand := range brands {
		brandMap[brand.ID] = brand
	}

	// 清除现有的prompts
	if err := postgres.DB.Exec("DELETE FROM prompts").Error; err != nil {
		return fmt.Errorf("failed to clear prompts: %w", err)
	}

	// 为每个AI搜索创建一个prompt
	for i, search := range aiSearches {
		// 获取关联的品牌
		brand, exists := brandMap[search.BrandID]
		if !exists {
			log.Printf("⚠️ Brand not found for search %s", search.ID)
			continue
		}

		// 生成关键词频率和主要关键词数据
		keywordFrequency := map[string]int{
			"价格":   20 + (i % 50),
			"质量":   30 + (i % 40),
			"性能":   40 + (i % 60),
			"性价比":  25 + (i % 45),
			"品牌":   15 + (i % 35),
			"产品":   25 + (i % 55),
			"显卡":   35 + (i % 25),
			"游戏":   28 + (i % 30),
			"配置":   22 + (i % 20),
			"推荐":   18 + (i % 15),
			"技术":   15 + (i % 10),
			"创新":   12 + (i % 8),
			"解决方案": 8 + (i % 6),
			"服务":   10 + (i % 7),
			"专业":   6 + (i % 5),
		}

		mainKeywords := map[string]int{
			"技术":   15 + (i % 10),
			"创新":   12 + (i % 8),
			"解决方案": 8 + (i % 6),
			"服务":   10 + (i % 7),
			"专业":   6 + (i % 5),
			"显卡":   35 + (i % 25),
			"性能":   40 + (i % 60),
			"游戏":   28 + (i % 30),
		}

		// 将map转换为JSON
		keywordFrequencyJSON, _ := json.Marshal(keywordFrequency)
		mainKeywordsJSON, _ := json.Marshal(mainKeywords)

		// 创建prompt
		prompt := models.Prompt{
			ID:               uuid.New(),
			BrandID:          brand.ID,
			AISearchID:       search.ID,
			Content:          search.Question, // 使用AI搜索问题作为内容
			Category:         "search_prompt",
			Score:            75.0 + float64(i%25),
			Ranking:          i + 1,
			ShareRate:        float64(10 + (i * 5)),
			ClickCount:       int64(100 + (i * 50)),
			ViewCount:        int64(500 + (i * 200)),
			ShareCount:       int64(20 + (i * 10)),
			ReferenceCount:   int64(5 + (i * 3)),
			EngagementRate:   float64(15 + (i * 3)),
			KeywordDensity:   2.5 + float64(i%3),
			QualityScore:     70.0 + float64(i%20),
			RelevanceScore:   75.0 + float64(i%15),
			OriginalityScore: 65.0 + float64(i%30),
			KeywordFrequency: keywordFrequencyJSON,
			MainKeywords:     mainKeywordsJSON,
			Status:           models.PromptStatusActive,
			Priority:         1,
			Region:           "global",
			Language:         "zh",
		}

		prompt.UpdateRankingTier()

		if err := postgres.DB.Create(&prompt).Error; err != nil {
			log.Printf("⚠️ Failed to create prompt for search %s: %v", search.ID, err)
			continue
		}

		log.Printf("✅ Created prompt for brand %s based on search: %s", brand.Name, search.Question)
	}

	log.Printf("✅ Created prompts for %d AI searches", len(aiSearches))
	return nil
}

// CreateBlogs creates mock blog data
func CreateBlogs(brands []models.Brand) error {
	log.Println("📝 Creating blogs...")

	// Clear existing blogs
	if err := postgres.DB.Exec("DELETE FROM blogs").Error; err != nil {
		return fmt.Errorf("failed to clear blogs: %w", err)
	}

	// Blog templates based on Figma design - simplified content to avoid encoding issues
	// 英伟达相关博客模板 (前3个)
	nvidiaTemplates := []struct {
		title    string
		content  string
		category string
		tags     []string
	}{
		{
			title:    "全能战士登场:英伟达RTX 4090，4K游戏性能王者",
			content:  "# 全能战士登场:英伟达RTX 4090，4K游戏性能王者\n\n在这个追求极致游戏体验的时代，英伟达RTX 4090以其无与伦比的性能表现，重新定义了4K游戏的标准。\n\n## 性能表现\n\n英伟达RTX 4090采用Ada Lovelace架构，拥有16384个CUDA核心，24GB GDDR6X显存，为4K游戏提供了强大的性能支撑。\n\n### 4K游戏性能\n\n在4K游戏性能方面，RTX 4090表现卓越：\n- **《赛博朋克2077》**: 4K光追超级画质下平均帧率达到65fps\n- **《战地2042》**: 4K最高画质稳定在90fps以上\n- **《原神》**: 4K最高画质流畅运行，帧率稳定在120fps\n\n### 光线追踪表现\n\n在光线追踪游戏中同样表现优异：\n- **《控制》**: 4K光追最高设置下60fps流畅运行\n- **《地铁：离去》**: 光追全开依然保持流畅体验\n- **《我的世界RTX》**: 光追效果全开，画面震撼\n\n## DLSS 3.0技术\n\n### 帧生成技术\n\nDLSS 3.0的帧生成技术为游戏性能带来了革命性提升：\n- **性能提升**: 相比原生渲染提升2-4倍\n- **画质保持**: AI算法确保画质不受影响\n- **延迟优化**: Reflex技术降低输入延迟\n\n## 总结\n\n英伟达RTX 4090凭借其强大的4K游戏性能、先进的DLSS 3.0技术以及出色的创作者应用表现，无疑是当前最强的消费级显卡。",
			category: "硬件评测",
			tags:     []string{"英伟达", "RTX", "4K游戏", "DLSS", "显卡"},
		},
		{
			title:    "时空折叠者：i5-14600KF的四维性能革命当算力战争",
			content:  "# 时空折叠者：i5-14600KF的四维性能革命\n\n从硅基平面跃升至时空维度，英特尔酷睿i5-14600KF与AMD锐龙5 9600X的对抗已超越传统参数竞赛，进入了一个全新的性能哲学领域。\n\n## 架构革命：超越摩尔定律的思考\n\n### 混合架构的时空折叠\n\ni5-14600KF采用的混合架构不仅仅是P核与E核的简单组合，更是对计算任务时空分布的深度理解：\n\n- **P核心（Performance Cores）**: 6个高性能核心，主频可达5.3GHz\n- **E核心（Efficiency Cores）**: 8个效率核心，专注多线程负载\n- **智能调度**: Thread Director技术实现任务的时空最优分配\n\n### 缓存层次的维度重构\n\n```\nL1缓存: 48KB指令 + 32KB数据 (每P核)\nL2缓存: 1.25MB (每P核) + 2MB (每4个E核)\nL3缓存: 24MB 智能缓存\n```\n\n## 性能维度分析\n\n### 单核性能：时间维度的极致\n\n在Cinebench R23单核测试中，i5-14600KF展现出了惊人的时间效率：\n\n- **基准分数**: 2,150分\n- **相比13600KF提升**: 8%\n- **相比AMD 7600X**: 领先12%\n\n### 多核性能：空间维度的扩展\n\n多核性能测试显示了其在空间维度上的优势：\n\n- **Cinebench R23多核**: 28,500分\n- **Blender BMW渲染**: 3分42秒\n- **7-Zip压缩**: 85,000 MIPS\n\n## 游戏性能：虚拟世界的现实映射\n\n### 1080p游戏测试\n\n| 游戏标题 | 平均帧率 | 1%低帧率 | 功耗 |\n|---------|---------|---------|------|\n| 《赛博朋克2077》| 165fps | 142fps | 125W |\n| 《战神》| 178fps | 156fps | 118W |\n| 《地平线5》| 195fps | 171fps | 132W |\n\n### 1440p游戏表现\n\n在2K分辨率下，i5-14600KF依然保持了强劲的性能输出，为高刷新率显示器提供了充足的帧率支撑。\n\n## 功耗与温度：能量守恒的艺术\n\n### 功耗特性\n\n- **基础功耗(PL1)**: 125W\n- **最大睿频功耗(PL2)**: 181W\n- **实际游戏功耗**: 90-120W\n- **待机功耗**: 15W\n\n### 散热需求\n\n推荐散热器规格：\n- **入门级**: 4热管塔式散热器\n- **主流级**: 6热管双塔散热器\n- **高端级**: 240mm一体式水冷\n\n## 平台生态：Z790的完整体验\n\n### 主板选择策略\n\n**入门Z790**:\n- 华硕 PRIME Z790-P WIFI\n- 微星 PRO Z790-A WIFI\n\n**中端Z790**:\n- 华硕 TUF GAMING Z790-PLUS WIFI\n- 微星 MAG Z790 TOMAHAWK WIFI\n\n**高端Z790**:\n- 华硕 ROG STRIX Z790-E GAMING WIFI\n- 微星 MEG Z790 ACE\n\n### 内存兼容性\n\nDDR5内存支持：\n- **JEDEC标准**: DDR5-4800\n- **超频支持**: DDR5-7200+\n- **推荐配置**: DDR5-6000 32GB (16GB×2)\n\n## 竞品对比：多维度较量\n\n### vs AMD Ryzen 5 7600X\n\n| 项目 | i5-14600KF | R5 7600X | 优势方 |\n|------|-----------|----------|--------|\n| 核心数 | 6P+8E | 6P | Intel |\n| 单核性能 | 2150 | 1920 | Intel |\n| 多核性能 | 28500 | 22800 | Intel |\n| 功耗 | 125W | 105W | AMD |\n| 价格 | ¥2199 | ¥1899 | AMD |\n\n## 应用场景分析\n\n### 游戏玩家\n\n对于追求高帧率游戏体验的玩家，i5-14600KF提供了：\n- 1080p下的极致帧率\n- 1440p下的流畅体验\n- 未来3-5年的性能保障\n\n### 内容创作者\n\n在内容创作领域，其混合架构带来了：\n- 视频编码的效率提升\n- 多任务处理的流畅性\n- 直播推流的稳定性\n\n### 专业用户\n\n对于专业应用，i5-14600KF展现了：\n- CAD软件的响应速度\n- 编程编译的时间优势\n- 虚拟机运行的稳定性\n\n## 超频潜力：突破极限的艺术\n\n### 超频设置建议\n\n**保守超频**:\n- P核全核: 5.1GHz\n- E核全核: 4.0GHz\n- 电压: 1.35V\n\n**激进超频**:\n- P核全核: 5.4GHz\n- E核全核: 4.3GHz\n- 电压: 1.45V\n\n### 超频收益\n\n经过合理超频后，性能提升显著：\n- 单核性能提升: 8-12%\n- 多核性能提升: 15-20%\n- 游戏帧率提升: 5-10%\n\n## 总结：时空交汇的性能艺术\n\n英特尔酷睿i5-14600KF不仅仅是一颗处理器，更是对计算性能哲学的深度思考。它在时间维度上追求极致的单核性能，在空间维度上扩展多核能力，在能量维度上平衡功耗与性能，在成本维度上兼顾价格与价值。\n\n这颗处理器适合：\n- 追求极致游戏体验的玩家\n- 需要强大生产力的创作者\n- 预算有限但不愿妥协性能的用户\n\n在这个算力即正义的时代，i5-14600KF以其独特的混合架构和出色的性能表现，为用户提供了一个通往高性能计算世界的理想入口。",
			category: "深度评测",
			tags:     []string{"Intel", "CPU", "超频", "性能分析", "技术"},
		},
		{
			title:    "中端处理器的4K游戏革命：14600KF与RTX 50系的完美组合",
			content:  "# 中端处理器的4K游戏革命：14600KF与RTX 50系的完美组合\n\n在2025年的游戏硬件市场中，Intel Core i5-14600KF处理器以其出色的性价比赢得了玩家青睐。这款中端处理器不仅在1080p和1440p分辨率下表现优异，更是在4K游戏领域展现出了惊人的潜力。\n\n## 4K游戏的新时代\n\n### 硬件需求的演进\n\n4K游戏对硬件的要求极高，传统观念认为需要顶级处理器才能胜任。然而，随着游戏引擎的优化和显卡性能的飞跃，中端处理器也开始在4K游戏中发光发热。\n\n### i5-14600KF的4K表现\n\n在4K分辨率下，游戏性能的瓶颈往往转移到显卡端，这为中端处理器提供了发挥空间：\n\n- **CPU占用率**: 在4K游戏中通常保持在60-80%\n- **帧率稳定性**: 1%低帧率表现优异\n- **功耗控制**: 相比高端处理器更加节能\n\n## RTX 50系显卡的革命性提升\n\n### 架构创新\n\nNVIDIA RTX 50系列显卡带来了革命性的改进：\n\n- **Ada Lovelace架构**: 全新的GPU架构\n- **DLSS 3.5**: 更智能的AI超分辨率技术\n- **光线追踪**: 第三代RT核心\n- **AV1编码**: 支持最新的视频编码标准\n\n### 性能飞跃\n\n相比RTX 40系列，RTX 50系列在4K游戏中的提升显著：\n\n| 显卡型号 | 4K原生性能 | 4K DLSS性能 | 功耗 |\n|---------|-----------|------------|------|\n| RTX 5070 | 85fps | 135fps | 220W |\n| RTX 5070 Ti | 105fps | 165fps | 285W |\n| RTX 5080 | 135fps | 210fps | 320W |\n\n## 完美组合：14600KF + RTX 50系\n\n### 性能匹配度分析\n\ni5-14600KF与RTX 50系列显卡的搭配堪称完美：\n\n**优势**:\n- 无明显性能瓶颈\n- 功耗控制合理\n- 价格比例协调\n- 升级路径清晰\n\n**搭配建议**:\n- **入门4K**: i5-14600KF + RTX 5070\n- **主流4K**: i5-14600KF + RTX 5070 Ti\n- **高端4K**: i5-14600KF + RTX 5080\n\n### 实际游戏测试\n\n#### 《赛博朋克2077》4K测试\n\n配置：i5-14600KF + RTX 5070 Ti + 32GB DDR5-6000\n\n- **4K原生 + 光追中等**: 68fps\n- **4K DLSS质量 + 光追高**: 95fps\n- **4K DLSS性能 + 光追超级**: 125fps\n\n#### 《战神》4K测试\n\n- **4K原生 + 高画质**: 85fps\n- **4K DLSS质量**: 115fps\n- **4K DLSS性能**: 145fps\n\n#### 《地平线：西之绝境》4K测试\n\n- **4K原生 + 高画质**: 78fps\n- **4K DLSS质量**: 108fps\n- **4K DLSS性能**: 138fps\n\n## 系统配置推荐\n\n### 4K游戏专用配置\n\n**CPU**: Intel Core i5-14600KF\n**主板**: MSI MAG Z790 TOMAHAWK WIFI\n**内存**: 芝奇 Trident Z5 DDR5-6000 32GB (16GB×2)\n**显卡**: NVIDIA GeForce RTX 5070 Ti\n**存储**: 西数 SN850X 2TB NVMe SSD\n**电源**: 海韵 Focus GX-850W 80Plus金牌\n**散热**: 利民 Frozen Prism 360 ARGB\n**机箱**: 联力 O11 Dynamic EVO\n\n### 预算优化方案\n\n对于预算有限的用户，可以考虑以下配置：\n\n**CPU**: Intel Core i5-14600KF\n**主板**: 华硕 TUF GAMING B760M-PLUS WIFI\n**内存**: 金士顿 FURY Beast DDR5-5600 32GB\n**显卡**: NVIDIA GeForce RTX 5070\n**存储**: 致钛 TiPlus7100 1TB NVMe SSD\n**电源**: 安钛克 NE750金牌\n**散热**: 利民 Peerless Assassin 120 SE\n\n## 4K游戏优化技巧\n\n### DLSS设置建议\n\n- **追求画质**: 使用DLSS质量模式\n- **平衡体验**: 使用DLSS平衡模式\n- **追求帧率**: 使用DLSS性能模式\n\n### 游戏设置优化\n\n1. **纹理质量**: 设置为高或超高\n2. **阴影质量**: 设置为中等\n3. **反射质量**: 根据显卡性能调整\n4. **光线追踪**: 选择性开启\n5. **抗锯齿**: 关闭原生AA，依赖DLSS\n\n### 系统优化\n\n- **内存**: 开启XMP配置文件\n- **存储**: 使用NVMe SSD存放游戏\n- **电源**: 设置为高性能模式\n- **温度**: 保持良好散热\n\n## 未来展望\n\n### 技术发展趋势\n\n4K游戏的普及将推动以下技术发展：\n\n- **AI超分辨率**: DLSS、FSR等技术持续进化\n- **光线追踪**: 硬件加速能力不断提升\n- **VRR技术**: 可变刷新率显示器普及\n- **HDR标准**: 更好的色彩表现\n\n### 升级建议\n\n对于当前配置的升级路径：\n\n**短期升级**:\n- 增加内存容量至64GB\n- 升级到更快的NVMe SSD\n- 优化散热系统\n\n**中期升级**:\n- 考虑RTX 60系列显卡\n- 升级到下一代处理器\n- 更换支持PCIe 5.0的主板\n\n**长期规划**:\n- 关注8K游戏发展\n- 考虑VR/AR设备兼容性\n- 准备迎接下一代游戏引擎\n\n## 总结\n\nIntel Core i5-14600KF与NVIDIA RTX 50系列显卡的组合，为4K游戏带来了全新的可能性。这套配置不仅能够在当前的4K游戏中提供流畅的体验，更为未来的游戏发展预留了充足的性能空间。\n\n对于追求4K游戏体验的玩家来说，这样的配置组合提供了：\n- 出色的性价比\n- 稳定的游戏性能\n- 合理的功耗控制\n- 清晰的升级路径\n\n在4K游戏即将成为主流的今天，选择合适的硬件配置至关重要。i5-14600KF + RTX 50系列的组合，无疑是当前市场上最具吸引力的4K游戏解决方案之一。",
			category: "游戏硬件",
			tags:     []string{"4K游戏", "RTX", "Intel", "显卡", "配置推荐"},
		},
	}

	// 享界相关博客模板 (后3个)
	aitoTemplates := []struct {
		title    string
		content  string
		category string
		tags     []string
	}{
		{
			title:    "智能出行新标杆：享界S7，重新定义豪华电动轿车",
			content:  "# 智能出行新标杆：享界S7，重新定义豪华电动轿车\n\n在新能源汽车市场竞争日趋激烈的今天，享界S7以其卓越的智能化水平和豪华品质，为消费者带来了全新的出行体验。\n\n## 设计美学\n\n享界S7采用了前卫的设计语言，流线型车身与科技感十足的细节完美融合。\n\n### 外观设计\n\n- **前脸设计**: 封闭式格栅配合贯穿式LED灯带\n- **车身线条**: 溜背式设计，风阻系数仅为0.203Cd\n- **轮毂造型**: 20英寸低风阻轮毂，兼顾美观与节能\n\n### 内饰工艺\n\n- **材质选择**: 真皮包覆配合实木装饰\n- **座椅设计**: 零重力座椅，支持多种按摩模式\n- **氛围灯**: 64色氛围灯系统，营造豪华氛围\n\n## 智能科技\n\n### HarmonyOS智能座舱\n\n享界S7搭载了华为HarmonyOS智能座舱系统：\n- **15.6英寸中控屏**: 2K分辨率，支持多点触控\n- **语音助手**: 小艺智能语音，支持连续对话\n- **生态互联**: 与华为手机、平板无缝连接\n\n### 智能驾驶辅助\n\n- **ADS 2.0**: 华为高阶智能驾驶辅助系统\n- **激光雷达**: 前向激光雷达，探测距离200米\n- **高精地图**: 覆盖全国高速及城市快速路\n\n## 性能表现\n\n### 动力系统\n\n享界S7提供多种动力配置：\n- **后驱版本**: 最大功率215kW，峰值扭矩360N·m\n- **四驱版本**: 前后双电机，综合功率365kW\n- **加速性能**: 四驱版0-100km/h加速仅需3.5秒\n\n### 续航能力\n\n- **电池容量**: 100kWh三元锂电池\n- **CLTC续航**: 最高可达855公里\n- **充电速度**: 支持250kW超级快充，15分钟补能400公里\n\n## 总结\n\n享界S7凭借其出色的设计、先进的智能科技和强劲的性能表现，在豪华电动轿车市场中树立了新的标杆。",
			category: "新能源汽车",
			tags:     []string{"享界", "电动汽车", "智能驾驶", "华为", "豪华轿车"},
		},
		{
			title:    "科技与豪华的完美融合：享界M9，大型SUV的智能化革命",
			content:  "# 科技与豪华的完美融合：享界M9，大型SUV的智能化革命\n\n作为享界品牌的旗舰SUV，享界M9将科技创新与豪华体验完美结合，为用户带来了前所未有的智能出行体验。\n\n## 空间设计\n\n### 车身尺寸\n\n享界M9拥有宽敞的车内空间：\n- **车身长度**: 5230mm\n- **轴距**: 3110mm\n- **座椅布局**: 2+2+2六座布局\n\n### 豪华配置\n\n- **第二排座椅**: 航空级座椅，支持加热、通风、按摩\n- **车载冰箱**: 第二排中央扶手内置冰箱\n- **全景天窗**: 超大全景天窗，采光面积达1.9㎡\n\n## 智能科技\n\n### 华为全栈智能汽车解决方案\n\n- **鸿蒙座舱**: HarmonyOS 4.0车机系统\n- **智能驾驶**: ADS 2.0高阶智能驾驶\n- **智能车控**: 华为DriveONE三合一电驱系统\n\n### 娱乐系统\n\n- **音响系统**: 华为Sound音响，19个扬声器\n- **显示屏幕**: 三块高清显示屏，支持4K视频播放\n- **游戏娱乐**: 支持云游戏，车内即可畅玩大作\n\n## 安全性能\n\n### 被动安全\n\n- **车身结构**: 笼式车身结构，关键部位采用热成型钢\n- **安全气囊**: 10个安全气囊全方位保护\n- **C-NCAP**: 五星安全评级\n\n### 主动安全\n\n- **预碰撞系统**: 前向碰撞预警及自动紧急制动\n- **盲点监测**: 360度全景监测，消除视觉盲区\n- **车道保持**: 智能车道保持辅助系统\n\n## 总结\n\n享界M9作为大型豪华SUV的代表作品，在智能化、豪华性和安全性方面都达到了行业领先水平。",
			category: "新能源汽车",
			tags:     []string{"享界", "SUV", "智能座舱", "华为", "豪华SUV"},
		},
		{
			title:    "城市通勤新选择：享界M5，智能电动SUV的性价比之王",
			content:  "# 城市通勤新选择：享界M5，智能电动SUV的性价比之王\n\n在竞争激烈的中型SUV市场，享界M5以其出色的智能化配置和合理的价格定位，成为了城市家庭的理想选择。\n\n## 产品定位\n\n### 市场定位\n\n享界M5定位于智能电动中型SUV：\n- **目标用户**: 城市中产家庭\n- **使用场景**: 日常通勤、家庭出行\n- **价格区间**: 25-35万元\n\n### 竞品对比\n\n相比同级别竞品，享界M5具有明显优势：\n- **智能化水平**: 搭载华为全栈智能汽车解决方案\n- **续航能力**: CLTC续航里程达到620公里\n- **充电便利性**: 支持华为超充网络\n\n## 智能配置\n\n### HarmonyOS智能座舱\n\n- **中控屏幕**: 15.6英寸2K中控屏\n- **仪表盘**: 10.25英寸液晶仪表\n- **HUD**: AR-HUD抬头显示系统\n\n### 智能驾驶\n\n- **NCA**: 高速及城区领航辅助\n- **APA**: 自动泊车辅助\n- **RCW**: 后方碰撞预警\n\n## 动力性能\n\n### 电驱系统\n\n- **电机功率**: 前电机150kW，后电机200kW\n- **综合功率**: 350kW\n- **加速性能**: 0-100km/h加速4.4秒\n\n### 电池技术\n\n- **电池类型**: 三元锂电池\n- **电池容量**: 80kWh\n- **充电功率**: 支持150kW直流快充\n\n## 实用配置\n\n### 舒适配置\n\n- **座椅**: 前排座椅加热、通风、按摩\n- **空调**: 三区独立自动空调\n- **储物空间**: 后备箱容积达到500L\n\n### 便利配置\n\n- **无线充电**: 前排无线充电板\n- **USB接口**: 全车6个USB接口\n- **220V电源**: 对外放电功能\n\n## 总结\n\n享界M5凭借其均衡的产品力和出色的性价比，在中型电动SUV市场中占据了重要地位，是城市家庭的理想选择。",
			category: "新能源汽车",
			tags:     []string{"享界", "电动SUV", "性价比", "城市通勤", "智能汽车"},
		},
	}

	// 为每个品牌分配对应的博客模板
	for i, brand := range brands {
		var templates []struct {
			title    string
			content  string
			category string
			tags     []string
		}

		if brand.Name == "英伟达显卡" {
			templates = nvidiaTemplates
		} else if brand.Name == "享界" {
			templates = aitoTemplates
		} else {
			// 如果有其他品牌，使用英伟达模板作为默认
			templates = nvidiaTemplates
		}

		for j, template := range templates {
			// Create author info
			authorInfo := map[string]interface{}{
				"name":   "Harry",
				"email":  "<EMAIL>",
				"avatar": "/avatars/harry.jpg",
			}
			authorJSON, _ := json.Marshal(authorInfo)

			// Create tags
			tagsJSON, _ := json.Marshal(template.tags)

			// Create brand mentions
			brandMentions := map[string]interface{}{
				brand.Name: map[string]interface{}{
					"count":     15 + (j * 5),
					"sentiment": "positive",
					"context":   []string{"性能优秀", "值得推荐", "性价比高"},
				},
			}
			brandMentionsJSON, _ := json.Marshal(brandMentions)

			// Create meta keywords
			metaKeywords := append(template.tags, brand.Name)
			metaKeywordsJSON, _ := json.Marshal(metaKeywords)

			blog := models.Blog{
				ID:              uuid.New(),
				BrandID:         brand.ID,
				Title:           template.title,
				Slug:            fmt.Sprintf("blog-%d-%d", i, j),
				Excerpt:         SafeString(template.content, 200),
				Content:         template.content,
				Author:          datatypes.JSON(authorJSON),
				Category:        template.category,
				Tags:            datatypes.JSON(tagsJSON),
				Status:          models.BlogStatusPublished,
				FeaturedImage:   fmt.Sprintf("/images/blog-%d-%d.jpg", i, j),
				ViewCount:       int64(1000 + (j * 500)),
				LikeCount:       int64(50 + (j * 20)),
				ShareCount:      int64(10 + (j * 5)),
				CommentCount:    int64(5 + (j * 2)),
				BrandMentions:   datatypes.JSON(brandMentionsJSON),
				MentionRate:     float64(60 + (j * 10)),
				MetaTitle:       template.title,
				MetaDescription: SafeString(template.content, 100),
				MetaKeywords:    datatypes.JSON(metaKeywordsJSON),
				PublishedAt:     timePtr(time.Now().AddDate(0, 0, -(j * 7))),
			}

			if err := postgres.DB.Create(&blog).Error; err != nil {
				log.Printf("⚠️ Failed to create blog: %v", err)
				continue
			}

			log.Printf("✅ Created blog: %s", blog.Title)
		}
	}

	log.Printf("✅ Created blogs for %d brands", len(brands))
	return nil
}

// CreateReferences creates references for AI search responses
func CreateReferences(aiSearches []models.AISearch, aiResponses []models.AISearchResponse, brands []models.Brand) error {
	log.Println("🔗 Creating references...")

	referenceTemplates := []struct {
		title    string
		url      string
		domain   string
		category models.ReferenceCategory
		refType  models.ReferenceType
	}{
		{
			title:    "Wikipedia - 企业技术解决方案",
			url:      "https://zh.wikipedia.org/wiki/企业技术解决方案",
			domain:   "wikipedia.org",
			category: models.ReferenceCategoryEducation,
			refType:  models.ReferenceTypeWebsite,
		},
		{
			title:    "TechCrunch - 创新科技报道",
			url:      "https://techcrunch.com/innovation-report",
			domain:   "techcrunch.com",
			category: models.ReferenceCategoryNews,
			refType:  models.ReferenceTypeArticle,
		},
		{
			title:    "GitHub - 开源项目",
			url:      "https://github.com/example/project",
			domain:   "github.com",
			category: models.ReferenceCategoryTechnology,
			refType:  models.ReferenceTypeWebsite,
		},
	}

	// Create brand map for quick lookup
	brandMap := make(map[uuid.UUID]models.Brand)
	for _, brand := range brands {
		brandMap[brand.ID] = brand
	}

	// Create AI search map for quick lookup
	searchMap := make(map[uuid.UUID]models.AISearch)
	for _, search := range aiSearches {
		searchMap[search.ID] = search
	}

	for _, response := range aiResponses {
		search := searchMap[response.AISearchID]
		brand := brandMap[search.BrandID]

		// Create 2 references per response
		for i := 0; i < 2 && i < len(referenceTemplates); i++ {
			template := referenceTemplates[i]

			reference := models.Reference{
				ID:             uuid.New(),
				AISearchID:     response.AISearchID,
				AIResponseID:   response.ID,
				BrandID:        search.BrandID,
				Title:          template.title,
				URL:            template.url,
				Domain:         template.domain,
				Type:           template.refType,
				Category:       template.category,
				Description:    fmt.Sprintf("来自%s的相关引用", template.domain),
				Ranking:        i + 1,
				Position:       i * 10,
				Prominence:     float64(90 - (i * 10)),
				RelevanceScore: 85.0 + float64(i*5),
				MentionCount:   1,
				CitationCount:  1,
				Frequency:      0.8 + (float64(i) * 0.1),
				Weight:         1.0 - (float64(i) * 0.1),
				ShareCount:     int64(50 + (i * 20)),
				ClickCount:     int64(100 + (i * 30)),
				ViewCount:      int64(500 + (i * 100)),
				EngagementRate: 15.0 + float64(i*2),
				AuthorityScore: 85.0 + float64(i*3),
				TrustScore:     80.0 + float64(i*2),
				QualityScore:   88.0 + float64(i*1),
				FreshnessScore: 75.0 + float64(i*5),
				ContentLength:  1000 + (i * 200),
				SentimentScore: 0.7 + (float64(i) * 0.1),
				Author:         "AI Assistant",
				Publisher:      template.domain,
				PublishedDate:  timePtr(time.Now().AddDate(0, 0, -i-1)),
				Language:       "zh",
				Country:        "CN",
				ContentType:    "text/html",
				IsAccessible:   true,
				ContextBefore:  "在讨论相关技术时",
				ContextAfter:   "被广泛认可和引用",
				QuoteText:      fmt.Sprintf("%s在该领域表现出色", brand.Name),
				Region:         "global",
			}

			if err := postgres.DB.Create(&reference).Error; err != nil {
				log.Printf("⚠️ Failed to create reference: %v", err)
				continue
			}
		}
	}

	log.Printf("✅ Created references for AI responses")
	return nil
}

// CreateAIVisibilityMetrics creates AI visibility metrics for AI search responses
func CreateAIVisibilityMetrics(aiSearches []models.AISearch, aiResponses []models.AISearchResponse, brands []models.Brand) error {
	log.Println("📊 Creating AI visibility metrics...")

	// Create brand map for quick lookup
	brandMap := make(map[uuid.UUID]models.Brand)
	for _, brand := range brands {
		brandMap[brand.ID] = brand
	}

	// Create AI search map for quick lookup
	searchMap := make(map[uuid.UUID]models.AISearch)
	for _, search := range aiSearches {
		searchMap[search.ID] = search
	}

	for i, response := range aiResponses {
		search := searchMap[response.AISearchID]
		brand := brandMap[search.BrandID]

		// Create visibility metrics data (基于Figma设计的关键词频率结构)
		visibilityData := map[string]interface{}{
			"品牌推荐率":       75.0 + float64(i%25),
			"品牌搜索率":       80.0 + float64(i%20),
			"品牌在AI市场的首推率": 65.0 + float64(i%35),
			"品牌提及位置":      response.BrandPosition,
			"品牌情感倾向":      response.BrandSentiment,
			"竞争对手数量":      2 + (i % 4),
			"市场占有率":       25.0 + float64(i%50),
			"额外指标": map[string]interface{}{
				"响应质量": response.Confidence * 100,
				"相关性":  response.Relevance * 100,
			},
		}

		// Create keyword metrics data (基于用户需求的聚合结构)
		keywordData := map[string]interface{}{
			// 聚合的关键指标
			"在AI中出现频率": map[string]interface{}{
				"价格":   20 + (i % 50),
				"质量":   30 + (i % 40),
				"性能":   40 + (i % 60),
				"性价比":  25 + (i % 45),
				"品牌":   15 + (i % 35),
				"产品":   25 + (i % 55),
				"技术":   15 + (i % 10),
				"创新":   12 + (i % 8),
				"解决方案": 8 + (i % 6),
				"服务":   10 + (i % 7),
				"专业":   6 + (i % 5),
				"显卡":   35 + (i % 25),
				"游戏":   28 + (i % 30),
				"配置":   22 + (i % 20),
				"推荐":   18 + (i % 15),
			},
			"品牌推荐率": map[string]interface{}{
				"当前品牌":  75.0 + float64(i%20),
				"竞争对手1": 15.0 + float64(i%10),
				"竞争对手2": 8.0 + float64(i%5),
				"竞争对手3": 2.0 + float64(i%3),
				"其他品牌":  0.0 + float64(i%2),
			},
			"品牌搜索率": map[string]interface{}{
				"直接搜索": 60.0 + float64(i%25),
				"相关搜索": 25.0 + float64(i%15),
				"竞品对比": 10.0 + float64(i%8),
				"行业搜索": 5.0 + float64(i%5),
			},
			"品牌在AI市场的首推率": map[string]interface{}{
				"当前品牌":  65.0 + float64(i%25),
				"竞争对手1": 20.0 + float64(i%15),
				"竞争对手2": 10.0 + float64(i%8),
				"竞争对手3": 3.0 + float64(i%4),
				"其他品牌":  2.0 + float64(i%3),
			},
			// 新增的缺失字段
			"main_keywords": map[string]int{
				"技术":   15 + (i % 10),
				"创新":   12 + (i % 8),
				"解决方案": 8 + (i % 6),
				"服务":   10 + (i % 7),
				"专业":   6 + (i % 5),
				"显卡":   35 + (i % 25),
				"性能":   40 + (i % 60),
				"游戏":   28 + (i % 30),
			},
			"keyword_frequency": map[string]int{
				"价格":   20 + (i % 50),
				"质量":   30 + (i % 40),
				"性能":   40 + (i % 60),
				"性价比":  25 + (i % 45),
				"品牌":   15 + (i % 35),
				"产品":   25 + (i % 55),
				"显卡":   35 + (i % 25),
				"游戏":   28 + (i % 30),
				"配置":   22 + (i % 20),
				"推荐":   18 + (i % 15),
				"技术":   15 + (i % 10),
				"创新":   12 + (i % 8),
				"解决方案": 8 + (i % 6),
				"服务":   10 + (i % 7),
				"专业":   6 + (i % 5),
			},
			// 元数据
			"关键词密度":   2.5 + float64(i%3),
			"语义相关性":   85.0 + float64(i%15),
			"搜索意图匹配度": 78.0 + float64(i%20),
			"长尾关键词":   []string{"企业级解决方案", "数字化转型", "云计算服务", "游戏显卡推荐", "高性能计算"},
			"关键词分类": map[string][]string{
				"产品相关": {"解决方案", "服务", "平台", "显卡", "配置"},
				"品牌相关": {"专业", "可靠", "领先", "推荐"},
				"技术相关": {"技术", "创新", "算法", "性能", "游戏"},
			},
			"额外关键词数据": map[string]interface{}{
				"唯一词数":  50 + (i % 20),
				"复杂度评分": 75.0 + float64(i%25),
				"总词数":   len(response.Response),
			},
		}

		// Create AI visibility metrics record
		metrics := models.AIVisibilityMetrics{
			ID:           uuid.New(),
			AISearchID:   search.ID,
			AIResponseID: response.ID,
			BrandID:      brand.ID,
			CalculatedAt: time.Now().AddDate(0, 0, -i),
		}

		// Convert to JSON and set data directly
		visibilityJSON, _ := json.Marshal(visibilityData)
		keywordJSON, _ := json.Marshal(keywordData)

		metrics.VisibilityData = visibilityJSON
		metrics.KeywordData = keywordJSON

		// Set individual scores from map data
		metrics.FrequencyScore = 70.0 + float64(i%30) // 计算频率得分
		metrics.RecommendationScore = visibilityData["品牌推荐率"].(float64)
		metrics.SearchRateScore = visibilityData["品牌搜索率"].(float64)
		metrics.FirstChoiceScore = visibilityData["品牌在AI市场的首推率"].(float64)

		// Calculate overall score
		metrics.OverallScore = (metrics.FrequencyScore + metrics.RecommendationScore +
			metrics.SearchRateScore + metrics.FirstChoiceScore) / 4

		// Save to database
		if err := postgres.DB.Create(&metrics).Error; err != nil {
			log.Printf("⚠️ Failed to create AI visibility metrics: %v", err)
			continue
		}

		log.Printf("✅ Created AI visibility metrics for %s response", brand.Name)
	}

	log.Printf("✅ Created AI visibility metrics for %d responses", len(aiResponses))
	return nil
}

// CreateAIVisibilityAggregations creates AI visibility aggregations for brands
func CreateAIVisibilityAggregations(brands []models.Brand) error {
	log.Println("📈 Creating AI visibility aggregations...")

	for i, brand := range brands {
		// Create monthly aggregation
		startDate := time.Now().AddDate(0, -1, 0) // 1 month ago
		endDate := time.Now()

		aggregation := models.AIVisibilityAggregation{
			ID:                         uuid.New(),
			BrandID:                    brand.ID,
			StartDate:                  startDate,
			EndDate:                    endDate,
			TotalQuestions:             15 + (i * 5),
			AverageOverallScore:        72.5 + float64(i*8),
			AverageFrequencyScore:      75.0 + float64(i*6),
			AverageRecommendationScore: 78.0 + float64(i*7),
			AverageSearchRateScore:     80.0 + float64(i*5),
			AverageFirstChoiceScore:    68.0 + float64(i*9),
			TrendDirection:             []string{"上升", "稳定", "下降"}[i%3],
			ChangePercentage:           float64(5 + (i * 3)),
		}

		// Create aggregated visibility data
		aggregatedVisibilityData := map[string]interface{}{
			"总问题数量":     aggregation.TotalQuestions,
			"平均AI中出现频率": aggregation.AverageFrequencyScore,
			"平均品牌推荐率":   aggregation.AverageRecommendationScore,
			"平均品牌搜索率":   aggregation.AverageSearchRateScore,
			"平均首推率":     aggregation.AverageFirstChoiceScore,
			"热门关键词":     []string{"技术", "创新", "解决方案", "服务", "专业"},
			"关键词总频率": map[string]int{
				"技术":   150 + (i * 20),
				"创新":   120 + (i * 15),
				"解决方案": 80 + (i * 10),
				"服务":   100 + (i * 12),
			},
			"情感分布": map[string]int{
				"positive": 12 + (i * 2),
				"neutral":  2 + i,
				"negative": 1,
			},
			"竞争对手分析": map[string]interface{}{
				"average_competitor_count": 3.2 + float64(i),
				"market_position":          []string{"领先", "跟随", "新兴"}[i%3],
			},
		}

		// Create aggregated keyword data
		aggregatedKeywordData := map[string]interface{}{
			"总关键词数量":  200 + (i * 30),
			"唯一关键词数量": 150 + (i * 20),
			"平均关键词密度": 2.8 + float64(i),
			"平均语义相关性": 85.5 + float64(i*2),
			"平均意图匹配度": 78.2 + float64(i*3),
			"关键词分类汇总": map[string][]string{
				"技术相关": {"技术", "创新", "算法", "系统"},
				"产品相关": {"解决方案", "服务", "平台", "产品"},
				"品牌相关": {"专业", "可靠", "领先", "优秀"},
			},
			"长尾关键词汇总": []string{
				"企业级技术解决方案",
				"数字化转型服务",
				"云计算平台",
				"人工智能应用",
			},
		}

		// Set aggregated data as JSON
		visibilityJSON, _ := json.Marshal(aggregatedVisibilityData)
		keywordJSON, _ := json.Marshal(aggregatedKeywordData)
		aggregation.AggregatedVisibilityData = visibilityJSON
		aggregation.AggregatedKeywordData = keywordJSON

		// Save to database
		if err := postgres.DB.Create(&aggregation).Error; err != nil {
			log.Printf("⚠️ Failed to create AI visibility aggregation for %s: %v", brand.Name, err)
			continue
		}

		log.Printf("✅ Created AI visibility aggregation for %s", brand.Name)
	}

	log.Printf("✅ Created AI visibility aggregations for %d brands", len(brands))
	return nil
}

// CreateGEOOptimizations creates GEO optimization mock data based on AI search data
func CreateGEOOptimizations(brands []models.Brand) error {
	log.Println("🌍 Creating GEO optimizations based on AI search data...")

	// Use the new GEO optimization generator
	geoGenerator := NewGEOOptimizationGenerator(postgres.DB)

	for _, brand := range brands {
		if err := geoGenerator.GenerateGEOOptimizationsForBrand(brand.ID); err != nil {
			log.Printf("⚠️ Failed to generate GEO optimizations for brand %s: %v", brand.Name, err)
			continue
		}
		log.Printf("✅ Generated GEO optimizations for brand %s", brand.Name)
	}

	log.Printf("✅ Created GEO optimizations for %d brands", len(brands))
	return nil
}

// Legacy GEO performance data (kept for reference, but not used)
func getLegacyGEOPerformanceData() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"region":             "北美",
			"country":            "美国",
			"visibility_score":   92.1,
			"market_penetration": 78.5,
			"search_volume":      45200,
			"competition_level":  "high",
			"opportunity_score":  8.7,
			"key_metrics": map[string]interface{}{
				"brand_awareness":           85.2,
				"customer_acquisition_cost": 125.50,
				"conversion_rate":           3.8,
				"retention_rate":            89.3,
			},
			"trending_keywords": []string{
				"enterprise software",
				"cloud solutions",
				"digital transformation",
			},
			"recommended_actions": []string{
				"加强本地化内容营销",
				"优化移动端用户体验",
				"扩大社交媒体影响力",
			},
		},
		{
			"region":             "欧洲",
			"country":            "德国",
			"visibility_score":   76.8,
			"market_penetration": 62.3,
			"search_volume":      28900,
			"competition_level":  "medium",
			"opportunity_score":  7.2,
			"key_metrics": map[string]interface{}{
				"brand_awareness":           71.5,
				"customer_acquisition_cost": 98.75,
				"conversion_rate":           4.2,
				"retention_rate":            92.1,
			},
			"trending_keywords": []string{
				"Unternehmenssoftware",
				"Cloud-Lösungen",
				"Datenschutz",
			},
			"recommended_actions": []string{
				"增强GDPR合规性宣传",
				"建立本地合作伙伴关系",
				"投资德语内容创作",
			},
		},
		{
			"region":             "亚太",
			"country":            "日本",
			"visibility_score":   68.4,
			"market_penetration": 45.7,
			"search_volume":      19600,
			"competition_level":  "high",
			"opportunity_score":  6.8,
			"key_metrics": map[string]interface{}{
				"brand_awareness":           58.9,
				"customer_acquisition_cost": 156.20,
				"conversion_rate":           2.9,
				"retention_rate":            94.7,
			},
			"trending_keywords": []string{
				"企業向けソフトウェア",
				"クラウドサービス",
				"デジタル変革",
			},
			"recommended_actions": []string{
				"深化本地文化适应",
				"建立日本研发中心",
				"加强B2B渠道合作",
			},
		},
		{
			"region":             "亚太",
			"country":            "中国",
			"visibility_score":   82.3,
			"market_penetration": 71.2,
			"search_volume":      67800,
			"competition_level":  "very_high",
			"opportunity_score":  9.1,
			"key_metrics": map[string]interface{}{
				"brand_awareness":           79.6,
				"customer_acquisition_cost": 89.30,
				"conversion_rate":           5.1,
				"retention_rate":            87.4,
			},
			"trending_keywords": []string{
				"企业软件",
				"云计算",
				"数字化转型",
			},
			"recommended_actions": []string{
				"加强本土化产品开发",
				"建立政府关系网络",
				"投资本地人才培养",
			},
		},
		{
			"region":             "南美",
			"country":            "巴西",
			"visibility_score":   54.2,
			"market_penetration": 28.9,
			"search_volume":      12400,
			"competition_level":  "low",
			"opportunity_score":  8.5,
			"key_metrics": map[string]interface{}{
				"brand_awareness":           42.1,
				"customer_acquisition_cost": 67.80,
				"conversion_rate":           6.2,
				"retention_rate":            91.8,
			},
			"trending_keywords": []string{
				"software empresarial",
				"soluções em nuvem",
				"transformação digital",
			},
			"recommended_actions": []string{
				"建立本地销售团队",
				"开发葡语支持",
				"制定价格本地化策略",
			},
		},
	}
}

// createGEODatabaseEntries creates GEO database entries for a GEO optimization
func createGEODatabaseEntries(brand models.Brand, geo models.GEOOptimization) error {
	// Sample GEO database entries for different content types
	geoDBEntries := []models.GEODatabase{
		{
			ID:                uuid.New(),
			BrandID:           brand.ID,
			GeoOptimizationID: geo.ID,
			ContentType:       models.GEOContentTypeSearchResult,
			Title:             fmt.Sprintf("%s在%s的搜索结果分析", brand.Name, geo.Region),
			Content:           fmt.Sprintf("基于AI搜索的%s品牌在%s地区的表现分析，可见性评分为%.1f", brand.Name, geo.Region, geo.VisibilityScore),
			Region:            geo.Region,
			Country:           geo.Country,
			Language:          "zh",
		},
		{
			ID:                uuid.New(),
			BrandID:           brand.ID,
			GeoOptimizationID: geo.ID,
			ContentType:       models.GEOContentTypeKeywordAnalysis,
			Title:             fmt.Sprintf("%s在%s的关键词分析", brand.Name, geo.Region),
			Content:           fmt.Sprintf("针对%s地区的关键词优化建议和趋势分析", geo.Region),
			Region:            geo.Region,
			Country:           geo.Country,
			Language:          "zh",
		},
		{
			ID:                uuid.New(),
			BrandID:           brand.ID,
			GeoOptimizationID: geo.ID,
			ContentType:       models.GEOContentTypeMarketInsight,
			Title:             fmt.Sprintf("%s在%s的市场洞察", brand.Name, geo.Region),
			Content:           fmt.Sprintf("市场渗透率%.1f%%，竞争水平：%s", geo.MarketPenetration, geo.CompetitionLevel),
			Region:            geo.Region,
			Country:           geo.Country,
			Language:          "zh",
		},
	}

	// Save GEO database entries
	for _, entry := range geoDBEntries {
		if err := postgres.DB.Create(&entry).Error; err != nil {
			log.Printf("⚠️ Failed to create GEO database entry %s: %v", entry.Title, err)
			continue
		}
	}

	return nil
}

// convertToFloat64 safely converts interface{} to float64
func convertToFloat64(v interface{}) float64 {
	switch val := v.(type) {
	case float64:
		return val
	case int:
		return float64(val)
	case int64:
		return float64(val)
	default:
		return 0.0
	}
}

// convertToInt64 safely converts interface{} to int64
func convertToInt64(v interface{}) int64 {
	switch val := v.(type) {
	case int64:
		return val
	case int:
		return int64(val)
	case float64:
		return int64(val)
	default:
		return 0
	}
}

// timePtr returns a pointer to time.Time
func timePtr(t time.Time) *time.Time {
	return &t
}
