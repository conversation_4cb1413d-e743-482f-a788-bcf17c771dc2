# GEOK Center Mock Data

本目录包含 GEOK Center 项目的模拟数据，用于开发和测试阶段。

## 品牌中心架构

新的 mock data 采用品牌中心的生成方式：

1. **品牌优先**: 所有数据生成都从品牌实体开始
2. **关系完整**: 确保所有依赖数据都有有效的品牌引用
3. **动态生成**: 不再依赖静态 JSON 文件，而是动态生成真实的数据关系
4. **清理支持**: 提供完整的数据清理功能，支持数据重置

## 目录结构

```
mockdata/
├── loader.go          # 品牌中心的数据生成和清理逻辑
├── homepage/           # 首页相关静态数据
│   ├── visibility.json # 品牌可见性数据
│   ├── ai_visibility.json # AI可见性指标数据
│   ├── tips.json      # AI提示内容数据
│   └── references.json # AI引用数据
├── ceo_optimization/   # CEO优化模块数据
│   ├── geo.json       # 地理位置优化数据
│   ├── ai_visibility_analysis.json # CEO级AI可见性分析
│   └── database_list.json # 数据库列表数据
├── ai_content/        # AI内容生产数据
│   ├── content_list.json # 内容列表数据
│   ├── visibility_metrics.json # AI内容可见性指标
│   └── blog.json      # 博客内容数据
└── README.md          # 本说明文件
```

## 数据模块说明

### 1. 首页模块 (homepage/)

#### visibility.json - 品牌可见性数据

- **用途**: 首页可见性模块的数据展示
- **包含内容**:
  - 总体概览指标
  - 品牌可见性指标列表
  - 地区分布数据
  - 平台分布数据

#### ai_visibility.json - AI 可见性指标数据

- **用途**: 首页 AI 可见性模块的详细数据展示
- **包含内容**:
  - AI 搜索可见性概览统计
  - 品牌在 AI 中的表现指标
  - AI 平台分布和性能分析
  - 关键词分析和优化建议
  - 竞争对手分析
  - 趋势分析和预测
  - 区域性能分析
  - 性能洞察和建议

#### tips.json - AI 提示内容数据

- **用途**: 首页提示模块的数据展示
- **包含内容**:
  - 提示内容概览
  - 提示内容列表
  - 分类统计
  - 性能指标

#### references.json - AI 引用数据

- **用途**: 首页引用模块的数据展示
- **包含内容**:
  - 引用数据概览
  - AI 引用记录列表
  - 情感分析统计
  - AI 平台分布
  - 热门查询趋势

### 2. CEO 优化模块 (ceo_optimization/)

#### geo.json - 地理位置优化数据

- **用途**: CEO 优化中的地理位置分析
- **包含内容**:
  - 全球市场概览
  - 各地区性能数据
  - 市场洞察和建议
  - 新兴市场分析

#### ai_visibility_analysis.json - CEO 级 AI 可见性分析

- **用途**: CEO 优化中的 AI 可见性战略分析
- **包含内容**:
  - 高管摘要和战略指标
  - 业务影响和 ROI 分析
  - 竞争格局和市场定位
  - 地理区域表现分析
  - AI 平台战略建议
  - 风险评估和缓解策略
  - 投资建议和预算分配
  - KPI 仪表板和目标设定

#### database_list.json - 数据库列表数据

- **用途**: CEO 优化中的数据库管理
- **包含内容**:
  - 数据库概览统计
  - 数据库详细信息
  - 性能监控数据
  - 健康状态摘要

### 3. AI 内容生产模块 (ai_content/)

#### content_list.json - 内容列表数据

- **用途**: AI 内容生产的内容管理
- **包含内容**:
  - 内容概览统计
  - 内容列表详情
  - 分类统计
  - 性能指标

#### visibility_metrics.json - AI 内容可见性指标

- **用途**: AI 内容生产中的可见性优化分析
- **包含内容**:
  - 内容可见性概览统计
  - 单个内容的详细表现指标
  - 关键词优化分析和建议
  - AI 平台表现对比
  - 内容优化建议
  - 趋势分析和预测
  - 竞争内容分析
  - 可执行的优化洞察

#### blog.json - 博客内容数据

- **用途**: AI 内容生产的博客管理
- **包含内容**:
  - 博客概览统计
  - 特色博客内容
  - 最新博客列表
  - 分类和标签统计
  - 分析数据

## 数据格式说明

所有 JSON 文件都遵循统一的响应格式：

```json
{
  "success": true,
  "message": "操作成功信息",
  "data": {
    "overview": {
      // 概览统计数据
    },
    "主要数据字段": [
      // 具体数据列表
    ],
    "其他统计字段": {
      // 其他相关统计信息
    }
  }
}
```

## 品牌中心数据生成

### 生成的数据实体

1. **品牌 (Brands)**: 2 个测试品牌

   - TechCorp: 技术公司，专注云计算和软件创新
   - InnovateLab: AI 研究公司，专注机器学习和研究

2. **搜索指标 (SearchMetrics)**: 每个品牌生成 9 条记录

   - 3 个地区 × 3 个平台 = 9 条记录/品牌
   - 地区: 北美、欧洲、亚太
   - 平台: Google、Bing、Baidu

3. **AI 提示 (Prompts)**: 每个品牌生成 3 条记录

   - 不同类型的 AI 优化建议
   - 涵盖可见性、营销策略、数字化转型

4. **AI 搜索 (AISearches)**: 每个品牌生成 5 条记录

   - 涵盖不同类型的推荐查询
   - 包含关键词、状态、优先级等信息

5. **AI 搜索响应 (AISearchResponses)**: 每个搜索生成 3 条响应

   - 3 个 AI 模型: ChatGPT、Claude、Deepseek
   - 包含响应内容、品牌提及分析、质量指标

6. **AI 可见性指标 (AIVisibilityMetrics)**: 每个响应生成 1 条记录

   - 包含可见性指标数据 (JSON 格式，支持中文 key)
   - 关键词指标数据和分析结果
   - 综合得分和各项子得分

7. **AI 可见性聚合 (AIVisibilityAggregations)**: 每个品牌生成 1 条记录
   - 品牌级别的聚合统计数据
   - 趋势分析和变化百分比
   - 聚合的可见性和关键词数据

## CLI 命令使用

### 生成 Mock 数据

```bash
# 生成品牌中心的mock数据
./geok_center migrate --mock

# 或者使用短参数
./geok_center migrate -m
```

### 清理 Mock 数据

```bash
# 清理所有mock数据
./geok_center migrate --clean-mock

# 或者使用短参数
./geok_center migrate -c
```

### 重置 Mock 数据

```bash
# 先清理再生成
./geok_center migrate --clean-mock --mock

# 或者使用短参数
./geok_center migrate -c -m
```

## 数据特点

- **真实关系**: 所有依赖数据都正确引用品牌 ID
- **动态生成**: 基于品牌特征生成相应的指标数据
- **完整清理**: 按照外键依赖顺序正确删除数据
- **可重复性**: 支持多次生成和清理操作
- **AI 可见性指标**:
  - 支持中文 key 的 JSON 格式数据存储
  - 包含完整的可见性和关键词分析
  - 提供品牌级别的聚合统计
  - 支持趋势分析和竞争对比
- **多层次数据**: 从单个响应到品牌聚合的完整数据链路
- **静态补充**: 提供 JSON 文件补充动态数据，支持前端开发和测试

## 注意事项

- 这些数据仅用于开发和测试目的
- 不包含任何真实的用户或企业信息
- 在生产环境中应替换为真实的 API 接口
- 数据结构可能会根据业务需求进行调整
