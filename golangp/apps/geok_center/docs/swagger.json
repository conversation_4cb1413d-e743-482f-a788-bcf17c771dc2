{"info": {"title": "geok", "description": "", "version": "1.0.0"}, "openapi": "3.0.0", "servers": [{"url": "", "description": "默认环境", "variables": {}}, {"url": "http://localhost:8012/api/v1", "description": "Development server", "variables": {}}, {"url": "http://localhost:8012/api/v1", "description": "Development server", "variables": {}}, {"url": "http://localhost:8012/api/v1", "description": "Development server", "variables": {}}], "components": {"schemas": {"LoginRequest": {"properties": {"email": {"example": "<EMAIL>", "format": "email", "type": "string"}, "password": {"example": "password123", "type": "string"}, "id_token": {"description": "Google ID token for OAuth login", "type": "string"}}, "type": "object"}, "RegisterRequest": {"properties": {"username": {"example": "john_doe", "type": "string"}, "email": {"example": "<EMAIL>", "format": "email", "type": "string"}, "password": {"example": "password123", "minLength": 6, "type": "string"}, "first_name": {"example": "<PERSON>", "type": "string"}, "last_name": {"example": "<PERSON><PERSON>", "type": "string"}}, "required": ["username", "email", "password"], "type": "object"}, "PromptRequest": {"properties": {"brand_id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "title": {"example": "AI Content Generation Prompt", "type": "string"}, "content": {"example": "Generate engaging content for social media posts about technology trends", "type": "string"}, "type": {"apipost_enable_enum": true, "enum": ["visibility", "engagement", "conversion"], "example": "visibility", "type": "string"}, "priority": {"example": 5, "maximum": 10, "minimum": 1, "type": "integer"}, "region": {"example": "CN", "type": "string"}, "platform": {"example": "web", "type": "string"}, "tags": {"example": ["AI", "content", "social media"], "items": {"type": "string"}, "type": "array"}, "topic_ids": {"example": [], "items": {"format": "uuid", "type": "string"}, "type": "array"}}, "required": ["brand_id", "title", "content", "type"], "type": "object"}, "PromptUpdateRequest": {"properties": {"title": {"example": "Updated AI Content Generation Prompt", "type": "string"}, "content": {"example": "Generate highly engaging and viral content for social media posts about emerging technology trends", "type": "string"}, "status": {"enum": ["active", "inactive", "draft"], "example": "active", "type": "string"}, "priority": {"example": 8, "maximum": 10, "minimum": 1, "type": "integer"}, "tags": {"example": ["AI", "content", "social media", "viral"], "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TopicRequest": {"properties": {"name": {"example": "Artificial Intelligence", "type": "string"}, "description": {"example": "Topics related to AI and machine learning", "type": "string"}, "color": {"example": "#3B82F6", "type": "string"}, "icon": {"example": "🤖", "type": "string"}}, "required": ["name"], "type": "object"}, "TopicUpdateRequest": {"properties": {"name": {"example": "Advanced AI", "type": "string"}, "description": {"example": "Advanced topics in artificial intelligence and deep learning", "type": "string"}, "color": {"example": "#8B5CF6", "type": "string"}, "icon": {"example": "🧠", "type": "string"}, "is_active": {"example": true, "type": "boolean"}}, "type": "object"}, "ConversationRequest": {"properties": {"title": {"example": "AI Content Strategy Discussion", "type": "string"}, "brand_id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}}, "required": ["title", "brand_id"], "type": "object"}, "MessageRequest": {"properties": {"content": {"example": "Hello, I need help with AI content generation strategies.", "type": "string"}, "message_type": {"apipost_enable_enum": true, "enum": ["text", "image", "file"], "example": "text", "type": "string"}, "is_from_ai": {"example": false, "type": "boolean"}}, "required": ["content", "message_type"], "type": "object"}, "SearchContentRequest": {"properties": {"brand_id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "query": {"example": "AI content generation tools", "type": "string"}, "platform": {"example": "google", "type": "string"}, "region": {"example": "CN", "type": "string"}, "frequency": {"example": 1250, "type": "integer"}, "ranking": {"example": 3, "type": "integer"}, "share_rate": {"example": 0.15, "format": "float", "type": "number"}, "click_count": {"example": 890, "type": "integer"}, "change_rate": {"example": 0.08, "format": "float", "type": "number"}, "last_week_data": {"properties": {"frequency": {"example": 1150, "type": "integer"}, "ranking": {"example": 4, "type": "integer"}}, "type": "object"}, "current_data": {"properties": {"frequency": {"example": 1250, "type": "integer"}, "ranking": {"example": 3, "type": "integer"}}, "type": "object"}}, "required": ["brand_id", "query", "platform"], "type": "object"}, "APIResponse": {"properties": {"success": {"example": true, "type": "boolean"}, "message": {"example": "Operation completed successfully", "type": "string"}, "data": {"type": "object"}, "error": {"example": "", "type": "string"}, "meta": {"properties": {"page": {"example": 1, "type": "integer"}, "per_page": {"example": 20, "type": "integer"}, "total": {"example": 100, "type": "integer"}, "total_pages": {"example": 5, "type": "integer"}}, "ref": "3793b06877019f", "type": "object"}}, "required": ["success"], "type": "object"}, "Meta": {"properties": {"page": {"example": 1, "type": "integer"}, "per_page": {"example": 20, "type": "integer"}, "total": {"example": 100, "type": "integer"}, "total_pages": {"example": 5, "type": "integer"}}, "type": "object"}, "User": {"properties": {"id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "username": {"example": "john_doe", "type": "string"}, "email": {"example": "<EMAIL>", "format": "email", "type": "string"}, "first_name": {"example": "<PERSON>", "type": "string"}, "last_name": {"example": "<PERSON><PERSON>", "type": "string"}, "role": {"enum": ["admin", "user"], "example": "user", "type": "string"}, "status": {"enum": ["active", "inactive"], "example": "active", "type": "string"}, "created_at": {"format": "date-time", "type": "string"}, "updated_at": {"format": "date-time", "type": "string"}}, "type": "object"}, "Prompt": {"properties": {"id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "brand_id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "title": {"example": "AI Content Generation Prompt", "type": "string"}, "content": {"example": "Generate engaging content for social media posts about technology trends", "type": "string"}, "type": {"apipost_enable_enum": true, "enum": ["visibility", "hint", "reference"], "example": "visibility", "type": "string"}, "status": {"enum": ["active", "inactive", "pending", "archived"], "example": "active", "type": "string"}, "priority": {"example": 5, "type": "integer"}, "score": {"example": 8.5, "format": "float", "type": "number"}, "ranking": {"example": 3, "type": "integer"}, "share_rate": {"example": 0.15, "format": "float", "type": "number"}, "click_count": {"example": 890, "type": "integer"}, "region": {"example": "CN", "type": "string"}, "platform": {"example": "web", "type": "string"}, "tags": {"example": "[\"AI\", \"content\", \"social media\"]", "type": "string"}, "created_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "updated_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}}, "type": "object"}, "Topic": {"properties": {"id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "name": {"example": "Artificial Intelligence", "type": "string"}, "description": {"example": "Topics related to AI and machine learning", "type": "string"}, "color": {"example": "#3B82F6", "type": "string"}, "icon": {"example": "🤖", "type": "string"}, "is_active": {"example": true, "type": "boolean"}, "usage_count": {"example": 25, "type": "integer"}, "created_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "updated_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}}, "type": "object"}, "Conversation": {"properties": {"id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "user_id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "brand_id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "title": {"example": "AI Content Strategy Discussion", "type": "string"}, "status": {"apipost_enable_enum": true, "enum": ["active", "archived", "deleted"], "example": "active", "type": "string"}, "message_count": {"example": 15, "type": "integer"}, "last_message_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "created_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "updated_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}}, "type": "object"}, "Message": {"properties": {"id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "conversation_id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "user_id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "content": {"example": "Hello, I need help with AI content generation strategies.", "type": "string"}, "message_type": {"enum": ["text", "image", "file"], "example": "text", "type": "string"}, "is_from_ai": {"example": false, "type": "boolean"}, "created_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}}, "type": "object"}, "SearchContent": {"properties": {"id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "brand_id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "query": {"example": "AI content generation tools", "type": "string"}, "platform": {"example": "google", "type": "string"}, "region": {"example": "CN", "type": "string"}, "frequency": {"example": 1250, "type": "integer"}, "ranking": {"example": 3, "type": "integer"}, "share_rate": {"example": 0.15, "format": "float", "type": "number"}, "click_count": {"example": 890, "type": "integer"}, "change_rate": {"example": 0.08, "format": "float", "type": "number"}, "last_week_data": {"example": "{\"frequency\": 1150, \"ranking\": 4}", "type": "string"}, "current_data": {"example": "{\"frequency\": 1250, \"ranking\": 3}", "type": "string"}, "analyzed_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "created_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "updated_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}}, "type": "object"}, "APIError": {"properties": {"code": {"description": "Standardized error code", "example": "INVALID_PARAMS", "type": "string"}, "message": {"description": "Human-readable error message", "example": "Invalid request parameters", "type": "string"}, "details": {"description": "Additional error details", "example": {"field": "email", "message": "Invalid email format"}, "type": "object"}, "timestamp": {"example": "2025-07-16T10:30:00Z", "format": "date-time", "type": "string"}, "path": {"description": "Request path where error occurred", "example": "/api/v1/auth/login", "type": "string"}, "request_id": {"description": "Unique request identifier", "example": "1721123400123456789", "type": "string"}}, "required": ["code", "message", "timestamp"], "type": "object"}, "ErrorResponse": {"properties": {"success": {"example": false, "type": "boolean"}, "message": {"example": "Error message", "type": "string"}, "error": {"example": "Detailed error information", "type": "string"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "Brand": {"properties": {"id": {"example": "4fc86ecb-8e0e-476b-8826-bf4dc95fce0d", "format": "uuid", "type": "string"}, "name": {"example": "TechCorp", "type": "string"}, "domain": {"example": "techcorp.com", "type": "string"}, "keywords": {"example": "technology,innovation,software", "type": "string"}, "description": {"example": "Leading technology company", "type": "string"}, "suggestions": {"type": "array", "items": {"type": "string"}, "example": ["What are the latest trends in technology?", "How can we improve our software development process?", "What are the best practices for digital marketing?"], "description": "Optional array of suggestions/prompts for the brand"}, "status": {"apipost_enable_enum": true, "enum": ["active", "inactive"], "example": "active", "type": "string"}, "created_at": {"format": "date-time", "type": "string"}, "updated_at": {"format": "date-time", "type": "string"}}, "type": "object"}, "BrandRequest": {"properties": {"name": {"example": "Tech Company", "type": "string"}, "domain": {"example": "techcompany.com", "type": "string"}, "keywords": {"example": "technology, innovation, software", "type": "string"}, "linked_url": {"example": "https://techcompany.com", "type": "string"}, "suggestions": {"type": "array", "items": {"type": "string"}, "example": ["What are the latest trends in technology?", "How can we improve our software development process?"], "description": "Optional array of suggestions/prompts for the brand"}, "origin_start_type": {"apipost_enable_enum": true, "enum": ["geo_keywords", "search_prompts", "specific_url"], "example": "geo_keywords", "type": "string"}}, "required": ["name", "domain"], "type": "object"}, "BrandUpdateRequest": {"properties": {"name": {"example": "Updated Tech Company", "type": "string"}, "domain": {"example": "newtechcompany.com", "type": "string"}, "keywords": {"example": "technology, innovation, software, AI", "type": "string"}, "linked_url": {"example": "https://newtechcompany.com", "type": "string"}, "suggestions": {"type": "array", "items": {"type": "string"}, "example": ["Updated suggestion 1", "Updated suggestion 2", "New suggestion 3"], "description": "Optional array of suggestions/prompts for the brand"}, "status": {"apipost_enable_enum": true, "enum": ["active", "inactive", "pending"], "example": "active", "type": "string"}, "is_asset_verified": {"example": true, "type": "boolean"}}, "type": "object"}, "CreateUserRequest": {"properties": {"username": {"example": "enterprise_user", "maxLength": 50, "minLength": 3, "type": "string"}, "email": {"example": "<EMAIL>", "format": "email", "type": "string"}, "password": {"example": "securepassword123", "minLength": 6, "type": "string"}, "first_name": {"example": "Enterprise", "type": "string"}, "last_name": {"example": "User", "type": "string"}, "role": {"apipost_enable_enum": true, "enum": ["user", "enterprise"], "example": "enterprise", "type": "string"}}, "required": ["username", "email", "password", "role"], "type": "object"}, "UpdateUserRequest": {"properties": {"first_name": {"example": "Updated First Name", "type": "string"}, "last_name": {"example": "Updated Last Name", "type": "string"}, "role": {"apipost_enable_enum": true, "enum": ["user", "enterprise", "admin"], "example": "enterprise", "type": "string"}, "status": {"apipost_enable_enum": true, "enum": ["active", "inactive", "suspended"], "example": "active", "type": "string"}}, "type": "object"}, "Notification": {"properties": {"id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "user_id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "type": {"enum": ["system", "prompt", "brand", "analytics", "alert", "update"], "example": "system", "type": "string"}, "priority": {"apipost_enable_enum": true, "enum": ["low", "normal", "high", "urgent"], "example": "normal", "type": "string"}, "title": {"example": "System Notification", "type": "string"}, "content": {"example": "Your system has been updated successfully", "type": "string"}, "data": {"example": "{}", "type": "string"}, "is_read": {"example": false, "type": "boolean"}, "read_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "action_url": {"example": "/dashboard", "type": "string"}, "expires_at": {"example": "2025-07-30T10:30:00Z", "format": "date-time", "type": "string"}, "created_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "updated_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}}, "type": "object"}, "GeoOverview": {"properties": {"id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "total_regions": {"example": 25, "type": "integer"}, "active_campaigns": {"example": 12, "type": "integer"}, "global_reach_score": {"example": 85.5, "type": "number"}, "top_performing_region": {"example": "North America", "type": "string"}, "created_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "updated_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}}, "type": "object"}, "GeoPerformance": {"properties": {"id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "region": {"example": "North America", "type": "string"}, "country": {"example": "United States", "type": "string"}, "visibility_score": {"example": 92.5, "type": "number"}, "market_penetration": {"example": 78.3, "type": "number"}, "search_volume": {"example": 125000, "type": "integer"}, "competition_level": {"enum": ["low", "medium", "high", "very_high"], "example": "high", "type": "string"}, "opportunity_score": {"example": 87.2, "type": "number"}, "recommended_actions": {"example": "[\"Increase content marketing\", \"Optimize SEO\"]", "type": "string"}, "key_metrics": {"example": "{\"ctr\": 3.2, \"conversion_rate\": 2.1}", "type": "string"}, "trending_keywords": {"example": "[\"AI technology\", \"machine learning\"]", "type": "string"}, "created_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "updated_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}}, "type": "object"}, "DatabaseOverview": {"properties": {"id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "total_databases": {"example": 5, "type": "integer"}, "active_connections": {"example": 150, "type": "integer"}, "total_storage": {"example": "2.5TB", "type": "string"}, "average_performance": {"example": 94.2, "type": "number"}, "created_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "updated_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}}, "type": "object"}, "DatabaseInfo": {"properties": {"id": {"example": "550e8400-e29b-41d4-a716-************", "format": "uuid", "type": "string"}, "name": {"example": "geok_production", "type": "string"}, "type": {"example": "PostgreSQL", "type": "string"}, "version": {"example": "14.9", "type": "string"}, "status": {"apipost_enable_enum": true, "enum": ["online", "offline", "maintenance"], "example": "online", "type": "string"}, "host": {"example": "***********", "type": "string"}, "port": {"example": 5439, "type": "integer"}, "size": {"example": "1.2GB", "type": "string"}, "connections": {"example": "{\"active\": 25, \"idle\": 5, \"max\": 100}", "type": "string"}, "performance": {"example": "{\"cpu_usage\": 45.2, \"memory_usage\": 67.8}", "type": "string"}, "backup_status": {"example": "{\"last_backup\": \"2025-07-15T02:00:00Z\", \"status\": \"success\"}", "type": "string"}, "tables": {"example": "{\"count\": 25, \"total_size\": \"800MB\"}", "type": "string"}, "created_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}, "updated_at": {"example": "2025-07-15T10:30:00Z", "format": "date-time", "type": "string"}}, "type": "object"}, "StandardResponse": {"properties": {"success": {"example": true, "type": "boolean"}, "message": {"example": "Operation successful", "type": "string"}, "data": {"type": "object"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "AIVisibilityMetrics": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "description": "可见性指标记录唯一标识符"}, "ai_search_id": {"type": "string", "format": "uuid", "description": "关联的AI搜索记录ID"}, "ai_response_id": {"type": "string", "format": "uuid", "description": "关联的AI搜索响应ID"}, "brand_id": {"type": "string", "format": "uuid", "description": "关联的品牌ID"}, "visibility_data": {"type": "object", "description": "可见性指标数据(JSON格式)", "properties": {"品牌推荐率": {"type": "number", "description": "品牌推荐率得分"}, "品牌搜索率": {"type": "number", "description": "品牌搜索率得分"}, "品牌在AI市场的首推率": {"type": "number", "description": "AI市场首推率得分"}, "品牌提及位置": {"type": "integer", "description": "品牌在响应中的位置"}, "品牌情感倾向": {"type": "string", "description": "品牌情感倾向"}, "竞争对手数量": {"type": "integer", "description": "竞争对手数量"}, "市场占有率": {"type": "number", "description": "市场占有率"}, "额外指标": {"type": "object", "properties": {"响应质量": {"type": "number", "description": "响应质量得分"}, "相关性": {"type": "number", "description": "相关性得分"}}}}}, "keyword_data": {"type": "object", "description": "关键词相关指标数据(JSON格式)", "properties": {"价格": {"type": "number", "description": "价格关键词频率"}, "质量": {"type": "number", "description": "质量关键词频率"}, "性能": {"type": "number", "description": "性能关键词频率"}, "性价比": {"type": "number", "description": "性价比关键词频率"}, "品牌": {"type": "number", "description": "品牌关键词频率"}, "产品": {"type": "number", "description": "产品关键词频率"}, "CPU需要配什么显卡": {"type": "number", "description": "复杂关键词频率"}, "CPU核心数量对游戏影响": {"type": "number", "description": "复杂关键词频率"}, "最佳处理器选择": {"type": "number", "description": "复杂关键词频率"}, "电脑配置推荐": {"type": "number", "description": "复杂关键词频率"}, "游戏性能对比": {"type": "number", "description": "复杂关键词频率"}, "办公电脑配置": {"type": "number", "description": "复杂关键词频率"}, "主要关键词": {"type": "array", "items": {"type": "string"}, "description": "主要关键词列表"}, "关键词密度": {"type": "number", "description": "关键词密度"}, "语义相关性": {"type": "number", "description": "语义相关性得分"}, "搜索意图匹配度": {"type": "number", "description": "搜索意图匹配度"}, "长尾关键词": {"type": "array", "items": {"type": "string"}, "description": "长尾关键词列表"}, "关键词分类": {"type": "object", "description": "关键词分类", "additionalProperties": {"type": "array", "items": {"type": "string"}}}, "额外关键词数据": {"type": "object", "properties": {"总词数": {"type": "integer", "description": "总词数"}, "唯一词数": {"type": "integer", "description": "唯一词数"}, "复杂度评分": {"type": "number", "description": "复杂度评分"}}}}}, "overall_score": {"type": "number", "description": "综合可见性得分(0-100)"}, "frequency_score": {"type": "number", "description": "AI中出现频率得分(0-100)"}, "recommendation_score": {"type": "number", "description": "品牌推荐率得分(0-100)"}, "search_rate_score": {"type": "number", "description": "品牌搜索率得分(0-100)"}, "first_choice_score": {"type": "number", "description": "AI市场首推率得分(0-100)"}, "calculated_at": {"type": "string", "format": "date-time", "description": "指标计算时间"}, "created_at": {"type": "string", "format": "date-time", "description": "记录创建时间"}, "updated_at": {"type": "string", "format": "date-time", "description": "记录最后更新时间"}}}, "BatchCalculateRequest": {"type": "object", "properties": {"response_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "AI响应ID列表"}}, "required": ["response_ids"]}, "Blog": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "550e8400-e29b-41d4-a716-************", "description": "博客唯一标识符"}, "title": {"type": "string", "example": "全能战士登场：英特尔酷睿i5-14600K测评", "description": "博客标题"}, "slug": {"type": "string", "example": "intel-i5-14600k-review-1753090027", "description": "博客URL别名，唯一"}, "excerpt": {"type": "string", "example": "英特尔酷睿i5-14600K以其出色的性能表现和合理的价格定位，成为中高端装机的热门选择。", "description": "博客摘要"}, "content": {"type": "string", "example": "# 全能战士登场：英特尔酷睿i5-14600K测评\n\n在这个追求极致性能与性价比并重的时代...", "description": "博客正文内容（Markdown格式）"}, "author": {"type": "object", "example": {"name": "<PERSON>", "email": "<EMAIL>", "avatar": "/avatars/harry.jpg"}, "description": "作者信息（JSON格式）"}, "category": {"type": "string", "example": "硬件评测", "description": "博客分类"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["Intel", "CPU", "硬件评测", "游戏性能"], "description": "博客标签"}, "status": {"type": "string", "enum": ["draft", "published", "scheduled", "archived"], "example": "published", "description": "博客状态"}, "featured_image": {"type": "string", "example": "/images/i5-14600k-review.jpg", "description": "特色图片URL"}, "word_count": {"type": "integer", "example": 1250, "description": "字数统计"}, "reading_time": {"type": "integer", "example": 6, "description": "预计阅读时间（分钟）"}, "view_count": {"type": "integer", "example": 1500, "description": "浏览次数"}, "like_count": {"type": "integer", "example": 85, "description": "点赞次数"}, "share_count": {"type": "integer", "example": 25, "description": "分享次数"}, "comment_count": {"type": "integer", "example": 12, "description": "评论次数"}, "brand_mentions": {"type": "object", "example": {"Intel": {"count": 15, "sentiment": "positive", "context": ["性能优秀", "值得推荐", "性价比高"]}}, "description": "品牌提及信息（JSON格式）"}, "mention_rate": {"type": "number", "format": "float", "example": 65.5, "description": "提及率百分比"}, "meta_title": {"type": "string", "example": "英特尔酷睿i5-14600K深度评测 - 中高端装机首选", "description": "SEO标题"}, "meta_description": {"type": "string", "example": "深度评测英特尔酷睿i5-14600K处理器的游戏性能和生产力表现，为您的装机选择提供专业建议。", "description": "SEO描述"}, "meta_keywords": {"type": "array", "items": {"type": "string"}, "example": ["Intel", "i5-14600K", "CPU评测", "游戏性能", "装机推荐"], "description": "SEO关键词"}, "published_at": {"type": "string", "format": "date-time", "example": "2025-07-21T09:30:00Z", "description": "发布时间"}, "scheduled_at": {"type": "string", "format": "date-time", "example": "2025-07-22T10:00:00Z", "description": "定时发布时间"}, "created_at": {"type": "string", "format": "date-time", "example": "2025-07-21T09:00:00Z", "description": "记录创建时间"}, "updated_at": {"type": "string", "format": "date-time", "example": "2025-07-21T09:30:00Z", "description": "记录最后更新时间"}}}, "CreateBlogRequest": {"type": "object", "required": ["title", "content", "category"], "properties": {"title": {"type": "string", "example": "全能战士登场：英特尔酷睿i5-14600K测评", "description": "博客标题"}, "slug": {"type": "string", "example": "intel-i5-14600k-review", "description": "博客URL别名（可选，不提供时自动生成）"}, "excerpt": {"type": "string", "example": "英特尔酷睿i5-14600K以其出色的性能表现和合理的价格定位，成为中高端装机的热门选择。", "description": "博客摘要"}, "content": {"type": "string", "example": "# 全能战士登场：英特尔酷睿i5-14600K测评\n\n在这个追求极致性能与性价比并重的时代，英特尔酷睿i5-14600K以其出色的表现赢得了广泛关注。\n\n## 性能表现\n\n### 游戏性能\n\n在游戏性能方面，i5-14600K表现出色：\n- **《赛博朋克2077》**: 1080p高画质下平均帧率达到85fps\n- **《战地2042》**: 1440p中高画质稳定在120fps以上\n\n## 总结\n\n英特尔酷睿i5-14600K凭借其出色的单核性能、优秀的多核表现以及合理的价格定位，无疑是当前中高端装机的最佳选择之一。", "description": "博客正文内容（Markdown格式）"}, "author": {"type": "object", "example": {"name": "<PERSON>", "email": "<EMAIL>", "avatar": "/avatars/harry.jpg"}, "description": "作者信息（JSON格式）"}, "category": {"type": "string", "example": "硬件评测", "description": "博客分类"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["Intel", "CPU", "硬件评测", "游戏性能"], "description": "博客标签"}, "status": {"type": "string", "enum": ["draft", "published", "scheduled"], "example": "published", "description": "博客状态"}, "featured_image": {"type": "string", "example": "/images/i5-14600k-review.jpg", "description": "特色图片URL"}, "meta_title": {"type": "string", "example": "英特尔酷睿i5-14600K深度评测 - 中高端装机首选", "description": "SEO标题"}, "meta_description": {"type": "string", "example": "深度评测英特尔酷睿i5-14600K处理器的游戏性能和生产力表现，为您的装机选择提供专业建议。", "description": "SEO描述"}, "meta_keywords": {"type": "array", "items": {"type": "string"}, "example": ["Intel", "i5-14600K", "CPU评测", "游戏性能", "装机推荐"], "description": "SEO关键词"}}}, "UpdateBlogRequest": {"type": "object", "properties": {"title": {"type": "string", "example": "全能战士登场：英特尔酷睿i5-14600K深度评测（更新版）", "description": "博客标题"}, "slug": {"type": "string", "example": "intel-i5-14600k-review-updated", "description": "博客URL别名"}, "excerpt": {"type": "string", "example": "英特尔酷睿i5-14600K深度评测更新版，包含最新的性能测试数据和装机建议。", "description": "博客摘要"}, "content": {"type": "string", "example": "# 全能战士登场：英特尔酷睿i5-14600K深度评测（更新版）\n\n更新了最新的测试数据...", "description": "博客正文内容（Markdown格式）"}, "author": {"type": "object", "example": {"name": "<PERSON>", "email": "<EMAIL>", "avatar": "/avatars/harry.jpg"}, "description": "作者信息（JSON格式）"}, "category": {"type": "string", "example": "硬件评测", "description": "博客分类"}, "tags": {"type": "array", "items": {"type": "string"}, "example": ["Intel", "CPU", "硬件评测", "游戏性能", "更新"], "description": "博客标签"}, "status": {"type": "string", "enum": ["draft", "published", "scheduled", "archived"], "example": "published", "description": "博客状态"}, "featured_image": {"type": "string", "example": "/images/i5-14600k-review-updated.jpg", "description": "特色图片URL"}, "meta_title": {"type": "string", "example": "英特尔酷睿i5-14600K深度评测（更新版） - 最新性能数据", "description": "SEO标题"}, "meta_description": {"type": "string", "example": "最新更新的英特尔酷睿i5-14600K评测，包含详细的性能测试数据和专业装机建议。", "description": "SEO描述"}, "meta_keywords": {"type": "array", "items": {"type": "string"}, "example": ["Intel", "i5-14600K", "CPU评测", "游戏性能", "装机推荐", "更新"], "description": "SEO关键词"}}}, "BlogListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Blogs retrieved successfully"}, "data": {"type": "object", "properties": {"blogs": {"type": "array", "items": {"$ref": "#/components/schemas/Blog"}}, "pagination": {"type": "object", "properties": {"current_page": {"type": "integer", "example": 1}, "page_size": {"type": "integer", "example": 10}, "total_items": {"type": "integer", "example": 25}, "total_pages": {"type": "integer", "example": 3}}}}}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-07-21T09:30:00Z"}}}}}, "paths": {"/health": {"get": {"summary": "健康检查", "description": "检查服务健康状态", "tags": ["Health"], "parameters": [], "responses": {"200": {"description": "服务正常", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "hello geok", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}}}}}}}, "/ping": {"get": {"summary": "<PERSON>检查", "description": "简单的ping检查，返回pong", "tags": ["Health"], "parameters": [], "responses": {"200": {"description": "<PERSON>成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object", "properties": {"time": {"type": "integer", "example": **********}}}, "message": {"example": "pong", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}}}}}}}, "/status": {"get": {"summary": "服务状态", "description": "获取详细的服务状态信息", "tags": ["Health"], "parameters": [], "responses": {"200": {"description": "状态信息", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object", "properties": {"api": {"type": "string", "example": "online"}, "database": {"type": "string", "example": "connected"}, "redis": {"type": "string", "example": "connected"}, "timestamp": {"type": "integer", "example": **********}}}, "message": {"example": "", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}}}}}}}, "/auth/login": {"post": {"summary": "用户登录", "description": "用户登录认证，支持邮箱密码登录和Google OAuth登录", "tags": ["Authentication"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"properties": {"email": {"example": "<EMAIL>", "format": "email", "type": "string"}, "id_token": {"description": "Google ID token for OAuth login", "type": "string"}, "password": {"example": "password123", "type": "string"}}, "type": "object"}, "example": "{\n\t\"email\": \"<EMAIL>\",\n\t\"password\": \"password123\",\n\t\"id_token\": \"\"\n}"}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"user": {"type": "object", "$$ref": "3ed8de0ef705e5", "properties": {"id": {"type": "string", "format": "uuid", "example": "550e8400-e29b-41d4-a716-************"}, "role": {"enum": ["admin", "user"], "type": "string", "example": "user"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "status": {"enum": ["active", "inactive"], "type": "string", "example": "active"}, "username": {"type": "string", "example": "john_doe"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "created_at": {"type": "string", "format": "date-time"}, "first_name": {"type": "string", "example": "<PERSON>"}, "updated_at": {"type": "string", "format": "date-time"}}}, "token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}}, "message": {"type": "string", "example": "Operation successful"}, "success": {"type": "boolean", "example": true}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-07-20T21:56:01Z"}}}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {\n\t\t\"token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n\t\t\"user\": {\n\t\t\t\"id\": \"550e8400-e29b-41d4-a716-************\",\n\t\t\t\"username\": \"john_doe\",\n\t\t\t\"email\": \"<EMAIL>\",\n\t\t\t\"first_name\": \"<PERSON>\",\n\t\t\t\"last_name\": \"<PERSON><PERSON>\",\n\t\t\t\"role\": \"user\",\n\t\t\t\"status\": \"active\",\n\t\t\t\"created_at\": \"\",\n\t\t\t\"updated_at\": \"\"\n\t\t}\n\t},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}, "400": {"description": "登录失败", "content": {"application/json": {"schema": {"properties": {"error": {"example": "Detailed error information", "type": "string"}, "message": {"example": "Error message", "type": "string"}, "success": {"example": false, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": \"\",\n\t\"message\": \"Error message\",\n\t\"error\": \"Detailed error information\",\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/auth/register": {"post": {"summary": "用户注册", "description": "创建新用户账户", "tags": ["Authentication"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"properties": {"email": {"example": "<EMAIL>", "format": "email", "type": "string"}, "first_name": {"example": "<PERSON>", "type": "string"}, "last_name": {"example": "<PERSON><PERSON>", "type": "string"}, "password": {"example": "password123", "minLength": 6, "type": "string"}, "username": {"example": "john_doe", "type": "string"}}, "type": "object"}, "example": "{\n\t\"username\": \"john_doe\",\n\t\"email\": \"<EMAIL>\",\n\t\"password\": \"password123\",\n\t\"first_name\": \"<PERSON>\",\n\t\"last_name\": \"<PERSON><PERSON>\"\n}"}}}, "responses": {"201": {"description": "注册成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}, "400": {"description": "注册失败", "content": {"application/json": {"schema": {"properties": {"error": {"example": "Detailed error information", "type": "string"}, "message": {"example": "Error message", "type": "string"}, "success": {"example": false, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": \"\",\n\t\"message\": \"Error message\",\n\t\"error\": \"Detailed error information\",\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/auth/logout": {"post": {"summary": "用户登出", "description": "用户登出，清除认证状态", "tags": ["Authentication"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "登出成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/auth/me": {"get": {"summary": "获取当前用户信息", "description": "获取当前登录用户的详细信息", "tags": ["Authentication"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "用户信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "$$ref": "3ed8de0ef705e5", "properties": {"id": {"type": "string", "format": "uuid", "example": "550e8400-e29b-41d4-a716-************"}, "role": {"enum": ["admin", "user"], "type": "string", "example": "user"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "status": {"enum": ["active", "inactive"], "type": "string", "example": "active"}, "username": {"type": "string", "example": "john_doe"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "created_at": {"type": "string", "format": "date-time"}, "first_name": {"type": "string", "example": "<PERSON>"}, "updated_at": {"type": "string", "format": "date-time"}}}, "message": {"type": "string", "example": "Operation successful"}, "success": {"type": "boolean", "example": true}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-07-20T21:56:01Z"}}}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {\n\t\t\"id\": \"550e8400-e29b-41d4-a716-************\",\n\t\t\"username\": \"john_doe\",\n\t\t\"email\": \"<EMAIL>\",\n\t\t\"first_name\": \"<PERSON>\",\n\t\t\"last_name\": \"<PERSON>e\",\n\t\t\"role\": \"user\",\n\t\t\"status\": \"active\",\n\t\t\"created_at\": \"\",\n\t\t\"updated_at\": \"\"\n\t},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"properties": {"error": {"example": "Detailed error information", "type": "string"}, "message": {"example": "Error message", "type": "string"}, "success": {"example": false, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": \"\",\n\t\"message\": \"Error message\",\n\t\"error\": \"Detailed error information\",\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/auth/refresh": {"post": {"summary": "刷新令牌", "description": "刷新JWT令牌", "tags": ["Authentication"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "令牌刷新成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/brands": {"get": {"summary": "获取品牌列表", "description": "获取用户的品牌列表，支持分页和搜索", "tags": ["Brands"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "page_size", "in": "query", "description": "每页数量", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "search", "in": "query", "description": "搜索关键词", "required": false, "example": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "品牌列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"page": {"type": "integer", "example": 1}, "total": {"type": "integer", "example": 100}, "brands": {"type": "array", "items": {"type": "object", "$$ref": "3ed8de0ef705e6", "properties": {"id": {"type": "string", "format": "uuid", "example": "4fc86ecb-8e0e-476b-8826-bf4dc95fce0d"}, "name": {"type": "string", "example": "TechCorp"}, "domain": {"type": "string", "example": "techcorp.com"}, "status": {"enum": ["active", "inactive"], "type": "string", "example": "active"}, "keywords": {"type": "string", "example": "technology,innovation,software"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "description": {"type": "string", "example": "Leading technology company"}}}}, "page_size": {"type": "integer", "example": 10}}}, "message": {"type": "string", "example": "Operation successful"}, "success": {"type": "boolean", "example": true}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-07-20T21:56:01Z"}}}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {\n\t\t\"brands\": [\n\t\t\t{\n\t\t\t\t\"id\": \"4fc86ecb-8e0e-476b-8826-bf4dc95fce0d\",\n\t\t\t\t\"name\": \"TechCorp\",\n\t\t\t\t\"domain\": \"techcorp.com\",\n\t\t\t\t\"keywords\": \"technology,innovation,software\",\n\t\t\t\t\"description\": \"Leading technology company\",\n\t\t\t\t\"status\": \"active\",\n\t\t\t\t\"created_at\": \"\",\n\t\t\t\t\"updated_at\": \"\"\n\t\t\t}\n\t\t],\n\t\t\"total\": 100,\n\t\t\"page\": 1,\n\t\t\"page_size\": 10\n\t},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "post": {"summary": "创建品牌", "description": "创建新的品牌", "tags": ["Brands"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["name", "domain"], "properties": {"name": {"type": "string", "example": "TechCorp"}, "domain": {"type": "string", "example": "techcorp.com"}, "keywords": {"type": "string", "example": "technology,innovation,software"}, "description": {"type": "string", "example": "Leading technology company"}}}, "example": "{\n\t\"name\": \"TechCorp\",\n\t\"domain\": \"techcorp.com\",\n\t\"keywords\": \"technology,innovation,software\",\n\t\"description\": \"Leading technology company\"\n}"}}}, "responses": {"201": {"description": "品牌创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "$$ref": "3ed8de0ef705e6", "properties": {"id": {"type": "string", "format": "uuid", "example": "4fc86ecb-8e0e-476b-8826-bf4dc95fce0d"}, "name": {"type": "string", "example": "TechCorp"}, "domain": {"type": "string", "example": "techcorp.com"}, "status": {"enum": ["active", "inactive"], "type": "string", "example": "active"}, "keywords": {"type": "string", "example": "technology,innovation,software"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "description": {"type": "string", "example": "Leading technology company"}}}, "message": {"type": "string", "example": "Operation successful"}, "success": {"type": "boolean", "example": true}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-07-20T21:56:01Z"}}}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {\n\t\t\"id\": \"4fc86ecb-8e0e-476b-8826-bf4dc95fce0d\",\n\t\t\"name\": \"TechCorp\",\n\t\t\"domain\": \"techcorp.com\",\n\t\t\"keywords\": \"technology,innovation,software\",\n\t\t\"description\": \"Leading technology company\",\n\t\t\"status\": \"active\",\n\t\t\"created_at\": \"\",\n\t\t\"updated_at\": \"\"\n\t},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/brands/{id}": {"get": {"summary": "获取品牌详情", "description": "根据ID获取品牌详细信息", "tags": ["Brands"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "品牌详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "$$ref": "3ed8de0ef705e6", "properties": {"id": {"type": "string", "format": "uuid", "example": "4fc86ecb-8e0e-476b-8826-bf4dc95fce0d"}, "name": {"type": "string", "example": "TechCorp"}, "domain": {"type": "string", "example": "techcorp.com"}, "status": {"enum": ["active", "inactive"], "type": "string", "example": "active"}, "keywords": {"type": "string", "example": "technology,innovation,software"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "description": {"type": "string", "example": "Leading technology company"}}}, "message": {"type": "string", "example": "Operation successful"}, "success": {"type": "boolean", "example": true}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-07-20T21:56:01Z"}}}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {\n\t\t\"id\": \"4fc86ecb-8e0e-476b-8826-bf4dc95fce0d\",\n\t\t\"name\": \"TechCorp\",\n\t\t\"domain\": \"techcorp.com\",\n\t\t\"keywords\": \"technology,innovation,software\",\n\t\t\"description\": \"Leading technology company\",\n\t\t\"status\": \"active\",\n\t\t\"created_at\": \"\",\n\t\t\"updated_at\": \"\"\n\t},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}, "404": {"description": "品牌不存在", "content": {"application/json": {"schema": {"properties": {"error": {"example": "Detailed error information", "type": "string"}, "message": {"example": "Error message", "type": "string"}, "success": {"example": false, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": \"\",\n\t\"message\": \"Error message\",\n\t\"error\": \"Detailed error information\",\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "put": {"summary": "更新品牌", "description": "更新品牌信息", "tags": ["Brands"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "example": "TechCorp"}, "domain": {"type": "string", "example": "techcorp.com"}, "keywords": {"type": "string", "example": "technology,innovation,software"}, "description": {"type": "string", "example": "Leading technology company"}}}, "example": "{\n\t\"name\": \"TechCorp\",\n\t\"domain\": \"techcorp.com\",\n\t\"keywords\": \"technology,innovation,software\",\n\t\"description\": \"Leading technology company\"\n}"}}}, "responses": {"200": {"description": "品牌更新成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "delete": {"summary": "删除品牌", "description": "删除指定品牌", "tags": ["Brands"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "品牌删除成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/brands/{id}/ai-searches": {"get": {"summary": "获取品牌AI搜索列表", "description": "获取指定品牌的AI搜索记录", "tags": ["AI Searches"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "page_size", "in": "query", "description": "每页数量", "required": false, "example": "", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "AI搜索列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "post": {"summary": "创建品牌AI搜索", "description": "为指定品牌创建AI搜索任务", "tags": ["AI Searches"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["questions"], "properties": {"questions": {"type": "array", "items": {"type": "string"}, "example": ["推荐一个可靠的技术解决方案提供商", "哪家公司在云计算领域表现最好？"]}}}, "example": "{\n\t\"questions\": [\n\t\t\"\"\n\t]\n}"}}}, "responses": {"200": {"description": "AI搜索创建成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/brands/{id}/ai-visibility/trend": {"get": {"summary": "获取品牌可见性趋势", "description": "获取指定品牌的AI可见性趋势分析", "tags": ["AI Visibility"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "品牌ID"}, {"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "default": 30}, "description": "分析天数"}], "responses": {"200": {"description": "趋势分析数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Visibility trend retrieved successfully"}, "data": {"type": "object", "properties": {"brand_id": {"type": "string", "format": "uuid"}, "days": {"type": "integer"}, "trend": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "ai_search_id": {"type": "string", "format": "uuid", "description": "关联的AI搜索ID"}, "ai_response_id": {"type": "string", "format": "uuid", "description": "关联的AI响应ID"}, "brand_id": {"type": "string", "format": "uuid"}, "visibility_data": {"type": "object", "description": "可见性指标数据"}, "keyword_data": {"type": "object", "description": "关键词数据"}, "overall_score": {"type": "number"}, "frequency_score": {"type": "number"}, "recommendation_score": {"type": "number"}, "search_rate_score": {"type": "number"}, "first_choice_score": {"type": "number"}, "calculated_at": {"type": "string", "format": "date-time"}, "ai_search": {"type": "object", "description": "关联的AI搜索信息", "properties": {"id": {"type": "string", "format": "uuid"}, "question": {"type": "string", "description": "AI搜索问题"}, "question_type": {"type": "string", "description": "问题类型"}, "keywords": {"type": "string", "description": "关键词"}, "status": {"type": "string", "description": "搜索状态"}, "region": {"type": "string", "description": "地理区域"}, "language": {"type": "string", "description": "语言"}}}, "ai_response": {"type": "object", "description": "关联的AI响应信息", "properties": {"id": {"type": "string", "format": "uuid"}, "response": {"type": "string", "description": "AI响应内容"}, "brand_position": {"type": "integer", "description": "品牌位置"}, "brand_sentiment": {"type": "string", "description": "品牌情感"}, "confidence": {"type": "number", "description": "置信度"}, "relevance": {"type": "number", "description": "相关性"}}}, "brand": {"type": "object", "description": "关联的品牌信息", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "description": "品牌名称"}, "domain": {"type": "string", "description": "品牌域名"}, "keywords": {"type": "string", "description": "品牌关键词"}, "description": {"type": "string", "description": "品牌描述"}}}}}}, "count": {"type": "integer"}, "summary": {"type": "object", "description": "趋势分析摘要", "properties": {"total_metrics": {"type": "integer", "description": "总指标数量"}, "date_range_start": {"type": "string", "format": "date", "description": "开始日期"}, "date_range_end": {"type": "string", "format": "date", "description": "结束日期"}}}}}}}}}}}}}, "/brands/{id}/ai-visibility/aggregation": {"post": {"summary": "计算品牌可见性聚合", "description": "计算指定品牌的可见性聚合数据", "tags": ["AI Visibility"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "品牌ID"}], "responses": {"200": {"description": "聚合计算成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Brand aggregation calculated successfully"}, "data": {"type": "object"}}}}}}}}, "get": {"summary": "获取品牌可见性聚合历史", "description": "获取指定品牌的可见性聚合历史数据", "tags": ["AI Visibility"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "品牌ID"}], "responses": {"200": {"description": "聚合历史数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Aggregation history retrieved successfully"}, "data": {"type": "array", "items": {"type": "object"}}}}}}}}}}, "/brands/{id}/ai-visibility/report": {"get": {"summary": "生成品牌可见性报告", "description": "生成指定品牌的综合可见性报告", "tags": ["AI Visibility"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "品牌ID"}], "responses": {"200": {"description": "可见性报告", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Visibility report generated successfully"}, "data": {"type": "object", "properties": {"brand_id": {"type": "string", "format": "uuid"}, "report_date": {"type": "string", "format": "date-time"}, "summary": {"type": "object", "description": "报告摘要"}, "metrics": {"type": "object", "description": "详细指标"}, "recommendations": {"type": "array", "items": {"type": "string"}}}}}}}}}}}}, "/ai-visibility/compare/brands/{id1}/{id2}": {"get": {"summary": "品牌可见性对比", "description": "对比两个品牌的可见性指标", "tags": ["AI Visibility"], "parameters": [{"name": "id1", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "第一个品牌ID"}, {"name": "id2", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "第二个品牌ID"}], "responses": {"200": {"description": "品牌对比结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Brand comparison completed successfully"}, "data": {"type": "object", "properties": {"brand1": {"type": "object", "description": "品牌1数据"}, "brand2": {"type": "object", "description": "品牌2数据"}, "comparison": {"type": "object", "description": "对比分析"}, "insights": {"type": "array", "items": {"type": "string"}}}}}}}}}}}}, "/ai-searches": {"get": {"summary": "获取AI搜索列表", "description": "获取用户的AI搜索记录列表", "tags": ["AI Searches"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "page_size", "in": "query", "description": "每页数量", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "status", "in": "query", "description": "搜索状态", "required": false, "example": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "AI搜索列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "post": {"summary": "创建AI搜索", "description": "创建新的AI搜索记录", "tags": ["AI Searches"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"brand_id": {"type": "string", "format": "uuid", "description": "品牌ID"}, "query": {"type": "string", "description": "搜索查询"}, "model_type": {"type": "string", "enum": ["chatgpt", "claude", "gemini", "perplexity"], "description": "AI模型类型"}}, "required": ["brand_id", "query", "model_type"]}}}}, "responses": {"201": {"description": "AI搜索创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "AI search created successfully"}, "data": {"type": "object"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}, "error": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "401": {"description": "未授权", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}, "error": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}, "/ai-searches/{id}": {"get": {"summary": "获取AI搜索详情", "description": "获取指定AI搜索的详细信息", "tags": ["AI Searches"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "AI搜索详情", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "put": {"summary": "更新AI搜索状态", "description": "更新指定AI搜索的状态", "tags": ["AI Searches"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "AI搜索ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["pending", "processing", "completed", "failed"], "description": "新的状态"}}, "required": ["status"]}}}}, "responses": {"200": {"description": "状态更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "AI search status updated successfully"}, "data": {"type": "object"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}, "error": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "404": {"description": "AI搜索不存在", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}, "error": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}, "delete": {"summary": "删除AI搜索", "description": "删除指定的AI搜索记录", "tags": ["AI Searches"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/ai-visibility/responses/{responseId}/calculate": {"post": {"summary": "计算AI响应可见性指标", "description": "为指定的AI响应计算可见性指标", "tags": ["AI Visibility"], "parameters": [{"name": "responseId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "AI响应ID"}], "responses": {"200": {"description": "成功计算可见性指标", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Visibility metrics calculated successfully"}, "data": {"type": "object"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "无效的响应ID", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Invalid response ID"}, "error": {"type": "string"}}}}}}}}}, "/ai-visibility/responses/{responseId}/metrics": {"get": {"summary": "获取AI响应可见性指标", "description": "获取指定AI响应的可见性指标数据", "tags": ["AI Visibility"], "parameters": [{"name": "responseId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "AI响应ID"}], "responses": {"200": {"description": "成功获取可见性指标", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Visibility metrics retrieved successfully"}, "data": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "ai_response_id": {"type": "string", "format": "uuid"}, "brand_id": {"type": "string", "format": "uuid"}, "visibility_data": {"type": "object", "description": "可见性数据JSON"}, "keyword_data": {"type": "object", "description": "关键词数据JSON"}, "overall_score": {"type": "number", "description": "综合得分"}, "frequency_score": {"type": "number", "description": "频率得分"}, "recommendation_score": {"type": "number", "description": "推荐得分"}, "search_rate_score": {"type": "number", "description": "搜索率得分"}, "first_choice_score": {"type": "number", "description": "首选得分"}, "calculated_at": {"type": "string", "format": "date-time"}}}}}}}}}}}, "/ai-visibility/batch-calculate": {"post": {"summary": "批量计算可见性指标", "description": "批量计算多个AI响应的可见性指标", "tags": ["AI Visibility"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"response_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "AI响应ID列表"}}, "required": ["response_ids"]}}}}, "responses": {"200": {"description": "批量计算结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Batch calculation completed"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总数"}, "success_count": {"type": "integer", "description": "成功数量"}, "error_count": {"type": "integer", "description": "失败数量"}, "results": {"type": "object", "description": "详细结果"}}}}}}}}}}}, "/ai-visibility/stats": {"get": {"summary": "获取可见性指标统计", "description": "获取可见性指标的统计信息", "tags": ["AI Visibility"], "parameters": [{"name": "brand_id", "in": "query", "required": false, "schema": {"type": "string", "format": "uuid"}, "description": "品牌ID（可选）"}], "responses": {"200": {"description": "统计信息", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Statistics retrieved successfully"}, "data": {"type": "object", "properties": {"total_metrics": {"type": "integer", "description": "总指标数"}, "average_score": {"type": "number", "description": "平均得分"}, "brand_id": {"type": "string", "format": "uuid"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}}}, "/brands/{id}/prompts": {"get": {"summary": "获取品牌提示词", "description": "获取指定品牌的提示词列表", "tags": ["Prompts"], "parameters": [{"name": "category", "in": "query", "description": "提示词分类", "required": false, "example": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "提示词列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/prompts": {"get": {"summary": "获取提示词列表", "description": "获取用户的提示词列表", "tags": ["Prompts"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "page_size", "in": "query", "description": "每页数量", "required": false, "example": "", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "提示词列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "post": {"summary": "创建提示词", "description": "创建新的提示词", "tags": ["Prompts"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["brand_id", "title", "content"], "properties": {"title": {"type": "string", "example": "品牌优势分析"}, "content": {"type": "string", "example": "该品牌在技术创新方面表现突出"}, "brand_id": {"type": "string", "format": "uuid", "example": "4fc86ecb-8e0e-476b-8826-bf4dc95fce0d"}, "category": {"type": "string", "example": "analysis"}, "description": {"type": "string", "example": "基于AI分析生成的品牌优势总结"}}}, "example": "{\n\t\"brand_id\": \"4fc86ecb-8e0e-476b-8826-bf4dc95fce0d\",\n\t\"title\": \"品牌优势分析\",\n\t\"content\": \"该品牌在技术创新方面表现突出\",\n\t\"description\": \"基于AI分析生成的品牌优势总结\",\n\t\"category\": \"analysis\"\n}"}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/brands/{id}/references": {"get": {"summary": "获取品牌引用", "description": "获取指定品牌的引用列表", "tags": ["References"], "parameters": [{"name": "category", "in": "query", "description": "引用分类", "required": false, "example": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "引用列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/references": {"get": {"summary": "获取引用列表", "description": "获取用户的引用列表", "tags": ["References"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "page_size", "in": "query", "description": "每页数量", "required": false, "example": "", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "引用列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/analytics/dashboard": {"get": {"summary": "获取仪表板数据", "description": "获取用户仪表板的统计数据", "tags": ["Analytics"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "仪表板数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "object", "properties": {"total_brands": {"type": "integer", "example": 5}, "total_searches": {"type": "integer", "example": 150}, "total_responses": {"type": "integer", "example": 450}, "avg_visibility_score": {"type": "number", "example": 75.5}}}, "message": {"type": "string", "example": "Operation successful"}, "success": {"type": "boolean", "example": true}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-07-20T21:56:01Z"}}}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {\n\t\t\"total_brands\": 5,\n\t\t\"total_searches\": 150,\n\t\t\"total_responses\": 450,\n\t\t\"avg_visibility_score\": 75.5\n\t},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/analytics/trends": {"get": {"summary": "获取趋势分析", "description": "获取品牌可见性趋势分析数据", "tags": ["Analytics"], "parameters": [{"name": "period", "in": "query", "description": "分析周期", "required": false, "example": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "趋势分析数据", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/auth/profile": {"put": {"summary": "更新个人资料", "description": "更新当前用户的个人资料信息", "tags": ["Authentication"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"bio": {"type": "string", "example": "Software developer"}, "phone": {"type": "string", "example": "******-0123"}, "company": {"type": "string", "example": "Tech Corp"}, "country": {"type": "string", "example": "United States"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "first_name": {"type": "string", "example": "<PERSON>"}}}, "example": "{\n\t\"first_name\": \"<PERSON>\",\n\t\"last_name\": \"<PERSON><PERSON>\",\n\t\"bio\": \"Software developer\",\n\t\"phone\": \"******-0123\",\n\t\"company\": \"Tech Corp\",\n\t\"country\": \"United States\"\n}"}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/auth/avatar": {"put": {"summary": "上传头像", "description": "上传或更新用户头像", "tags": ["Authentication"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object"}, "example": "{\r\n    \"url\":\"/asd\"\r\n}"}}}, "responses": {"200": {"description": "上传成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/auth/password": {"put": {"summary": "修改密码", "description": "修改当前用户的密码", "tags": ["Authentication"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["current_password", "new_password"], "properties": {"new_password": {"type": "string", "example": "newpassword123", "minLength": 6}, "current_password": {"type": "string", "example": "oldpassword123"}}}, "example": "{\n\t\"current_password\": \"password123\",\n\t\"new_password\": \"newpassword123\",\n\t\"confirm_password\":\"newpassword123\"\n}\n"}}}, "responses": {"200": {"description": "密码修改成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/auth/email": {"put": {"summary": "修改邮箱", "description": "修改当前用户的邮箱地址", "tags": ["Authentication"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["new_email"], "properties": {"new_email": {"type": "string", "format": "email", "example": "<EMAIL>"}}}, "example": "{\n\t\"new_email\": \"<EMAIL>\"\n}"}}}, "responses": {"200": {"description": "邮箱修改成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/ai-searches/status/{status}": {"get": {"summary": "按状态获取AI搜索", "description": "根据状态获取AI搜索列表", "tags": ["AI Searches"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "AI搜索列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/ai-searches/stats": {"get": {"summary": "获取AI搜索统计", "description": "获取AI搜索的统计信息", "tags": ["AI Searches"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "AI搜索统计数据", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/ai-searches/process-pending": {"post": {"summary": "处理待处理的搜索", "description": "批量处理状态为pending的AI搜索", "tags": ["AI Searches"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "处理成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/ai-responses": {"get": {"summary": "获取AI响应列表", "description": "获取AI搜索响应列表", "tags": ["AI Searches"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "page_size", "in": "query", "description": "每页数量", "required": false, "example": "", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "AI响应列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/ai-responses/{id}": {"get": {"summary": "获取AI响应详情", "description": "根据ID获取AI响应详细信息", "tags": ["AI Searches"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "AI响应详情", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "delete": {"summary": "删除AI响应", "description": "删除指定的AI响应", "tags": ["AI Searches"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/ai-responses/search/{searchId}": {"get": {"summary": "获取搜索的AI响应", "description": "根据搜索ID获取相关的AI响应", "tags": ["AI Searches"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "AI响应列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/ai-responses/model/{modelType}": {"get": {"summary": "按模型类型获取AI响应", "description": "根据AI模型类型获取响应", "tags": ["AI Searches"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "AI响应列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/prompts/{id}": {"get": {"summary": "获取提示词详情", "description": "根据ID获取提示词详细信息", "tags": ["Prompts"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "提示词详情", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "put": {"summary": "更新提示词", "description": "更新指定的提示词", "tags": ["Prompts"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string", "example": "品牌优势分析"}, "content": {"type": "string", "example": "该品牌在技术创新方面表现突出"}, "category": {"type": "string", "example": "analysis"}, "description": {"type": "string", "example": "基于AI分析生成的品牌优势总结"}}}, "example": "{\n\t\"title\": \"品牌优势分析\",\n\t\"content\": \"该品牌在技术创新方面表现突出\",\n\t\"description\": \"基于AI分析生成的品牌优势总结\",\n\t\"category\": \"analysis\"\n}"}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "delete": {"summary": "删除提示词", "description": "删除指定的提示词", "tags": ["Prompts"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/prompts/top": {"get": {"summary": "获取热门提示词", "description": "获取排名靠前的热门提示词", "tags": ["Prompts"], "parameters": [{"name": "limit", "in": "query", "description": "返回数量限制", "required": false, "example": "", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "热门提示词列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/references/{id}": {"get": {"summary": "获取引用详情", "description": "根据ID获取引用详细信息", "tags": ["References"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "引用详情", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "delete": {"summary": "删除引用", "description": "删除指定的引用", "tags": ["References"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/references/top": {"get": {"summary": "获取热门引用", "description": "获取排名靠前的热门引用", "tags": ["References"], "parameters": [{"name": "limit", "in": "query", "description": "返回数量限制", "required": false, "example": "", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "热门引用列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/references/domain/{domain}": {"get": {"summary": "按域名获取引用", "description": "根据域名获取引用列表", "tags": ["References"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "引用列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/references/extract/{responseId}": {"post": {"summary": "从AI响应提取引用", "description": "从指定的AI响应中提取引用信息", "tags": ["References"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "提取成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/geo-optimizations": {"post": {"summary": "创建GEO优化记录", "description": "为品牌创建新的GEO优化记录", "tags": ["GEO Optimization"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["brand_id", "region", "country"], "properties": {"brand_id": {"type": "string", "format": "uuid", "description": "品牌ID"}, "region": {"type": "string", "description": "地理区域"}, "country": {"type": "string", "description": "国家"}, "city": {"type": "string", "description": "城市"}, "visibility_score": {"type": "number", "description": "可见性评分"}, "market_penetration": {"type": "number", "description": "市场渗透率"}, "search_volume": {"type": "integer", "description": "搜索量"}, "competition_level": {"type": "string", "enum": ["low", "medium", "high", "very_high"], "description": "竞争水平"}, "opportunity_score": {"type": "number", "description": "机会评分"}, "priority": {"type": "integer", "description": "优先级"}}}}}}, "responses": {"201": {"description": "GEO优化记录创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO optimization created successfully"}, "data": {"type": "object"}}}}}}}}, "get": {"summary": "获取GEO优化列表", "description": "获取GEO优化记录列表", "tags": ["GEO Optimization"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "default": 10}, "description": "每页数量"}], "responses": {"200": {"description": "GEO优化列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO optimizations retrieved successfully"}, "data": {"type": "object", "properties": {"data": {"type": "array", "items": {"type": "object"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "page_size": {"type": "integer"}, "total_pages": {"type": "integer"}}}}}}}}}}}, "/geo-optimizations/{id}": {"get": {"summary": "获取GEO优化详情", "description": "根据ID获取GEO优化记录详情", "tags": ["GEO Optimization"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "GEO优化记录ID"}], "responses": {"200": {"description": "GEO优化详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO optimization retrieved successfully"}, "data": {"type": "object"}}}}}}}}, "put": {"summary": "更新GEO优化记录", "description": "更新指定的GEO优化记录", "tags": ["GEO Optimization"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "GEO优化记录ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"visibility_score": {"type": "number"}, "market_penetration": {"type": "number"}, "search_volume": {"type": "integer"}, "competition_level": {"type": "string"}, "opportunity_score": {"type": "number"}, "priority": {"type": "integer"}}}}}}, "responses": {"200": {"description": "GEO优化记录更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO optimization updated successfully"}, "data": {"type": "object"}}}}}}}}, "delete": {"summary": "删除GEO优化记录", "description": "删除指定的GEO优化记录", "tags": ["GEO Optimization"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "GEO优化记录ID"}], "responses": {"200": {"description": "GEO优化记录删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO optimization deleted successfully"}}}}}}}}}, "/brands/{brandId}/geo-optimizations": {"get": {"summary": "获取品牌GEO优化", "description": "获取指定品牌的GEO优化记录", "tags": ["GEO Optimization"], "parameters": [{"name": "brandId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "品牌ID"}, {"name": "page", "in": "query", "schema": {"type": "integer", "default": 1}, "description": "页码"}, {"name": "page_size", "in": "query", "schema": {"type": "integer", "default": 10}, "description": "每页数量"}], "responses": {"200": {"description": "品牌GEO优化列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO optimizations retrieved successfully"}, "data": {"type": "object"}}}}}}}}}, "/brands/{brandId}/geo-optimizations/analysis": {"get": {"summary": "分析品牌GEO表现", "description": "分析指定品牌的GEO表现数据", "tags": ["GEO Optimization"], "parameters": [{"name": "brandId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "品牌ID"}], "responses": {"200": {"description": "GEO表现分析结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO performance analysis completed"}, "data": {"type": "object"}}}}}}}}}, "/brands/{brandId}/geo-optimizations/recommendations": {"get": {"summary": "生成GEO优化建议", "description": "为指定品牌生成GEO优化建议", "tags": ["GEO Optimization"], "parameters": [{"name": "brandId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "品牌ID"}], "responses": {"200": {"description": "GEO优化建议", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO recommendations generated successfully"}, "data": {"type": "array", "items": {"type": "object"}}}}}}}}}}, "/geo-optimizations/stats": {"get": {"summary": "获取GEO统计数据", "description": "获取GEO优化的统计数据", "tags": ["GEO Optimization"], "parameters": [{"name": "brand_id", "in": "query", "schema": {"type": "string", "format": "uuid"}, "description": "品牌ID（可选）"}], "responses": {"200": {"description": "GEO统计数据", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO stats retrieved successfully"}, "data": {"type": "object"}}}}}}}}}, "/geo-optimizations/region/{region}": {"get": {"summary": "按地区获取GEO优化", "description": "根据地区获取GEO优化记录", "tags": ["GEO Optimization"], "security": [{"bearerAuth": []}], "parameters": [{"name": "region", "in": "path", "required": true, "schema": {"type": "string"}, "description": "地区名称"}], "responses": {"200": {"description": "地区GEO优化记录", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO optimizations retrieved successfully"}, "data": {"type": "object", "properties": {"geo_optimizations": {"type": "array", "items": {"type": "object"}}}}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "401": {"description": "未授权", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}, "error": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}, "/analytics/summary": {"get": {"summary": "获取指标摘要", "description": "获取各项指标的摘要统计", "tags": ["Analytics"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "指标摘要数据", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/analytics/brand-distribution": {"get": {"summary": "获取品牌分布", "description": "获取品牌分布统计数据", "tags": ["Analytics"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "品牌分布数据", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/analytics/search-trends": {"get": {"summary": "获取搜索趋势", "description": "获取AI搜索趋势分析", "tags": ["Analytics"], "parameters": [{"name": "period", "in": "query", "description": "分析周期", "required": false, "example": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "搜索趋势数据", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/analytics/ai-appearance": {"get": {"summary": "获取AI出现指标", "description": "获取品牌在AI响应中的出现指标", "tags": ["Analytics"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "AI出现指标数据", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/analytics/competitor-analysis": {"get": {"summary": "获取竞争对手分析", "description": "获取竞争对手分析数据", "tags": ["Analytics"], "parameters": [{"name": "brand_id", "in": "query", "description": "品牌ID", "required": false, "example": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "竞争对手分析数据", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/analytics/activity": {"get": {"summary": "获取最近活动", "description": "获取最近的系统活动记录", "tags": ["Analytics"], "parameters": [{"name": "limit", "in": "query", "description": "返回数量限制", "required": false, "example": "", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "最近活动数据", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/users": {"get": {"summary": "获取用户列表", "description": "获取系统用户列表（仅管理员）", "tags": ["Users"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "page_size", "in": "query", "description": "每页数量", "required": false, "example": "", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "用户列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "post": {"summary": "创建用户", "description": "创建新用户（仅管理员）", "tags": ["Users"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"properties": {"email": {"example": "<EMAIL>", "format": "email", "type": "string"}, "first_name": {"example": "<PERSON>", "type": "string"}, "last_name": {"example": "<PERSON><PERSON>", "type": "string"}, "password": {"example": "password123", "minLength": 6, "type": "string"}, "username": {"example": "john_doe", "type": "string"}}, "type": "object"}, "example": "{\n\t\"username\": \"john_doe\",\n\t\"email\": \"<EMAIL>\",\n\t\"password\": \"password123\",\n\t\"first_name\": \"<PERSON>\",\n\t\"last_name\": \"<PERSON><PERSON>\"\n}"}}}, "responses": {"201": {"description": "用户创建成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/users/{id}": {"get": {"summary": "获取用户详情", "description": "根据ID获取用户详细信息（仅管理员）", "tags": ["Users"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "用户详情", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "put": {"summary": "更新用户", "description": "更新用户信息（仅管理员）", "tags": ["Users"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"role": {"enum": ["admin", "user"], "type": "string", "example": "user"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "status": {"enum": ["active", "inactive"], "type": "string", "example": "active"}, "username": {"type": "string", "example": "john_doe"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "first_name": {"type": "string", "example": "<PERSON>"}}}, "example": "{\n\t\"username\": \"john_doe\",\n\t\"email\": \"<EMAIL>\",\n\t\"first_name\": \"<PERSON>\",\n\t\"last_name\": \"<PERSON><PERSON>\",\n\t\"role\": \"user\",\n\t\"status\": \"active\"\n}"}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "delete": {"summary": "删除用户", "description": "删除指定用户（仅管理员）", "tags": ["Users"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/notifications": {"get": {"summary": "获取通知列表", "description": "获取用户的通知列表", "tags": ["Notifications"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "page_size", "in": "query", "description": "每页数量", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "unread_only", "in": "query", "description": "仅显示未读通知", "required": false, "example": "", "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "通知列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "delete": {"summary": "删除所有通知", "description": "删除用户的所有通知", "tags": ["Notifications"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/notifications/{id}": {"get": {"summary": "获取通知详情", "description": "根据ID获取通知详细信息", "tags": ["Notifications"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "通知详情", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "delete": {"summary": "删除通知", "description": "删除指定的通知", "tags": ["Notifications"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "删除成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/notifications/{id}/read": {"put": {"summary": "标记通知为已读", "description": "将指定通知标记为已读状态", "tags": ["Notifications"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "标记成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/notifications/read-all": {"put": {"summary": "标记所有通知为已读", "description": "将用户的所有通知标记为已读状态", "tags": ["Notifications"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "标记成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/notifications/settings": {"get": {"summary": "获取通知设置", "description": "获取用户的通知设置", "tags": ["Notifications"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "通知设置", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}, "put": {"summary": "更新通知设置", "description": "更新用户的通知设置", "tags": ["Notifications"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"brand_alerts": {"type": "boolean", "example": true}, "push_notifications": {"type": "boolean", "example": false}, "email_notifications": {"type": "boolean", "example": true}, "ai_search_notifications": {"type": "boolean", "example": true}}}, "example": "{\n\t\"email_notifications\": true,\n\t\"push_notifications\": \"\",\n\t\"ai_search_notifications\": true,\n\t\"brand_alerts\": true\n}"}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/exports/dashboard": {"post": {"summary": "导出仪表板数据", "description": "导出仪表板数据为文件", "tags": ["Exports"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"format": {"enum": ["csv", "excel", "pdf"], "type": "string", "example": "excel"}, "date_range": {"type": "object", "properties": {"end_date": {"type": "string", "format": "date", "example": "2025-07-20"}, "start_date": {"type": "string", "format": "date", "example": "2025-01-01"}}}}}, "example": "{\n\t\"format\": \"excel\",\n\t\"date_range\": {\n\t\t\"start_date\": \"2025-01-01\",\n\t\t\"end_date\": \"2025-07-20\"\n\t}\n}"}}}, "responses": {"200": {"description": "导出任务创建成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/exports/analytics": {"post": {"summary": "导出分析数据", "description": "导出分析数据为文件", "tags": ["Exports"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"format": {"enum": ["csv", "excel", "pdf"], "type": "string", "example": "csv"}, "brand_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "指定品牌ID列表（可选）"}, "data_type": {"enum": ["brands", "ai_searches", "visibility_metrics", "prompts", "references"], "type": "string", "example": "brands"}}}, "example": "{\n\t\"format\": \"csv\",\n\t\"data_type\": \"brands\",\n\t\"brand_ids\": [\n\t\t\"\"\n\t]\n}"}}}, "responses": {"200": {"description": "导出任务创建成功", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/exports/download/{filename}": {"get": {"summary": "下载导出文件", "description": "下载已生成的导出文件", "tags": ["Exports"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {}}}, "/exports/history": {"get": {"summary": "获取导出历史", "description": "获取用户的导出历史记录", "tags": ["Exports"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "example": "", "schema": {"type": "integer"}}, {"name": "page_size", "in": "query", "description": "每页数量", "required": false, "example": "", "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "导出历史列表", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/exports/status/{id}": {"get": {"summary": "获取导出状态", "description": "获取导出任务的状态", "tags": ["Exports"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {}}}}}, "responses": {"200": {"description": "导出状态", "content": {"application/json": {"schema": {"properties": {"data": {"type": "object"}, "message": {"example": "Operation successful", "type": "string"}, "success": {"example": true, "type": "boolean"}, "timestamp": {"example": "2025-07-20T21:56:01Z", "format": "date-time", "type": "string"}}, "type": "object"}, "example": "{\n\t\"success\": true,\n\t\"message\": \"Operation successful\",\n\t\"data\": {},\n\t\"timestamp\": \"2025-07-20T21:56:01Z\"\n}"}}}}}}, "/upload/file": {"post": {"summary": "上传文件", "description": "", "tags": ["Upload"], "parameters": [], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "file", "example": "D:\\下载\\Snipaste_2025-07-18_17-52-01.png", "description": ""}}, "required": ["file"]}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}, "404": {"description": "失败", "content": {"application/json": {"schema": {"type": "object", "properties": {}}, "example": ""}}}}}}, "/blogs": {"get": {"summary": "获取博客列表", "description": "获取博客列表，支持分页、搜索和筛选", "tags": ["Blogs"], "parameters": [{"name": "page", "in": "query", "description": "页码（默认：1）", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "example": 1}}, {"name": "page_size", "in": "query", "description": "每页数量（默认：10，最大：100）", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "example": 10}}, {"name": "status", "in": "query", "description": "按状态筛选", "required": false, "schema": {"type": "string", "enum": ["draft", "published", "scheduled", "archived"], "example": "published"}}, {"name": "category", "in": "query", "description": "按分类筛选", "required": false, "schema": {"type": "string", "example": "硬件评测"}}, {"name": "search", "in": "query", "description": "搜索关键词（在标题、摘要、内容中搜索）", "required": false, "schema": {"type": "string", "example": "Intel"}}], "responses": {"200": {"description": "博客列表获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BlogListResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"summary": "创建博客", "description": "创建新的博客文章", "tags": ["Blogs"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogRequest"}}}}, "responses": {"201": {"description": "博客创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Blog created successfully"}, "data": {"type": "object", "properties": {"blog": {"$ref": "#/components/schemas/Blog"}}}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-07-21T09:30:00Z"}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授权", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/blogs/{id}": {"get": {"summary": "获取博客详情", "description": "根据ID或slug获取博客详情", "tags": ["Blogs"], "parameters": [{"name": "id", "in": "path", "description": "博客ID（UUID）或slug", "required": true, "schema": {"type": "string", "example": "550e8400-e29b-41d4-a716-************"}}], "responses": {"200": {"description": "博客详情获取成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Blog retrieved successfully"}, "data": {"type": "object", "properties": {"blog": {"$ref": "#/components/schemas/Blog"}}}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-07-21T09:30:00Z"}}}}}}, "404": {"description": "博客不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"summary": "更新博客", "description": "更新指定ID的博客", "tags": ["Blogs"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "博客ID（UUID）", "required": true, "schema": {"type": "string", "format": "uuid", "example": "550e8400-e29b-41d4-a716-************"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBlogRequest"}}}}, "responses": {"200": {"description": "博客更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Blog updated successfully"}, "data": {"type": "object", "properties": {"blog": {"$ref": "#/components/schemas/Blog"}}}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-07-21T09:30:00Z"}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授权", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "博客不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"summary": "删除博客", "description": "删除指定ID的博客", "tags": ["Blogs"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "博客ID（UUID）", "required": true, "schema": {"type": "string", "format": "uuid", "example": "550e8400-e29b-41d4-a716-************"}}], "responses": {"200": {"description": "博客删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Blog deleted successfully"}, "timestamp": {"type": "string", "format": "date-time", "example": "2025-07-21T09:30:00Z"}}}}}}, "401": {"description": "未授权", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "博客不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/geo-optimizations/{id}/geo-databases": {"get": {"summary": "获取GEO优化的数据库列表", "description": "获取指定GEO优化记录的数据库列表", "tags": ["GEO Optimization"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "GEO优化记录ID"}], "responses": {"200": {"description": "GEO数据库列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO databases retrieved successfully"}, "data": {"type": "object", "properties": {"geo_databases": {"type": "array", "items": {"type": "object"}}}}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}, "/brands/{id}/geo-databases": {"get": {"summary": "获取品牌的GEO数据库列表", "description": "获取指定品牌的GEO数据库列表", "tags": ["GEO Optimization"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "品牌ID"}], "responses": {"200": {"description": "品牌GEO数据库列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Brand GEO databases retrieved successfully"}, "data": {"type": "object", "properties": {"geo_databases": {"type": "array", "items": {"type": "object"}}}}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}, "/geo-databases": {"post": {"summary": "创建GEO数据库记录", "description": "创建新的GEO数据库记录", "tags": ["GEO Optimization"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"geo_optimization_id": {"type": "string", "format": "uuid", "description": "GEO优化ID"}, "database_name": {"type": "string", "description": "数据库名称"}, "database_type": {"type": "string", "enum": ["mysql", "postgresql", "mongodb", "redis"], "description": "数据库类型"}, "connection_string": {"type": "string", "description": "连接字符串"}}, "required": ["geo_optimization_id", "database_name", "database_type"]}}}}, "responses": {"201": {"description": "GEO数据库记录创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO database created successfully"}, "data": {"type": "object"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}, "error": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}, "/geo-databases/{id}": {"get": {"summary": "获取GEO数据库详情", "description": "根据ID获取GEO数据库记录详情", "tags": ["GEO Optimization"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "GEO数据库记录ID"}], "responses": {"200": {"description": "GEO数据库详情", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO database retrieved successfully"}, "data": {"type": "object"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}, "404": {"description": "GEO数据库记录不存在", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}, "error": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}, "put": {"summary": "更新GEO数据库记录", "description": "更新指定的GEO数据库记录", "tags": ["GEO Optimization"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "GEO数据库记录ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"database_name": {"type": "string"}, "database_type": {"type": "string", "enum": ["mysql", "postgresql", "mongodb", "redis"]}, "connection_string": {"type": "string"}, "status": {"type": "string", "enum": ["active", "inactive"]}}}}}}, "responses": {"200": {"description": "GEO数据库记录更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO database updated successfully"}, "data": {"type": "object"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}, "delete": {"summary": "删除GEO数据库记录", "description": "删除指定的GEO数据库记录", "tags": ["GEO Optimization"], "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "GEO数据库记录ID"}], "responses": {"200": {"description": "GEO数据库记录删除成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "GEO database deleted successfully"}, "timestamp": {"type": "string", "format": "date-time"}}}}}}}}}}, "tags": [{"name": "Health", "description": "健康检查接口"}, {"name": "Authentication", "description": "用户认证相关接口"}, {"name": "Blogs", "description": "博客管理接口"}, {"name": "Brands", "description": "品牌管理接口"}, {"name": "AI Searches", "description": "AI搜索管理接口"}, {"name": "AI Visibility", "description": "AI可见性指标接口"}, {"name": "Prompts", "description": "提示词管理接口"}, {"name": "References", "description": "引用管理接口"}, {"name": "GEO Optimization", "description": "GEO优化管理接口"}, {"name": "Analytics", "description": "分析统计接口"}, {"name": "Users", "description": "用户管理接口"}, {"name": "Notifications", "description": "通知管理接口"}, {"name": "Exports", "description": "数据导出接口"}, {"name": "Upload", "description": ""}]}