# AI可见性趋势API更新总结

## 📋 更新概述

根据用户需求，我们已经成功更新了品牌可见性趋势API (`/brands/{id}/ai-visibility/trend?days=7`)，确保在获取这些指标时能够查询到对应的 `ai_search_id` 和 `ai_response_id`，并返回完整的关联数据。

## 🔧 技术实现更新

### 1. 服务层更新 (ai_visibility_service.go)

#### 更新前
```go
func (s *AIVisibilityService) GetBrandVisibilityTrend(brandID uuid.UUID, days int) ([]models.AIVisibilityMetrics, error) {
    startDate := time.Now().AddDate(0, 0, -days)
    var metrics []models.AIVisibilityMetrics
    if err := s.db.Where("brand_id = ? AND calculated_at >= ?", brandID, startDate).
        Order("calculated_at ASC").
        Find(&metrics).Error; err != nil {
        return nil, fmt.Errorf("failed to get visibility trend: %w", err)
    }
    return metrics, nil
}
```

#### 更新后
```go
func (s *AIVisibilityService) GetBrandVisibilityTrend(brandID uuid.UUID, days int) ([]models.AIVisibilityMetrics, error) {
    startDate := time.Now().AddDate(0, 0, -days)
    var metrics []models.AIVisibilityMetrics
    if err := s.db.Preload("AISearch").Preload("AIResponse").Preload("Brand").
        Where("brand_id = ? AND calculated_at >= ?", brandID, startDate).
        Order("calculated_at ASC").
        Find(&metrics).Error; err != nil {
        return nil, fmt.Errorf("failed to get visibility trend: %w", err)
    }
    logging.Info("Retrieved %d visibility metrics for brand %s over %d days", len(metrics), brandID, days)
    return metrics, nil
}
```

**关键改进**：
- ✅ 添加了 `Preload("AISearch")` - 预加载AI搜索信息
- ✅ 添加了 `Preload("AIResponse")` - 预加载AI响应信息  
- ✅ 添加了 `Preload("Brand")` - 预加载品牌信息
- ✅ 增加了详细的日志记录

### 2. 处理器层更新 (ai_visibility_handler.go)

#### 响应数据结构增强

更新后的处理器现在返回包含完整关联信息的结构化数据：

```go
trendData := make([]map[string]interface{}, len(trend))
for i, metric := range trend {
    trendData[i] = map[string]interface{}{
        // 基础指标数据
        "id":                   metric.ID,
        "ai_search_id":         metric.AISearchID,      // ✅ 关键：AI搜索ID
        "ai_response_id":       metric.AIResponseID,    // ✅ 关键：AI响应ID
        "brand_id":             metric.BrandID,
        "visibility_data":      metric.VisibilityData,
        "keyword_data":         metric.KeywordData,
        "overall_score":        metric.OverallScore,
        "frequency_score":      metric.FrequencyScore,
        "recommendation_score": metric.RecommendationScore,
        "search_rate_score":    metric.SearchRateScore,
        "first_choice_score":   metric.FirstChoiceScore,
        "calculated_at":        metric.CalculatedAt,
        "created_at":           metric.CreatedAt,
        "updated_at":           metric.UpdatedAt,
    }

    // 关联的AI搜索信息
    if metric.AISearch.ID != uuid.Nil {
        trendData[i]["ai_search"] = map[string]interface{}{
            "id":            metric.AISearch.ID,
            "question":      metric.AISearch.Question,      // AI搜索问题
            "question_type": metric.AISearch.QuestionType,  // 问题类型
            "keywords":      metric.AISearch.Keywords,      // 关键词
            "status":        metric.AISearch.Status,        // 搜索状态
            "region":        metric.AISearch.Region,        // 地理区域
            "language":      metric.AISearch.Language,      // 语言
            "created_at":    metric.AISearch.CreatedAt,
        }
    }

    // 关联的AI响应信息
    if metric.AIResponse.ID != uuid.Nil {
        trendData[i]["ai_response"] = map[string]interface{}{
            "id":              metric.AIResponse.ID,
            "response":        metric.AIResponse.Response,        // AI响应内容
            "brand_position":  metric.AIResponse.BrandPosition,   // 品牌位置
            "brand_sentiment": metric.AIResponse.BrandSentiment,  // 品牌情感
            "confidence":      metric.AIResponse.Confidence,      // 置信度
            "relevance":       metric.AIResponse.Relevance,       // 相关性
        }
    }

    // 关联的品牌信息
    if metric.Brand.ID != uuid.Nil {
        trendData[i]["brand"] = map[string]interface{}{
            "id":          metric.Brand.ID,
            "name":        metric.Brand.Name,        // 品牌名称
            "domain":      metric.Brand.Domain,      // 品牌域名
            "keywords":    metric.Brand.Keywords,    // 品牌关键词
            "description": metric.Brand.Description, // 品牌描述
        }
    }
}
```

### 3. API响应结构更新

#### 新的响应格式
```json
{
  "success": true,
  "message": "Visibility trend retrieved successfully",
  "data": {
    "brand_id": "4fc86ecb-8e0e-476b-8826-bf4dc95fce0d",
    "days": 7,
    "trend": [
      {
        "id": "metric-uuid",
        "ai_search_id": "search-uuid",     // ✅ AI搜索ID
        "ai_response_id": "response-uuid", // ✅ AI响应ID
        "brand_id": "brand-uuid",
        "visibility_data": { /* 可见性指标数据 */ },
        "keyword_data": { /* 关键词数据 */ },
        "overall_score": 85.5,
        "frequency_score": 78.2,
        "recommendation_score": 82.1,
        "search_rate_score": 89.3,
        "first_choice_score": 92.7,
        "calculated_at": "2025-07-21T14:30:00Z",
        "ai_search": {
          "id": "search-uuid",
          "question": "什么是TechCorp？",
          "question_type": "brand_analysis",
          "keywords": "technology,innovation,software",
          "status": "completed",
          "region": "global",
          "language": "zh"
        },
        "ai_response": {
          "id": "response-uuid",
          "response": "TechCorp是一家领先的技术公司...",
          "brand_position": 1,
          "brand_sentiment": "positive",
          "confidence": 0.95,
          "relevance": 0.88
        },
        "brand": {
          "id": "brand-uuid",
          "name": "TechCorp",
          "domain": "techcorp.com",
          "keywords": "technology,innovation,software",
          "description": "Leading technology company"
        }
      }
    ],
    "count": 30,
    "summary": {
      "total_metrics": 30,
      "date_range_start": "2025-07-14",
      "date_range_end": "2025-07-21"
    }
  }
}
```

## 📊 Swagger文档更新

### 更新的API文档结构

我们已经更新了 `swagger.json` 文件，确保趋势API的文档完整反映新的响应结构：

```json
{
  "trend": {
    "type": "array",
    "items": {
      "type": "object",
      "properties": {
        "id": { "type": "string", "format": "uuid" },
        "ai_search_id": { 
          "type": "string", 
          "format": "uuid", 
          "description": "关联的AI搜索ID" 
        },
        "ai_response_id": { 
          "type": "string", 
          "format": "uuid", 
          "description": "关联的AI响应ID" 
        },
        "ai_search": {
          "type": "object",
          "description": "关联的AI搜索信息",
          "properties": {
            "question": { "type": "string", "description": "AI搜索问题" },
            "question_type": { "type": "string", "description": "问题类型" },
            "keywords": { "type": "string", "description": "关键词" }
          }
        },
        "ai_response": {
          "type": "object", 
          "description": "关联的AI响应信息",
          "properties": {
            "response": { "type": "string", "description": "AI响应内容" },
            "brand_position": { "type": "integer", "description": "品牌位置" },
            "brand_sentiment": { "type": "string", "description": "品牌情感" }
          }
        }
      }
    }
  }
}
```

## 🎯 关键改进点

### 1. **完整的关联数据查询**
- ✅ 每个可见性指标现在都包含对应的 `ai_search_id` 和 `ai_response_id`
- ✅ 预加载了完整的AI搜索、AI响应和品牌信息
- ✅ 避免了N+1查询问题，提高了性能

### 2. **丰富的上下文信息**
- ✅ **AI搜索上下文**：问题内容、类型、关键词、状态等
- ✅ **AI响应上下文**：响应内容、品牌位置、情感分析、置信度等
- ✅ **品牌上下文**：品牌名称、域名、关键词、描述等

### 3. **增强的可追溯性**
- ✅ 每个可见性指标都可以追溯到具体的AI搜索问题
- ✅ 每个指标都可以追溯到具体的AI模型响应
- ✅ 便于分析和调试可见性指标的计算过程

### 4. **改进的数据分析能力**
- ✅ 可以分析不同问题类型的可见性表现
- ✅ 可以分析不同AI模型响应的质量影响
- ✅ 可以进行更精细的趋势分析和优化

## 🚀 API使用示例

### 请求示例
```bash
GET /api/v1/brands/4fc86ecb-8e0e-476b-8826-bf4dc95fce0d/ai-visibility/trend?days=7
```

### 响应特点
- ✅ 包含完整的 `ai_search_id` 和 `ai_response_id`
- ✅ 提供详细的AI搜索问题上下文
- ✅ 提供完整的AI响应分析数据
- ✅ 支持深度数据分析和可视化
- ✅ 便于前端展示详细的趋势信息

## ✅ 验证结果

1. **服务器启动成功** - API路由正确注册
2. **数据库连接正常** - 可以查询到现有的可见性指标数据
3. **预加载功能正常** - 关联数据能够正确加载
4. **响应结构完整** - 包含所有必需的关联信息
5. **文档更新完成** - Swagger文档反映最新的API结构

现在，当调用 `/brands/{id}/ai-visibility/trend?days=7` API时，返回的每个可见性指标都包含完整的 `ai_search_id`、`ai_response_id` 以及相关的详细信息，满足了用户的需求。
