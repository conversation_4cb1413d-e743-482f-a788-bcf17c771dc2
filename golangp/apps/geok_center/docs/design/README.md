# GEOK Center 后端服务设计文档

## 概述

本文档基于 Figma 设计稿 "GEOK 站点" 的业务需求，设计和规划 GEOK Center 后端服务架构。文档包含了数据模型设计、API 接口规范、服务架构和实现策略。

## 业务需求分析

基于 Figma 设计分析，GEOK Center 需要支持以下核心业务功能：

### 1. 品牌可见性分析

**核心指标**:

- 品牌搜索率统计
- AI 平台出现频率
- 品牌推荐率分析
- 竞争对手对比

### 2. 数据聚合与处理

**数据源**:

- AI 平台爬虫数据
- 搜索引擎数据
- 社交媒体数据
- 第三方数据接口

### 3. 用户管理与权限

**功能需求**:

- 企业用户注册认证
- 多租户数据隔离
- 角色权限管理
- 使用配额控制

### 4. 数据导出与报告

**输出格式**:

- CSV/Excel 数据导出
- PDF 报告生成
- 实时数据推送
- 定时报告邮件

## 技术架构设计

### 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   API Gateway   │    │   Web Frontend  │
│    (Nginx)      │────│   (Gin Router)  │────│   (Optional)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │ Auth Service │ │ Data Service│ │Export Service│
        │              │ │             │ │             │
        └──────────────┘ └─────────────┘ └────────────┘
                │               │               │
        ┌───────▼──────┐ ┌──────▼──────┐ ┌─────▼──────┐
        │  PostgreSQL  │ │   Redis     │ │  File Store│
        │   (Primary)  │ │  (Cache)    │ │   (MinIO)  │
        └──────────────┘ └─────────────┘ └────────────┘
                │
        ┌───────▼──────┐
        │ Data Pipeline│
        │ (Kafka/ETL)  │
        └──────────────┘
```

## 核心服务模块设计

### 1. 认证授权服务 (Auth Service)

**功能职责**:

- JWT Token 生成和验证
- 用户注册、登录、密码重置
- 企业多租户管理
- 角色权限控制 (RBAC)
- OAuth2 第三方登录集成

**技术实现**:

```go
type AuthService struct {
    userRepo     repository.UserRepository
    companyRepo  repository.CompanyRepository
    jwtManager   *jwt.Manager
    emailService *email.Service
}

type User struct {
    ID        uint      `gorm:"primaryKey" json:"id"`
    Email     string    `gorm:"uniqueIndex" json:"email"`
    Password  string    `gorm:"not null" json:"-"`
    CompanyID uint      `gorm:"not null" json:"company_id"`
    Role      UserRole  `gorm:"type:varchar(20)" json:"role"`
    Status    UserStatus `gorm:"type:varchar(20)" json:"status"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

### 2. 数据分析服务 (Analytics Service)

**功能职责**:

- 品牌可见性指标计算
- AI 平台数据聚合分析
- 竞争对手对比分析
- 趋势预测和异常检测
- 实时数据更新推送

**核心算法**:

```go
type AnalyticsService struct {
    brandRepo      repository.BrandRepository
    metricsRepo    repository.MetricsRepository
    aiDataRepo     repository.AIDataRepository
    cacheService   *cache.RedisService
    eventPublisher *event.Publisher
}

// 品牌可见性计算
func (s *AnalyticsService) CalculateBrandVisibility(
    brandID uint,
    timeRange TimeRange,
) (*BrandVisibilityMetrics, error) {
    // 实现品牌可见性算法
}
```

### 3. 数据采集服务 (Data Collection Service)

**功能职责**:

- AI 平台数据爬虫
- 搜索引擎数据采集
- 社交媒体监控
- 第三方 API 数据集成
- 数据清洗和标准化

**数据流架构**:

```go
type DataCollector struct {
    crawlers    []crawler.Crawler
    processors  []processor.DataProcessor
    storage     storage.DataStorage
    scheduler   *scheduler.CronScheduler
}

type AIDataPoint struct {
    ID          uint      `gorm:"primaryKey"`
    Platform    string    `gorm:"index"`
    BrandName   string    `gorm:"index"`
    Query       string    `gorm:"index"`
    Mentions    int       `gorm:"not null"`
    Sentiment   float64   `gorm:"type:decimal(3,2)"`
    Timestamp   time.Time `gorm:"index"`
    RawData     string    `gorm:"type:text"`
}
```

## 数据模型设计

### 核心实体关系图

```mermaid
erDiagram
    Company ||--o{ User : has
    Company ||--o{ Brand : owns
    Brand ||--o{ BrandMetrics : generates
    Brand ||--o{ AIDataPoint : mentioned_in
    User ||--o{ ExportTask : creates
    BrandMetrics ||--o{ MetricHistory : tracks

    Company {
        uint id PK
        string name
        string domain
        string subscription_plan
        timestamp created_at
        timestamp updated_at
    }

    User {
        uint id PK
        uint company_id FK
        string email
        string password_hash
        string role
        string status
        timestamp created_at
        timestamp updated_at
    }

    Brand {
        uint id PK
        uint company_id FK
        string name
        string description
        string industry
        string website
        timestamp created_at
        timestamp updated_at
    }
```

## 技术栈规范

### 后端技术栈

- **语言**: Go 1.23+
- **框架**: Gin Web Framework
- **数据库**: PostgreSQL 15+
- **缓存**: Redis 7+
- **消息队列**: Apache Kafka
- **文件存储**: MinIO (S3 兼容)
- **监控**: Prometheus + Grafana

## API 接口设计

### RESTful API 规范

#### 认证相关 API

```go
// POST /api/v1/auth/register
type RegisterRequest struct {
    Email       string `json:"email" binding:"required,email"`
    Password    string `json:"password" binding:"required,min=8"`
    CompanyName string `json:"company_name" binding:"required"`
    Domain      string `json:"domain"`
}

// POST /api/v1/auth/login
type LoginRequest struct {
    Email    string `json:"email" binding:"required,email"`
    Password string `json:"password" binding:"required"`
}

// Response
type AuthResponse struct {
    Token     string    `json:"token"`
    ExpiresAt time.Time `json:"expires_at"`
    User      UserInfo  `json:"user"`
}
```

#### 品牌分析 API

```go
// GET /api/v1/analytics/dashboard
type DashboardResponse struct {
    BrandVisibility    *VisibilityMetrics    `json:"brand_visibility"`
    AIAppearance       *AIAppearanceMetrics  `json:"ai_appearance"`
    CompetitorAnalysis *CompetitorMetrics    `json:"competitor_analysis"`
    TrendAnalysis      *TrendMetrics         `json:"trend_analysis"`
}

// GET /api/v1/analytics/brands/{id}/visibility
type VisibilityMetrics struct {
    BrandID        uint    `json:"brand_id"`
    VisibilityScore float64 `json:"visibility_score"`
    SearchRate     float64 `json:"search_rate"`
    AIMentions     int     `json:"ai_mentions"`
    TrendDirection string  `json:"trend_direction"`
    Period         string  `json:"period"`
}
```

## 实施阶段规划

### Phase 1: 核心服务架构 (P0)

**预计时间**: 3-4 周
**状态**: ❌ 未开始

#### 任务列表:

- [ ] 数据库模型设计和迁移
- [ ] 用户认证服务实现
- [ ] 基础 API 框架搭建
- [ ] 多租户数据隔离
- [ ] 基础监控和日志

### Phase 2: 数据采集与分析 (P0)

**预计时间**: 4-5 周
**状态**: ❌ 未开始

#### 任务列表:

- [ ] AI 平台数据爬虫开发
- [ ] 数据清洗和标准化流程
- [ ] 品牌可见性算法实现
- [ ] 实时数据处理管道
- [ ] 缓存策略优化

### Phase 3: 高级分析功能 (P1)

**预计时间**: 3-4 周
**状态**: ❌ 未开始

#### 任务列表:

- [ ] 竞争对手分析算法
- [ ] 趋势预测模型
- [ ] 数据导出服务
- [ ] 报告生成系统
- [ ] 告警通知机制

## 相关文档

- [API 集成指南](./api-integration.md) - 详细的 API 接口文档
- [测试策略](./testing-strategy.md) - 后端服务测试方案
- [部署指南](./deployment.md) - 服务部署和运维指南
- [实施计划](./implementation-phases.md) - 详细的开发计划

## 下一步行动

1. **数据库设计**: 完善实体关系模型
2. **API 规范**: 制定详细的接口文档
3. **架构搭建**: 建立基础服务框架
4. **开发环境**: 配置本地和测试环境
