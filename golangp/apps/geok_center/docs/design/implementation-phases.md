# GEOK Center 后端服务分阶段实施计划

## 实施概述

基于当前代码库分析和业务需求，本文档将后端服务开发分为 3 个阶段，重点解决技术债务、实现核心业务功能和系统优化。

## 阶段总览

| 阶段    | 名称                   | 预计时间 | 优先级 | 状态      |
| ------- | ---------------------- | -------- | ------ | --------- |
| Phase 1 | 技术债务清理与架构完善 | 3-4 周   | P0     | ⏳ 进行中 |
| Phase 2 | 核心业务功能实现       | 4-5 周   | P0     | ❌ 未开始 |
| Phase 3 | 高级功能与系统优化     | 3-4 周   | P1     | ❌ 未开始 |

## 当前项目状态

**总体完成度**: 65% ✅
**已完成模块**: 基础架构(90%)、认证系统(85%)、基础 API(70%)
**主要技术债务**: 多租户架构缺失、缓存系统未实现、数据采集服务缺失

## Phase 1: 技术债务清理与架构完善 (P0)

**目标**: 解决现有技术债务，完善系统架构，为核心功能开发奠定基础

**当前状态**: ⏳ 进行中 (30% 完成)

### 1.1 多租户架构重构

**状态**: ❌ 未开始
**预计时间**: 5 天 (40 小时)
**优先级**: 🔥 P0 (阻塞)

#### 任务清单:

- [ ] 设计和实现 Company 模型
- [ ] 重构 User 模型添加 CompanyID 关联
- [ ] 实现租户数据隔离中间件
- [ ] 更新所有数据查询逻辑
- [ ] 编写租户隔离测试用例

#### 技术实现:

```go
// Company 模型设计
type Company struct {
    ID                   uint      `gorm:"primaryKey" json:"id"`
    Name                 string    `gorm:"size:255;not null" json:"name"`
    Domain               string    `gorm:"size:255;uniqueIndex" json:"domain"`
    SubscriptionPlan     string    `gorm:"size:50;default:basic" json:"subscription_plan"`
    SubscriptionExpiresAt *time.Time `json:"subscription_expires_at"`
    Settings             datatypes.JSON `gorm:"type:jsonb;default:'{}'" json:"settings"`
    Status               string    `gorm:"size:20;default:active" json:"status"`
    CreatedAt            time.Time `json:"created_at"`
    UpdatedAt            time.Time `json:"updated_at"`
}

// 租户中间件
func TenantMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        userID := c.GetString("user_id")
        // 获取用户的公司ID并设置到上下文
        companyID := getUserCompanyID(userID)
        c.Set("company_id", companyID)
        c.Next()
    }
}
```

#### 数据库迁移:

```sql
-- 添加 companies 表
CREATE TABLE companies (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    subscription_plan VARCHAR(50) DEFAULT 'basic',
    subscription_expires_at TIMESTAMP,
    settings JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 更新 users 表
ALTER TABLE users ADD COLUMN company_id INTEGER REFERENCES companies(id);
CREATE INDEX idx_users_company_id ON users(company_id);
```

### 1.2 用户认证系统

**状态**: ❌ 未开始
**预计时间**: 3-4 天

#### 任务清单:

- [ ] 实现登录页面 UI (基于 Figma 设计)
- [ ] 集成 JWT 认证流程
- [ ] 实现用户注册功能
- [ ] 添加密码重置功能
- [ ] 配置路由守卫

#### 页面组件:

```typescript
// views/auth/LoginView.vue
interface LoginForm {
  email: string;
  password: string;
  remember: boolean;
}

// views/auth/RegisterView.vue
interface RegisterForm {
  email: string;
  password: string;
  confirmPassword: string;
  company?: string;
}
```

### 1.3 主布局框架

**状态**: ❌ 未开始
**预计时间**: 4-5 天

#### 任务清单:

- [ ] 创建主布局组件 (MainLayout.vue)
- [ ] 实现侧边导航栏 (Sidebar.vue)
- [ ] 添加顶部导航栏 (Header.vue)
- [ ] 实现响应式布局
- [ ] 配置路由系统

#### 核心组件:

```typescript
// components/layout/MainLayout.vue
interface LayoutProps {
  sidebarCollapsed: boolean;
  showHeader: boolean;
}

// components/layout/Sidebar.vue
interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  route: string;
  active: boolean;
  children?: NavigationItem[];
}
```

### 1.4 基础页面模板

**状态**: ❌ 未开始
**预计时间**: 2-3 天

#### 任务清单:

- [ ] 创建仪表板页面骨架
- [ ] 实现 GEO 分析页面框架
- [ ] 添加设置页面基础结构
- [ ] 配置页面路由和导航

#### 交付成果:

- ✅ 完整的项目架构
- ✅ 用户认证流程
- ✅ 响应式布局框架
- ✅ 基础页面导航

## Phase 2: 核心数据可视化 (P0)

**目标**: 实现主要的数据可视化组件和仪表板功能

### 2.1 图表组件开发

**状态**: ❌ 未开始
**预计时间**: 1-2 周

#### 任务清单:

- [ ] 品牌搜索率图表 (BrandSearchChart.vue)
- [ ] 品牌分布饼图 (BrandDistributionChart.vue)
- [ ] AI 出现频率线图 (AIFrequencyChart.vue)
- [ ] 数据表格组件 (DataTable.vue)

#### 图表规格:

```typescript
// components/charts/BrandSearchChart.vue
interface BrandSearchData {
  totalData: string;
  percentage: number;
  trend: "up" | "down";
  chartData: {
    labels: string[];
    values: number[];
  };
}

// components/charts/BrandDistributionChart.vue
interface BrandDistribution {
  brands: {
    name: string;
    percentage: number;
    color: string;
    value: number;
  }[];
}
```

### 2.2 仪表板页面实现

**状态**: ❌ 未开始
**预计时间**: 1 周

#### 任务清单:

- [ ] 仪表板布局实现
- [ ] 图表组件集成
- [ ] 数据加载和状态管理
- [ ] 响应式图表适配

#### 页面结构:

```vue
<!-- views/DashboardView.vue -->
<template>
  <div class="dashboard-container">
    <div class="dashboard-header">
      <h1>品牌可见性分析</h1>
      <DataFilter @filter-change="handleFilterChange" />
    </div>

    <div class="dashboard-grid">
      <div class="chart-card">
        <BrandSearchChart :data="searchData" />
      </div>
      <div class="chart-card">
        <BrandDistributionChart :data="distributionData" />
      </div>
      <div class="chart-card">
        <AIFrequencyChart :data="frequencyData" />
      </div>
    </div>
  </div>
</template>
```

### 2.3 API 集成

**状态**: ❌ 未开始
**预计时间**: 3-4 天

#### 任务清单:

- [ ] 配置 API 客户端
- [ ] 实现数据获取服务
- [ ] 添加错误处理机制
- [ ] 实现数据缓存策略

#### API 服务:

```typescript
// api/dashboard.ts
export class DashboardAPI {
  static async getDashboardData(filters: FilterParams): Promise<DashboardData> {
    const response = await apiClient.get("/analytics/dashboard", {
      params: filters,
    });
    return response.data;
  }

  static async getBrandDistribution(
    params: BrandDistributionParams
  ): Promise<BrandDistributionData> {
    const response = await apiClient.get("/analytics/brand-distribution", {
      params,
    });
    return response.data;
  }
}
```

### 2.4 状态管理

**状态**: ❌ 未开始
**预计时间**: 2-3 天

#### 任务清单:

- [ ] 创建仪表板状态 store
- [ ] 实现数据缓存逻辑
- [ ] 添加加载状态管理
- [ ] 配置错误状态处理

#### 交付成果:

- ✅ 完整的数据可视化组件
- ✅ 功能完整的仪表板页面
- ✅ API 数据集成
- ✅ 状态管理系统

## Phase 3: 高级功能与交互 (P1)

**目标**: 实现高级功能，提升用户体验

### 3.1 数据筛选和搜索

**状态**: ❌ 未开始
**预计时间**: 1 周

#### 任务清单:

- [ ] 高级筛选器组件 (AdvancedFilter.vue)
- [ ] 时间范围选择器
- [ ] 多维度筛选功能
- [ ] 搜索和排序功能

#### 筛选组件:

```typescript
// components/filters/AdvancedFilter.vue
interface FilterOptions {
  timeRange: TimeRange;
  regions: string[];
  aiPlatforms: string[];
  brands: string[];
  sortBy: SortOption;
}
```

### 3.2 数据导出功能

**状态**: ❌ 未开始
**预计时间**: 3-4 天

#### 任务清单:

- [ ] 导出按钮组件
- [ ] 多格式导出支持 (CSV, JSON, PDF)
- [ ] 导出进度指示器
- [ ] 导出历史记录

### 3.3 实时数据更新

**状态**: ❌ 未开始
**预计时间**: 4-5 天

#### 任务清单:

- [ ] WebSocket 连接管理
- [ ] 实时数据推送
- [ ] 数据更新动画
- [ ] 连接状态指示器

### 3.4 用户偏好设置

**状态**: ❌ 未开始
**预计时间**: 2-3 天

#### 任务清单:

- [ ] 设置页面实现
- [ ] 主题切换功能
- [ ] 图表偏好配置
- [ ] 通知设置

#### 交付成果:

- ✅ 高级筛选和搜索功能
- ✅ 数据导出系统
- ✅ 实时数据更新
- ✅ 用户个性化设置

## Phase 4: 优化与完善 (P2)

**目标**: 性能优化、用户体验提升和系统完善

### 4.1 性能优化

**状态**: ❌ 未开始
**预计时间**: 3-4 天

#### 任务清单:

- [ ] 组件懒加载实现
- [ ] 图表渲染优化
- [ ] 数据虚拟化
- [ ] 代码分割优化

#### 优化策略:

```typescript
// 组件懒加载
const BrandDistributionChart = defineAsyncComponent(
  () => import("@/components/charts/BrandDistributionChart.vue")
);

// 数据虚拟化
const virtualScrollConfig = {
  itemHeight: 60,
  bufferSize: 10,
  threshold: 5,
};
```

### 4.2 用户体验优化

**状态**: ❌ 未开始
**预计时间**: 2-3 天

#### 任务清单:

- [ ] 加载状态优化
- [ ] 错误页面美化
- [ ] 空状态设计
- [ ] 微交互动画

### 4.3 可访问性支持

**状态**: ❌ 未开始
**预计时间**: 2 天

#### 任务清单:

- [ ] 键盘导航支持
- [ ] 屏幕阅读器适配
- [ ] 颜色对比度优化
- [ ] ARIA 标签添加

### 4.4 国际化支持

**状态**: ❌ 未开始
**预计时间**: 1-2 天

#### 任务清单:

- [ ] i18n 配置
- [ ] 中英文切换
- [ ] 日期时间本地化
- [ ] 数字格式本地化

#### 交付成果:

- ✅ 性能优化完成
- ✅ 用户体验提升
- ✅ 可访问性支持
- ✅ 国际化功能

## 里程碑和验收标准

### Phase 1 验收标准:

- [ ] 用户可以成功登录和注册
- [ ] 侧边导航正常工作
- [ ] 页面路由切换无误
- [ ] 响应式布局在各设备正常显示

### Phase 2 验收标准:

- [ ] 所有图表正确显示数据
- [ ] API 数据正常加载
- [ ] 图表交互功能正常
- [ ] 错误状态正确处理

### Phase 3 验收标准:

- [ ] 筛选功能正常工作
- [ ] 数据导出成功
- [ ] 实时更新正常
- [ ] 用户设置保存正确

### Phase 4 验收标准:

- [ ] 页面加载时间 < 3 秒
- [ ] 图表渲染流畅
- [ ] 可访问性测试通过
- [ ] 多语言切换正常

## 风险评估和缓解策略

### 技术风险:

- **风险**: 图表库性能问题
- **缓解**: 提前进行性能测试，准备备选方案

### 时间风险:

- **风险**: API 开发延期
- **缓解**: 使用 Mock 数据并行开发

### 质量风险:

- **风险**: 浏览器兼容性问题
- **缓解**: 建立自动化测试流程

## 团队协作计划

### 角色分工:

- **前端开发**: 2-3 人
- **UI/UX 设计**: 1 人
- **测试工程师**: 1 人
- **项目经理**: 1 人

### 协作流程:

1. 每日站会同步进度
2. 每周代码审查
3. 每阶段演示验收
4. 持续集成部署

## 下一步行动

### 立即开始 (本周):

1. 搭建项目脚手架
2. 配置开发环境
3. 创建基础组件库
4. 开始用户认证开发

### 准备工作:

1. 确认技术栈选型
2. 准备开发环境
3. 建立代码仓库
4. 配置 CI/CD 流程
