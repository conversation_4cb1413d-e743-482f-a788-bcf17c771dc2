# GEOK站点 部署指南

## 部署概述

本文档描述了GEOK站点前端UI的部署策略，包括开发、测试和生产环境的配置。

## 环境架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Development   │    │     Staging     │    │   Production    │
│                 │    │                 │    │                 │
│ localhost:3000  │    │ staging.geok.com│    │   geok.com      │
│                 │    │                 │    │                 │
│ Hot Reload      │    │ Pre-production  │    │ CDN + Cache     │
│ Debug Mode      │    │ Testing         │    │ Monitoring      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 1. 开发环境部署

### 1.1 本地开发设置

**前置要求**:
- Node.js 18+
- npm 或 yarn
- Git

**安装步骤**:
```bash
# 克隆项目
git clone <repository-url>
cd geok-center-frontend

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.development

# 启动开发服务器
npm run dev
```

**开发环境配置** (`.env.development`):
```bash
# API配置
VITE_API_BASE_URL=http://localhost:8080/api/v1
VITE_WS_URL=ws://localhost:8080/api/v1/ws

# 应用配置
VITE_APP_TITLE=GEOK站点 - 开发环境
VITE_APP_ENV=development

# 调试配置
VITE_DEBUG_MODE=true
VITE_MOCK_API=false

# 热重载配置
VITE_HMR_PORT=3001
```

### 1.2 Docker开发环境

**Dockerfile.dev**:
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 3000

# 启动开发服务器
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
```

**docker-compose.dev.yml**:
```yaml
version: '3.8'
services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - VITE_API_BASE_URL=http://backend:8080/api/v1
    depends_on:
      - backend
  
  backend:
    image: geok-center-backend:latest
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
```

## 2. 构建配置

### 2.1 Vite构建配置

**vite.config.ts**:
```typescript
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig(({ mode }) => ({
  plugins: [vue()],
  
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@assets': resolve(__dirname, 'src/assets')
    }
  },
  
  build: {
    outDir: 'dist',
    sourcemap: mode === 'development',
    minify: mode === 'production' ? 'terser' : false,
    
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          charts: ['chart.js', 'vue-chartjs'],
          ui: ['ant-design-vue']
        }
      }
    },
    
    terserOptions: {
      compress: {
        drop_console: mode === 'production',
        drop_debugger: mode === 'production'
      }
    }
  },
  
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false
      }
    }
  }
}));
```

### 2.2 构建脚本

**package.json**:
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc --noEmit && vite build",
    "build:staging": "vue-tsc --noEmit && vite build --mode staging",
    "build:production": "vue-tsc --noEmit && vite build --mode production",
    "preview": "vite preview",
    "lint": "eslint src --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "test": "vitest",
    "test:e2e": "playwright test",
    "analyze": "vite-bundle-analyzer"
  }
}
```

## 3. 测试环境部署

### 3.1 Staging环境配置

**环境变量** (`.env.staging`):
```bash
# API配置
VITE_API_BASE_URL=https://api-staging.geok.com/api/v1
VITE_WS_URL=wss://api-staging.geok.com/api/v1/ws

# 应用配置
VITE_APP_TITLE=GEOK站点 - 测试环境
VITE_APP_ENV=staging

# 功能开关
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_DEBUG_MODE=false
```

### 3.2 CI/CD Pipeline (GitHub Actions)

**.github/workflows/deploy-staging.yml**:
```yaml
name: Deploy to Staging

on:
  push:
    branches: [develop]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm run test
      
      - name: Run E2E tests
        run: npm run test:e2e

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build for staging
        run: npm run build:staging
        env:
          VITE_API_BASE_URL: ${{ secrets.STAGING_API_URL }}
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: staging-build
          path: dist/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: staging-build
          path: dist/
      
      - name: Deploy to staging server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            cd /var/www/staging
            rm -rf *
            # 上传和解压构建文件的命令
```

## 4. 生产环境部署

### 4.1 生产环境配置

**环境变量** (`.env.production`):
```bash
# API配置
VITE_API_BASE_URL=https://api.geok.com/api/v1
VITE_WS_URL=wss://api.geok.com/api/v1/ws

# 应用配置
VITE_APP_TITLE=GEOK站点
VITE_APP_ENV=production

# 性能配置
VITE_ENABLE_PWA=true
VITE_ENABLE_COMPRESSION=true

# 监控配置
VITE_SENTRY_DSN=${{ secrets.SENTRY_DSN }}
VITE_GA_TRACKING_ID=${{ secrets.GA_TRACKING_ID }}
```

### 4.2 生产构建优化

**构建优化配置**:
```typescript
// vite.config.production.ts
import { defineConfig } from 'vite';
import { VitePWA } from 'vite-plugin-pwa';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [
    // PWA配置
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}']
      },
      manifest: {
        name: 'GEOK站点',
        short_name: 'GEOK',
        description: '品牌可见性分析平台',
        theme_color: '#2663FF',
        icons: [
          {
            src: 'pwa-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          }
        ]
      }
    }),
    
    // 构建分析
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true
    })
  ],
  
  build: {
    // 启用压缩
    minify: 'terser',
    
    // 代码分割
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            if (id.includes('vue')) return 'vue-vendor';
            if (id.includes('chart')) return 'chart-vendor';
            return 'vendor';
          }
        }
      }
    }
  }
});
```

### 4.3 Docker生产部署

**Dockerfile**:
```dockerfile
# 多阶段构建
FROM node:18-alpine AS builder

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production

# 构建应用
COPY . .
RUN npm run build:production

# 生产镜像
FROM nginx:alpine

# 复制构建文件
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

**nginx.conf**:
```nginx
server {
    listen 80;
    server_name geok.com www.geok.com;
    root /usr/share/nginx/html;
    index index.html;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket支持
    location /api/v1/ws {
        proxy_pass http://backend:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 5. CDN和缓存策略

### 5.1 CDN配置

**CloudFlare配置**:
```javascript
// cloudflare-worker.js
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});

async function handleRequest(request) {
  const url = new URL(request.url);
  
  // 静态资源缓存策略
  if (url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg)$/)) {
    const response = await fetch(request);
    const newResponse = new Response(response.body, response);
    newResponse.headers.set('Cache-Control', 'public, max-age=31536000');
    return newResponse;
  }
  
  // HTML文件缓存策略
  if (url.pathname.endsWith('.html') || url.pathname === '/') {
    const response = await fetch(request);
    const newResponse = new Response(response.body, response);
    newResponse.headers.set('Cache-Control', 'public, max-age=3600');
    return newResponse;
  }
  
  return fetch(request);
}
```

### 5.2 浏览器缓存策略

```typescript
// src/utils/cache.ts
class CacheManager {
  private static instance: CacheManager;
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  set(key: string, data: any, ttl: number = 300000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }
}
```

## 6. 监控和日志

### 6.1 错误监控 (Sentry)

```typescript
// src/plugins/sentry.ts
import * as Sentry from '@sentry/vue';
import { Integrations } from '@sentry/tracing';

export function setupSentry(app: App) {
  if (import.meta.env.PROD) {
    Sentry.init({
      app,
      dsn: import.meta.env.VITE_SENTRY_DSN,
      integrations: [
        new Integrations.BrowserTracing({
          routingInstrumentation: Sentry.vueRouterInstrumentation(router),
        }),
      ],
      tracesSampleRate: 0.1,
      environment: import.meta.env.VITE_APP_ENV,
    });
  }
}
```

### 6.2 性能监控

```typescript
// src/utils/performance.ts
export class PerformanceMonitor {
  static measurePageLoad(): void {
    window.addEventListener('load', () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const loadTime = navigation.loadEventEnd - navigation.fetchStart;
      
      // 发送性能数据到监控服务
      this.sendMetric('page_load_time', loadTime);
    });
  }

  static measureChartRender(chartName: string): void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      this.sendMetric(`chart_render_time_${chartName}`, renderTime);
    };
  }

  private static sendMetric(name: string, value: number): void {
    if (import.meta.env.PROD) {
      // 发送到监控服务
      fetch('/api/v1/metrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name, value, timestamp: Date.now() })
      });
    }
  }
}
```

## 7. 安全配置

### 7.1 内容安全策略 (CSP)

```html
<!-- index.html -->
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https:;
  connect-src 'self' https://api.geok.com wss://api.geok.com;
">
```

### 7.2 环境变量安全

```bash
# 生产环境变量管理
# 使用 Kubernetes Secrets 或 Docker Secrets

# 敏感信息不应出现在构建文件中
VITE_API_BASE_URL=https://api.geok.com/api/v1
# 不要在前端暴露敏感的API密钥
```

## 8. 回滚策略

### 8.1 蓝绿部署

```yaml
# kubernetes/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: geok-frontend-blue
spec:
  replicas: 3
  selector:
    matchLabels:
      app: geok-frontend
      version: blue
  template:
    metadata:
      labels:
        app: geok-frontend
        version: blue
    spec:
      containers:
      - name: frontend
        image: geok-frontend:v1.0.0
        ports:
        - containerPort: 80
```

### 8.2 快速回滚脚本

```bash
#!/bin/bash
# rollback.sh

PREVIOUS_VERSION=$1

if [ -z "$PREVIOUS_VERSION" ]; then
  echo "Usage: ./rollback.sh <previous_version>"
  exit 1
fi

echo "Rolling back to version: $PREVIOUS_VERSION"

# 更新Kubernetes部署
kubectl set image deployment/geok-frontend frontend=geok-frontend:$PREVIOUS_VERSION

# 等待部署完成
kubectl rollout status deployment/geok-frontend

# 验证部署
kubectl get pods -l app=geok-frontend

echo "Rollback completed successfully"
```

## 9. 部署检查清单

### 部署前检查
- [ ] 所有测试通过
- [ ] 代码审查完成
- [ ] 环境变量配置正确
- [ ] 构建无错误和警告
- [ ] 性能测试通过

### 部署后验证
- [ ] 应用正常启动
- [ ] 健康检查端点响应正常
- [ ] 主要功能可用
- [ ] 监控指标正常
- [ ] 错误日志无异常

### 回滚准备
- [ ] 回滚脚本测试
- [ ] 数据库迁移兼容性
- [ ] 依赖服务版本兼容
- [ ] 监控告警配置
