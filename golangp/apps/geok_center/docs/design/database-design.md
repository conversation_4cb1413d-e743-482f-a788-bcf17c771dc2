# GEOK Center 数据库设计文档

## 概述

本文档详细描述了GEOK Center的数据库设计，包括实体关系模型、表结构定义、索引策略和数据迁移方案。

## 数据库架构

### 技术选型
- **主数据库**: PostgreSQL 15+
- **缓存数据库**: Redis 7+
- **搜索引擎**: Elasticsearch 8+ (可选)
- **时序数据库**: InfluxDB 2+ (用于指标存储)

### 数据库分层

```
┌─────────────────┐
│   Application   │
│     Layer       │
└─────────────────┘
         │
┌─────────────────┐
│   Repository    │  ← GORM ORM
│     Layer       │
└─────────────────┘
         │
┌─────────────────┐
│   PostgreSQL    │  ← 主要业务数据
│   (Primary DB)  │
└─────────────────┘
         │
┌─────────────────┐
│     Redis       │  ← 缓存和会话
│    (Cache)      │
└─────────────────┘
```

## 核心实体设计

### 1. 企业实体 (Company)

```sql
CREATE TABLE companies (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    industry VARCHAR(100),
    subscription_plan VARCHAR(50) DEFAULT 'basic',
    subscription_expires_at TIMESTAMP,
    settings JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT companies_name_check CHECK (LENGTH(name) >= 2),
    CONSTRAINT companies_status_check CHECK (status IN ('active', 'suspended', 'deleted'))
);

-- 索引
CREATE INDEX idx_companies_domain ON companies(domain);
CREATE INDEX idx_companies_status ON companies(status);
CREATE INDEX idx_companies_subscription ON companies(subscription_plan);
```

**Go模型定义**:
```go
type Company struct {
    ID                   uint      `gorm:"primaryKey" json:"id"`
    Name                 string    `gorm:"size:255;not null" json:"name"`
    Domain               string    `gorm:"size:255;uniqueIndex" json:"domain"`
    Industry             string    `gorm:"size:100" json:"industry"`
    SubscriptionPlan     string    `gorm:"size:50;default:basic" json:"subscription_plan"`
    SubscriptionExpiresAt *time.Time `json:"subscription_expires_at"`
    Settings             datatypes.JSON `gorm:"type:jsonb;default:'{}'" json:"settings"`
    Status               string    `gorm:"size:20;default:active" json:"status"`
    CreatedAt            time.Time `json:"created_at"`
    UpdatedAt            time.Time `json:"updated_at"`
    
    // 关联关系
    Users  []User  `gorm:"foreignKey:CompanyID" json:"users,omitempty"`
    Brands []Brand `gorm:"foreignKey:CompanyID" json:"brands,omitempty"`
}
```

### 2. 用户实体 (User)

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user',
    status VARCHAR(20) DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP,
    login_count INTEGER DEFAULT 0,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT users_email_check CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT users_role_check CHECK (role IN ('admin', 'manager', 'user', 'viewer')),
    CONSTRAINT users_status_check CHECK (status IN ('active', 'inactive', 'suspended'))
);

-- 索引
CREATE INDEX idx_users_company_id ON users(company_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_role ON users(role);
```

**Go模型定义**:
```go
type User struct {
    ID            uint      `gorm:"primaryKey" json:"id"`
    CompanyID     uint      `gorm:"not null;index" json:"company_id"`
    Email         string    `gorm:"size:255;uniqueIndex;not null" json:"email"`
    PasswordHash  string    `gorm:"size:255;not null" json:"-"`
    FirstName     string    `gorm:"size:100" json:"first_name"`
    LastName      string    `gorm:"size:100" json:"last_name"`
    Role          string    `gorm:"size:20;default:user" json:"role"`
    Status        string    `gorm:"size:20;default:active" json:"status"`
    EmailVerified bool      `gorm:"default:false" json:"email_verified"`
    LastLoginAt   *time.Time `json:"last_login_at"`
    LoginCount    int       `gorm:"default:0" json:"login_count"`
    Preferences   datatypes.JSON `gorm:"type:jsonb;default:'{}'" json:"preferences"`
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
    
    // 关联关系
    Company Company `gorm:"foreignKey:CompanyID" json:"company,omitempty"`
}
```

### 3. 品牌实体 (Brand)

```sql
CREATE TABLE brands (
    id SERIAL PRIMARY KEY,
    company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    industry VARCHAR(100),
    website VARCHAR(500),
    logo_url VARCHAR(500),
    keywords TEXT[],
    competitors TEXT[],
    monitoring_enabled BOOLEAN DEFAULT TRUE,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT brands_name_check CHECK (LENGTH(name) >= 1),
    CONSTRAINT brands_status_check CHECK (status IN ('active', 'inactive', 'archived'))
);

-- 索引
CREATE INDEX idx_brands_company_id ON brands(company_id);
CREATE INDEX idx_brands_name ON brands(name);
CREATE INDEX idx_brands_status ON brands(status);
CREATE INDEX idx_brands_keywords ON brands USING GIN(keywords);
```

**Go模型定义**:
```go
type Brand struct {
    ID                uint           `gorm:"primaryKey" json:"id"`
    CompanyID         uint           `gorm:"not null;index" json:"company_id"`
    Name              string         `gorm:"size:255;not null" json:"name"`
    Description       string         `gorm:"type:text" json:"description"`
    Industry          string         `gorm:"size:100" json:"industry"`
    Website           string         `gorm:"size:500" json:"website"`
    LogoURL           string         `gorm:"size:500" json:"logo_url"`
    Keywords          pq.StringArray `gorm:"type:text[]" json:"keywords"`
    Competitors       pq.StringArray `gorm:"type:text[]" json:"competitors"`
    MonitoringEnabled bool           `gorm:"default:true" json:"monitoring_enabled"`
    Status            string         `gorm:"size:20;default:active" json:"status"`
    CreatedAt         time.Time      `json:"created_at"`
    UpdatedAt         time.Time      `json:"updated_at"`
    
    // 关联关系
    Company      Company        `gorm:"foreignKey:CompanyID" json:"company,omitempty"`
    Metrics      []BrandMetric  `gorm:"foreignKey:BrandID" json:"metrics,omitempty"`
    AIDataPoints []AIDataPoint  `gorm:"foreignKey:BrandID" json:"ai_data_points,omitempty"`
}
```

### 4. 品牌指标实体 (BrandMetric)

```sql
CREATE TABLE brand_metrics (
    id SERIAL PRIMARY KEY,
    brand_id INTEGER NOT NULL REFERENCES brands(id) ON DELETE CASCADE,
    metric_type VARCHAR(50) NOT NULL,
    value DECIMAL(10,4) NOT NULL,
    unit VARCHAR(20),
    period_start TIMESTAMP NOT NULL,
    period_end TIMESTAMP NOT NULL,
    metadata JSONB DEFAULT '{}',
    calculated_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT brand_metrics_period_check CHECK (period_end > period_start),
    CONSTRAINT brand_metrics_value_check CHECK (value >= 0)
);

-- 索引
CREATE INDEX idx_brand_metrics_brand_id ON brand_metrics(brand_id);
CREATE INDEX idx_brand_metrics_type ON brand_metrics(metric_type);
CREATE INDEX idx_brand_metrics_period ON brand_metrics(period_start, period_end);
CREATE INDEX idx_brand_metrics_calculated ON brand_metrics(calculated_at);

-- 复合索引
CREATE INDEX idx_brand_metrics_brand_type_period ON brand_metrics(brand_id, metric_type, period_start);
```

**Go模型定义**:
```go
type BrandMetric struct {
    ID           uint           `gorm:"primaryKey" json:"id"`
    BrandID      uint           `gorm:"not null;index" json:"brand_id"`
    MetricType   string         `gorm:"size:50;not null;index" json:"metric_type"`
    Value        decimal.Decimal `gorm:"type:decimal(10,4);not null" json:"value"`
    Unit         string         `gorm:"size:20" json:"unit"`
    PeriodStart  time.Time      `gorm:"not null;index" json:"period_start"`
    PeriodEnd    time.Time      `gorm:"not null;index" json:"period_end"`
    Metadata     datatypes.JSON `gorm:"type:jsonb;default:'{}'" json:"metadata"`
    CalculatedAt time.Time      `gorm:"default:CURRENT_TIMESTAMP" json:"calculated_at"`
    
    // 关联关系
    Brand Brand `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
}
```

### 5. AI数据点实体 (AIDataPoint)

```sql
CREATE TABLE ai_data_points (
    id SERIAL PRIMARY KEY,
    brand_id INTEGER NOT NULL REFERENCES brands(id) ON DELETE CASCADE,
    platform VARCHAR(100) NOT NULL,
    query_text VARCHAR(1000) NOT NULL,
    mentions_count INTEGER DEFAULT 0,
    sentiment_score DECIMAL(3,2),
    confidence_score DECIMAL(3,2),
    source_url VARCHAR(1000),
    raw_data JSONB,
    collected_at TIMESTAMP NOT NULL,
    processed_at TIMESTAMP DEFAULT NOW(),
    
    CONSTRAINT ai_data_sentiment_check CHECK (sentiment_score BETWEEN -1.0 AND 1.0),
    CONSTRAINT ai_data_confidence_check CHECK (confidence_score BETWEEN 0.0 AND 1.0),
    CONSTRAINT ai_data_mentions_check CHECK (mentions_count >= 0)
);

-- 索引
CREATE INDEX idx_ai_data_brand_id ON ai_data_points(brand_id);
CREATE INDEX idx_ai_data_platform ON ai_data_points(platform);
CREATE INDEX idx_ai_data_collected ON ai_data_points(collected_at);
CREATE INDEX idx_ai_data_processed ON ai_data_points(processed_at);

-- 复合索引
CREATE INDEX idx_ai_data_brand_platform_collected ON ai_data_points(brand_id, platform, collected_at);
```

**Go模型定义**:
```go
type AIDataPoint struct {
    ID              uint            `gorm:"primaryKey" json:"id"`
    BrandID         uint            `gorm:"not null;index" json:"brand_id"`
    Platform        string          `gorm:"size:100;not null;index" json:"platform"`
    QueryText       string          `gorm:"size:1000;not null" json:"query_text"`
    MentionsCount   int             `gorm:"default:0" json:"mentions_count"`
    SentimentScore  *decimal.Decimal `gorm:"type:decimal(3,2)" json:"sentiment_score"`
    ConfidenceScore *decimal.Decimal `gorm:"type:decimal(3,2)" json:"confidence_score"`
    SourceURL       string          `gorm:"size:1000" json:"source_url"`
    RawData         datatypes.JSON  `gorm:"type:jsonb" json:"raw_data"`
    CollectedAt     time.Time       `gorm:"not null;index" json:"collected_at"`
    ProcessedAt     time.Time       `gorm:"default:CURRENT_TIMESTAMP" json:"processed_at"`
    
    // 关联关系
    Brand Brand `gorm:"foreignKey:BrandID" json:"brand,omitempty"`
}
```

## 数据迁移策略

### 迁移文件结构
```
migrations/
├── 001_create_companies_table.up.sql
├── 001_create_companies_table.down.sql
├── 002_create_users_table.up.sql
├── 002_create_users_table.down.sql
├── 003_create_brands_table.up.sql
├── 003_create_brands_table.down.sql
├── 004_create_brand_metrics_table.up.sql
├── 004_create_brand_metrics_table.down.sql
├── 005_create_ai_data_points_table.up.sql
└── 005_create_ai_data_points_table.down.sql
```

### 迁移管理工具
```go
// 使用golang-migrate
import (
    "github.com/golang-migrate/migrate/v4"
    "github.com/golang-migrate/migrate/v4/database/postgres"
    _ "github.com/golang-migrate/migrate/v4/source/file"
)

func RunMigrations(db *sql.DB) error {
    driver, err := postgres.WithInstance(db, &postgres.Config{})
    if err != nil {
        return err
    }
    
    m, err := migrate.NewWithDatabaseInstance(
        "file://migrations",
        "postgres", driver)
    if err != nil {
        return err
    }
    
    return m.Up()
}
```

## 性能优化策略

### 1. 索引优化
- 为频繁查询的字段创建索引
- 使用复合索引优化多字段查询
- 定期分析查询性能并调整索引

### 2. 分区策略
```sql
-- 按时间分区AI数据表
CREATE TABLE ai_data_points_y2025m01 PARTITION OF ai_data_points
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE ai_data_points_y2025m02 PARTITION OF ai_data_points
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
```

### 3. 缓存策略
```go
// Redis缓存键命名规范
const (
    CacheKeyBrandMetrics    = "brand:metrics:%d:%s"     // brand_id:metric_type
    CacheKeyDashboard       = "dashboard:%d"            // company_id
    CacheKeyBrandVisibility = "brand:visibility:%d"     // brand_id
    CacheKeyUserSession     = "session:%s"              // session_id
)

// 缓存TTL配置
var CacheTTL = map[string]time.Duration{
    "brand_metrics":    15 * time.Minute,
    "dashboard":        5 * time.Minute,
    "brand_visibility": 10 * time.Minute,
    "user_session":     24 * time.Hour,
}
```

## 数据备份和恢复

### 备份策略
```bash
# 每日全量备份
pg_dump -h localhost -U postgres -d geok_center > backup_$(date +%Y%m%d).sql

# 每小时增量备份 (使用WAL-E或类似工具)
wal-e backup-push /var/lib/postgresql/data
```

### 恢复流程
```bash
# 从备份恢复
psql -h localhost -U postgres -d geok_center_new < backup_20250715.sql

# 验证数据完整性
psql -h localhost -U postgres -d geok_center_new -c "SELECT COUNT(*) FROM companies;"
```

## 监控和维护

### 性能监控
```sql
-- 查询慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- 检查表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 定期维护任务
```sql
-- 清理过期数据 (保留1年数据)
DELETE FROM ai_data_points 
WHERE collected_at < NOW() - INTERVAL '1 year';

-- 重建索引
REINDEX TABLE ai_data_points;

-- 更新表统计信息
ANALYZE;
```
