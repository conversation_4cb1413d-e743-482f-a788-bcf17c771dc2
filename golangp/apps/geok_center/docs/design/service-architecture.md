# GEOK Center 服务架构设计

## 概述

本文档描述了GEOK Center的微服务架构设计，包括服务拆分、通信机制、数据流和部署策略。

## 整体架构图

```
                    ┌─────────────────┐
                    │   Load Balancer │
                    │     (Nginx)     │
                    └─────────────────┘
                             │
                    ┌─────────────────┐
                    │   API Gateway   │
                    │   (Gin Router)  │
                    └─────────────────┘
                             │
        ┌────────────────────┼────────────────────┐
        │                    │                    │
┌───────▼──────┐    ┌────────▼────────┐    ┌─────▼──────┐
│ Auth Service │    │ Analytics Service│    │Data Service│
│              │    │                 │    │            │
└──────────────┘    └─────────────────┘    └────────────┘
        │                    │                    │
        │            ┌───────▼──────┐            │
        │            │Export Service│            │
        │            │              │            │
        │            └──────────────┘            │
        │                    │                    │
        └────────────────────┼────────────────────┘
                             │
                    ┌─────────────────┐
                    │  Message Queue  │
                    │    (Kafka)      │
                    └─────────────────┘
                             │
        ┌────────────────────┼────────────────────┐
        │                    │                    │
┌───────▼──────┐    ┌────────▼────────┐    ┌─────▼──────┐
│  PostgreSQL  │    │     Redis       │    │   MinIO    │
│  (Primary)   │    │   (Cache)       │    │(File Store)│
└──────────────┘    └─────────────────┘    └────────────┘
```

## 核心服务模块

### 1. 认证服务 (Auth Service)

**职责范围**:
- 用户注册、登录、登出
- JWT Token 生成和验证
- 密码重置和邮箱验证
- 多租户权限管理
- OAuth2 第三方登录集成

**技术实现**:
```go
type AuthService struct {
    userRepo     repository.UserRepository
    companyRepo  repository.CompanyRepository
    jwtManager   *jwt.Manager
    emailService *email.Service
    cache        cache.Cache
}

// 核心接口
type AuthServiceInterface interface {
    Register(ctx context.Context, req *RegisterRequest) (*AuthResponse, error)
    Login(ctx context.Context, req *LoginRequest) (*AuthResponse, error)
    RefreshToken(ctx context.Context, token string) (*AuthResponse, error)
    Logout(ctx context.Context, userID uint) error
    ResetPassword(ctx context.Context, email string) error
    VerifyEmail(ctx context.Context, token string) error
}
```

**数据模型**:
```go
type AuthRequest struct {
    Email    string `json:"email" binding:"required,email"`
    Password string `json:"password" binding:"required,min=8"`
}

type AuthResponse struct {
    Token        string    `json:"token"`
    RefreshToken string    `json:"refresh_token"`
    ExpiresAt    time.Time `json:"expires_at"`
    User         UserInfo  `json:"user"`
}

type UserInfo struct {
    ID        uint   `json:"id"`
    Email     string `json:"email"`
    FirstName string `json:"first_name"`
    LastName  string `json:"last_name"`
    Role      string `json:"role"`
    CompanyID uint   `json:"company_id"`
}
```

### 2. 数据分析服务 (Analytics Service)

**职责范围**:
- 品牌可见性指标计算
- AI平台数据聚合分析
- 竞争对手对比分析
- 趋势预测和异常检测
- 实时指标更新

**技术实现**:
```go
type AnalyticsService struct {
    brandRepo      repository.BrandRepository
    metricsRepo    repository.MetricsRepository
    aiDataRepo     repository.AIDataRepository
    cacheService   cache.Service
    eventPublisher event.Publisher
}

// 核心接口
type AnalyticsServiceInterface interface {
    GetDashboard(ctx context.Context, companyID uint, filters *DashboardFilters) (*DashboardData, error)
    GetBrandVisibility(ctx context.Context, brandID uint, period string) (*VisibilityMetrics, error)
    GetAIAppearance(ctx context.Context, brandID uint, timeRange *TimeRange) (*AIAppearanceData, error)
    GetCompetitorAnalysis(ctx context.Context, brandID uint, competitors []string) (*CompetitorData, error)
    CalculateMetrics(ctx context.Context, brandID uint) error
}
```

**算法模块**:
```go
// 品牌可见性计算算法
type VisibilityCalculator struct {
    weightConfig *WeightConfig
}

type WeightConfig struct {
    SearchMentions    float64 `json:"search_mentions"`
    AIPlatformRanking float64 `json:"ai_platform_ranking"`
    SentimentScore    float64 `json:"sentiment_score"`
    FrequencyTrend    float64 `json:"frequency_trend"`
}

func (vc *VisibilityCalculator) Calculate(data *BrandData) *VisibilityScore {
    score := data.SearchMentions*vc.weightConfig.SearchMentions +
             data.AIPlatformRanking*vc.weightConfig.AIPlatformRanking +
             data.SentimentScore*vc.weightConfig.SentimentScore +
             data.FrequencyTrend*vc.weightConfig.FrequencyTrend
    
    return &VisibilityScore{
        Overall:    normalizeScore(score),
        Components: calculateComponents(data),
        Trend:      calculateTrend(data),
    }
}
```

### 3. 数据采集服务 (Data Collection Service)

**职责范围**:
- AI平台数据爬虫
- 搜索引擎数据采集
- 社交媒体监控
- 第三方API数据集成
- 数据清洗和标准化

**技术实现**:
```go
type DataCollectionService struct {
    crawlers    []crawler.Crawler
    processors  []processor.DataProcessor
    storage     storage.DataStorage
    scheduler   scheduler.Scheduler
    queue       queue.MessageQueue
}

// 爬虫接口
type Crawler interface {
    Name() string
    Crawl(ctx context.Context, query *CrawlQuery) (*CrawlResult, error)
    IsRateLimited() bool
    GetNextAvailableTime() time.Time
}

// AI平台爬虫实现
type ChatGPTCrawler struct {
    client     *http.Client
    apiKey     string
    rateLimit  *ratelimit.Limiter
}

func (c *ChatGPTCrawler) Crawl(ctx context.Context, query *CrawlQuery) (*CrawlResult, error) {
    // 实现ChatGPT数据采集逻辑
    if !c.rateLimit.Allow() {
        return nil, ErrRateLimited
    }
    
    // 发送请求并处理响应
    response, err := c.sendRequest(ctx, query)
    if err != nil {
        return nil, err
    }
    
    return c.parseResponse(response)
}
```

**数据处理管道**:
```go
type DataPipeline struct {
    stages []PipelineStage
}

type PipelineStage interface {
    Process(ctx context.Context, data *RawData) (*ProcessedData, error)
    Name() string
}

// 数据清洗阶段
type DataCleaningStage struct {
    rules []CleaningRule
}

func (dcs *DataCleaningStage) Process(ctx context.Context, data *RawData) (*ProcessedData, error) {
    cleaned := &ProcessedData{
        OriginalData: data,
        CleanedText:  data.Text,
    }
    
    for _, rule := range dcs.rules {
        cleaned.CleanedText = rule.Apply(cleaned.CleanedText)
    }
    
    return cleaned, nil
}
```

### 4. 导出服务 (Export Service)

**职责范围**:
- 数据导出任务管理
- 多格式文件生成 (CSV, PDF, Excel)
- 报告模板管理
- 文件存储和下载
- 导出历史记录

**技术实现**:
```go
type ExportService struct {
    taskRepo     repository.ExportTaskRepository
    fileStorage  storage.FileStorage
    generators   map[string]Generator
    queue        queue.TaskQueue
}

// 导出生成器接口
type Generator interface {
    Generate(ctx context.Context, data interface{}, options *GenerateOptions) (*GeneratedFile, error)
    SupportedFormats() []string
}

// CSV生成器
type CSVGenerator struct{}

func (cg *CSVGenerator) Generate(ctx context.Context, data interface{}, options *GenerateOptions) (*GeneratedFile, error) {
    var buf bytes.Buffer
    writer := csv.NewWriter(&buf)
    
    // 根据数据类型生成CSV
    switch d := data.(type) {
    case *DashboardData:
        return cg.generateDashboardCSV(d, writer)
    case *BrandMetrics:
        return cg.generateMetricsCSV(d, writer)
    default:
        return nil, ErrUnsupportedDataType
    }
}

// PDF生成器
type PDFGenerator struct {
    templateEngine *template.Engine
}

func (pg *PDFGenerator) Generate(ctx context.Context, data interface{}, options *GenerateOptions) (*GeneratedFile, error) {
    // 使用模板生成HTML
    htmlContent, err := pg.templateEngine.Render(options.Template, data)
    if err != nil {
        return nil, err
    }
    
    // 转换为PDF
    pdfBytes, err := wkhtmltopdf.GeneratePDF(htmlContent)
    if err != nil {
        return nil, err
    }
    
    return &GeneratedFile{
        Content:  pdfBytes,
        MimeType: "application/pdf",
        Filename: options.Filename,
    }, nil
}
```

## 服务间通信

### 1. 同步通信 (HTTP/gRPC)

**HTTP REST API**:
```go
// 服务间HTTP客户端
type ServiceClient struct {
    baseURL    string
    httpClient *http.Client
    auth       AuthProvider
}

func (sc *ServiceClient) GetBrandMetrics(ctx context.Context, brandID uint) (*BrandMetrics, error) {
    url := fmt.Sprintf("%s/api/v1/brands/%d/metrics", sc.baseURL, brandID)
    
    req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
    if err != nil {
        return nil, err
    }
    
    // 添加认证头
    token, err := sc.auth.GetToken()
    if err != nil {
        return nil, err
    }
    req.Header.Set("Authorization", "Bearer "+token)
    
    resp, err := sc.httpClient.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()
    
    var metrics BrandMetrics
    return &metrics, json.NewDecoder(resp.Body).Decode(&metrics)
}
```

### 2. 异步通信 (Message Queue)

**Kafka消息队列**:
```go
// 事件发布者
type EventPublisher struct {
    producer *kafka.Producer
}

func (ep *EventPublisher) PublishBrandMetricsUpdated(ctx context.Context, event *BrandMetricsUpdatedEvent) error {
    message := &kafka.Message{
        Topic: "brand.metrics.updated",
        Key:   []byte(fmt.Sprintf("brand:%d", event.BrandID)),
        Value: mustMarshalJSON(event),
        Headers: []kafka.Header{
            {Key: "event_type", Value: []byte("brand_metrics_updated")},
            {Key: "timestamp", Value: []byte(time.Now().Format(time.RFC3339))},
        },
    }
    
    return ep.producer.Produce(message, nil)
}

// 事件消费者
type EventConsumer struct {
    consumer *kafka.Consumer
    handlers map[string]EventHandler
}

func (ec *EventConsumer) Start(ctx context.Context) error {
    for {
        select {
        case <-ctx.Done():
            return ctx.Err()
        default:
            msg, err := ec.consumer.ReadMessage(100 * time.Millisecond)
            if err != nil {
                continue
            }
            
            eventType := getHeaderValue(msg.Headers, "event_type")
            if handler, exists := ec.handlers[eventType]; exists {
                go handler.Handle(ctx, msg)
            }
        }
    }
}
```

## 配置管理

### 环境配置
```go
type Config struct {
    Server   ServerConfig   `mapstructure:"server"`
    Database DatabaseConfig `mapstructure:"database"`
    Redis    RedisConfig    `mapstructure:"redis"`
    Kafka    KafkaConfig    `mapstructure:"kafka"`
    Auth     AuthConfig     `mapstructure:"auth"`
    External ExternalConfig `mapstructure:"external"`
}

type ServerConfig struct {
    Host         string        `mapstructure:"host"`
    Port         int           `mapstructure:"port"`
    ReadTimeout  time.Duration `mapstructure:"read_timeout"`
    WriteTimeout time.Duration `mapstructure:"write_timeout"`
    IdleTimeout  time.Duration `mapstructure:"idle_timeout"`
}

type DatabaseConfig struct {
    Host         string `mapstructure:"host"`
    Port         int    `mapstructure:"port"`
    User         string `mapstructure:"user"`
    Password     string `mapstructure:"password"`
    Database     string `mapstructure:"database"`
    SSLMode      string `mapstructure:"ssl_mode"`
    MaxOpenConns int    `mapstructure:"max_open_conns"`
    MaxIdleConns int    `mapstructure:"max_idle_conns"`
}
```

### 配置文件示例
```yaml
# config/production.yaml
server:
  host: "0.0.0.0"
  port: 8080
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"

database:
  host: "***********"
  port: 5439
  user: "geok"
  password: "${DB_PASSWORD}"
  database: "geok"
  ssl_mode: "require"
  max_open_conns: 25
  max_idle_conns: 5

redis:
  host: "***********"
  port: 6389
  password: "geok"
  db: 0
  pool_size: 10

kafka:
  brokers: ["kafka1:9092", "kafka2:9092", "kafka3:9092"]
  consumer_group: "geok-analytics"
  auto_offset_reset: "earliest"

auth:
  jwt_secret: "${JWT_SECRET}"
  jwt_expiry: "24h"
  refresh_expiry: "168h"

external:
  openai_api_key: "${OPENAI_API_KEY}"
  claude_api_key: "${CLAUDE_API_KEY}"
  email_service_url: "${EMAIL_SERVICE_URL}"
```

## 监控和可观测性

### 指标收集
```go
// Prometheus指标定义
var (
    httpRequestsTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "http_requests_total",
            Help: "Total number of HTTP requests",
        },
        []string{"method", "endpoint", "status"},
    )
    
    httpRequestDuration = prometheus.NewHistogramVec(
        prometheus.HistogramOpts{
            Name: "http_request_duration_seconds",
            Help: "HTTP request duration in seconds",
            Buckets: prometheus.DefBuckets,
        },
        []string{"method", "endpoint"},
    )
    
    brandMetricsCalculated = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "brand_metrics_calculated_total",
            Help: "Total number of brand metrics calculated",
        },
        []string{"brand_id", "metric_type"},
    )
)
```

### 健康检查
```go
type HealthChecker struct {
    db    *gorm.DB
    redis *redis.Client
    kafka *kafka.Producer
}

func (hc *HealthChecker) Check(ctx context.Context) *HealthStatus {
    status := &HealthStatus{
        Status: "healthy",
        Checks: make(map[string]CheckResult),
    }
    
    // 检查数据库连接
    if err := hc.checkDatabase(ctx); err != nil {
        status.Checks["database"] = CheckResult{Status: "unhealthy", Error: err.Error()}
        status.Status = "unhealthy"
    } else {
        status.Checks["database"] = CheckResult{Status: "healthy"}
    }
    
    // 检查Redis连接
    if err := hc.checkRedis(ctx); err != nil {
        status.Checks["redis"] = CheckResult{Status: "unhealthy", Error: err.Error()}
        status.Status = "unhealthy"
    } else {
        status.Checks["redis"] = CheckResult{Status: "healthy"}
    }
    
    return status
}
```

## 部署策略

### Docker容器化
```dockerfile
# Dockerfile
FROM golang:1.23-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o main ./cmd/server

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/main .
COPY --from=builder /app/config ./config

CMD ["./main"]
```

### Kubernetes部署
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: geok-analytics-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: geok-analytics-service
  template:
    metadata:
      labels:
        app: geok-analytics-service
    spec:
      containers:
      - name: analytics-service
        image: geok/analytics-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: auth-secret
              key: jwt-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 扩展性考虑

### 水平扩展
- 无状态服务设计
- 数据库读写分离
- 缓存层分布式部署
- 消息队列分区策略

### 垂直扩展
- 资源监控和自动调整
- 数据库连接池优化
- 内存使用优化
- CPU密集型任务优化

### 数据分片
```go
// 品牌数据分片策略
type BrandShardingStrategy struct {
    shardCount int
}

func (bss *BrandShardingStrategy) GetShardKey(brandID uint) string {
    shardIndex := brandID % uint(bss.shardCount)
    return fmt.Sprintf("shard_%d", shardIndex)
}

func (bss *BrandShardingStrategy) GetDatabaseName(shardKey string) string {
    return fmt.Sprintf("geok_%s", shardKey)
}
```
