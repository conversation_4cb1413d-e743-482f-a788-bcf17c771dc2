# GEOK站点 测试策略文档

## 测试概述

本文档定义了GEOK站点前端UI的全面测试策略，确保基于Figma设计实现的组件质量和用户体验。

## 测试金字塔

```
    /\
   /  \     E2E Tests (10%)
  /____\    
 /      \   Integration Tests (20%)
/________\  Unit Tests (70%)
```

## 1. 单元测试 (Unit Tests)

### 1.1 组件测试

**测试框架**: Vitest + Vue Test Utils / Jest + React Testing Library

#### 布局组件测试

**MainLayout.vue**
```typescript
describe('MainLayout', () => {
  it('should render sidebar when showSidebar is true', () => {
    const wrapper = mount(MainLayout, {
      props: { showSidebar: true }
    });
    expect(wrapper.find('[data-testid="sidebar"]').exists()).toBe(true);
  });

  it('should toggle sidebar collapse state', async () => {
    const wrapper = mount(MainLayout);
    await wrapper.find('[data-testid="sidebar-toggle"]').trigger('click');
    expect(wrapper.emitted('sidebar-toggle')).toBeTruthy();
  });
});
```

**Sidebar.vue**
```typescript
describe('Sidebar', () => {
  const mockNavigationItems = [
    { id: 'home', label: '首页', route: '/dashboard', active: true },
    { id: 'inbox', label: '收件箱', route: '/inbox', active: false }
  ];

  it('should highlight active navigation item', () => {
    const wrapper = mount(Sidebar, {
      props: { navigationItems: mockNavigationItems }
    });
    
    const activeItem = wrapper.find('[data-testid="nav-item-home"]');
    expect(activeItem.classes()).toContain('active');
  });

  it('should emit navigation event on item click', async () => {
    const wrapper = mount(Sidebar, {
      props: { navigationItems: mockNavigationItems }
    });
    
    await wrapper.find('[data-testid="nav-item-inbox"]').trigger('click');
    expect(wrapper.emitted('navigate')).toEqual([['inbox']]);
  });
});
```

#### 图表组件测试

**BrandSearchChart.vue**
```typescript
describe('BrandSearchChart', () => {
  const mockData = {
    totalData: "159.8%",
    percentage: 159.8,
    trend: 'up' as const,
    chartData: [10, 20, 15, 25, 30]
  };

  it('should render chart with correct data', () => {
    const wrapper = mount(BrandSearchChart, {
      props: { data: mockData }
    });
    
    expect(wrapper.find('[data-testid="total-percentage"]').text()).toBe('159.8%');
    expect(wrapper.find('[data-testid="trend-indicator"]').classes()).toContain('trend-up');
  });

  it('should handle empty data gracefully', () => {
    const wrapper = mount(BrandSearchChart, {
      props: { data: null }
    });
    
    expect(wrapper.find('[data-testid="empty-state"]').exists()).toBe(true);
  });
});
```

**BrandDistributionChart.vue**
```typescript
describe('BrandDistributionChart', () => {
  const mockData = {
    brands: [
      { name: 'Intel', percentage: 72.5, color: '#2663FF' },
      { name: 'AMD', percentage: 15.7, color: '#FF6B35' }
    ]
  };

  it('should render pie chart with brand data', () => {
    const wrapper = mount(BrandDistributionChart, {
      props: { data: mockData }
    });
    
    expect(wrapper.findAll('[data-testid="brand-segment"]')).toHaveLength(2);
  });

  it('should show brand legend with correct colors', () => {
    const wrapper = mount(BrandDistributionChart, {
      props: { data: mockData }
    });
    
    const intelLegend = wrapper.find('[data-testid="legend-Intel"]');
    expect(intelLegend.attributes('style')).toContain('background-color: #2663FF');
  });
});
```

### 1.2 工具函数测试

**数据格式化函数**
```typescript
describe('formatUtils', () => {
  describe('formatPercentage', () => {
    it('should format number to percentage with 1 decimal place', () => {
      expect(formatPercentage(0.725)).toBe('72.5%');
      expect(formatPercentage(0.157)).toBe('15.7%');
    });

    it('should handle edge cases', () => {
      expect(formatPercentage(0)).toBe('0.0%');
      expect(formatPercentage(1)).toBe('100.0%');
      expect(formatPercentage(null)).toBe('N/A');
    });
  });

  describe('formatNumber', () => {
    it('should format large numbers with commas', () => {
      expect(formatNumber(1234567)).toBe('1,234,567');
      expect(formatNumber(1000)).toBe('1,000');
    });
  });
});
```

### 1.3 状态管理测试

**Pinia Store测试**
```typescript
describe('dashboardStore', () => {
  let store: ReturnType<typeof useDashboardStore>;

  beforeEach(() => {
    setActivePinia(createPinia());
    store = useDashboardStore();
  });

  it('should fetch dashboard data successfully', async () => {
    const mockData = { brandSearchRate: { percentage: 159.8 } };
    vi.mocked(api.getDashboardData).mockResolvedValue(mockData);

    await store.fetchDashboardData();

    expect(store.dashboardData).toEqual(mockData);
    expect(store.loading).toBe(false);
  });

  it('should handle API errors', async () => {
    const error = new Error('API Error');
    vi.mocked(api.getDashboardData).mockRejectedValue(error);

    await store.fetchDashboardData();

    expect(store.error).toBe('API Error');
    expect(store.loading).toBe(false);
  });
});
```

## 2. 集成测试 (Integration Tests)

### 2.1 组件集成测试

**仪表板页面集成**
```typescript
describe('Dashboard Integration', () => {
  it('should load and display all dashboard components', async () => {
    const wrapper = mount(Dashboard, {
      global: {
        plugins: [createTestingPinia()]
      }
    });

    // 等待数据加载
    await flushPromises();

    expect(wrapper.find('[data-testid="brand-search-chart"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="brand-distribution-chart"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="ai-frequency-chart"]').exists()).toBe(true);
  });

  it('should handle filter changes across components', async () => {
    const wrapper = mount(Dashboard);
    
    // 更改时间范围筛选
    await wrapper.find('[data-testid="time-range-filter"]').setValue('30d');
    await wrapper.find('[data-testid="apply-filter"]').trigger('click');

    // 验证所有图表都更新了
    expect(wrapper.vm.chartFilters.timeRange).toBe('30d');
  });
});
```

### 2.2 API集成测试

**使用MSW模拟API**
```typescript
import { setupServer } from 'msw/node';
import { rest } from 'msw';

const server = setupServer(
  rest.get('/api/v1/analytics/dashboard', (req, res, ctx) => {
    return res(ctx.json({
      brandSearchRate: { percentage: 159.8, trend: 'up' },
      brandDistribution: { brands: [] }
    }));
  })
);

describe('API Integration', () => {
  beforeAll(() => server.listen());
  afterEach(() => server.resetHandlers());
  afterAll(() => server.close());

  it('should fetch and display dashboard data', async () => {
    const wrapper = mount(Dashboard);
    
    await waitFor(() => {
      expect(wrapper.find('[data-testid="search-rate"]').text()).toBe('159.8%');
    });
  });

  it('should handle API errors gracefully', async () => {
    server.use(
      rest.get('/api/v1/analytics/dashboard', (req, res, ctx) => {
        return res(ctx.status(500));
      })
    );

    const wrapper = mount(Dashboard);
    
    await waitFor(() => {
      expect(wrapper.find('[data-testid="error-message"]').exists()).toBe(true);
    });
  });
});
```

## 3. 端到端测试 (E2E Tests)

### 3.1 用户流程测试

**使用Playwright**

```typescript
import { test, expect } from '@playwright/test';

test.describe('Dashboard User Flow', () => {
  test('user can view brand analytics dashboard', async ({ page }) => {
    // 登录
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password');
    await page.click('[data-testid="login-button"]');

    // 验证跳转到仪表板
    await expect(page).toHaveURL('/dashboard');
    
    // 验证主要组件加载
    await expect(page.locator('[data-testid="brand-search-chart"]')).toBeVisible();
    await expect(page.locator('[data-testid="brand-distribution-chart"]')).toBeVisible();
    
    // 验证数据显示
    await expect(page.locator('[data-testid="search-rate"]')).toContainText('%');
  });

  test('user can filter data by time range', async ({ page }) => {
    await page.goto('/dashboard');
    
    // 更改时间筛选
    await page.selectOption('[data-testid="time-range-select"]', '30d');
    await page.click('[data-testid="apply-filter"]');
    
    // 验证URL参数更新
    await expect(page).toHaveURL(/timeRange=30d/);
    
    // 验证图表数据更新
    await page.waitForResponse('/api/v1/analytics/dashboard*');
    await expect(page.locator('[data-testid="chart-loading"]')).not.toBeVisible();
  });

  test('user can export dashboard data', async ({ page }) => {
    await page.goto('/dashboard');
    
    // 点击导出按钮
    await page.click('[data-testid="export-button"]');
    await page.selectOption('[data-testid="export-format"]', 'csv');
    await page.click('[data-testid="confirm-export"]');
    
    // 验证导出请求
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="download-link"]');
    const download = await downloadPromise;
    
    expect(download.suggestedFilename()).toMatch(/dashboard.*\.csv$/);
  });
});
```

### 3.2 响应式测试

```typescript
test.describe('Responsive Design', () => {
  test('dashboard adapts to mobile viewport', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/dashboard');
    
    // 验证移动端布局
    await expect(page.locator('[data-testid="mobile-sidebar"]')).toBeVisible();
    await expect(page.locator('[data-testid="desktop-sidebar"]')).not.toBeVisible();
    
    // 验证图表堆叠显示
    const charts = page.locator('[data-testid^="chart-"]');
    const chartCount = await charts.count();
    
    for (let i = 0; i < chartCount; i++) {
      const chart = charts.nth(i);
      const box = await chart.boundingBox();
      expect(box?.width).toBeLessThan(400);
    }
  });

  test('sidebar collapses on tablet viewport', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.goto('/dashboard');
    
    // 验证侧边栏可以折叠
    await page.click('[data-testid="sidebar-toggle"]');
    await expect(page.locator('[data-testid="sidebar"]')).toHaveClass(/collapsed/);
  });
});
```

## 4. 视觉回归测试

### 4.1 组件截图测试

```typescript
test.describe('Visual Regression', () => {
  test('dashboard components match design', async ({ page }) => {
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // 整体页面截图
    await expect(page).toHaveScreenshot('dashboard-full.png');
    
    // 单个组件截图
    await expect(page.locator('[data-testid="brand-search-chart"]'))
      .toHaveScreenshot('brand-search-chart.png');
    
    await expect(page.locator('[data-testid="brand-distribution-chart"]'))
      .toHaveScreenshot('brand-distribution-chart.png');
  });

  test('dark mode renders correctly', async ({ page }) => {
    await page.goto('/dashboard');
    await page.click('[data-testid="theme-toggle"]');
    
    await expect(page).toHaveScreenshot('dashboard-dark-mode.png');
  });
});
```

## 5. 性能测试

### 5.1 组件性能测试

```typescript
describe('Performance Tests', () => {
  it('should render large dataset efficiently', async () => {
    const largeDataset = generateMockData(10000);
    
    const startTime = performance.now();
    const wrapper = mount(BrandDistributionChart, {
      props: { data: largeDataset }
    });
    await wrapper.vm.$nextTick();
    const endTime = performance.now();
    
    expect(endTime - startTime).toBeLessThan(100); // 100ms内完成渲染
  });

  it('should handle frequent data updates without memory leaks', async () => {
    const wrapper = mount(AIFrequencyChart);
    
    // 模拟频繁数据更新
    for (let i = 0; i < 100; i++) {
      await wrapper.setProps({ data: generateMockData() });
    }
    
    // 检查内存使用情况
    expect(wrapper.vm.$el.children.length).toBeLessThan(1000);
  });
});
```

## 6. 可访问性测试

### 6.1 自动化可访问性测试

```typescript
import { injectAxe, checkA11y } from 'axe-playwright';

test.describe('Accessibility', () => {
  test('dashboard meets WCAG 2.1 AA standards', async ({ page }) => {
    await page.goto('/dashboard');
    await injectAxe(page);
    
    await checkA11y(page, null, {
      detailedReport: true,
      detailedReportOptions: { html: true }
    });
  });

  test('keyboard navigation works correctly', async ({ page }) => {
    await page.goto('/dashboard');
    
    // 测试Tab键导航
    await page.keyboard.press('Tab');
    await expect(page.locator(':focus')).toHaveAttribute('data-testid', 'sidebar-toggle');
    
    await page.keyboard.press('Tab');
    await expect(page.locator(':focus')).toHaveAttribute('data-testid', 'nav-item-home');
  });
});
```

## 7. 测试数据管理

### 7.1 Mock数据生成

```typescript
// test/fixtures/mockData.ts
export const generateDashboardData = (overrides = {}) => ({
  brandSearchRate: {
    totalData: "159.8%",
    percentage: 159.8,
    trend: 'up',
    chartData: [10, 20, 15, 25, 30],
    ...overrides.brandSearchRate
  },
  brandDistribution: {
    title: "品牌在AI市场的首推率",
    brands: [
      { name: 'Intel', percentage: 72.5, color: '#2663FF' },
      { name: 'AMD', percentage: 15.7, color: '#FF6B35' },
      { name: 'Apple', percentage: 9.3, color: '#FFD93D' }
    ],
    ...overrides.brandDistribution
  }
});
```

## 8. 持续集成配置

### 8.1 GitHub Actions配置

```yaml
name: Frontend Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit
      
      - name: Run integration tests
        run: npm run test:integration
      
      - name: Run E2E tests
        run: npm run test:e2e
        
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results
          path: test-results/
```

## 9. 测试覆盖率目标

- **单元测试覆盖率**: ≥ 80%
- **集成测试覆盖率**: ≥ 60%
- **E2E测试覆盖率**: ≥ 40%
- **关键路径覆盖率**: 100%

## 10. 测试维护策略

### 定期维护任务
- 每周更新测试数据
- 每月审查测试用例
- 季度性能基准测试
- 年度测试策略评估

### 测试失败处理
1. 立即调查失败原因
2. 修复或更新测试用例
3. 记录测试变更日志
4. 通知相关开发人员
