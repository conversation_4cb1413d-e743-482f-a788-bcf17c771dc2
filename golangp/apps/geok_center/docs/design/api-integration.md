# GEOK Center 后端 API 设计指南

## 概述

本文档详细描述了 GEOK Center 后端 API 的设计规范、接口定义和实现方案，基于品牌可见性分析的业务需求制定。

## API 架构设计

### 服务分层架构

```
┌─────────────────┐
│   API Gateway   │  ← 统一入口，认证，限流
│   (Gin Router)  │
└─────────────────┘
         │
┌─────────────────┐
│  Handler Layer  │  ← 请求处理，参数验证
│                 │
└─────────────────┘
         │
┌─────────────────┐
│ Service Layer   │  ← 业务逻辑，算法实现
│                 │
└─────────────────┘
         │
┌─────────────────┐
│Repository Layer │  ← 数据访问，缓存管理
│                 │
└─────────────────┘
         │
┌─────────────────┐
│  Database Layer │  ← PostgreSQL, Redis
│                 │
└─────────────────┘
```

### 核心 API 模块

#### 1. 认证授权模块 (Auth Module)

```go
// 用户认证相关接口
POST   /api/v1/auth/register     // 用户注册
POST   /api/v1/auth/login        // 用户登录
POST   /api/v1/auth/refresh      // Token刷新
DELETE /api/v1/auth/logout       // 用户登出
POST   /api/v1/auth/reset-password // 密码重置
```

#### 2. 企业管理模块 (Company Module)

```go
// 企业信息管理
GET    /api/v1/companies/profile    // 获取企业信息
PUT    /api/v1/companies/profile    // 更新企业信息
GET    /api/v1/companies/users      // 获取企业用户列表
POST   /api/v1/companies/users      // 邀请用户
DELETE /api/v1/companies/users/:id  // 移除用户
```

#### 3. 品牌管理模块 (Brand Module)

```go
// 品牌信息管理
GET    /api/v1/brands              // 获取品牌列表
POST   /api/v1/brands              // 创建品牌
GET    /api/v1/brands/:id          // 获取品牌详情
PUT    /api/v1/brands/:id          // 更新品牌信息
DELETE /api/v1/brands/:id          // 删除品牌
GET    /api/v1/brands/search       // 品牌搜索
```

#### 4. 数据分析模块 (Analytics Module)

```go
// 核心分析接口
GET /api/v1/analytics/dashboard           // 仪表板数据
GET /api/v1/analytics/brand-visibility    // 品牌可见性分析
GET /api/v1/analytics/ai-appearance       // AI平台出现频率
GET /api/v1/analytics/competitor-analysis // 竞争对手分析
GET /api/v1/analytics/trend-analysis      // 趋势分析
GET /api/v1/analytics/search-performance  // 搜索表现分析
```

## UI 组件数据映射

### 1. 侧边导航栏数据

**组件**: `Sidebar.vue`
**API 端点**: 无需 API (静态配置)

```typescript
// 导航配置
const navigationItems = [
  {
    id: "home",
    label: "首页",
    icon: "home",
    route: "/dashboard",
    active: true,
  },
  { id: "inbox", label: "收件箱", icon: "inbox", route: "/inbox" },
  { id: "conversations", label: "对话", icon: "chat", route: "/conversations" },
  { id: "geo", label: "GEO优化", icon: "location", route: "/geo" },
  {
    id: "ai-content",
    label: "AI内容生成",
    icon: "robot",
    route: "/ai-content",
  },
  { id: "settings", label: "设置", icon: "settings", route: "/settings" },
];
```

### 2. 仪表板数据

**组件**: `Dashboard.vue`
**API 端点**: `GET /api/v1/analytics/dashboard`

```typescript
interface DashboardResponse {
  brandSearchRate: {
    totalData: string;
    percentage: number;
    trend: "up" | "down";
    chartData: {
      labels: string[];
      values: number[];
    };
  };
  brandDistribution: {
    title: string;
    description: string;
    data: {
      brand: string;
      percentage: number;
      color: string;
    }[];
  };
  aiFrequency: {
    title: string;
    description: string;
    keywords: string[];
    timeComparison: {
      current: string;
      previous: string;
    };
    data: {
      brand: string;
      currentPeriod: number[];
      previousPeriod: number[];
      color: string;
    }[];
  };
}
```

### 3. 品牌分布数据

**组件**: `BrandDistributionChart.vue`
**API 端点**: `GET /api/v1/analytics/brand-distribution`

```typescript
interface BrandDistributionResponse {
  title: string;
  description: string;
  totalBrands: number;
  data: {
    brand: string;
    percentage: number;
    color: string;
    marketShare: number;
    trend: "up" | "down" | "stable";
  }[];
  lastUpdated: string;
}
```

**请求参数**:

```typescript
interface BrandDistributionParams {
  timeRange?: "7d" | "30d" | "90d";
  region?: string;
  aiPlatform?: string[];
}
```

### 4. AI 出现频率数据

**组件**: `AIFrequencyChart.vue`
**API 端点**: `GET /api/v1/analytics/ai-appearance`

```typescript
interface AIAppearanceResponse {
  title: string;
  description: string;
  keywords: string[];
  timeComparison: {
    current: {
      label: string;
      period: string;
    };
    previous: {
      label: string;
      period: string;
    };
  };
  brands: {
    name: string;
    color: string;
    currentData: number[];
    previousData: number[];
    totalAppearances: number;
    changePercentage: number;
  }[];
  chartConfig: {
    xAxisLabels: string[];
    yAxisMax: number;
  };
}
```

### 5. 品牌推荐率数据

**组件**: `BrandRecommendationTable.vue`
**API 端点**: `GET /api/v1/analytics/brand-recommendations`

```typescript
interface BrandRecommendationResponse {
  brands: {
    id: string;
    name: string;
    logo?: string;
    currentMonthSearch: number;
    currentMonthLoss: number;
    recommendationRate: number;
    trend: "up" | "down" | "stable";
    trendPercentage: number;
    detailsUrl: string;
  }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
```

## 数据筛选和查询

### 筛选参数标准化

所有分析 API 端点支持以下通用筛选参数：

```typescript
interface CommonFilterParams {
  // 时间范围
  timeRange?: "7d" | "30d" | "90d" | "custom";
  startDate?: string; // ISO 8601 format
  endDate?: string; // ISO 8601 format

  // 地理位置
  region?: string[];
  country?: string[];

  // AI平台
  aiPlatform?: string[];

  // 品牌筛选
  brands?: string[];

  // 排序
  sortBy?: string;
  sortOrder?: "asc" | "desc";

  // 分页
  page?: number;
  limit?: number;
}
```

### 实时数据更新

**WebSocket 连接** (可选实现):

```typescript
// WebSocket事件类型
interface WebSocketEvents {
  "dashboard-update": DashboardResponse;
  "brand-distribution-update": BrandDistributionResponse;
  "ai-frequency-update": AIAppearanceResponse;
}

// 连接示例
const ws = new WebSocket("ws://localhost:8080/api/v1/ws");
ws.addEventListener("message", (event) => {
  const { type, data } = JSON.parse(event.data);
  handleRealtimeUpdate(type, data);
});
```

## 错误处理

### 标准错误响应格式

```typescript
interface APIError {
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
  path: string;
}
```

### 常见错误码

- `AUTH_REQUIRED`: 需要认证
- `INVALID_PARAMS`: 参数无效
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `DATA_NOT_FOUND`: 数据不存在
- `INTERNAL_ERROR`: 服务器内部错误

## 缓存策略

### 前端缓存配置

```typescript
interface CacheConfig {
  // 仪表板数据缓存5分钟
  dashboard: { ttl: 300000 };

  // 品牌分布数据缓存10分钟
  brandDistribution: { ttl: 600000 };

  // AI频率数据缓存3分钟
  aiFrequency: { ttl: 180000 };

  // 品牌列表缓存30分钟
  brands: { ttl: 1800000 };
}
```

### 缓存失效策略

- 用户主动刷新时清除所有缓存
- 数据更新后自动失效相关缓存
- 定时检查缓存有效性

## 数据导出集成

### 导出请求格式

```typescript
interface ExportRequest {
  type: "dashboard" | "brand-distribution" | "ai-frequency";
  format: "csv" | "json" | "pdf";
  filters: CommonFilterParams;
  options?: {
    includeCharts?: boolean;
    includeRawData?: boolean;
    customFields?: string[];
  };
}
```

### 导出流程

1. 前端发送导出请求到 `POST /api/v1/exports/dashboard`
2. 后端返回任务 ID 和状态
3. 前端轮询任务状态或通过 WebSocket 接收完成通知
4. 导出完成后通过 `GET /api/v1/exports/download/:filename` 下载

## 性能优化

### 数据预加载

```typescript
// 页面加载时预加载关键数据
const preloadData = async () => {
  const promises = [
    api.get("/api/v1/analytics/dashboard"),
    api.get("/api/v1/brands"),
    api.get("/api/v1/analytics/brand-distribution"),
  ];

  const [dashboard, brands, distribution] = await Promise.allSettled(promises);
  return { dashboard, brands, distribution };
};
```

### 分页和虚拟滚动

对于大数据集，实现分页加载和虚拟滚动：

```typescript
interface PaginatedRequest {
  page: number;
  limit: number;
  filters: CommonFilterParams;
}

// 虚拟滚动配置
const virtualScrollConfig = {
  itemHeight: 60,
  bufferSize: 10,
  threshold: 5,
};
```

## 开发环境配置

### API 代理配置 (Vite)

```typescript
// vite.config.ts
export default defineConfig({
  server: {
    proxy: {
      "/api": {
        target: "http://localhost:8080",
        changeOrigin: true,
        secure: false,
      },
    },
  },
});
```

### 环境变量

```bash
# .env.development
VITE_API_BASE_URL=http://localhost:8080/api/v1
VITE_WS_URL=ws://localhost:8080/api/v1/ws

# .env.production
VITE_API_BASE_URL=https://api.geok.com/api/v1
VITE_WS_URL=wss://api.geok.com/api/v1/ws
```

## 测试策略

### API 集成测试

```typescript
// 使用 MSW (Mock Service Worker) 进行API模拟
import { rest } from "msw";

export const handlers = [
  rest.get("/api/v1/analytics/dashboard", (req, res, ctx) => {
    return res(ctx.json(mockDashboardData));
  }),

  rest.get("/api/v1/analytics/brand-distribution", (req, res, ctx) => {
    return res(ctx.json(mockBrandDistributionData));
  }),
];
```

### 错误场景测试

- 网络连接失败
- API 响应超时
- 数据格式错误
- 认证失效

## 部署注意事项

### CORS 配置

确保后端正确配置 CORS 头：

```go
// Go Gin CORS配置示例
config := cors.DefaultConfig()
config.AllowOrigins = []string{"http://localhost:3000", "https://geok.com"}
config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
config.AllowHeaders = []string{"Authorization", "Content-Type"}
```

### 生产环境优化

- 启用 API 响应压缩
- 配置适当的缓存头
- 实现 API 限流
- 监控 API 性能指标
