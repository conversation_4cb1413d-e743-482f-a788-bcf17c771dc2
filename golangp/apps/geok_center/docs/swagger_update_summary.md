# Swagger.json 更新总结

## 📋 更新概述

本次更新对 `swagger.json` 文件进行了全面的清理和补全，移除了不存在的API端点，补全了缺失的AI可见性指标相关API。

## ❌ 已删除的API端点（不存在的）

### 1. 旧的可见性指标API
- `DELETE /brands/{id}/visibility-metrics` - 旧的品牌可见性指标API
- `DELETE /visibility-metrics` - 旧的可见性指标列表API

这些API端点在实际代码中不存在，已从swagger文档中移除。

## ✅ 新增的AI可见性指标API端点

### 1. AI响应级别的可见性指标API
- `POST /ai-visibility/responses/{responseId}/calculate` - 计算AI响应可见性指标
- `GET /ai-visibility/responses/{responseId}/metrics` - 获取AI响应可见性指标

### 2. 批量处理和统计API
- `POST /ai-visibility/batch-calculate` - 批量计算可见性指标
- `GET /ai-visibility/stats` - 获取可见性指标统计

### 3. 品牌级别的AI可见性指标API
- `GET /brands/{id}/ai-visibility/trend` - 获取品牌可见性趋势
- `POST /brands/{id}/ai-visibility/aggregation` - 计算品牌可见性聚合
- `GET /brands/{id}/ai-visibility/aggregation` - 获取品牌可见性聚合历史
- `GET /brands/{id}/ai-visibility/report` - 生成品牌可见性报告

### 4. 品牌对比API
- `GET /ai-visibility/compare/brands/{id1}/{id2}` - 品牌可见性对比

## 📊 新增的数据模型

### 1. AIVisibilityMetrics Schema
完整的AI可见性指标数据模型，包含：

#### 基础字段
- `id`: 记录唯一标识符
- `ai_search_id`: 关联的AI搜索记录ID
- `ai_response_id`: 关联的AI搜索响应ID
- `brand_id`: 关联的品牌ID

#### 可见性数据 (visibility_data)
- `品牌推荐率`: 品牌推荐率得分
- `品牌搜索率`: 品牌搜索率得分
- `品牌在AI市场的首推率`: AI市场首推率得分
- `品牌提及位置`: 品牌在响应中的位置
- `品牌情感倾向`: 品牌情感倾向
- `竞争对手数量`: 竞争对手数量
- `市场占有率`: 市场占有率
- `额外指标`: 响应质量和相关性得分

#### 关键词数据 (keyword_data)
支持基础关键词和复杂关键词短语：

**基础关键词**：
- `价格`, `质量`, `性能`, `性价比`, `品牌`, `产品`

**复杂关键词短语**：
- `CPU需要配什么显卡`
- `CPU核心数量对游戏影响`
- `最佳处理器选择`
- `电脑配置推荐`
- `游戏性能对比`
- `办公电脑配置`

**元数据**：
- `主要关键词`: 主要关键词列表
- `关键词密度`: 关键词密度
- `语义相关性`: 语义相关性得分
- `搜索意图匹配度`: 搜索意图匹配度
- `长尾关键词`: 长尾关键词列表
- `关键词分类`: 关键词分类对象
- `额外关键词数据`: 总词数、唯一词数、复杂度评分

#### 得分字段
- `overall_score`: 综合可见性得分(0-100)
- `frequency_score`: AI中出现频率得分(0-100)
- `recommendation_score`: 品牌推荐率得分(0-100)
- `search_rate_score`: 品牌搜索率得分(0-100)
- `first_choice_score`: AI市场首推率得分(0-100)

#### 时间字段
- `calculated_at`: 指标计算时间
- `created_at`: 记录创建时间
- `updated_at`: 记录最后更新时间

### 2. BatchCalculateRequest Schema
批量计算请求的数据模型：
- `response_ids`: AI响应ID列表

## 🏷️ 更新的标签

- 将 `"Visibility Metrics"` 更新为 `"AI Visibility"`
- 描述更新为 `"AI可见性指标接口"`

## 🎯 与Figma设计的对应关系

新的API文档完全符合Figma设计要求：

1. **关键词直接作为JSON key** - 支持"价格"、"质量"等基础关键词
2. **复杂关键词短语支持** - 支持"CPU需要配什么显卡"等长关键词
3. **完整的可见性指标** - 包含推荐率、搜索率、首推率等所有指标
4. **灵活的数据结构** - 支持嵌套对象、数组等复杂数据类型

## ✅ 验证结果

- ✅ JSON格式验证通过
- ✅ 所有新增API端点已正确定义
- ✅ 数据模型完整且符合实际实现
- ✅ 旧的不存在API端点已清理
- ✅ 标签和描述已更新

## 📝 使用说明

更新后的swagger.json文件可以：
1. 导入到APIPost等API测试工具中进行测试
2. 用于生成API文档
3. 作为前端开发的接口规范参考
4. 用于API自动化测试

所有AI可见性指标相关的API都已完整定义，支持从单个响应计算到批量处理，从趋势分析到品牌对比的全套功能。
