# GEOK Center Models 重构总结

## 重构概述

根据新的业务流程需求，对 GEOK Center 的 models 进行了全面重构，实现了以下业务流程：

1. **用户创建品牌** → **自动生成 AI 搜索问题** → **触发 AI 交互** → **收集多模型回答**
2. **基于 AI 回答生成可见性指标** → **品牌推荐率、搜索率、首推率分析**
3. **生成提示内容分析** → **评分、排名、份额占比、点击数据、关键词频率**
4. **提取引用信息** → **排名、域名、类别、数量、分享数据**

## 保留的 Models

按照要求，以下 models 保持不变：

### 1. user.go

- **User** - 用户基本信息
- **UserSession** - 用户会话管理
- 支持 Google OAuth 登录

### 2. blog.go

- **BlogOverview** - 博客概览统计
- **Blog** - 博客内容
- **BlogCategory** - 博客分类
- **BlogTag** - 博客标签

### 3. subscription.go

- **Subscription** - 用户订阅信息
- **SubscriptionFeatures** - 订阅功能特性

### 4. notification.go

- **Notification** - 通知信息
- **NotificationSetting** - 通知设置

## 新增的 Models

### 1. ai_searches.go - AI 搜索核心表

**核心业务流程表，实现品牌问题生成和 AI 多模型交互**

#### AISearch - AI 搜索记录

- 存储品牌相关的问题
- 支持批次处理
- 包含问题类型、关键词、状态管理
- 自动生成品牌相关问题的函数

#### AISearchResponse - AI 搜索响应

- 存储各 AI 模型的回答（ChatGPT、Claude、Deepseek 等）
- 品牌提及分析（位置、情感、上下文）
- 响应质量指标（置信度、相关性、完整性）
- 提取结构化数据和引用信息

#### AISearchBatch - AI 搜索批次

- 批量处理 AI 搜索任务
- 进度跟踪和状态管理
- 支持多模型并行处理

### 2. ai_visibility_metrics.go - AI 可见性指标表

**存储 AI 搜索问题的可见性指标数据**

#### AIVisibilityMetrics - AI 可见性指标

- **可见性指标数据** (JSON 格式，支持中文 key)
- **关键词相关指标** (JSON 格式，支持中文 key)
- **聚合得分** (便于快速查询和排序)
- 与 AI 搜索响应直接关联

#### AIVisibilityAggregation - AI 可见性聚合

- 品牌级别的聚合统计数据
- 时间范围内的平均得分
- 趋势分析和变化百分比

### 3. prompts.go - 重构后的提示表

**基于 AI 回答生成的提示内容分析**

#### Prompt - 提示内容

- **评分系统** (0-100 分)
- **排名层级** (top/high/medium/low)
- **份额占比** 数据
- **点击数量** 和参与度指标
- **关键词频率** 分析 (JSON 格式)
- **主要关键词** 列表 (JSON 格式)
- 质量、相关性、原创性得分

#### PromptMetric - 提示指标

- 时间序列性能数据
- 点击率、转化率分析
- 地区和平台分解数据

#### PromptKeyword - 提示关键词

- 关键词重要性分析
- 搜索量和竞争度数据
- 搜索意图分类

### 4. references.go - 引用表

**AI 回答中的引用信息分析**

#### Reference - 引用记录

- **排名位置** 在 AI 回答中的排名
- **域名信息** 和网站分类
- **引用类别** (新闻、教育、技术等)
- **引用数量** 和提及次数
- **分享数据** 和参与度指标
- 权威性、信任度、质量得分
- 内容分析和情感得分

#### ReferenceAnalysis - 引用分析

- 聚合引用统计
- 域名分布分析
- 类别和类型分布
- 趋势和增长率分析

### 5. brand.go - 简化的品牌表

**保留核心品牌信息，移除复杂关联**

#### Brand - 品牌基本信息

- 基本品牌信息（名称、域名、关键词）
- 资产验证状态
- 监控起始类型
- 简化的关联关系

## 删除的 Models

以下 models 已被删除或重构：

1. **geo_optimization.go** - 地理优化相关（与新业务流程不符）
2. **visibility.go** - 旧的可见性模型（被新的 visibility_metrics.go 替代）
3. **prompt.go** - 旧的提示模型（被新的 prompts.go 替代）
4. **brand.go 中的复杂模型** - SearchMetric、AIAppearance、CompetitorData 等

## 核心业务流程实现

### 1. 品牌创建 → AI 问题生成

```go
// 自动生成品牌相关问题
questions := GenerateBrandQuestions(brand)
// 创建AI搜索记录
for _, question := range questions {
    aiSearch := &AISearch{
        BrandID: brand.ID,
        Question: question,
        Status: AISearchStatusPending,
    }
}
```

### 2. AI 多模型交互

```go
// 触发AI交互（占位函数，待实现）
CreateAISearchFunction(aiSearch)
// 存储多模型响应
for _, model := range []AIModelType{AIModelChatGPT, AIModelClaude, AIModelDeepseek} {
    response := &AISearchResponse{
        AISearchID: aiSearch.ID,
        ModelType: model,
        Response: "AI回答内容",
        BrandMentioned: true,
        BrandPosition: 1,
    }
}
```

### 3. 可见性指标生成

```go
// 基于AI回答生成可见性指标
visibilityMetric := &VisibilityMetric{
    BrandID: brand.ID,
    MetricType: VisibilityMetricRecommendation,
    RecommendationData: json.Marshal(recommendationData),
    SearchRateData: json.Marshal(searchRateData),
    FirstChoiceData: json.Marshal(firstChoiceData),
}
```

### 4. 提示内容分析

```go
// 生成提示内容
prompt := &Prompt{
    BrandID: brand.ID,
    Content: "基于AI回答生成的提示内容",
    Score: 85.5,
    Ranking: 3,
    RankingTier: PromptRankingTierTop,
    ShareRate: 12.5,
    ClickCount: 1250,
    MainKeywords: json.Marshal(keywords),
    KeywordFrequency: json.Marshal(frequency),
}
```

### 5. 引用信息提取

```go
// 提取AI回答中的引用
reference := &Reference{
    AISearchID: aiSearch.ID,
    BrandID: brand.ID,
    Domain: "example.com",
    Category: ReferenceCategoryTechnology,
    Ranking: 1,
    ShareCount: 500,
    AuthorityScore: 90.0,
}
```

## 数据库迁移

更新了 `cmd/migrate.go` 文件，新的迁移顺序：

1. 用户和品牌基础表
2. AI 搜索相关表 (ai_searches, ai_search_responses, ai_search_batches)
3. AI 可见性指标表 (ai_visibility_metrics, ai_visibility_aggregations)
4. 提示表 (prompts, prompt_metrics, prompt_keywords)
5. 引用表 (references, reference_analyses)
6. 通知系统表

## 特性亮点

### 1. 灵活的 JSON 存储

- 可见性指标使用 JSON 格式存储复杂数据
- 支持动态字段扩展
- 便于前端展示和分析

### 2. 多模型 AI 支持

- 支持 ChatGPT、Claude、Deepseek 等多种 AI 模型
- 统一的响应格式和分析框架
- 可扩展的模型类型定义

### 3. 完整的分析链路

- 从品牌创建到最终分析报告的完整数据流
- 支持批量处理和实时分析
- 丰富的指标和趋势分析

### 4. 高性能设计

- 合理的索引设计
- 软删除支持
- 分页和过滤支持

## 下一步工作

1. **实现 AI 交互逻辑** - 完善 `CreateAISearchFunction` 函数
2. **开发分析算法** - 实现可见性指标计算逻辑
3. **创建 API 接口** - 为新的 models 创建 CRUD 接口
4. **编写测试用例** - 确保新 models 的稳定性
5. **数据迁移脚本** - 从旧 models 迁移现有数据

## 总结

本次重构成功实现了新的业务流程需求，建立了从品牌创建到 AI 分析的完整数据模型。新的架构更加灵活、可扩展，能够支持复杂的 AI 分析和可见性指标计算。同时保持了代码的整洁性和可维护性。
