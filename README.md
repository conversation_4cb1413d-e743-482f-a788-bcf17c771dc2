# PointerWorkspace

The monorepo for PointerWorkspace

The workspace is managed by <PERSON><PERSON>.

You can find a Bazel plugin for VS Code and JetBrains IDEs.

We recommend using VS Code and CLion as development tools.

CLion also supports Python.

## Running Demo Apps

You can try the demo Go and Python apps.

```shell
bazel run //golangp/apps/golang_example_app:hello_pointer
```

```shell
bazel run //pythonp/apps/python_example_app:hello_pointer
```

## Testing

To run tests, use the following commands:

```shell
# Run all tests
bazel test //...

# Run specific test
bazel test //pythonp/test/common/wechat:robot_test
bazel test //pythonp/test/common/perplexity:perplexity_client_test

# Run tests in a specific directory
bazel test //pythonp/test/...
```

Tests are organized in the `test` directory, mirroring the structure of the source code. For example:

- Source: `pythonp/common/wechat/robot.py`
- Test: `pythonp/test/common/wechat/robot_test.py`

Dependency Compilation

- Python

pip-tools generates requirements.txt through requirements.in

```shell
bazel run //:requirements.update
```

The requirements_lock.txt will be updated. If you need to create a new version set of python packages, you need to create a separate requirements.in file.

- Go

Add a New Dependency:

```shell
bazel run @rules_go//go get golang.org/x/crypto/bcrypt
```

go get example.com/package This will add example.com/package to your `go.mod` file.

Dependencies are defined in the `go.mod` file. To integrate these dependencies into the Bazel build system, execute the following command:

```shell
# tidy
bazel run @rules_go//go -- mod tidy -v
# bazel run //:gazelle-update-repos
```

This command uses the Gazelle tool to read the go.mod file and update the deps.bzl file, which contains all the Go dependencies.

For auto-formatting:

```
 go fmt ./golangp/...
```

Coding style formatter for Python

We will be using YAPF to manage our code style.

https://github.com/google/yapf

For auto-formatting:

```shell
pip install yapf
yapf -r -i ./pythonp/
```

\-r represents recursive -i means in-place

We will enforce yapf check in CI/CD.

## Java Project

This repository contains a simple Java example application located in the `javap/` directory. The demo shows how to use Bazel to build and run a Java program, and how to integrate the Log4j logging library.

### Dependency Management

Java dependencies are managed centrally via the `maven.install` block in the `MODULE.bazel` file. For example, Log4j dependencies are declared as follows:

```python
maven.install(
    artifacts = [
        "org.apache.logging.log4j:log4j-api:2.24.3",
        "org.apache.logging.log4j:log4j-core:2.24.3",
        # Add new dependencies here
    ],
    ...
)
```

To add a new dependency:

1. Add the corresponding Maven coordinate to the `artifacts` list in `MODULE.bazel`.

2. Update the dependency lock file by running:

   ```shell
   bazel run @unpinned_maven//:pin
   ```

### Running the Java Demo

From the project root, run:

```shell
bazel run //javap/src/main/java/com/example:hello
```

### Code Overview

- Main class: `javap/src/main/java/com/example/HelloWorld.java`
- Log configuration: `javap/src/main/resources/log4j2.xml`
- Bazel build file: `javap/BUILD.bazel`

This demo uses Log4j for logging and demonstrates usage of info, debug, and error log levels.

### Code Formatting

To format all Java files in the project, run:

```shell
bash ./scripts/format_all.sh
```

This will format all Java files in the `javap` directory using Google Java Format with AOSP style.

### Format Bazel build files

```
bazel run //:buildifier
```