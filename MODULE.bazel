###############################################################################
# <PERSON><PERSON> now uses B<PERSON>lmod by default to manage external dependencies.
# Please consider migrating your external dependencies from WORKSPACE.bazel to MODULE.bazel.
#
# For more details, please check https://github.com/bazelbuild/bazel/issues/18958
###############################################################################

module(
    name = "pointer",
    version = "0.0.1",
)

bazel_dep(name = "rules_go", version = "0.50.1")
bazel_dep(name = "gazelle", version = "0.40.0")
bazel_dep(name = "rules_python", version = "1.2.0")
bazel_dep(name = "rules_pkg", version = "1.0.1")

## proto
bazel_dep(name = "rules_proto", version = "7.1.0")
bazel_dep(name = "rules_proto_grpc", version = "5.0.1")
bazel_dep(name = "rules_proto_grpc_cpp", version = "5.0.1")
bazel_dep(name = "rules_proto_grpc_go", version = "5.0.1")
bazel_dep(name = "rules_proto_grpc_python", version = "5.0.1")  # 5.27.2
bazel_dep(name = "rules_proto_grpc_java", version = "5.0.1")
bazel_dep(name = "rules_proto_grpc_grpc_gateway", version = "5.1.0")
bazel_dep(name = "rules_proto_grpc_doc", version = "5.1.0")

## fix protobuf version mismatch
# bazel_dep(name = "protobuf", version = "29.2", repo_name = "com_google_protobuf")  # 5.29.2
bazel_dep(name = "toolchains_protoc", version = "0.3.7")

protoc = use_extension("@toolchains_protoc//protoc:extensions.bzl", "protoc")
protoc.toolchain(
    google_protobuf = "com_google_protobuf",
    # Pin to any version of protoc
    version = "v27.2",
)
use_repo(protoc, "com_google_protobuf", "toolchains_protoc_hub")

register_toolchains("@toolchains_protoc_hub//:all")

bazel_dep(name = "googleapis", version = "0.0.0-20241220-5e258e33.bcr.1", repo_name = "com_google_googleapis")
bazel_dep(name = "googleapis-go", version = "1.0.0")
bazel_dep(name = "rules_kotlin", version = "2.1.0")

go_sdk = use_extension("@rules_go//go:extensions.bzl", "go_sdk")
go_sdk.download(
    goarch = "amd64",
    goos = "linux",
    version = "1.23.2",
)

go_deps = use_extension("@gazelle//:extensions.bzl", "go_deps")
go_deps.from_file(go_mod = "//:go.mod")
use_repo(
    go_deps,
    "com_github_aliyun_alibaba_cloud_sdk_go",
    "com_github_aliyun_aliyun_oss_go_sdk",
    "com_github_artisancloud_powerwechat",
    "com_github_artisancloud_powerwechat_v3",
    "com_github_gin_gonic_gin",
    "com_github_go_redsync_redsync_v4",
    "com_github_golang_jwt_jwt",
    "com_github_google_uuid",
    "com_github_k3a_html2text",
    "com_github_minio_minio_go_v7",
    "com_github_olivere_elastic_v7",
    "com_github_pkg_errors",
    "com_github_rabbitmq_amqp091_go",
    "com_github_redis_go_redis_v9",
    "com_github_smartwalle_alipay_v3",
    "com_github_sony_sonyflake",
    "com_github_spf13_cobra",
    "com_github_spf13_viper",
    "in_gopkg_gomail_v2",
    "io_gorm_datatypes",
    "io_gorm_driver_mysql",
    "io_gorm_driver_postgres",
    "io_gorm_gorm",
    "org_golang_google_api",
    "org_golang_x_crypto",
    "org_mongodb_go_mongo_driver",
)

# python bazel config
python = use_extension("@rules_python//python/extensions:python.bzl", "python")
python.toolchain(
    is_default = True,
    python_version = "3.10",
)

pip = use_extension("@rules_python//python/extensions:pip.bzl", "pip")
pip.parse(
    hub_name = "py_deps",
    python_version = "3.10",
    requirements_lock = "//:requirements_lock.txt",
)
use_repo(pip, "py_deps")

## JAVA
bazel_dep(name = "rules_jvm_external", version = "6.6")
bazel_dep(name = "rules_java", version = "8.6.3")
bazel_dep(name = "grpc-java", version = "1.71.0")

# To update maven dependencies, update the lines below and then run:
# bazel run @unpinned_maven//:pin
maven = use_extension("@rules_jvm_external//:extensions.bzl", "maven")
maven.install(
    artifacts = [
        # Logger dependencies
        "org.apache.logging.log4j:log4j-api:2.24.3",
        "org.apache.logging.log4j:log4j-core:2.24.3",
        # grpc
        "com.google.guava:guava:32.0.1-jre",
        "io.grpc:grpc-netty-shaded:1.62.2",
        "io.grpc:grpc-protobuf:1.62.2",
        "io.grpc:grpc-stub:1.62.2",
        "javax.annotation:javax.annotation-api:1.3.2",
        "com.google.protobuf:protobuf-java:3.21.12",
        # Command line arg parsing
        "com.beust:jcommander:1.82",
        "com.fasterxml.jackson.core:jackson-databind:2.18.2",
        "com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.18.2",
        "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.18.2",
        "com.github.spotbugs:spotbugs-annotations:4.8.6",
        "com.google.dagger:dagger-compiler:2.54",
        "com.google.dagger:dagger-producers:2.54",
        "com.google.dagger:dagger-spi:2.54",
        "com.google.dagger:dagger:2.54",
        "com.google.googlejavaformat:google-java-format:1.25.2",
        "com.google.guava:guava:33.4.0-jre",
    ],
    fail_if_repin_required = True,
    fetch_sources = True,
    lock_file = "//:maven_install.json",
    repositories = [
        "https://repo1.maven.org/maven2",
        "https://maven.google.com",
    ],
)
use_repo(maven, "maven", "unpinned_maven")

# Buildifier
bazel_dep(
    name = "buildifier_prebuilt",
    version = "7.3.1",
    dev_dependency = True,
)

# rules_oci
bazel_dep(name = "aspect_bazel_lib", version = "2.10.0")
bazel_dep(name = "container_structure_test", version = "1.19.1")
bazel_dep(name = "rules_oci", version = "2.2.0")

oci = use_extension("@rules_oci//oci:extensions.bzl", "oci")
oci.pull(
    name = "distroless_java",
    digest = "sha256:38e4b51e5fbd44e5b3f8d77bcc8ae573f265174249dad7316aa3a9ce0ada0cfc",
    image = "gcr.io/distroless/java17",
    #platforms = ["linux/arm64"],
)
use_repo(oci, "distroless_java")
